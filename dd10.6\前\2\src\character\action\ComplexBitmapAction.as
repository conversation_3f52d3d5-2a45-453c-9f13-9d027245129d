package character.action
{
   public class ComplexBitmapAction extends BaseAction
   {
      
      private var _assets:Vector.<FrameByFrameItem>;
      
      private var _index:int;
      
      public function ComplexBitmapAction(_arg_1:Vector.<FrameByFrameItem>, _arg_2:String = "", _arg_3:String = "", _arg_4:uint = 0, _arg_5:<PERSON><PERSON><PERSON> = false)
      {
         var _local_6:FrameByFrameItem = null;
         super(_arg_2,_arg_3,_arg_4,_arg_5);
         _type = BaseAction.COMPLEX_ACTION;
         this._assets = _arg_1;
         for each(_local_6 in this._assets)
         {
            _len = Math.max(_len,_local_6.totalFrames);
         }
         this._index = 0;
      }
      
      override public function get len() : int
      {
         return _len;
      }
      
      override public function reset() : void
      {
         var _local_1:FrameByFrameItem = null;
         for each(_local_1 in this._assets)
         {
            _local_1.reset();
         }
         this._index = 0;
      }
      
      public function update() : void
      {
         ++this._index;
      }
      
      override public function dispose() : void
      {
         this._assets = null;
         super.dispose();
      }
      
      override public function get isEnd() : Bo<PERSON>an
      {
         return this._index >= _len - 1;
      }
      
      public function get assets() : Vector.<FrameByFrameItem>
      {
         return this._assets;
      }
      
      override public function toXml() : XML
      {
         var _local_3:FrameByFrameItem = null;
         var _local_2:int = 0;
         var _local_1:XML = super.toXml();
         while(_local_2 < this._assets.length)
         {
            _local_3 = this._assets[_local_2];
            _local_1.appendChild(_local_3.toXml());
            _local_2++;
         }
         return _local_1;
      }
   }
}

