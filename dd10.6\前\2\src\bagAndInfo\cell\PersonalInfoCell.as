package bagAndInfo.cell
{
   import baglocked.BaglockedManager;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.events.InteractiveEvent;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.utils.DoubleClickManager;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.events.CellEvent;
   import ddt.manager.DragManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.PositionUtils;
   import ddt.view.tips.GoodTipInfo;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import gemstone.info.GemstoneInfo;
   
   public class PersonalInfoCell extends BagCell
   {
      
      public var gemstoneList:Vector.<GemstoneInfo>;
      
      private var _isGemstone:Boolean = false;
      
      private var _shineObject:MovieClip;
      
      public function PersonalInfoCell(_arg_1:int = 0, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true)
      {
         super(_arg_1,_arg_2,_arg_3);
         _bg.visible = false;
         _picPos = new Point(2,0);
         this.initEvents();
      }
      
      private function initEvents() : void
      {
         addEventListener("interactive_click",this.onClick);
         addEventListener("interactive_double_click",this.onDoubleClick);
         DoubleClickManager.Instance.enableDoubleClick(this);
      }
      
      private function removeEvents() : void
      {
         removeEventListener("interactive_click",this.onClick);
         removeEventListener("interactive_double_click",this.onDoubleClick);
         DoubleClickManager.Instance.disableDoubleClick(this);
      }
      
      override public function set info(_arg_1:ItemTemplateInfo) : void
      {
         super.info = _arg_1;
         if(Boolean(_tipData) && _tipData is GoodTipInfo)
         {
            GoodTipInfo(_tipData).suitIcon = true;
         }
      }
      
      override public function dragStart() : void
      {
         if(_info && !locked && stage && allowDrag)
         {
            if(DragManager.startDrag(this,_info,createDragImg(),stage.mouseX,stage.mouseY,"move"))
            {
               SoundManager.instance.play("008");
               locked = true;
            }
         }
      }
      
      override protected function onMouseOver(_arg_1:MouseEvent) : void
      {
      }
      
      override protected function onMouseClick(_arg_1:MouseEvent) : void
      {
      }
      
      protected function onClick(_arg_1:InteractiveEvent) : void
      {
         dispatchEvent(new CellEvent("itemclick",this));
      }
      
      protected function onDoubleClick(_arg_1:InteractiveEvent) : void
      {
         SoundManager.instance.playButtonSound();
         if(Boolean(info))
         {
            dispatchEvent(new CellEvent("doubleclick",this));
         }
      }
      
      override public function dragDrop(_arg_1:DragEffect) : void
      {
         var _local_3:int = 0;
         var _local_2:* = null;
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            _arg_1.action = "none";
            return;
         }
         var _local_4:InventoryItemInfo = _arg_1.data as InventoryItemInfo;
         if(Boolean(_local_4))
         {
            if(PlayerManager.Instance.Self.bagLocked)
            {
               return;
            }
            if(_local_4.Place < 29 && _local_4.BagType != 1)
            {
               return;
            }
            if((_local_4.BindType == 1 || _local_4.BindType == 2 || _local_4.BindType == 3) && _local_4.IsBinds == false && _local_4.TemplateID != 11560 && _local_4.TemplateID != 11561 && _local_4.TemplateID != 11562)
            {
               _local_2 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("tank.view.bagII.BagIIView.BindsInfo"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),true,true,true,2);
               _local_2.addEventListener("response",this.__onBindResponse);
               temInfo = _local_4;
               DragManager.acceptDrag(this,"none");
               return;
            }
            if(PlayerManager.Instance.Self.canEquip(_local_4))
            {
               if(this.getCellIndex(_local_4).indexOf(place) != -1)
               {
                  _local_3 = place;
               }
               else
               {
                  _local_3 = PlayerManager.Instance.getDressEquipPlace(_local_4);
               }
               if(EquipType.isArmShell(_local_4))
               {
                  DragManager.acceptDrag(this,"none");
                  return;
               }
               SocketManager.Instance.out.sendMoveGoods(0,_local_4.Place,0,_local_3,_local_4.Count);
               _arg_1.action = "none";
               DragManager.acceptDrag(this,"move");
            }
            else
            {
               DragManager.acceptDrag(this,"none");
            }
         }
      }
      
      override protected function createLoading() : void
      {
         super.createLoading();
         PositionUtils.setPos(_loadingasset,"ddt.personalInfocell.loadingPos");
      }
      
      override public function checkOverDate() : void
      {
         if(Boolean(_bgOverDate))
         {
            _bgOverDate.visible = false;
         }
      }
      
      private function __onResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.target as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__onResponse);
         _local_2.dispose();
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            this.sendDefy();
         }
      }
      
      private function __onBindResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.target as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__onBindResponse);
         _local_2.dispose();
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            this.sendBindDefy();
         }
      }
      
      private function sendDefy() : void
      {
         var _local_1:int = 0;
         if(PlayerManager.Instance.Self.canEquip(temInfo))
         {
            _local_1 = PlayerManager.Instance.getDressEquipPlace(temInfo);
            SocketManager.Instance.out.sendMoveGoods(0,temInfo.Place,0,_local_1);
         }
      }
      
      private function sendBindDefy() : void
      {
         if(PlayerManager.Instance.Self.canEquip(temInfo))
         {
            SocketManager.Instance.out.sendMoveGoods(0,temInfo.Place,0,_place,temInfo.Count);
         }
      }
      
      private function getCellIndex(_arg_1:ItemTemplateInfo) : Array
      {
         if(EquipType.isWeddingRing(_arg_1))
         {
            return [9,10,16];
         }
         switch(_arg_1.CategoryID)
         {
            case 1:
               return [0];
            case 2:
               return [1];
            case 3:
               return [2];
            case 4:
               return [3];
            case 5:
               return [4];
            case 6:
               return [5];
            case 7:
               return [6];
            case 8:
            case 28:
               return [7,8];
            case 9:
            case 29:
               return [9,10];
            case 13:
               return [11];
            case 14:
               return [12];
            case 15:
               return [13];
            case 16:
               return [14];
            case 17:
               return [15];
            case 70:
               return [18];
            default:
               return [-1];
         }
      }
      
      override public function dragStop(_arg_1:DragEffect) : void
      {
         if(PlayerManager.Instance.Self.bagLocked)
         {
            _arg_1.action = "none";
            super.dragStop(_arg_1);
         }
         locked = false;
         dispatchEvent(new CellEvent("dragStop",null,true));
      }
      
      public function shine() : void
      {
         if(this._shineObject == null)
         {
            this._shineObject = ComponentFactory.Instance.creatCustomObject("asset.core.playerInfoCellShine") as MovieClip;
         }
         addChild(this._shineObject);
         this._shineObject.gotoAndPlay(1);
      }
      
      public function stopShine() : void
      {
         if(this._shineObject != null && this.contains(this._shineObject))
         {
            removeChild(this._shineObject);
         }
         if(this._shineObject != null)
         {
            this._shineObject.gotoAndStop(1);
         }
      }
      
      override public function updateCount() : void
      {
         if(Boolean(_tbxCount))
         {
            if(_info && itemInfo && itemInfo.Count > 1)
            {
               _tbxCount.text = String(itemInfo.Count);
               _tbxCount.visible = true;
               addChild(_tbxCount);
            }
            else
            {
               _tbxCount.visible = false;
            }
         }
      }
      
      override public function dispose() : void
      {
         this.removeEvents();
         if(this._shineObject != null)
         {
            ObjectUtils.disposeAllChildren(this._shineObject);
         }
         this._shineObject = null;
         super.dispose();
      }
   }
}

