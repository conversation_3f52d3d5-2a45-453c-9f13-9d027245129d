package church
{
   import baglocked.BaglockedManager;
   import campbattle.CampBattleManager;
   import church.events.WeddingRoomEvent;
   import church.view.ChurchAlertFrame;
   import com.pickgliss.action.AlertAction;
   import com.pickgliss.action.FunctionAction;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.loader.LoaderEvent;
   import com.pickgliss.manager.CacheSysManager;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.action.FrameShowAction;
   import ddt.data.ChurchRoomInfo;
   import ddt.data.ServerInfo;
   import ddt.data.player.BasePlayer;
   import ddt.data.player.PlayerInfo;
   import ddt.events.PkgEvent;
   import ddt.manager.ChatManager;
   import ddt.manager.ExternalInterfaceManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PathManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.StateManager;
   import ddt.view.UIModuleSmallLoading;
   import ddt.view.chat.ChatData;
   import ddt.view.common.church.ChurchDialogueAgreePropose;
   import ddt.view.common.church.ChurchDialogueRejectPropose;
   import ddt.view.common.church.ChurchDialogueUnmarried;
   import ddt.view.common.church.ChurchMarryApplySuccess;
   import ddt.view.common.church.ChurchProposeFrame;
   import ddt.view.common.church.ChurchProposeResponseFrame;
   import ddtBuried.BuriedManager;
   import email.MailManager;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import invite.InviteManager;
   import quest.TaskManager;
   import road7th.comm.PackageIn;
   import road7th.utils.StringHelper;
   
   public class ChurchManager extends EventDispatcher
   {
      
      private static var _instance:ChurchManager;
      
      public static const GENERALSCENEINDEX:int = 1;
      
      public static const ADVANCEDSCENEINDEX:int = 2;
      
      public static const COSTLYSCENEINDEX:int = 3;
      
      private static const MOON_SCENE:Boolean = true;
      
      public static const CIVIL_PLAYER_INFO_MODIFY:String = "civilplayerinfomodify";
      
      public static const CIVIL_SELFINFO_CHANGE:String = "civilselfinfochange";
      
      public static const SUBMIT_REFUND:String = "submitRefund";
      
      private var _currentScene:int = 1;
      
      private var _churchDialogueUnmarried:ChurchDialogueUnmarried;
      
      private var _churchProposeFrame:ChurchProposeFrame;
      
      private var _proposeResposeFrame:ChurchProposeResponseFrame;
      
      private var _churchMarryApplySuccess:ChurchMarryApplySuccess;
      
      private var _alertMarried:BaseAlerFrame;
      
      public var _weddingSuccessfulComplete:Boolean;
      
      public var seniorType:int;
      
      public var lastScene:int = 1;
      
      private var _selfRoom:ChurchRoomInfo;
      
      private var _currentRoom:ChurchRoomInfo;
      
      private var _mapLoader01:BaseLoader;
      
      private var _mapLoader02:BaseLoader;
      
      private var _mapLoader00:BaseLoader;
      
      private var _isRemoveLoading:Boolean = true;
      
      private var _userID:int;
      
      public var isUnwedding:Boolean;
      
      private var money:int;
      
      private var marryApplyList:Array = [];
      
      private var _churchDialogueAgreePropose:ChurchDialogueAgreePropose;
      
      private var _churchDialogueRejectPropose:ChurchDialogueRejectPropose;
      
      private var unwedingmsg:String;
      
      private var _linkServerInfo:ServerInfo;
      
      public function ChurchManager()
      {
         super();
      }
      
      public static function get instance() : ChurchManager
      {
         if(!_instance)
         {
            _instance = new ChurchManager();
         }
         return _instance;
      }
      
      public function get currentScene() : int
      {
         return this._currentScene;
      }
      
      public function set currentScene(_arg_1:int) : void
      {
         if(this._currentScene == _arg_1)
         {
            return;
         }
         this._currentScene = _arg_1;
         dispatchEvent(new WeddingRoomEvent("scene change",this._currentScene));
      }
      
      public function get selfRoom() : ChurchRoomInfo
      {
         return this._selfRoom;
      }
      
      public function set selfRoom(_arg_1:ChurchRoomInfo) : void
      {
         this._selfRoom = _arg_1;
      }
      
      public function set currentRoom(_arg_1:ChurchRoomInfo) : void
      {
         if(this._currentRoom == _arg_1)
         {
            return;
         }
         this._currentRoom = _arg_1;
         this.onChurchRoomInfoChange();
      }
      
      public function get currentRoom() : ChurchRoomInfo
      {
         return this._currentRoom;
      }
      
      private function onChurchRoomInfoChange() : void
      {
         if(this._currentRoom != null)
         {
            this.loadMap();
         }
      }
      
      public function loadMap() : void
      {
         this._mapLoader00 = LoadResourceManager.Instance.createLoader(PathManager.solveChurchSceneSourcePath("Map00"),4);
         this._mapLoader00.addEventListener("complete",this.onMapSrcLoadedComplete);
         LoadResourceManager.Instance.startLoad(this._mapLoader00);
         this._mapLoader01 = LoadResourceManager.Instance.createLoader(PathManager.solveChurchSceneSourcePath("Map01"),4);
         this._mapLoader01.addEventListener("complete",this.onMapSrcLoadedComplete);
         LoadResourceManager.Instance.startLoad(this._mapLoader01);
         this._mapLoader02 = LoadResourceManager.Instance.createLoader(PathManager.solveChurchSceneSourcePath("Map02"),4);
         this._mapLoader02.addEventListener("complete",this.onMapSrcLoadedComplete);
         LoadResourceManager.Instance.startLoad(this._mapLoader02);
      }
      
      protected function onMapSrcLoadedComplete(_arg_1:LoaderEvent = null) : void
      {
         if(this._mapLoader01.isSuccess && this._mapLoader02.isSuccess)
         {
            this.tryLoginScene();
         }
      }
      
      public function tryLoginScene() : void
      {
         if(StateManager.getState("churchRoom") == null)
         {
            this._isRemoveLoading = false;
            UIModuleSmallLoading.Instance.addEventListener("close",this.__loadingIsClose);
         }
         StateManager.setState("churchRoom");
      }
      
      private function __loadingIsClose(_arg_1:Event) : void
      {
         this._isRemoveLoading = true;
         UIModuleSmallLoading.Instance.removeEventListener("close",this.__loadingIsClose);
         SocketManager.Instance.out.sendExitRoom();
      }
      
      public function removeLoadingEvent() : void
      {
         if(!this._isRemoveLoading)
         {
            UIModuleSmallLoading.Instance.removeEventListener("close",this.__loadingIsClose);
         }
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(242),this.__roomLogin);
         SocketManager.Instance.addEventListener(PkgEvent.format(252),this.__updateSelfRoom);
         SocketManager.Instance.addEventListener(PkgEvent.format(244),this.__removePlayer);
         SocketManager.Instance.addEventListener(PkgEvent.format(246),this.__showPropose);
         SocketManager.Instance.addEventListener(PkgEvent.format(247),this.__marryApply);
         SocketManager.Instance.addEventListener(PkgEvent.format(250),this.__marryApplyReply);
         SocketManager.Instance.addEventListener(PkgEvent.format(248),this.__divorceApply);
         SocketManager.Instance.addEventListener(PkgEvent.format(249,4),this.__churchInvite);
         SocketManager.Instance.addEventListener(PkgEvent.format(234),this.__marryPropGet);
         SocketManager.Instance.addEventListener(PkgEvent.format(239),this.__upCivilPlayerView);
         SocketManager.Instance.addEventListener(PkgEvent.format(235),this.__getMarryInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(207),this.reqeustPayHander);
         this.addEventListener("submitRefund",this.__onSubmitRefund);
      }
      
      private function reqeustPayHander(_arg_1:PkgEvent) : void
      {
         var _local_3:* = null;
         var _local_2:int = _arg_1.pkg.readByte();
         if(_local_2 == 1)
         {
            this.isUnwedding = true;
            this._userID = _arg_1.pkg.readInt();
            _local_3 = _arg_1.pkg.readUTF();
            this.money = _arg_1.pkg.readInt();
            this.unwedingmsg = LanguageMgr.GetTranslation("ddt.friendPay.action",_local_3,this.money);
            if(!CampBattleManager.instance.isFighting)
            {
               this.openAlert();
            }
         }
      }
      
      public function openAlert() : void
      {
         this.isUnwedding = false;
         var _local_1:ChurchAlertFrame = ComponentFactory.Instance.creat("church.view.ChurchAlertFrame");
         _local_1.setTxt(this.unwedingmsg);
         LayerManager.Instance.addToLayer(_local_1,3,true,2);
         _local_1.addEventListener("response",this.reponseHander);
      }
      
      private function reponseHander(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.playButtonSound();
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            if(PlayerManager.Instance.Self.bagLocked)
            {
               BaglockedManager.Instance.show();
               return;
            }
            if(BuriedManager.Instance.checkMoney(false,this.money))
            {
               return;
            }
            SocketManager.Instance.out.isAcceptPay(true,this._userID);
         }
         else if(_arg_1.responseCode == 1 || _arg_1.responseCode == 4 || _arg_1.responseCode == 0)
         {
            SocketManager.Instance.out.isAcceptPay(false,this._userID);
         }
         var _local_2:ChurchAlertFrame = _arg_1.currentTarget as ChurchAlertFrame;
         _local_2.removeEventListener("response",this.reponseHander);
         _local_2.dispose();
         _local_2 = null;
      }
      
      private function __onSubmitRefund(_arg_1:Event) : void
      {
         SocketManager.Instance.out.refund();
      }
      
      private function __upCivilPlayerView(_arg_1:PkgEvent) : void
      {
         PlayerManager.Instance.Self.MarryInfoID = _arg_1.pkg.readInt();
         var _local_2:Boolean = _arg_1.pkg.readBoolean();
         if(_local_2)
         {
            PlayerManager.Instance.Self.ID = _arg_1.pkg.readInt();
            PlayerManager.Instance.Self.IsPublishEquit = _arg_1.pkg.readBoolean();
            PlayerManager.Instance.Self.Introduction = _arg_1.pkg.readUTF();
         }
         dispatchEvent(new Event("civilplayerinfomodify"));
      }
      
      private function __getMarryInfo(_arg_1:PkgEvent) : void
      {
         PlayerManager.Instance.Self.Introduction = _arg_1.pkg.readUTF();
         PlayerManager.Instance.Self.IsPublishEquit = _arg_1.pkg.readBoolean();
         dispatchEvent(new Event("civilselfinfochange"));
      }
      
      public function __showPropose(_arg_1:PkgEvent) : void
      {
         var _local_3:int = _arg_1.pkg.readInt();
         var _local_2:Boolean = _arg_1.pkg.readBoolean();
         if(_local_2)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.married"));
         }
         else if(PlayerManager.Instance.Self.IsMarried)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.youMarried"));
         }
         else
         {
            this._churchProposeFrame = ComponentFactory.Instance.creat("common.church.ChurchProposeFrame");
            this._churchProposeFrame.addEventListener("close",this.churchProposeFrameClose);
            this._churchProposeFrame.spouseID = _local_3;
            this._churchProposeFrame.show();
         }
      }
      
      private function churchProposeFrameClose(_arg_1:Event) : void
      {
         if(Boolean(this._churchProposeFrame))
         {
            this._churchProposeFrame.removeEventListener("close",this.churchProposeFrameClose);
            if(Boolean(this._churchProposeFrame.parent))
            {
               this._churchProposeFrame.parent.removeChild(this._churchProposeFrame);
            }
         }
         this._churchProposeFrame = null;
      }
      
      private function __marryApply(_arg_1:PkgEvent) : void
      {
         var _local_4:int = _arg_1.pkg.readInt();
         var _local_5:String = _arg_1.pkg.readUTF();
         var _local_2:String = _arg_1.pkg.readUTF();
         var _local_3:int = _arg_1.pkg.readInt();
         if(_local_4 == PlayerManager.Instance.Self.ID)
         {
            this._churchMarryApplySuccess = ComponentFactory.Instance.creat("common.church.ChurchMarryApplySuccess");
            this._churchMarryApplySuccess.addEventListener("close",this.churchMarryApplySuccessClose);
            this._churchMarryApplySuccess.show();
            return;
         }
         if(this.checkMarryApplyList(_local_3))
         {
            return;
         }
         this.marryApplyList.push(_local_3);
         SoundManager.instance.play("018");
         this._proposeResposeFrame = ComponentFactory.Instance.creat("common.church.ChurchProposeResponseFrame");
         this._proposeResposeFrame.addEventListener("close",this.ProposeResposeFrameClose);
         this._proposeResposeFrame.spouseID = _local_4;
         this._proposeResposeFrame.spouseName = _local_5;
         this._proposeResposeFrame.answerId = _local_3;
         this._proposeResposeFrame.love = _local_2;
         if(CacheSysManager.isLock("alertInFight"))
         {
            CacheSysManager.getInstance().cache("alertInFight",new AlertAction(this._proposeResposeFrame,4,1));
         }
         else if(StateManager.currentStateType == "login")
         {
            CacheSysManager.getInstance().cacheFunction("alertInHall",new FunctionAction(this._proposeResposeFrame.show));
         }
         else
         {
            this._proposeResposeFrame.show();
         }
      }
      
      private function checkMarryApplyList(_arg_1:int) : Boolean
      {
         var _local_2:int = 0;
         _local_2 = 0;
         while(_local_2 < this.marryApplyList.length)
         {
            if(_arg_1 == this.marryApplyList[_local_2])
            {
               return true;
            }
            _local_2++;
         }
         return false;
      }
      
      private function churchMarryApplySuccessClose(_arg_1:Event) : void
      {
         if(Boolean(this._churchMarryApplySuccess))
         {
            this._churchMarryApplySuccess.removeEventListener("close",this.churchMarryApplySuccessClose);
            if(Boolean(this._churchMarryApplySuccess.parent))
            {
               this._churchMarryApplySuccess.parent.removeChild(this._churchMarryApplySuccess);
            }
            this._churchMarryApplySuccess.dispose();
         }
         this._churchMarryApplySuccess = null;
      }
      
      private function ProposeResposeFrameClose(_arg_1:Event) : void
      {
         if(Boolean(this._proposeResposeFrame))
         {
            this._proposeResposeFrame.removeEventListener("close",this.ProposeResposeFrameClose);
            if(Boolean(this._proposeResposeFrame.parent))
            {
               this._proposeResposeFrame.parent.removeChild(this._proposeResposeFrame);
            }
            this._proposeResposeFrame.dispose();
         }
         this._proposeResposeFrame = null;
      }
      
      private function __marryApplyReply(_arg_1:PkgEvent) : void
      {
         var _local_6:* = null;
         var _local_4:* = null;
         var _local_5:int = _arg_1.pkg.readInt();
         var _local_2:Boolean = _arg_1.pkg.readBoolean();
         var _local_7:String = _arg_1.pkg.readUTF();
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         if(_local_2)
         {
            PlayerManager.Instance.Self.IsMarried = true;
            PlayerManager.Instance.Self.SpouseID = _local_5;
            PlayerManager.Instance.Self.SpouseName = _local_7;
            TaskManager.instance.onMarriaged();
            TaskManager.instance.requestCanAcceptTask();
            if(PathManager.solveExternalInterfaceEnabel())
            {
               ExternalInterfaceManager.sendToAgent(7,PlayerManager.Instance.Self.ID,PlayerManager.Instance.Self.NickName,ServerManager.Instance.zoneName,-1,"",_local_7);
            }
         }
         if(_local_3)
         {
            _local_6 = new ChatData();
            _local_4 = "";
            if(_local_2)
            {
               _local_6.channel = 6;
               _local_4 = "<" + _local_7 + ">" + LanguageMgr.GetTranslation("tank.manager.PlayerManager.isApplicant");
               this._churchDialogueAgreePropose = ComponentFactory.Instance.creat("common.church.ChurchDialogueAgreePropose");
               this._churchDialogueAgreePropose.msgInfo = _local_7;
               this._churchDialogueAgreePropose.addEventListener("close",this.churchDialogueAgreeProposeClose);
               if(CacheSysManager.isLock("alertInFight"))
               {
                  CacheSysManager.getInstance().cache("alertInFight",new FrameShowAction(this._churchDialogueAgreePropose));
               }
               else
               {
                  this._churchDialogueAgreePropose.show();
               }
            }
            else
            {
               _local_6.channel = 7;
               _local_4 = "<" + _local_7 + ">" + LanguageMgr.GetTranslation("tank.manager.PlayerManager.refuseMarry");
               if(Boolean(this._churchDialogueRejectPropose))
               {
                  this._churchDialogueRejectPropose.dispose();
                  this._churchDialogueRejectPropose = null;
               }
               this._churchDialogueRejectPropose = ComponentFactory.Instance.creat("common.church.ChurchDialogueRejectPropose");
               this._churchDialogueRejectPropose.msgInfo = _local_7;
               this._churchDialogueRejectPropose.addEventListener("close",this.churchDialogueRejectProposeClose);
               if(CacheSysManager.isLock("alertInFight"))
               {
                  CacheSysManager.getInstance().cache("alertInFight",new AlertAction(this._churchDialogueRejectPropose,3,1,"018",true));
               }
               else
               {
                  this._churchDialogueRejectPropose.show();
               }
            }
            _local_6.msg = StringHelper.rePlaceHtmlTextField(_local_4);
            ChatManager.Instance.chat(_local_6);
         }
         else if(_local_2)
         {
            this._alertMarried = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tank.view.task.TaskCatalogContentView.tip"),LanguageMgr.GetTranslation("tank.manager.PlayerManager.youAndOtherMarried",_local_7),LanguageMgr.GetTranslation("ok"),"",false,false,false,0,"alertInFight");
            this._alertMarried.addEventListener("response",this.marriedResponse);
         }
      }
      
      private function marriedResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
            case 3:
            case 4:
               if(Boolean(this._alertMarried))
               {
                  if(Boolean(this._alertMarried.parent))
                  {
                     this._alertMarried.parent.removeChild(this._alertMarried);
                  }
                  this._alertMarried.dispose();
               }
               this._alertMarried = null;
         }
         ObjectUtils.disposeObject(_arg_1.target);
      }
      
      private function churchDialogueRejectProposeClose(_arg_1:Event) : void
      {
         if(Boolean(this._churchDialogueRejectPropose))
         {
            this._churchDialogueRejectPropose.removeEventListener("close",this.churchDialogueRejectProposeClose);
            if(Boolean(this._churchDialogueRejectPropose.parent))
            {
               this._churchDialogueRejectPropose.parent.removeChild(this._churchDialogueRejectPropose);
            }
            this._churchDialogueRejectPropose.dispose();
         }
         this._churchDialogueRejectPropose = null;
      }
      
      private function churchDialogueAgreeProposeClose(_arg_1:Event) : void
      {
         if(Boolean(this._churchDialogueAgreePropose))
         {
            this._churchDialogueAgreePropose.removeEventListener("close",this.churchDialogueAgreeProposeClose);
            if(Boolean(this._churchDialogueAgreePropose.parent))
            {
               this._churchDialogueAgreePropose.parent.removeChild(this._churchDialogueAgreePropose);
            }
            this._churchDialogueAgreePropose.dispose();
         }
         this._churchDialogueAgreePropose = null;
      }
      
      private function __divorceApply(_arg_1:PkgEvent) : void
      {
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         var _local_2:Boolean = _arg_1.pkg.readBoolean();
         if(!_local_3)
         {
            return;
         }
         PlayerManager.Instance.Self.IsMarried = false;
         PlayerManager.Instance.Self.SpouseID = 0;
         PlayerManager.Instance.Self.SpouseName = "";
         ChurchManager.instance.selfRoom = null;
         if(!_local_2)
         {
            SoundManager.instance.play("018");
            this._churchDialogueUnmarried = ComponentFactory.Instance.creat("ddt.common.church.ChurchDialogueUnmarried");
            if(CacheSysManager.isLock("alertInFight"))
            {
               CacheSysManager.getInstance().cache("alertInFight",new AlertAction(this._churchDialogueUnmarried,3,1));
            }
            else
            {
               this._churchDialogueUnmarried.show();
            }
            this._churchDialogueUnmarried.addEventListener("close",this.churchDialogueUnmarriedClose);
         }
         else
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.divorce"));
         }
         if(StateManager.currentStateType == "churchRoom" && (this.currentRoom.brideID == PlayerManager.Instance.Self.ID || this.currentRoom.createID == PlayerManager.Instance.Self.ID))
         {
            StateManager.setState("ddtchurchroomlist");
         }
      }
      
      private function churchDialogueUnmarriedClose(_arg_1:Event) : void
      {
         SoundManager.instance.play("008");
         if(Boolean(this._churchDialogueUnmarried))
         {
            this._churchDialogueUnmarried.removeEventListener("close",this.churchDialogueUnmarriedClose);
            if(Boolean(this._churchDialogueUnmarried.parent))
            {
               this._churchDialogueUnmarried.parent.removeChild(this._churchDialogueUnmarried);
            }
            this._churchDialogueUnmarried.dispose();
         }
         this._churchDialogueUnmarried = null;
      }
      
      private function __churchInvite(_arg_1:PkgEvent) : void
      {
         var _local_3:* = null;
         var _local_4:* = null;
         var _local_2:* = null;
         if(InviteManager.Instance.enabled)
         {
            _local_3 = _arg_1.pkg;
            _local_4 = {};
            _local_4["inviteID"] = _local_3.readInt();
            _local_4["inviteName"] = _local_3.readUTF();
            _local_4["IsVip"] = _local_3.readBoolean();
            _local_4["VIPLevel"] = _local_3.readInt();
            _local_4["roomID"] = _local_3.readInt();
            _local_4["roomName"] = _local_3.readUTF();
            _local_4["pwd"] = _local_3.readUTF();
            _local_4["sceneIndex"] = _local_3.readInt();
            if(BuriedManager.Instance.isOpening)
            {
               return;
            }
            _local_2 = ComponentFactory.Instance.creatComponentByStylename("common.church.ChurchInviteFrame");
            _local_2.msgInfo = _local_4;
            _local_2.show();
            SoundManager.instance.play("018");
         }
      }
      
      private function __marryPropGet(_arg_1:PkgEvent) : void
      {
         var _local_3:* = null;
         var _local_4:PackageIn = _arg_1.pkg;
         PlayerManager.Instance.Self.IsMarried = _local_4.readBoolean();
         PlayerManager.Instance.Self.SpouseID = _local_4.readInt();
         PlayerManager.Instance.Self.SpouseName = _local_4.readUTF();
         var _local_2:Boolean = _local_4.readBoolean();
         var _local_6:int = _local_4.readInt();
         var _local_5:Boolean = _local_4.readBoolean();
         if(_local_2)
         {
            if(!ChurchManager.instance.selfRoom)
            {
               _local_3 = new ChurchRoomInfo();
               _local_3.id = _local_6;
               ChurchManager.instance.selfRoom = _local_3;
            }
         }
         else
         {
            ChurchManager.instance.selfRoom = null;
         }
      }
      
      private function __roomLogin(_arg_1:PkgEvent) : void
      {
         var _local_8:int = 0;
         var _local_6:int = 0;
         var _local_10:* = null;
         var _local_4:* = null;
         var _local_7:PackageIn = _arg_1.pkg;
         var _local_3:Boolean = _local_7.readBoolean();
         if(!_local_3)
         {
            _local_8 = _local_7.readInt();
            if(MailManager.Instance.linkChurchRoomId != -1 && (_local_8 == 5 || _local_8 == 6))
            {
               StateManager.setState("ddtchurchroomlist");
               MailManager.Instance.hide();
            }
            else if(_local_8 == 7)
            {
               _local_6 = _local_7.readInt();
               this._linkServerInfo = ServerManager.Instance.getServerInfoByID(_local_6);
               if(Boolean(this._linkServerInfo))
               {
                  _local_10 = LanguageMgr.GetTranslation("ddt.church.serverInFail",this._linkServerInfo.Name,MailManager.Instance.linkChurchRoomId);
                  _local_4 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),_local_10,"",LanguageMgr.GetTranslation("cancel"),true,true,false,2);
                  _local_4.addEventListener("response",this.__tipsMarryRoomframeResponse);
               }
               else
               {
                  MailManager.Instance.linkChurchRoomId = -1;
               }
            }
            else
            {
               MailManager.Instance.linkChurchRoomId = -1;
            }
            return;
         }
         var _local_5:ChurchRoomInfo = new ChurchRoomInfo();
         _local_5.id = _local_7.readInt();
         _local_5.roomName = _local_7.readUTF();
         _local_5.mapID = _local_7.readInt();
         _local_5.valideTimes = _local_7.readInt();
         _local_5.currentNum = _local_7.readInt();
         _local_5.createID = _local_7.readInt();
         _local_5.createName = _local_7.readUTF();
         _local_5.groomID = _local_7.readInt();
         _local_5.groomName = _local_7.readUTF();
         _local_5.brideID = _local_7.readInt();
         _local_5.brideName = _local_7.readUTF();
         _local_5.creactTime = _local_7.readDate();
         _local_5.isStarted = _local_7.readBoolean();
         var _local_9:int = _local_7.readByte();
         if(_local_9 == 1)
         {
            _local_5.status = "wedding_none";
         }
         else
         {
            _local_5.status = "wedding_ing";
         }
         _local_5.discription = _local_7.readUTF();
         _local_5.canInvite = _local_7.readBoolean();
         var _local_2:int = _local_7.readInt();
         ChurchManager.instance.currentScene = _local_2;
         _local_5.isUsedSalute = _local_7.readBoolean();
         this.seniorType = _local_7.readInt();
         _local_5.seniorType = this.seniorType;
         this.currentRoom = _local_5;
         if(this.isAdmin(PlayerManager.Instance.Self))
         {
            this.selfRoom = _local_5;
         }
      }
      
      private function __tipsMarryRoomframeResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            ServerManager.Instance.current = this._linkServerInfo;
            ServerManager.Instance.connentCurrentServer();
            ServerManager.Instance.dispatchEvent(new Event("changeServer"));
         }
         else
         {
            MailManager.Instance.linkChurchRoomId = -1;
         }
         this._linkServerInfo = null;
         var _local_2:Frame = Frame(_arg_1.currentTarget);
         _local_2.removeEventListener("response",this.__tipsMarryRoomframeResponse);
         ObjectUtils.disposeAllChildren(_local_2);
         ObjectUtils.disposeObject(_local_2);
         _local_2 = null;
      }
      
      private function __updateSelfRoom(_arg_1:PkgEvent) : void
      {
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_4.readInt();
         var _local_3:Boolean = _local_4.readBoolean();
         if(!_local_3)
         {
            this.selfRoom = null;
            return;
         }
         if(this.selfRoom == null)
         {
            this.selfRoom = new ChurchRoomInfo();
         }
         this.selfRoom.id = _local_4.readInt();
         this.selfRoom.roomName = _local_4.readUTF();
         this.selfRoom.mapID = _local_4.readInt();
         this.selfRoom.valideTimes = _local_4.readInt();
         this.selfRoom.createID = _local_4.readInt();
         this.selfRoom.groomID = _local_4.readInt();
         this.selfRoom.brideID = _local_4.readInt();
         this.selfRoom.creactTime = _local_4.readDate();
         this.selfRoom.isUsedSalute = _local_4.readBoolean();
         this.selfRoom.seniorType = _local_4.readInt();
      }
      
      public function __removePlayer(_arg_1:PkgEvent) : void
      {
         var _local_2:int = _arg_1.pkg.clientId;
         if(_local_2 == PlayerManager.Instance.Self.ID)
         {
            StateManager.setState("ddtchurchroomlist");
         }
      }
      
      public function isAdmin(_arg_1:PlayerInfo) : Boolean
      {
         if(Boolean(this._currentRoom) && Boolean(_arg_1))
         {
            return _arg_1.ID == this._currentRoom.groomID || _arg_1.ID == this._currentRoom.brideID;
         }
         return false;
      }
      
      public function sendValidateMarry(_arg_1:BasePlayer) : void
      {
         if(PlayerManager.Instance.Self.Grade < 14)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.notLvWoo",14));
         }
         else if(_arg_1.Grade < 14)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.notOtherLvWoo",14));
         }
         else if(PlayerManager.Instance.Self.IsMarried)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.IsMarried"));
         }
         else if(PlayerManager.Instance.Self.Sex == _arg_1.Sex)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.notAllow"));
         }
         else if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
         }
         else
         {
            SocketManager.Instance.out.sendValidateMarry(_arg_1.ID);
         }
      }
   }
}

