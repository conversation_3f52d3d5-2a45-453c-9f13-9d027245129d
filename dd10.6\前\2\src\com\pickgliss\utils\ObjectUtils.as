package com.pickgliss.utils
{
   import com.pickgliss.ui.core.Disposeable;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.geom.Rectangle;
   import flash.utils.ByteArray;
   import flash.utils.Dictionary;
   import flash.utils.describeType;
   import flash.utils.getQualifiedClassName;
   
   public final class ObjectUtils
   {
      
      private static var _copyAbleTypes:Vector.<String>;
      
      private static var _descriptOjbXMLs:Dictionary;
      
      public function ObjectUtils()
      {
         super();
      }
      
      public static function cloneSimpleObject(_arg_1:Object) : Object
      {
         var _local_2:ByteArray = new ByteArray();
         _local_2.writeObject(_arg_1);
         _local_2.position = 0;
         return _local_2.readObject();
      }
      
      public static function copyPorpertiesByXML(_arg_1:Object, _arg_2:XML) : void
      {
         var _local_3:* = undefined;
         var _local_5:* = undefined;
         var _local_4:XML = null;
         _local_3 = undefined;
         _local_5 = undefined;
         _local_3 = null;
         _local_5 = null;
         var _local_6:XMLList = _arg_2.attributes();
         for each(_local_4 in _local_6)
         {
            _local_3 = _local_4.name().toString();
            if(_arg_1.hasOwnProperty(_local_3))
            {
               try
               {
                  _local_5 = _local_4.toString();
                  if(_local_5 == "false")
                  {
                     _arg_1[_local_3] = false;
                  }
                  else
                  {
                     _arg_1[_local_3] = _local_5;
                  }
               }
               catch(e:Error)
               {
                  trace("-----类ObjectUtils报错:",e.message + "\n-----",_local_3,_local_5);
                  continue;
               }
            }
         }
      }
      
      public static function copyProperties(_arg_1:Object, _arg_2:Object) : void
      {
         var _local_9:XML = null;
         var _local_5:XML = null;
         var _local_10:* = null;
         var _local_6:* = null;
         var _local_3:* = null;
         var _local_7:* = null;
         var _local_4:* = null;
         if(_descriptOjbXMLs == null)
         {
            _descriptOjbXMLs = new Dictionary();
         }
         var _local_8:Vector.<String> = getCopyAbleType();
         var _local_11:XML = describeTypeSave(_arg_2);
         _local_10 = _local_11.variable;
         for each(_local_9 in _local_10)
         {
            _local_6 = _local_9.@type;
            if(_local_8.indexOf(_local_6) != -1)
            {
               _local_3 = _local_9.@name;
               if(_arg_1.hasOwnProperty(_local_3))
               {
                  _arg_1[_local_3] = _arg_2[_local_3];
               }
            }
         }
         _local_10 = _local_11.accessor;
         for each(_local_5 in _local_10)
         {
            _local_7 = _local_5.@type;
            if(_local_8.indexOf(_local_7) != -1)
            {
               _local_4 = _local_5.@name;
               try
               {
                  _arg_1[_local_4] = _arg_2[_local_4];
               }
               catch(err:Error)
               {
               }
            }
         }
      }
      
      public static function disposeAllChildren(_arg_1:DisplayObjectContainer) : void
      {
         var _local_2:* = null;
         if(_arg_1 == null)
         {
            return;
         }
         while(_arg_1.numChildren > 0)
         {
            _local_2 = _arg_1.getChildAt(0);
            ObjectUtils.disposeObject(_local_2);
         }
      }
      
      public static function removeChildAllChildren(_arg_1:DisplayObjectContainer) : void
      {
         while(_arg_1.numChildren > 0)
         {
            _arg_1.removeChildAt(0);
         }
      }
      
      public static function disposeObject(_arg_1:Object) : void
      {
         var _local_5:* = null;
         var _local_3:* = null;
         var _local_2:* = null;
         var _local_4:* = null;
         if(_arg_1 == null)
         {
            return;
         }
         if(_arg_1 is Disposeable)
         {
            if(_arg_1 is DisplayObject)
            {
               _local_5 = _arg_1 as DisplayObject;
               if(Boolean(_local_5.parent))
               {
                  _local_5.parent.removeChild(_local_5);
               }
            }
            Disposeable(_arg_1).dispose();
         }
         else if(_arg_1 is Bitmap)
         {
            _local_3 = Bitmap(_arg_1);
            if(Boolean(_local_3.parent))
            {
               _local_3.parent.removeChild(_local_3);
            }
            if(Boolean(_local_3.bitmapData))
            {
               _local_3.bitmapData.dispose();
            }
         }
         else if(_arg_1 is BitmapData)
         {
            _local_2 = BitmapData(_arg_1);
            _local_2.dispose();
         }
         else if(_arg_1 is DisplayObject)
         {
            _local_4 = DisplayObject(_arg_1);
            if(Boolean(_local_4.parent))
            {
               _local_4.parent.removeChild(_local_4);
            }
         }
      }
      
      private static function getCopyAbleType() : Vector.<String>
      {
         if(_copyAbleTypes == null)
         {
            _copyAbleTypes = new Vector.<String>();
            _copyAbleTypes.push("int");
            _copyAbleTypes.push("uint");
            _copyAbleTypes.push("String");
            _copyAbleTypes.push("Boolean");
            _copyAbleTypes.push("Number");
         }
         return _copyAbleTypes;
      }
      
      public static function describeTypeSave(_arg_1:Object) : XML
      {
         var _local_2:* = null;
         var _local_3:String = getQualifiedClassName(_arg_1);
         if(_descriptOjbXMLs[_local_3] != null)
         {
            _local_2 = _descriptOjbXMLs[_local_3];
         }
         else
         {
            _local_2 = describeType(_arg_1);
            _descriptOjbXMLs[_local_3] = _local_2;
         }
         return _local_2;
      }
      
      public static function encode(_arg_1:String, _arg_2:Object) : XML
      {
         var _local_7:String = null;
         var _local_3:XML = null;
         var _local_5:* = null;
         var _local_6:String = "<" + _arg_1 + " ";
         var _local_4:XML = describeTypeSave(_arg_2);
         if(<EMAIL>() == "Object")
         {
            for(_local_7 in _arg_2)
            {
               _local_5 = _arg_2[_local_7];
               if(!(_local_5 is Function))
               {
                  _local_6 += encodingProperty(_local_7,_local_5);
               }
            }
         }
         else
         {
            for each(_local_3 in _local_4..*.(name() == "variable" || name() == "accessor"))
            {
               _local_6 += encodingProperty(<EMAIL>(),_arg_2[_local_3.@name]);
            }
         }
         _local_6 += "/>";
         return new XML(_local_6);
      }
      
      private static function encodingProperty(_arg_1:String, _arg_2:Object) : String
      {
         if(_arg_2 is Array)
         {
            return "";
         }
         return escapeString(_arg_1) + "=\"" + String(_arg_2) + "\" ";
      }
      
      private static function escapeString(_arg_1:String) : String
      {
         var _local_7:int = 0;
         var _local_5:* = null;
         var _local_3:* = null;
         var _local_6:* = null;
         var _local_2:String = "";
         var _local_4:Number = _arg_1.length;
         _local_7 = 0;
         while(_local_7 < _local_4)
         {
            _local_5 = _arg_1.charAt(_local_7);
            switch(_local_5)
            {
               case "\"":
                  _local_2 += "\\\"";
                  break;
               case "/":
                  _local_2 += "\\/";
                  break;
               case "\\":
                  _local_2 += "\\\\";
                  break;
               case "\b":
                  _local_2 += "\\b";
                  break;
               case "\f":
                  _local_2 += "\\f";
                  break;
               case "\n":
                  _local_2 += "\\n";
                  break;
               case "\r":
                  _local_2 += "\\r";
                  break;
               case "\t":
                  _local_2 += "\\t";
                  break;
               default:
                  if(_local_5 < " ")
                  {
                     _local_3 = _local_5.charCodeAt(0).toString(16);
                     _local_6 = _local_3.length == 2 ? "00" : "000";
                     _local_2 += "\\u" + _local_6 + _local_3;
                  }
                  else
                  {
                     _local_2 += _local_5;
                  }
                  break;
            }
            _local_7++;
         }
         return _local_2;
      }
      
      public static function modifyVisibility(_arg_1:Boolean, ... _args) : void
      {
         var _local_3:int = 0;
         _local_3 = 0;
         while(_local_3 < _args.length)
         {
            (_args[_local_3] as DisplayObject).visible = _arg_1;
            _local_3++;
         }
      }
      
      public static function copyPropertyByRectangle(_arg_1:DisplayObject, _arg_2:Rectangle) : void
      {
         _arg_1.x = _arg_2.x;
         _arg_1.y = _arg_2.y;
         if(_arg_2.width != 0)
         {
            _arg_1.width = _arg_2.width;
         }
         if(_arg_2.height != 0)
         {
            _arg_1.height = _arg_2.height;
         }
      }
      
      public static function combineXML(_arg_1:XML, _arg_2:XML) : void
      {
         var _local_4:XML = null;
         var _local_3:* = null;
         var _local_5:* = null;
         if(_arg_2 == null || _arg_1 == null)
         {
            trace("警告！！！！  combineXML 出现问题  请马上解决");
            return;
         }
         var _local_6:XMLList = _arg_2.attributes();
         for each(_local_4 in _local_6)
         {
            _local_3 = _local_4.name().toString();
            _local_5 = _local_4.toString();
            if(!_arg_1.hasOwnProperty("@" + _local_3))
            {
               _arg_1["@" + _local_3] = _local_5;
            }
         }
      }
      
      public static function getDisplayObjectSuperParent(_arg_1:*, _arg_2:Class, _arg_3:*) : *
      {
         while(_arg_1 != null && _arg_1 != _arg_3)
         {
            if(_arg_1 is _arg_2)
            {
               return _arg_1;
            }
            _arg_1 = _arg_1.parent;
         }
         return null;
      }
      
      public static function getDisplayObjectSuperParentByName(_arg_1:*, _arg_2:String, _arg_3:*) : *
      {
         while(_arg_1 != null && _arg_1 != _arg_3)
         {
            if(getQualifiedClassName(_arg_1) == _arg_2)
            {
               return _arg_1;
            }
            _arg_1 = _arg_1.parent;
         }
         return null;
      }
   }
}

