package com.pickgliss.loader
{
   import deng.fzip.FZip;
   import flash.events.Event;
   import flash.net.URLVariables;
   import flash.utils.ByteArray;
   
   public class ZipLoader extends BaseLoader
   {
      
      public function ZipLoader(_arg_1:int, _arg_2:String, _arg_3:URLVariables = null, _arg_4:String = "GET")
      {
         super(_arg_1,_arg_2,_arg_3,_arg_4);
      }
      
      override protected function __onDataLoadComplete(_arg_1:Event) : void
      {
         removeEvent();
         unload();
         var _local_2:ByteArray = _loader.data;
         LoaderSavingManager.cacheFile(_url,_local_2,true);
         this.zipLoad(_local_2);
      }
      
      override public function loadFromBytes(_arg_1:ByteArray) : void
      {
         this.zipLoad(_arg_1);
         unload();
      }
      
      private function zipLoad(_arg_1:ByteArray) : void
      {
         var _local_2:FZip = new FZip();
         _local_2.addEventListener("complete",this.__onZipParaComplete);
         _local_2.loadBytes(_arg_1);
      }
      
      private function __onZipParaComplete(_arg_1:Event) : void
      {
         var _local_2:FZip = _arg_1.currentTarget as FZip;
         _local_2.removeEventListener("complete",this.__onZipParaComplete);
         if(Boolean(analyzer))
         {
            analyzer.analyzeCompleteCall = this.fireCompleteEvent;
            analyzer.analyzeErrorCall = fireErrorEvent;
            analyzer.analyze(_local_2);
            analyzer.analyzeWait(_local_2);
         }
         else
         {
            _local_2.close();
            this.fireCompleteEvent();
         }
      }
      
      override protected function fireCompleteEvent() : void
      {
         super.fireCompleteEvent();
      }
   }
}

