using System;
using System.Collections.Generic;
using System.Reflection;
using Game.Base.Config;
using log4net;
using SqlDataProvider.Data;

namespace Bussiness
{
	// Token: 0x02000FAE RID: 4014
	public abstract class GameProperties
	{
		// Token: 0x0600887C RID: 34940 RVA: 0x002D285C File Offset: 0x002D0A5C
		public static List<int> VIPExp()
		{
			return GameProperties.getProp(GameProperties.VIPExpNeededForEachLv);
		}

		// Token: 0x0600887D RID: 34941 RVA: 0x002D2878 File Offset: 0x002D0A78
		public static List<int> GiftExp()
		{
			return GameProperties.getProp(GameProperties.GiftExpForEachLv);
		}

		// Token: 0x0600887E RID: 34942 RVA: 0x002D2894 File Offset: 0x002D0A94
		public static List<int> PairBoxPrice()
		{
			return GameProperties.getBaseProp(GameProperties.PairBoxPriceConfig);
		}

		// Token: 0x0600887F RID: 34943 RVA: 0x002D28B0 File Offset: 0x002D0AB0
		public static Dictionary<int, int> AcademyApprenticeAwardArr()
		{
			return GameProperties.ConvertIntDict(GameProperties.AcademyApprenticeAward, ',', '|');
		}

		// Token: 0x06008880 RID: 34944 RVA: 0x002D28D0 File Offset: 0x002D0AD0
		public static Dictionary<int, int> AcademyMasterAwardArr()
		{
			return GameProperties.ConvertIntDict(GameProperties.AcademyMasterAward, ',', '|');
		}

		// Token: 0x06008881 RID: 34945 RVA: 0x002D28F0 File Offset: 0x002D0AF0
		public static int[] MissionRichesArr()
		{
			return GameProperties.ConvertIntArray(GameProperties.MissionRiches, '|');
		}

		// Token: 0x06008882 RID: 34946 RVA: 0x002D2910 File Offset: 0x002D0B10
		public static int[] MissionAwardRichesArr()
		{
			return GameProperties.ConvertIntArray(GameProperties.MissionAwardRiches, '|');
		}

		// Token: 0x06008883 RID: 34947 RVA: 0x002D2930 File Offset: 0x002D0B30
		public static int[] MissionAwardGPArr()
		{
			return GameProperties.ConvertIntArray(GameProperties.MissionAwardGP, '|');
		}

		// Token: 0x06008884 RID: 34948 RVA: 0x002D2950 File Offset: 0x002D0B50
		public static int[] MissionAwardOfferArr()
		{
			return GameProperties.ConvertIntArray(GameProperties.MissionAwardOffer, '|');
		}

		// Token: 0x06008885 RID: 34949 RVA: 0x002D2970 File Offset: 0x002D0B70
		private static void Load(Type type)
		{
			using (ServiceBussiness serviceBussiness = new ServiceBussiness())
			{
				FieldInfo[] fields = type.GetFields();
				FieldInfo[] array = fields;
				FieldInfo[] array2 = array;
				foreach (FieldInfo fieldInfo in array2)
				{
					bool isStatic = fieldInfo.IsStatic;
					if (isStatic)
					{
						object[] customAttributes = fieldInfo.GetCustomAttributes(typeof(ConfigPropertyAttribute), false);
						bool flag = customAttributes.Length != 0;
						if (flag)
						{
							ConfigPropertyAttribute configPropertyAttribute = (ConfigPropertyAttribute)customAttributes[0];
							fieldInfo.SetValue(null, GameProperties.LoadProperty(configPropertyAttribute, serviceBussiness));
						}
					}
				}
			}
		}

		// Token: 0x06008886 RID: 34950 RVA: 0x002D2A1C File Offset: 0x002D0C1C
		private static void Save(Type type)
		{
			using (ServiceBussiness serviceBussiness = new ServiceBussiness())
			{
				FieldInfo[] fields = type.GetFields();
				FieldInfo[] array = fields;
				FieldInfo[] array2 = array;
				foreach (FieldInfo fieldInfo in array2)
				{
					bool isStatic = fieldInfo.IsStatic;
					if (isStatic)
					{
						object[] customAttributes = fieldInfo.GetCustomAttributes(typeof(ConfigPropertyAttribute), false);
						bool flag = customAttributes.Length != 0;
						if (flag)
						{
							ConfigPropertyAttribute configPropertyAttribute = (ConfigPropertyAttribute)customAttributes[0];
							GameProperties.SaveProperty(configPropertyAttribute, serviceBussiness, fieldInfo.GetValue(null));
						}
					}
				}
			}
		}

		// Token: 0x06008887 RID: 34951 RVA: 0x002D2AC8 File Offset: 0x002D0CC8
		private static object LoadProperty(ConfigPropertyAttribute attrib, ServiceBussiness sb)
		{
			string key = attrib.Key;
			ServerProperty serverProperty = sb.GetServerPropertyByKey(key);
			bool flag = serverProperty == null;
			if (flag)
			{
				serverProperty = new ServerProperty();
				serverProperty.Key = key;
				serverProperty.Value = attrib.DefaultValue.ToString();
				GameProperties.log.Error("Cannot find server property " + key + ",keep it default value!");
			}
			GameProperties.log.Debug("Loading " + key + " Value is " + serverProperty.Value);
			object obj;
			try
			{
				obj = Convert.ChangeType(serverProperty.Value, attrib.DefaultValue.GetType());
			}
			catch (Exception ex)
			{
				GameProperties.log.Error("Exception in GameProperties Load: ", ex);
				obj = null;
			}
			return obj;
		}

		// Token: 0x06008888 RID: 34952 RVA: 0x002D2B90 File Offset: 0x002D0D90
		private static void SaveProperty(ConfigPropertyAttribute attrib, ServiceBussiness sb, object value)
		{
			try
			{
				sb.UpdateServerPropertyByKey(attrib.Key, value.ToString());
			}
			catch (Exception ex)
			{
				GameProperties.log.Error("Exception in GameProperties Save: ", ex);
			}
		}

		// Token: 0x06008889 RID: 34953 RVA: 0x002D2BDC File Offset: 0x002D0DDC
		public static List<int> getBaseProp(string prop)
		{
			List<int> list = new List<int>();
			char[] array = new char[] { ',' };
			string[] array2 = prop.Split(array);
			string[] array3 = array2;
			string[] array4 = array3;
			foreach (string text in array4)
			{
				list.Add(Convert.ToInt32(text));
			}
			return list;
		}

		// Token: 0x0600888A RID: 34954 RVA: 0x002D2C40 File Offset: 0x002D0E40
		public static List<int> getProp(string prop)
		{
			List<int> list = new List<int>();
			char[] array = new char[] { '|' };
			string[] array2 = prop.Split(array);
			string[] array3 = array2;
			string[] array4 = array3;
			foreach (string text in array4)
			{
				list.Add(Convert.ToInt32(text));
			}
			return list;
		}

		// Token: 0x0600888B RID: 34955 RVA: 0x002D2CA4 File Offset: 0x002D0EA4
		public static Dictionary<int, int> ConvertIntDict(string value, char splitChar, char subSplitChar)
		{
			string[] array = value.Split(new char[] { splitChar });
			Dictionary<int, int> dictionary = new Dictionary<int, int>();
			for (int i = 0; i < array.Length; i++)
			{
				string[] array2 = array[i].Split(new char[] { subSplitChar });
				bool flag = !dictionary.ContainsKey(int.Parse(array2[0]));
				if (flag)
				{
					dictionary.Add(int.Parse(array2[0]), int.Parse(array2[1]));
				}
			}
			return dictionary;
		}

		// Token: 0x0600888C RID: 34956 RVA: 0x002D2D2C File Offset: 0x002D0F2C
		public static int[] ConvertIntArray(string value, char splitChar)
		{
			string[] array = value.Split(new char[] { splitChar });
			int[] array2 = new int[array.Length];
			for (int i = 0; i < array.Length; i++)
			{
				array2[i] = int.Parse(array[i]);
			}
			return array2;
		}

		// Token: 0x0600888D RID: 34957 RVA: 0x00035B0E File Offset: 0x00033D0E
		public static void Refresh()
		{
			GameProperties.log.Info("Refreshing game properties!");
			GameProperties.Load(typeof(GameProperties));
		}

		// Token: 0x0600888E RID: 34958 RVA: 0x00035B31 File Offset: 0x00033D31
		public static void Save()
		{
			GameProperties.log.Info("Saving game properties into db!");
			GameProperties.Save(typeof(GameProperties));
		}

		// Token: 0x040053F6 RID: 21494
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x040053F7 RID: 21495
		[ConfigProperty("Edition", "当前游戏版本", "11000")]
		public static readonly string EDITION;

		// Token: 0x040053F8 RID: 21496
		[ConfigProperty("MustComposeGold", "合成消耗金币价格", 1000)]
		public static readonly int PRICE_COMPOSE_GOLD;

		// Token: 0x040053F9 RID: 21497
		[ConfigProperty("MustFusionGold", "熔炼消耗金币价格", 1000)]
		public static readonly int PRICE_FUSION_GOLD;

		// Token: 0x040053FA RID: 21498
		[ConfigProperty("MustStrengthenGold", "强化金币消耗价格", 1000)]
		public static readonly int PRICE_STRENGHTN_GOLD;

		// Token: 0x040053FB RID: 21499
		[ConfigProperty("MustTransferGold", "转移金币消耗", 10000)]
		public static readonly int MustTransferGold;

		// Token: 0x040053FC RID: 21500
		[ConfigProperty("CheckRewardItem", "验证码奖励物品", 11001)]
		public static readonly int CHECK_REWARD_ITEM;

		// Token: 0x040053FD RID: 21501
		[ConfigProperty("CheckFailCount", "最大验证码失败次数", 2)]
		public static readonly int CHECK_MAX_FAILED_COUNT;

		// Token: 0x040053FE RID: 21502
		[ConfigProperty("CheckCount", "多少场出一次验证码", 20)]
		public static readonly int CHECKCODE_PER_GAME_COUNT;

		// Token: 0x040053FF RID: 21503
		[ConfigProperty("HymenealMoney", "求婚的价格", 1000)]
		public static readonly int PRICE_PROPOSE;

		// Token: 0x04005400 RID: 21504
		[ConfigProperty("DivorcedMoney", "离婚的价格", 1000)]
		public static readonly int PRICE_DIVORCED;

		// Token: 0x04005401 RID: 21505
		[ConfigProperty("MarryRoomCreateMoney", "结婚房间的价格,2小时、3小时、4小时用逗号分隔", "2000,2700,3400")]
		public static readonly string PRICE_MARRY_ROOM;

		// Token: 0x04005402 RID: 21506
		[ConfigProperty("BoxAppearCondition", "箱子物品提示的等级", 4)]
		public static readonly int BOX_APPEAR_CONDITION;

		// Token: 0x04005403 RID: 21507
		[ConfigProperty("DisableCommands", "禁止使用的命令", "")]
		public static readonly string DISABLED_COMMANDS;

		// Token: 0x04005404 RID: 21508
		[ConfigProperty("AssState", "防沉迷系统的开关,True打开,False关闭", false)]
		public static bool ASS_STATE;

		// Token: 0x04005405 RID: 21509
		[ConfigProperty("DailyAwardState", "每日奖励开关,True打开,False关闭", true)]
		public static bool DAILY_AWARD_STATE;

		// Token: 0x04005406 RID: 21510
		[ConfigProperty("Cess", "交易扣税", 0.1)]
		public static readonly double Cess;

		// Token: 0x04005407 RID: 21511
		[ConfigProperty("BeginAuction", "拍买时起始随机时间", 20)]
		public static int BeginAuction;

		// Token: 0x04005408 RID: 21512
		[ConfigProperty("EndAuction", "拍买时结束随机时间", 40)]
		public static int EndAuction;

		// Token: 0x04005409 RID: 21513
		[ConfigProperty("VIPExpNeededForEachLv", "VIP升级所需经验", "1|2")]
		public static readonly string VIPExpNeededForEachLv;

		// Token: 0x0400540A RID: 21514
		[ConfigProperty("VIPGpBonusRatePerLevel", "VIP经验加成", 0.1)]
		public static readonly double VIPGpBonusRatePerLevel;

		// Token: 0x0400540B RID: 21515
		[ConfigProperty("VIPOfferDecreaseRate", "VIP功勋加成", 0.5)]
		public static readonly double VIPOfferDecreaseRate;

		// Token: 0x0400540C RID: 21516
		[ConfigProperty("ConsortiaFightRate", "公会经验加成", 0.1)]
		public static readonly double ConsortiaFightRate;

		// Token: 0x0400540D RID: 21517
		[ConfigProperty("CoupleFightGPRate", "夫妻经验加成", 0.5)]
		public static readonly double CoupleFightGPRate;

		// Token: 0x0400540E RID: 21518
		[ConfigProperty("GiftExpForEachLv", "礼品盒升级所需经验", "1|2")]
		public static readonly string GiftExpForEachLv;

		// Token: 0x0400540F RID: 21519
		[ConfigProperty("HotSpringExp", "温泉奖励经验", "400,480,576")]
		public static string HotSpringExp;

		// Token: 0x04005410 RID: 21520
		[ConfigProperty("SpaPubRoomLoginPay", "温泉消耗", "10000,2000")]
		public static string SpaPubRoomLoginPay;

		// Token: 0x04005411 RID: 21521
		[ConfigProperty("AcademyApprenticeFreezeHours", "断绝师徒徒弟冷却时间24小时", 24)]
		public static int AcademyApprenticeFreezeHours;

		// Token: 0x04005412 RID: 21522
		[ConfigProperty("AcademyMasterFreezeHours", "断绝师徒师傅冷却时间", 48)]
		public static int AcademyMasterFreezeHours;

		// Token: 0x04005413 RID: 21523
		[ConfigProperty("AcademyApprenticeAward", "徒弟达到等级奖励物品ID", "10|112085,15|112086,18|112087,20|112125")]
		public static string AcademyApprenticeAward;

		// Token: 0x04005414 RID: 21524
		[ConfigProperty("AcademyMasterAward", "徒弟达到等级师傅获得物品ID", "10|112088,15|112089,18|112090,20|112124")]
		public static string AcademyMasterAward;

		// Token: 0x04005415 RID: 21525
		[ConfigProperty("AcademyAppAwardComplete", "出师奖励（徒弟）男女", "1401|5293,1301|5192")]
		public static string AcademyAppAwardComplete;

		// Token: 0x04005416 RID: 21526
		[ConfigProperty("AcademyMasAwardComplete", "出师奖励（师傅）男女", "1414|5409,1314|5306")]
		public static string AcademyMasAwardComplete;

		// Token: 0x04005417 RID: 21527
		[ConfigProperty("WishBeadLimitLv", "镀金最低等级", 12)]
		public static readonly int WishBeadLimitLv;

		// Token: 0x04005418 RID: 21528
		[ConfigProperty("IsWishBeadLimit", "是否为镀金数量", false)]
		public static readonly bool IsWishBeadLimit;

		// Token: 0x04005419 RID: 21529
		[ConfigProperty("HoleLevelUpExpList", "开孔升级经验", "400|600|700|800|800")]
		public static string HoleLevelUpExpList;

		// Token: 0x0400541A RID: 21530
		[ConfigProperty("LittleGameStartHourse", "小游戏开始时间", 0)]
		public static int LittleGameStartHourse;

		// Token: 0x0400541B RID: 21531
		[ConfigProperty("LittleGameTimeSpending", "小游戏持续时间", 1)]
		public static int LittleGameTimeSpending;

		// Token: 0x0400541C RID: 21532
		[ConfigProperty("LittleGameBoguConfig", "小游戏波谷配置", "200,1,1|100,4,1|5000,80,3|10000,125,5")]
		public static string LittleGameBoguConfig;

		// Token: 0x0400541D RID: 21533
		[ConfigProperty("LittleGameMaxBoguCount", "小游戏最大波谷数量", 20)]
		public static int LittleGameMaxBoguCount;

		// Token: 0x0400541E RID: 21534
		[ConfigProperty("LeftRouterRateData", "充值转盘几率", "0.8|0.9|1.0|1.1|1.2|")]
		public static string LeftRouterRateData;

		// Token: 0x0400541F RID: 21535
		[ConfigProperty("LeftRoutteMaxDay", "充值转盘每天最大次数", 5)]
		public static int LeftRoutteMaxDay;

		// Token: 0x04005420 RID: 21536
		[ConfigProperty("LeftRouterEndDate", "充值转盘结束时间", "2012-01-01 20:55:27.270")]
		public static string LeftRouterEndDate;

		// Token: 0x04005421 RID: 21537
		[ConfigProperty("MissionRiches", "公会使命消耗财富", "3000|3000|5000|5000|8000|8000|10000|10000|12000|12000")]
		public static string MissionRiches;

		// Token: 0x04005422 RID: 21538
		[ConfigProperty("MissionAwardRiches", "公会使命奖励财富", "4500|4500|7500|7500|12000|12000|15000|15000|18000|18000")]
		public static string MissionAwardRiches;

		// Token: 0x04005423 RID: 21539
		[ConfigProperty("MissionAwardGP", "公会使命奖励经验", "100000|100000|200000|200000|300000|300000|400000|400000|500000|500000")]
		public static string MissionAwardGP;

		// Token: 0x04005424 RID: 21540
		[ConfigProperty("MissionAwardOffer", "公会使命奖励功勋", "500|500|1000|1000|1500|1500|2000|2000|2500|2500")]
		public static string MissionAwardOffer;

		// Token: 0x04005425 RID: 21541
		[ConfigProperty("MissionMinute", "公会使命时间", 360)]
		public static int MissionMinute;

		// Token: 0x04005426 RID: 21542
		[ConfigProperty("MissionBuffDay", "公会使命奖励BUFF时间", 1)]
		public static int MissionBuffDay;

		// Token: 0x04005427 RID: 21543
		[ConfigProperty("VirtualName", "机器人名称", "Doreamon,Nobita,Xuneo,Xuka")]
		public static readonly string VirtualName;

		// Token: 0x04005428 RID: 21544
		[ConfigProperty("SpaRoomCreateMoney", "温泉房间的价格,2小时", "800,1600")]
		public static readonly string PRICE_SPA_ROOM;

		// Token: 0x04005429 RID: 21545
		[ConfigProperty("SpaPubRoomCount", "温泉公共房间数量", "1,2")]
		public static readonly string COUNT_SPA_PUBROOM;

		// Token: 0x0400542A RID: 21546
		[ConfigProperty("SpaPriRoomCount", "温泉私有房间数量上限", 2000)]
		public static int COUNT_SPA_PRIROOM;

		// Token: 0x0400542B RID: 21547
		[ConfigProperty("SpaPubRoomServerID", "温泉公共房间的频道id", "1|10")]
		public static string SERVERID_SPA_PUBROOM;

		// Token: 0x0400542C RID: 21548
		[ConfigProperty("SpaPubRoomLoginPay", "登录温泉公共房间的扣费", "10000,200")]
		public static string LOGIN_PAY_SPA_PUBROOM;

		// Token: 0x0400542D RID: 21549
		[ConfigProperty("SpaPriRoomInitTime", "温泉私有房间的默认分钟", 60)]
		public static int INIT_TIME_SPA_PRIROOM;

		// Token: 0x0400542E RID: 21550
		[ConfigProperty("SpaPriRoomContinueTime", "温泉私有房间的每次续费的延长分钟", 60)]
		public static int CONTINUE_TIME_SPA_PRIROOM;

		// Token: 0x0400542F RID: 21551
		[ConfigProperty("SpaPubRoomTimeLimit", "玩家每日在公共房间的时间上限", "60,60")]
		public static string PLAYER_TIMELIMIT_SPA_PUBROOM;

		// Token: 0x04005430 RID: 21552
		[ConfigProperty("SpaPubRoomPlayerMaxCount", "温泉公共房间最大人数", "10,10")]
		public static string PLAYER_MAXCOUNT_SPA_PUBROOM;

		// Token: 0x04005431 RID: 21553
		[ConfigProperty("ShowCloseTreasure", "显示关闭迷宫藏宝", true)]
		public static bool ShowCloseTreasure;

		// Token: 0x04005432 RID: 21554
		[ConfigProperty("OpenCloseTreasure", "开启关闭迷宫藏宝", true)]
		public static bool OpenCloseTreasure;

		// Token: 0x04005433 RID: 21555
		[ConfigProperty("TreasureBeginTime", "迷宫藏宝开启时间", "2021/10/1 0:00:00")]
		public static readonly string TreasureBeginTime;

		// Token: 0x04005434 RID: 21556
		[ConfigProperty("TreasureEndTime", "迷宫藏宝关闭时间", "2050/10/1 0:00:00")]
		public static readonly string TreasureEndTime;

		// Token: 0x04005435 RID: 21557
		[ConfigProperty("TreasureNeedMoney", "迷宫单次需要点券", 2000)]
		public static int TreasureNeedMoney;

		// Token: 0x04005436 RID: 21558
		[ConfigProperty("EliteGameDayOpening", "精英赛开启时间", "0,6")]
		public static string EliteGameDayOpening;

		// Token: 0x04005437 RID: 21559
		[ConfigProperty("EliteGameBlockWeapon", "精英赛禁用武器", "7144|71441|71442|71443|71444|7145|71451|71452|71453|71454")]
		public static string EliteGameBlockWeapon;

		// Token: 0x04005438 RID: 21560
		[ConfigProperty("EmblemComposeRandom", "EmblemComposeRandom", "500,600")]
		public static string EmblemComposeRandom;

		// Token: 0x04005439 RID: 21561
		[ConfigProperty("BombEnterLevel", "扫雷最低等级", 10)]
		public static int BombEnterLevel;

		// Token: 0x0400543A RID: 21562
		[ConfigProperty("BombItemNum", "扫雷物品奖励数量", 10)]
		public static int BombItemNum;

		// Token: 0x0400543B RID: 21563
		[ConfigProperty("BombStartTime", "扫雷开启时间", "2022-07-20 10:00")]
		public static readonly string BombStartTime;

		// Token: 0x0400543C RID: 21564
		[ConfigProperty("BombEndTime", "扫雷结束时间", "2022-07-24 23:59")]
		public static readonly string BombEndTime;

		// Token: 0x0400543D RID: 21565
		[ConfigProperty("BombTimes", "扫雷每天次数", 2)]
		public static int BombTimes;

		// Token: 0x0400543E RID: 21566
		[ConfigProperty("BombFreeAutoMarkTimes", "扫雷免费探雷次数", 2)]
		public static int BombFreeAutoMarkTimes;

		// Token: 0x0400543F RID: 21567
		[ConfigProperty("AutoMarkPrice", "扫雷自动探雷价格", 300)]
		public static int AutoMarkPrice;

		// Token: 0x04005440 RID: 21568
		[ConfigProperty("AutoMarkFactor", "扫雷自动探雷价格", 300)]
		public static int AutoMarkFactor;

		// Token: 0x04005441 RID: 21569
		[ConfigProperty("RecoverPrice", "扫雷复活价格", 500)]
		public static int RecoverPrice;

		// Token: 0x04005442 RID: 21570
		[ConfigProperty("RecoverPriceFactor", "扫雷复活价格", 500)]
		public static int RecoverPriceFactor;

		// Token: 0x04005443 RID: 21571
		[ConfigProperty("BombFreeAutoResetTimes", "扫雷免费刷新次数", 2)]
		public static int BombFreeAutoResetTimes;

		// Token: 0x04005444 RID: 21572
		[ConfigProperty("BombResetPrice", "扫雷刷新价格", 5000)]
		public static int BombResetPrice;

		// Token: 0x04005445 RID: 21573
		[ConfigProperty("BombResetPriceFactor", "扫雷刷新价格", 5000)]
		public static int BombResetPriceFactor;

		// Token: 0x04005446 RID: 21574
		[ConfigProperty("BombAreaAward", "扫雷排名奖励", "1-1,1121494|2-2,1121495|3-3,1121496|4-4,1121497|5-5,1121498|6-6,1121499|7-7,1121499|8-8,1121499|9-9,1121499|10-10,1121499")]
		public static readonly string BombAreaAward;

		// Token: 0x04005447 RID: 21575
		[ConfigProperty("PairBoxBeginTime", "秘宝开启时间", "2022-07-20 10:00")]
		public static readonly string PairBoxBeginTime;

		// Token: 0x04005448 RID: 21576
		[ConfigProperty("PairBoxEndTime", "秘宝结束时间", "2022-07-24 23:59")]
		public static readonly string PairBoxEndTime;

		// Token: 0x04005449 RID: 21577
		[ConfigProperty(" DayBoxCount", "欧皇秘宝每日赠送次数", 2)]
		public static int PairBoxDayBoxCount;

		// Token: 0x0400544A RID: 21578
		[ConfigProperty("PairBoxExchangeHour", "欧皇秘宝兑换时长", 24)]
		public static int PairBoxExchangeHour;

		// Token: 0x0400544B RID: 21579
		[ConfigProperty("PairBoxDayActiveConfig", "", "30,50,90,120,150")]
		public static readonly string PairBoxDayActiveConfig;

		// Token: 0x0400544C RID: 21580
		[ConfigProperty("PairBoxLimitBuy", "欧皇秘宝礼金购买次数", 20)]
		public static int PairBoxLimitBuy;

		// Token: 0x0400544D RID: 21581
		[ConfigProperty("PairBoxPriceConfig", "欧皇秘宝价格配置", "500,500,300")]
		public static readonly string PairBoxPriceConfig;

		// Token: 0x0400544E RID: 21582
		[ConfigProperty("MysteryShopOpenTime", "吉普赛商店开启时间", "12|0")]
		public static readonly string MysteryShopOpenTime;

		// Token: 0x0400544F RID: 21583
		[ConfigProperty("MysteryShopFreshTime", "吉普赛商店每日刷新时间", 18)]
		public static readonly int MysteryShopFreshTime;

		// Token: 0x04005450 RID: 21584
		[ConfigProperty("MysteryShopMoneyRefreshValue", "吉普赛商店刷新需要的点券", 100)]
		public static readonly int MysteryShopMoneyRefreshValue;

		// Token: 0x04005451 RID: 21585
		[ConfigProperty("VIP11BagVIPLevelToBagCount", "VIP保管箱开启数量", "2,40|4,60|6,80|8,100")]
		public static readonly string VIP11BagVIPLevelToBagCount;

		// Token: 0x04005452 RID: 21586
		[ConfigProperty("MoonLightBoxOpenTime", "月光宝盒开启时间", "2022-07-20 10:00")]
		public static readonly string MoonLightBoxOpenTime;

		// Token: 0x04005453 RID: 21587
		[ConfigProperty("MoonLightBoxEndTime", "月光宝盒结束时间", "2022-07-20 10:00")]
		public static readonly string MoonLightBoxEndExchangeTime;

		// Token: 0x04005454 RID: 21588
		[ConfigProperty("MoonLightBoxEndTime", "月光宝盒结束时间", "2022-07-20 10:00")]
		public static readonly string MoonLightBoxEndTime;

		// Token: 0x04005455 RID: 21589
		[ConfigProperty("MoonlightBoxOpenTimes", "月光宝盒开启次数", "1,200,500")]
		public static readonly string MoonlightBoxOpenTimes;

		// Token: 0x04005456 RID: 21590
		[ConfigProperty("MoonlightBoxPrice", "月光宝盒消耗点券", "1,200|100,20000|500,100000")]
		public static readonly string MoonlightBoxPrice;

		// Token: 0x04005457 RID: 21591
		[ConfigProperty("MoonlightBoxLimitBuy", "月光宝盒每日限购", 200)]
		public static readonly int MoonlightBoxLimitBuy;

		// Token: 0x04005458 RID: 21592
		[ConfigProperty("MoonlightBoxRewardsTemplate", "月光宝盒累积奖励", "20,11212,1,1|100,11212,2,1|500,11212,2,1|1000,11212,2,1|1500,11212,2,1|3000,11212,2,1|5000,11212,2,1|10000,11212,2,1|10000,11212,2,1")]
		public static readonly string MoonlightBoxRewardsTemplate;

		// Token: 0x04005459 RID: 21593
		[ConfigProperty("MoonlightBoxCrossReward", "月光宝盒排名奖励", "1,1124925|2,1124926|3,1124927|4,1124928|5,1124929|6,1124929|7,1124929|8,1124929|9,1124929|10-999,1124929|")]
		public static readonly string MoonlightBoxCrossReward;

		// Token: 0x0400545A RID: 21594
		[ConfigProperty("NoviceUpGradeStartTime", "升级奖励开始时间", "2014/2/13 0:00:00")]
		public static string NoviceUpGradeStartTime;

		// Token: 0x0400545B RID: 21595
		[ConfigProperty("NoviceUpGradeEndTime", "升级奖励结束时间", "2014/2/13 0:00:00")]
		public static string NoviceUpGradeEndTime;

		// Token: 0x0400545C RID: 21596
		[ConfigProperty("NoviceUseMoneyStartTime", "消耗点券开始时间", "2014/2/13 0:00:00")]
		public static string NoviceUseMoneyStartTime;

		// Token: 0x0400545D RID: 21597
		[ConfigProperty("NoviceUseMoneyEndTime", "消耗点券结束时间", "2014/2/13 0:00:00")]
		public static string NoviceUseMoneyEndTime;

		// Token: 0x0400545E RID: 21598
		[ConfigProperty("NoviceReChangeMoneyStartTime", "充值点券开始时间", "2014/2/13 0:00:00")]
		public static string NoviceReChangeMoneyStartTime;

		// Token: 0x0400545F RID: 21599
		[ConfigProperty("NoviceReChangeMoneyEndTime", "充值点券结束时间", "2014/2/13 0:00:00")]
		public static string NoviceReChangeMoneyEndTime;

		// Token: 0x04005460 RID: 21600
		[ConfigProperty("NoviceStrengthenWeaponStartTime", "强化武器开始时间", "2014/2/13 0:00:00")]
		public static string NoviceStrengthenWeaponStartTime;

		// Token: 0x04005461 RID: 21601
		[ConfigProperty("NoviceStrengthenWeaponEndTime", "强化武器结束时间", "2014/2/13 0:00:00")]
		public static string NoviceStrengthenWeaponEndTime;

		// Token: 0x04005462 RID: 21602
		[ConfigProperty("SignRateBeginTime", "每日签到翻倍开始时间", "2014/2/13 0:00:00")]
		public static string SignRateBeginTime;

		// Token: 0x04005463 RID: 21603
		[ConfigProperty("SignRateEndTime", "每日签到翻倍结束时间", "2014/2/13 0:00:00")]
		public static string SignRateEndTime;

		// Token: 0x04005464 RID: 21604
		[ConfigProperty("ChargeActivityBeginTime", "充值大赠送开始时间", "2014/2/13 0:00:00")]
		public static string ChargeActivityBeginTime;

		// Token: 0x04005465 RID: 21605
		[ConfigProperty("ChargeActivityEndTime", "充值大赠送结束时间", "2014/2/13 0:00:00")]
		public static string ChargeActivityEndTime;

		// Token: 0x04005466 RID: 21606
		[ConfigProperty("FastGrowNeedMoney", "加速成长需要点券", 30)]
		public static readonly int FastGrowNeedMoney;

		// Token: 0x04005467 RID: 21607
		[ConfigProperty("FastGrowSubTime", "加速成长时间", 30)]
		public static readonly int FastGrowSubTime;

		// Token: 0x04005468 RID: 21608
		[ConfigProperty("MatchWin", "竞技胜利", 100)]
		public static readonly int MatchWin;

		// Token: 0x04005469 RID: 21609
		[ConfigProperty("MatchLose", "竞技失败", 200)]
		public static readonly int MatchLose;

		// Token: 0x0400546A RID: 21610
		[ConfigProperty("AmuletBuyDustCountAndNeedMoney", "AmuletBuyDustCountAndNeedMoney", "30|150|150")]
		public static readonly string AmuletBuyDustCountAndNeedMoney;

		// Token: 0x0400546B RID: 21611
		[ConfigProperty("AmuletBuyDustMax", "AmuletBuyDustMax", 20)]
		public static readonly int AmuletBuyDustMax;

		// Token: 0x0400546C RID: 21612
		[ConfigProperty("AmuletActiveRandom", "AmuletActiveRandom", 10)]
		public static readonly int AmuletActiveRandom;

		// Token: 0x0400546D RID: 21613
		[ConfigProperty("AmuletActiveMoney", "AmuletActiveMoney", 10)]
		public static readonly int AmuletActiveMoney;

		// Token: 0x0400546E RID: 21614
		[ConfigProperty("DevilTreasureOneCost", "DevilTreasureOneCost", 0)]
		public static readonly int DevilTreasureOneCost;

		// Token: 0x0400546F RID: 21615
		[ConfigProperty("DevilTreasureTenCost", "DevilTreasureTenCost", 0)]
		public static readonly int DevilTreasureTenCost;

		// Token: 0x04005470 RID: 21616
		[ConfigProperty("DevilTreasureFreeLotteryCount", "DevilTreasureFreeLotteryCount", 10)]
		public static readonly int DevilTreasureFreeLotteryCount;

		// Token: 0x04005471 RID: 21617
		[ConfigProperty("DevilTreasureFreeLotteryCD", "DevilTreasureFreeLotteryCD", 600)]
		public static readonly int DevilTreasureFreeLotteryCD;

		// Token: 0x04005472 RID: 21618
		[ConfigProperty("DevilTreasurePrizePool", "DevilTreasurePrizePool", 50000)]
		public static readonly int DevilTreasurePrizePool;

		// Token: 0x04005473 RID: 21619
		[ConfigProperty("DevilTreasurePrizePoolMax", "DevilTreasurePrizePoolMax", 100000)]
		public static readonly int DevilTreasurePrizePoolMax;

		// Token: 0x04005474 RID: 21620
		[ConfigProperty("DevilTreasureRankCount", "DevilTreasureRankCount", 2000)]
		public static readonly int DevilTreasureRankCount;

		// Token: 0x04005475 RID: 21621
		[ConfigProperty("DevilTreasureBoxExpriyMinutes", "DevilTreasureBoxExpriyMinutes", 240)]
		public static readonly int DevilTreasureBoxExpriyMinutes;

		// Token: 0x04005476 RID: 21622
		[ConfigProperty("DevilTreasureTemplateID", "DevilTreasureTemplateID", 11169)]
		public static readonly int DevilTreasureTemplateID;

		// Token: 0x04005477 RID: 21623
		[ConfigProperty("DevilTreasureBeginDate", "DevilTreasureBeginDate", "2020/4/26 10:00:00")]
		public static readonly string DevilTreasureBeginDate;

		// Token: 0x04005478 RID: 21624
		[ConfigProperty("DevilTreasureEndDate", "DevilTreasureEndDate", "2050/4/26 23:59:59")]
		public static readonly string DevilTreasureEndDate;

		// Token: 0x04005479 RID: 21625
		[ConfigProperty("DevilTreasureCfgBox", "DevilTreasureCfgBox", "12711|12712|12713")]
		public static readonly string DevilTreasureCfgBox;

		// Token: 0x0400547A RID: 21626
		[ConfigProperty("CarnivalConvertItem", "节日狂欢兑换商店", "1,52000903-1,20|2,52000904-1,20|3,52000900-1,10|4,46548-1,35|5,46557-1,35|6,52000304-1,20|7,52000358-1,30|8,52000913-1,50|9,52000914-1,70|10,52000915-1,90|11,47040-1,80|12,95384-1,120|13,46783-1,80|14,52000916-1,150|15,52000917-1,150|16,80000527-1,100|17,52000844-1,80\r\n")]
		public static readonly string CarnivalConvertItem;

		// Token: 0x0400547B RID: 21627
		[ConfigProperty("CarnivalBeginTime", "节日狂欢开启时间", "2013/12/17 0:00:00")]
		public static readonly string CarnivalBeginTime;

		// Token: 0x0400547C RID: 21628
		[ConfigProperty("CarnivalEndTime", "节日狂欢结束时间", "2050/12/25 0:00:00")]
		public static readonly string CarnivalEndTime;

		// Token: 0x0400547D RID: 21629
		[ConfigProperty("YearMonsterBeginDate", "年兽活动开始时间", "2014/1/17 0:00:00")]
		public static readonly string YearMonsterBeginDate;

		// Token: 0x0400547E RID: 21630
		[ConfigProperty("YearMonsterEndDate", "年兽活动关闭时间", "2050/2/25 0:00:00")]
		public static readonly string YearMonsterEndDate;

		// Token: 0x0400547F RID: 21631
		[ConfigProperty("YearMonsterBoxInfo", "年兽活动奖励", "112370,5|112371,30|112372,90|112373,150|112374,300")]
		public static readonly string YearMonsterBoxInfo;

		// Token: 0x04005480 RID: 21632
		[ConfigProperty("YearMonsterBuffMoney", "年兽活动buff购买所需要点卷", 300)]
		public static readonly int YearMonsterBuffMoney;

		// Token: 0x04005481 RID: 21633
		[ConfigProperty("YearMonsterFightNum", "YearMonsterFightNum", 1)]
		public static readonly int YearMonsterFightNum;

		// Token: 0x04005482 RID: 21634
		[ConfigProperty("YearMonsterHP", "年兽血量", 3000000)]
		public static readonly int YearMonsterHP;

		// Token: 0x04005483 RID: 21635
		[ConfigProperty("YearMonsterOpenLevel", "YearMonsterOpenLevel", 15)]
		public static readonly int YearMonsterOpenLevel;

		// Token: 0x04005484 RID: 21636
		[ConfigProperty("QXGameLimitCount", "海盗宝藏每日次数", 1)]
		public static readonly int QXGameLimitCount;

		// Token: 0x04005485 RID: 21637
		[ConfigProperty("FightSpiritLevelAddDamage", "战魂系统", "6,2")]
		public static readonly string FightSpiritLevelAddDamage;

		// Token: 0x04005486 RID: 21638
		[ConfigProperty("FightSpiritMaxLevel", "战魂系统", 12)]
		public static readonly int FightSpiritMaxLevel;

		// Token: 0x04005487 RID: 21639
		[ConfigProperty("FirstKillBeginDate", "副本首杀活动开启时间", "2022/4/20 0:00:00")]
		public static readonly string FirstKillBeginDate;

		// Token: 0x04005488 RID: 21640
		[ConfigProperty("FirstKillEndDate", "副本首杀活动结束时间", "2025/4/27 0:00:00")]
		public static readonly string FirstKillEndDate;

		// Token: 0x04005489 RID: 21641
		[ConfigProperty("ChristmasGifts", "圣诞堆雪人奖励", "11408,10|11906,35|100100,70|11408,120|11906,220|100100,370|11408,650|11906,1000|100100,100")]
		public static readonly string ChristmasGifts;

		// Token: 0x0400548A RID: 21642
		[ConfigProperty("ChristmasBuildSnowmanDoubleMoney", "圣诞堆雪人双倍点卷", 10)]
		public static readonly int ChristmasBuildSnowmanDoubleMoney;

		// Token: 0x0400548B RID: 21643
		[ConfigProperty("ChristmasBeginDate", "圣诞堆雪人开启时间", "2013/12/17 0:00:00")]
		public static readonly string ChristmasBeginDate;

		// Token: 0x0400548C RID: 21644
		[ConfigProperty("ChristmasEndDate", "圣诞堆雪人结束时间", "2050/12/25 0:00:00")]
		public static readonly string ChristmasEndDate;

		// Token: 0x0400548D RID: 21645
		[ConfigProperty("ChristmasGiftsMaxNum", "圣诞堆雪人礼物最大数量", 1000)]
		public static readonly int ChristmasGiftsMaxNum;

		// Token: 0x0400548E RID: 21646
		[ConfigProperty("ChristmasBuyTimeMoney", "圣诞堆雪人延时所需点卷", 150)]
		public static readonly int ChristmasBuyTimeMoney;

		// Token: 0x0400548F RID: 21647
		[ConfigProperty("ChristmasBuyMinute", "圣诞堆雪人购买分钟", 10)]
		public static readonly int ChristmasBuyMinute;

		// Token: 0x04005490 RID: 21648
		[ConfigProperty("ChristmasMinute", "圣诞堆雪人时间限制", 60)]
		public static readonly int ChristmasMinute;

		// Token: 0x04005491 RID: 21649
		[ConfigProperty("WarPassPriceConfig", "WarPassPriceConfig", "1,8,1000,800|2,88,5000,8800|3,688,15000,68800")]
		public static readonly string WarPassPriceConfig;

		// Token: 0x04005492 RID: 21650
		[ConfigProperty("WarPassPetConfig", "WarPassPetConfig", "1,140403_1120623_46465,150103_1120624_46466,190103_1120625_46467,160103_1120626_46468,200203_1121483_46469,230203_1123310_46470|2,140403_1120623_46471,150103_1120624_46472,190103_1120625_46473,160103_1120626_46474,200203_1121483_46475,230203_1123310_46476|3,140403_1120623_46477,150103_1120624_46477,190103_1120625_46477,160103_1120626_46477,200203_1121483_46477,230203_1123310_46477")]
		public static readonly string WarPassPetConfig;

		// Token: 0x04005493 RID: 21651
		[ConfigProperty("IsDDTMoneyActive", "IsDDTMoneyActive", false)]
		public static readonly bool IsDDTMoneyActive;

		// Token: 0x04005494 RID: 21652
		[ConfigProperty("RelicUpgradeItem", "RelicUpgradeItem", "386298,50")]
		public static readonly string RelicUpgradeItem;

		// Token: 0x04005495 RID: 21653
		[ConfigProperty("RelicBuffSubstatPrice", "RelicBuffSubstatPrice", "1,1,20,20,0|1,2,30,30,0|1,3,40,40,0|2,1,20,20,0|2,2,30,30,0|2,3,40,40,0|3,1,20,0,20|3,2,30,0,30|3,3,40,0,40|4,1,20,0,20|4,2,30,0,30|4,3,40,0,40|5,1,20,0,20|5,2,30,0,30|5,3,40,0,40")]
		public static readonly string RelicBuffSubstatPrice;
	}
}
