package bank
{
   import bank.analyzer.BankInvestmentDataAnalyzer;
   import bank.data.BankInvestmentModel;
   import bank.data.BankRecordInfo;
   import bank.data.GameBankEvent;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.utils.ClassUtils;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.TimeManager;
   import ddt.utils.AssetModuleLoader;
   import flash.display.Sprite;
   import flash.events.EventDispatcher;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   
   public class BankManager extends EventDispatcher
   {
      
      private static var _instance:BankManager;
      
      public static const BANK_GETINFO:int = 1;
      
      public static const BANK_ADD:int = 2;
      
      public static const BANK_UPDATA:int = 3;
      
      public static const BANK_UPDATE_TASK:int = 4;
      
      public static const BANK_SAVE_OR_GET_VIEW:int = 0;
      
      public static const BANK_SAVE_VIEW:int = 1;
      
      public static const BANK_GET_VIEW:int = 2;
      
      public static const BANK_SAVE_RECORD_VIEW:int = 3;
      
      private var _model:BankInvestmentModel;
      
      private var _totleSaveMoney:int = 0;
      
      private var _totleProfitMoney:int = 0;
      
      private var _icon:BaseButton;
      
      public function BankManager(_arg_1:inner)
      {
         super(null);
      }
      
      public static function get instance() : BankManager
      {
         if(_instance == null)
         {
            _instance = new BankManager(new inner());
         }
         return _instance;
      }
      
      public function setup() : void
      {
         this._model = new BankInvestmentModel();
         SocketManager.Instance.addEventListener(PkgEvent.format(389),this.__bankInfo);
      }
      
      private function showFrame() : void
      {
         var _local_1:Sprite = ClassUtils.CreatInstance("bank.view.BankMainFrameView");
         _local_1.width = 500;
         _local_1.height = 300;
         LayerManager.Instance.addToLayer(_local_1,3,true,1);
      }
      
      public function showIcon() : void
      {
         if(PlayerManager.Instance.Self.Grade >= 21)
         {
            HallIconManager.instance.updateSwitchHandler("bank",true);
         }
         else
         {
            HallIconManager.instance.executeCacheRightIconLevelLimit("bank",true,21);
         }
      }
      
      private function __bankInfo(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         var _local_3:int = _local_2.readInt();
         switch(_local_3)
         {
            case 1:
               this.getBankInfo(_local_2);
               return;
            case 2:
               this.addBankInfo(_local_2);
               return;
            case 3:
               this.UpdateBankInfo(_local_2);
         }
      }
      
      private function getBankInfo(_arg_1:PackageIn) : void
      {
         var _local_3:int = 0;
         var _local_2:* = null;
         while(Boolean(this._model.list.length))
         {
            this._model.list.shift();
         }
         var _local_4:int = _arg_1.readInt();
         _local_3 = 0;
         while(_local_3 < _local_4)
         {
            _local_2 = new BankRecordInfo();
            _local_2.bankId = _arg_1.readInt();
            _local_2.tempId = _arg_1.readInt();
            _local_2.userId = _arg_1.readInt();
            _local_2.Amount = _arg_1.readInt();
            _local_2.begainTime = _arg_1.readDate();
            this._model.list.push(_local_2);
            _local_3++;
         }
      }
      
      private function addBankInfo(_arg_1:PackageIn) : void
      {
         var _local_3:BankRecordInfo = new BankRecordInfo();
         var _local_2:int = _arg_1.readInt();
         _local_3.bankId = _arg_1.readInt();
         _local_3.tempId = _arg_1.readInt();
         _local_3.userId = _arg_1.readInt();
         _local_3.Amount = _arg_1.readInt();
         _local_3.begainTime = _arg_1.readDate();
         this._model.list.unshift(_local_3);
         this.dispatchEvent(new GameBankEvent("bank_save_success"));
      }
      
      private function UpdateBankInfo(_arg_1:PackageIn) : void
      {
         var _local_3:int = 0;
         var _local_2:Boolean = false;
         var _local_4:BankRecordInfo = new BankRecordInfo();
         _local_4.bankId = _arg_1.readInt();
         _local_4.Amount = _arg_1.readInt();
         _local_3 = 0;
         while(_local_3 < this._model.list.length)
         {
            if(this._model.list[_local_3].bankId == _local_4.bankId)
            {
               if(_local_4.Amount == 0)
               {
                  this._model.list.splice(_local_3,1);
                  _local_2 = true;
               }
               else
               {
                  this._model.list[_local_3].Amount = _local_4.Amount;
               }
            }
            _local_3++;
         }
         this.dispatchEvent(new GameBankEvent("bank_get_success",{"isDelete":_local_2}));
      }
      
      public function show() : void
      {
         var _local_2:* = null;
         var _local_1:int = PlayerManager.Instance.Self.Grade;
         if(_local_1 < 21)
         {
            _local_2 = LanguageMgr.GetTranslation("tank.bank.notOpen",21);
            MessageTipManager.getInstance().show(_local_2,0,true,1);
            return;
         }
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createBankLoader());
         AssetModuleLoader.addModelLoader("bank",5);
         SocketManager.Instance.out.sendBankUpdate(1);
         AssetModuleLoader.startCodeLoader(this.showFrame);
      }
      
      public function onDataComplete(_arg_1:BankInvestmentDataAnalyzer) : void
      {
         this._model.data = _arg_1.data;
      }
      
      public function get totleSaveMoney() : int
      {
         var _local_1:int = 0;
         this._totleSaveMoney = 0;
         _local_1 = 0;
         while(_local_1 < this._model.list.length)
         {
            this._totleSaveMoney += this._model.list[_local_1].Amount;
            _local_1++;
         }
         return this._totleSaveMoney;
      }
      
      public function get totleProfitMoney() : int
      {
         var _local_1:int = 0;
         this._totleProfitMoney = 0;
         _local_1 = 0;
         while(_local_1 < this._model.list.length)
         {
            this._totleProfitMoney += this.getProfitNum(this._model.list[_local_1],true);
            _local_1++;
         }
         return this._totleProfitMoney;
      }
      
      public function getProfitNum(_arg_1:BankRecordInfo, _arg_2:Boolean = false, _arg_3:int = 0, _arg_4:Boolean = true) : int
      {
         var _local_5:int = 0;
         var _local_7:int = _arg_2 ? _arg_1.Amount : _arg_3;
         var _local_6:Number = TimeManager.Instance.Now().time;
         var _local_8:int = int(int((_local_6 - _arg_1.begainTime.time) / 1000 / 60 / 60 / 24));
         if(_local_8 == 0 && _arg_2)
         {
            _local_8 = 1;
         }
         if(this._model.data[_arg_1.tempId].DeadLine == 0)
         {
            _local_5 = int(int(_local_7 * this.model.data[_arg_1.tempId].InterestRate * _local_8 / 10000));
         }
         else if(_arg_4)
         {
            _local_5 = int(int(_local_7 * this.model.data[_arg_1.tempId].InterestRate * this._model.data[_arg_1.tempId].DeadLine * 30 / 10000));
         }
         else
         {
            _local_5 = int(int(_local_7 * this.model.data[_arg_1.tempId].InterestRate * _local_8 / 10000));
         }
         return _local_5;
      }
      
      public function isAchieve(_arg_1:BankRecordInfo) : Boolean
      {
         var _local_2:Boolean = false;
         var _local_3:Number = TimeManager.Instance.Now().time;
         var _local_4:int = int(int((_local_3 - _arg_1.begainTime.time) / 1000 / 60 / 60 / 24));
         if(_local_4 >= this._model.data[_arg_1.tempId].DeadLine * 30)
         {
            return true;
         }
         return false;
      }
      
      public function get model() : BankInvestmentModel
      {
         return this._model;
      }
   }
}

class inner
{
   
   public function inner()
   {
      super();
   }
}
