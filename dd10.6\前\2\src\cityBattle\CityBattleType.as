package cityBattle
{
   public class CityBattleType
   {
      
      public static const CITYBATTLE_OPEN:int = 0;
      
      public static const CITYBATTLEINFO:int = 1;
      
      public static const JOINBATTLE:int = 2;
      
      public static const CONTENTIONRANK:int = 3;
      
      public static const EXCHANGEINFO:int = 4;
      
      public static const MYSCORE:int = 5;
      
      public static const EXCHANGE:int = 6;
      
      public static const INSPIRE:int = 7;
      
      public static const SCORE_RANK:int = 8;
      
      public function CityBattleType()
      {
         super();
      }
   }
}

