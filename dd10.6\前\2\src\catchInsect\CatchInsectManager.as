package catchInsect
{
   import catchInsect.event.CatchInsectEvent;
   import catchInsect.event.InsectEvent;
   import ddt.CoreManager;
   import ddt.data.player.SelfInfo;
   import ddt.manager.ChatManager;
   import ddt.manager.GameInSocketOut;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PathManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.StateManager;
   import ddt.manager.TimeManager;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.geom.Point;
   import hall.HallStateView;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   import road7th.data.DictionaryData;
   import times.utils.timerManager.TimerJuggler;
   import times.utils.timerManager.TimerManager;
   
   public class CatchInsectManager extends CoreManager
   {
      
      public static var isToRoom:Boolean;
      
      private static var _instance:CatchInsectManager;
      
      public static const UPDATE_INFO:String = "updateInfo";
      
      private var _self:SelfInfo;
      
      private var _model:CatchInsectModel;
      
      private var _hallStateView:HallStateView;
      
      private var _catchInsectIcon:MovieClip;
      
      private var _mapPath:String;
      
      private var _appearPos:Array = [];
      
      private var _catchInsectInfo:CatchInsectItemInfo;
      
      public var isReConnect:Boolean = false;
      
      public var loadUiModuleComplete:Boolean = false;
      
      private var _timer:TimerJuggler;
      
      private var _hasPrompted:DictionaryData;
      
      public var useCakeFlag:Boolean;
      
      private var _funcId:int = 0;
      
      public function CatchInsectManager()
      {
         super();
         this._timer = TimerManager.getInstance().addTimerJuggler(1000);
      }
      
      public static function get instance() : CatchInsectManager
      {
         if(!_instance)
         {
            _instance = new CatchInsectManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         this._model = new CatchInsectModel();
         this._self = new SelfInfo();
         SocketManager.Instance.addEventListener("catchInsect",this.pkgHandler);
      }
      
      private function pkgHandler(_arg_1:CatchInsectEvent) : void
      {
         var _local_3:CatchInsectEvent = null;
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_2:int = _arg_1.cmd;
         switch(_local_2)
         {
            case 128:
               this.openOrclose(_local_4);
               break;
            case 143:
               this.enterScene(_local_4);
               break;
            case 129:
               _local_3 = new CatchInsectEvent("addplayer_room",_local_4);
               break;
            case 141:
               _local_3 = new CatchInsectEvent("move",_local_4);
               break;
            case 131:
               _local_3 = new CatchInsectEvent("player_statue",_local_4);
               break;
            case 130:
               _local_3 = new CatchInsectEvent("removePlayer",_local_4);
               break;
            case 133:
               _local_3 = new CatchInsectEvent("addMonster",_local_4);
               break;
            case 135:
               this.updateScore(_local_4);
               break;
            case 137:
               _local_3 = new CatchInsectEvent("updateAreaRank",_local_4);
               break;
            case 132:
               _local_3 = new CatchInsectEvent("areaSelfInfo",_local_4);
               break;
            case 136:
               _local_3 = new CatchInsectEvent("updateLocalRank",_local_4);
               break;
            case 138:
               _local_3 = new CatchInsectEvent("localSelfInfo",_local_4);
               break;
            case 134:
               _local_3 = new CatchInsectEvent("cakeStatus",_local_4);
               break;
            case 139:
               _local_3 = new CatchInsectEvent("fightMonster",_local_4);
         }
         if(Boolean(_local_3))
         {
            dispatchEvent(_local_3);
         }
      }
      
      private function updateScore(_arg_1:PackageIn) : void
      {
         this.model.score = _arg_1.readInt();
         this.model.avaibleScore = _arg_1.readInt();
         this.model.prizeStatus = _arg_1.readInt();
         dispatchEvent(new Event("updateInfo"));
      }
      
      private function enterScene(_arg_1:PackageIn) : void
      {
         this._model.isEnter = _arg_1.readBoolean();
         if(this._model.isEnter)
         {
            this.initSceneData();
         }
         else
         {
            StateManager.setState("main");
         }
      }
      
      public function initSceneData() : void
      {
         this._mapPath = this.getCatchInsectResource() + "/map/CatchInsectMap.swf";
         this._catchInsectInfo.playerDefaultPos = new Point(500,500);
         this._catchInsectInfo.myPlayerVO.playerPos = this._catchInsectInfo.playerDefaultPos;
         this._catchInsectInfo.myPlayerVO.playerStauts = 0;
         dispatchEvent(new InsectEvent("catchInsectLoadMap"));
      }
      
      public function getCatchInsectResource() : String
      {
         return PathManager.SITE_MAIN + "image/scene/catchInsect";
      }
      
      public function solveMonsterPath(_arg_1:String) : String
      {
         return this.getCatchInsectResource() + "/monsters/" + _arg_1 + ".swf";
      }
      
      public function reConnectCatchInectFunc() : void
      {
         isToRoom = true;
         SocketManager.Instance.out.requestCakeStatus();
         if(!this.loadUiModuleComplete)
         {
            this.reConnect();
         }
         else
         {
            this.isReConnect = false;
            StateManager.setState("catchInsect");
         }
      }
      
      public function reConnect() : void
      {
         this.isReConnect = true;
         this._funcId = 2;
         show();
      }
      
      public function reConnectLoadUiComplete() : void
      {
         this.loadUiModuleComplete = true;
         SocketManager.Instance.out.enterOrLeaveInsectScene(0);
      }
      
      private function openOrclose(_arg_1:PackageIn) : void
      {
         this._model.isOpen = _arg_1.readBoolean();
         this.showEnterIcon(this._model.isOpen);
      }
      
      protected function __timerHandler(_arg_1:Event) : void
      {
         var _local_6:int = 0;
         var _local_2:int = 0;
         var _local_7:int = int(int(this.model.endTime.getTime() / 1000));
         var _local_5:Date = TimeManager.Instance.Now();
         var _local_4:int = int(int(_local_5.getTime() / 1000));
         var _local_3:int = _local_7 - _local_4;
         if(_local_3 > 0)
         {
            _local_6 = _local_3 % 3600;
            if(_local_6 < 5)
            {
               _local_2 = int(int(_local_3 / 3600));
               if(_local_2 <= 48 && _local_2 > 0 && !this._hasPrompted.hasKey(_local_2))
               {
                  ChatManager.Instance.sysChatAmaranth(LanguageMgr.GetTranslation("catchInsect.willEnd.promptTxt",_local_2));
                  this._hasPrompted.add(_local_2,1);
               }
            }
         }
      }
      
      public function showEnterIcon(_arg_1:Boolean) : void
      {
         HallIconManager.instance.updateSwitchHandler("catchinsect",_arg_1);
         if(_arg_1)
         {
            this._catchInsectInfo = new CatchInsectItemInfo();
            this._catchInsectInfo.myPlayerVO = new PlayerVO();
         }
         else if(StateManager.currentStateType == "catchInsect")
         {
            StateManager.setState("main");
         }
      }
      
      public function onClickCatchInsectIcon() : void
      {
         SoundManager.instance.playButtonSound();
         if(PlayerManager.Instance.Self.Grade < 20)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("catchInsect.Icon.NoEnter",20));
            return;
         }
         this._funcId = 1;
         show();
      }
      
      override protected function start() : void
      {
         dispatchEvent(new InsectEvent("catchInsectOpenView",this._funcId));
      }
      
      public function get catchInsectInfo() : CatchInsectItemInfo
      {
         return this._catchInsectInfo;
      }
      
      public function exitGame() : void
      {
         GameInSocketOut.sendGamePlayerExit();
      }
      
      public function get model() : CatchInsectModel
      {
         return this._model;
      }
      
      public function get mapPath() : String
      {
         return this._mapPath;
      }
      
      public function get isShowIcon() : Boolean
      {
         return this._model.isOpen;
      }
   }
}

