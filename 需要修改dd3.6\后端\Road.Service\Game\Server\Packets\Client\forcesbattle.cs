using System;
using System.Collections.Generic;
using System.Linq;
using Bussiness;
using Bussiness.Managers;
using EntityDatabase.PlayerModels;
using EntityDatabase.ServerModels;
using Game.Base.Packets;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Packets.Client
{
	// Token: 0x020009C8 RID: 2504
	[PacketHandler(768, "圣物")]
	public class forcesbattle : IPacketHandler
	{
		// Token: 0x060061CE RID: 25038 RVA: 0x001EAC18 File Offset: 0x001E8E18
		public int HandlePacket(GameClient client, GSPacketIn packet)
		{
			switch (packet.ReadByte())
			{
			case 1:
				this.SendForcesBaseInfo(client.Player);
				break;
			case 2:
				this.SendUserRelicItem(client.Player, 2);
				break;
			case 3:
				this.SendUpgradeRelic(client.Player, packet);
				break;
			case 4:
				this.SendAdvanceRelic(client.Player, packet);
				break;
			case 5:
				this.SendUseManualInfo(client.Player);
				break;
			case 6:
				this.SendRelicZF(client.Player, packet);
				break;
			case 7:
				this.SendUpgradeManual(client.Player, packet);
				break;
			case 8:
				this.SendEquipRelic(client.Player, packet);
				break;
			}
			return 0;
		}

		// Token: 0x060061CF RID: 25039 RVA: 0x001EACDC File Offset: 0x001E8EDC
		public void SendForcesBaseInfo(GamePlayer Player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(768);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteInt(Player.PlayerCharacter.RelicItemInfo.shopScore);
			string[] array = Player.PlayerCharacter.RelicItemInfo.RelicInfo.Split(new char[] { ',' });
			gspacketIn.WriteInt(Player.PlayerCharacter.RelicItemInfo.OpenCount);
			gspacketIn.WriteInt(Player.PlayerCharacter.RelicItemInfo.OpenCount);
			for (int i = 0; i < Player.PlayerCharacter.RelicItemInfo.OpenCount; i++)
			{
				gspacketIn.WriteInt(int.Parse(array[i]));
			}
			gspacketIn.WriteInt(1);
			for (int j = 0; j < 1; j++)
			{
				gspacketIn.WriteInt(j);
				gspacketIn.WriteInt(j);
			}
			Player.Out.SendTCP(gspacketIn);
		}

		// Token: 0x060061D0 RID: 25040 RVA: 0x001EADD4 File Offset: 0x001E8FD4
		public void SendUserRelicItem(GamePlayer Player, int Type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(768);
			gspacketIn.WriteByte((byte)Type);
			gspacketIn.WriteBoolean(true);
			gspacketIn.WriteInt(3);
			gspacketIn.WriteInt(3);
			gspacketIn.WriteInt(Player.PlayerCharacter.RelicItem.Count);
			foreach (Sys_User_RelicItemTemplate sys_User_RelicItemTemplate in Player.PlayerCharacter.RelicItem)
			{
				gspacketIn.WriteInt(sys_User_RelicItemTemplate.itemID);
				gspacketIn.WriteInt(sys_User_RelicItemTemplate.level);
				gspacketIn.WriteInt(sys_User_RelicItemTemplate.stage);
				gspacketIn.WriteInt(sys_User_RelicItemTemplate.curExp);
				gspacketIn.WriteInt(sys_User_RelicItemTemplate.ShardNum);
				List<RelicProDataInfo> list = RelicItemMgr.ParseToList(sys_User_RelicItemTemplate.ProArr);
				gspacketIn.WriteInt(list.Count);
				foreach (RelicProDataInfo relicProDataInfo in list)
				{
					gspacketIn.WriteInt(0);
					gspacketIn.WriteInt(relicProDataInfo.type);
					gspacketIn.WriteInt(relicProDataInfo.value);
					gspacketIn.WriteInt(relicProDataInfo.attachValue);
				}
			}
			Player.Out.SendTCP(gspacketIn);
		}

		// Token: 0x060061D1 RID: 25041 RVA: 0x001EAF48 File Offset: 0x001E9148
		public void SendUpgradeRelic(GamePlayer Player, GSPacketIn packet)
		{
			int _curDetailID = packet.ReadInt();
			int templateID = packet.ReadInt();
			int num = packet.ReadInt();

			// 解析圣物升级配置
			string[] relicUpgradeConfig = GameProperties.RelicUpgradeItem.Split(',');
			int configTemplateID = int.Parse(relicUpgradeConfig[0]);      // 386298
			int relicUpgradeExpValue = int.Parse(relicUpgradeConfig[1]);  // 50

			bool flag = num <= 0;
			if (flag)
			{
				Player.SendMessage("数量异常");
			}
			else
			{
				// 验证使用的是正确的圣物升级石
				bool flag1 = templateID != configTemplateID;
				if (flag1)
				{
					Player.SendMessage("请使用正确的圣物升级石！");
				}
				else
				{
					ItemInfo itemByTemplateID = Player.PropBag.GetItemByTemplateID(templateID);
					bool flag2 = itemByTemplateID == null || itemByTemplateID.Count < num;
					if (flag2)
					{
						Player.SendMessage("圣物升级石数量不足！");
					}
					else
					{
						bool flag3 = !Player.PropBag.RemoveCountFromStack(itemByTemplateID, num);
						if (flag3)
						{
							Player.SendMessage("扣除道具失败，请稍后重试！");
						}
						else
						{
							Sys_User_RelicItemTemplate sys_User_RelicItemTemplate = Player.PlayerCharacter.RelicItem.FirstOrDefault((Sys_User_RelicItemTemplate i) => i.itemID == _curDetailID);
							bool flag4 = sys_User_RelicItemTemplate == null;
							if (flag4)
							{
								Player.SendMessage("未找到对应的圣物信息！");
							}
							else
							{
								sys_User_RelicItemTemplate.curExp += num * relicUpgradeExpValue;
								int relicLevel = RelicItemMgr.GetRelicLevel(sys_User_RelicItemTemplate.itemID, sys_User_RelicItemTemplate.curExp, sys_User_RelicItemTemplate.level);
								bool flag5 = relicLevel != sys_User_RelicItemTemplate.level;
								if (flag5)
								{
									Sys_User_RelicItemTemplate sys_User_RelicItemTemplate2 = sys_User_RelicItemTemplate;
									int level = sys_User_RelicItemTemplate2.level;
									sys_User_RelicItemTemplate2.level = level + 1;
									sys_User_RelicItemTemplate.curExp = 0;
								}
								this.SendUserRelicItem(Player, 3);
								Player.EquipBag.UpdatePlayerProperties();
								Player.SendMessage("升级成功！");
							}
						}
					}
				}
			}
		}

		// Token: 0x060061D2 RID: 25042 RVA: 0x001EB0B0 File Offset: 0x001E92B0
		public void SendAdvanceRelic(GamePlayer Player, GSPacketIn packet)
		{
			int _curDetailID = packet.ReadInt();
			int num = packet.ReadInt();
			int num2 = packet.ReadInt();
			bool flag = num2 != 1 && num2 != 2;
			if (flag)
			{
				Player.SendMessage("类型错误！");
			}
			else
			{
				// 使用圣物进阶石 (ID: 386299)
				ItemInfo itemByTemplateID = Player.PropBag.GetItemByTemplateID(386299);
				int advancLevel = RelicItemMgr.GetAdvancLevel(_curDetailID, num2, num);
				int num3 = advancLevel * 2;
				bool flag2 = itemByTemplateID == null || itemByTemplateID.Count < num3;
				if (flag2)
				{
					Player.SendMessage("圣物进阶石数量不足！");
				}
				else
				{
					bool flag3 = !Player.PropBag.RemoveCountFromStack(itemByTemplateID, num3);
					if (flag3)
					{
						Player.SendMessage("扣除道具失败，请稍后重试！");
					}
					else
					{
						Sys_User_RelicItemTemplate sys_User_RelicItemTemplate = Player.PlayerCharacter.RelicItem.Where((Sys_User_RelicItemTemplate i) => i.itemID == _curDetailID).FirstOrDefault<Sys_User_RelicItemTemplate>();
						bool flag4 = sys_User_RelicItemTemplate == null;
						if (flag4)
						{
							Player.SendMessage("该圣物不存在！");
						}
						else
						{
							bool flag5 = num <= sys_User_RelicItemTemplate.stage || num != sys_User_RelicItemTemplate.stage + 1;
							if (flag5)
							{
								Player.SendMessage("进阶错误！");
							}
							else
							{
								bool flag6 = advancLevel == 0;
								if (flag6)
								{
									Player.SendMessage("该圣物无法进阶！");
								}
								else
								{
									this.UpgradeRelicItem(sys_User_RelicItemTemplate);
									Player.EquipBag.UpdatePlayerProperties();
									Player.SendMessage("进阶成功！");
									this.SendUserRelicItem(Player, 4);
								}
							}
						}
					}
				}
			}
		}

		// Token: 0x060061D3 RID: 25043 RVA: 0x001EB228 File Offset: 0x001E9428
		private bool CheckAndDeductAdvanceNum(GamePlayer Player, int NeedType, int ItemCount)
		{
			if (NeedType != 1)
			{
				if (NeedType == 2)
				{
					bool flag = Player.PlayerCharacter.RelicItemInfo.GJAdvanceNum < ItemCount;
					if (flag)
					{
						Player.SendMessage("进阶道具数量不足！");
						return false;
					}
					Player.PlayerCharacter.RelicItemInfo.GJAdvanceNum -= ItemCount;
				}
			}
			else
			{
				bool flag2 = Player.PlayerCharacter.RelicItemInfo.AdvanceNum < ItemCount;
				if (flag2)
				{
					Player.SendMessage("进阶道具数量不足！");
					return false;
				}
				Player.PlayerCharacter.RelicItemInfo.AdvanceNum -= ItemCount;
			}
			return true;
		}

		// Token: 0x060061D4 RID: 25044 RVA: 0x001EB2D8 File Offset: 0x001E94D8
		private bool CheckAndDeductZfNum(GamePlayer Player, int NeedType, int ItemCount)
		{
			if (NeedType != 1)
			{
				if (NeedType == 2)
				{
					bool flag = Player.PlayerCharacter.RelicItemInfo.GJZFNum < ItemCount;
					if (flag)
					{
						Player.SendMessage("高级圣物增幅石不足！！");
						return false;
					}
					Player.PlayerCharacter.RelicItemInfo.GJZFNum -= ItemCount;
				}
			}
			else
			{
				bool flag2 = Player.PlayerCharacter.RelicItemInfo.ZFNum < ItemCount;
				if (flag2)
				{
					Player.SendMessage("圣物增幅石不足！！");
					return false;
				}
				Player.PlayerCharacter.RelicItemInfo.ZFNum -= ItemCount;
			}
			return true;
		}

		// Token: 0x060061D5 RID: 25045 RVA: 0x001EB388 File Offset: 0x001E9588
		public void SendUseManualInfo(GamePlayer Player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(768);
			gspacketIn.WriteByte(5);
			List<ManualDataInfo> list = RelicItemMgr.ManualToList(Player.PlayerCharacter.RelicItemInfo.ManualInfo);
			gspacketIn.WriteInt(list.Count);
			foreach (ManualDataInfo manualDataInfo in list)
			{
				gspacketIn.WriteInt(manualDataInfo.type);
				gspacketIn.WriteInt(manualDataInfo.exp);
				gspacketIn.WriteInt(manualDataInfo.level);
			}
			Player.Out.SendTCP(gspacketIn);
		}

		// Token: 0x060061D6 RID: 25046 RVA: 0x001EB440 File Offset: 0x001E9640
		public void SendUpgradeManual(GamePlayer Player, GSPacketIn packet)
		{
			int ManualType = packet.ReadInt();
			int Manuallevel = packet.ReadInt();
			bool flag = Manuallevel > 10;
			if (flag)
			{
				Player.SendMessage("等级超过最大限制！");
			}
			else
			{
				List<ManualDataInfo> list = RelicItemMgr.ManualToList(Player.PlayerCharacter.RelicItemInfo.ManualInfo);
				bool flag2 = ManualType > 5 || ManualType < 0;
				if (flag2)
				{
					Player.SendMessage("类型异常！");
				}
				else
				{
					ManualDataInfo manualDataInfo = list[ManualType];
					TS_Relic_DegreeTemplate ts_Relic_DegreeTemplate = RelicItemMgr.RelicDegreeList.First((TS_Relic_DegreeTemplate i) => i.Quality == ManualType && Manuallevel == i.Level);
					bool flag3 = manualDataInfo.level + 1 != Manuallevel;
					if (flag3)
					{
						Player.SendMessage("等级错误！");
					}
					else
					{
						bool flag4 = (ts_Relic_DegreeTemplate == null) | (ts_Relic_DegreeTemplate.Exp > manualDataInfo.exp);
						if (flag4)
						{
							Player.SendMessage("宝典等级异常/宝典经验不足");
						}
						else
						{
							ManualDataInfo manualDataInfo2 = manualDataInfo;
							int level = manualDataInfo2.level;
							manualDataInfo2.level = level + 1;
							manualDataInfo.exp -= ts_Relic_DegreeTemplate.Exp;
							Player.PlayerCharacter.RelicItemInfo.ManualInfo = RelicItemMgr.ManualToJsonString(list);
							Player.SendMessage("宝典升级成功！");
							this.SendUseManualInfo(Player);
							Player.EquipBag.UpdatePlayerProperties();
						}
					}
				}
			}
		}

		// Token: 0x060061D7 RID: 25047 RVA: 0x001EB5A8 File Offset: 0x001E97A8
		private void UpgradeRelicItem(Sys_User_RelicItemTemplate RelicItem)
		{
			List<RelicProDataInfo> list = RelicItemMgr.ParseToList(RelicItem.ProArr);
			list.Add(RelicItemMgr.NewRelicProDataInfo(RelicItem.Quality));
			RelicItem.ProArr = RelicItemMgr.ConvertToJsonString(list);
			int stage = RelicItem.stage;
			RelicItem.stage = stage + 1;
		}

		// Token: 0x060061D8 RID: 25048 RVA: 0x001EB5F4 File Offset: 0x001E97F4
		public void SendRelicZF(GamePlayer Player, GSPacketIn packet)
		{
			int RelicID = packet.ReadInt();
			int num = packet.ReadInt();
			int num2 = packet.ReadInt();
			Sys_User_RelicItemTemplate sys_User_RelicItemTemplate = Player.PlayerCharacter.RelicItem.Where((Sys_User_RelicItemTemplate i) => i.itemID == RelicID).FirstOrDefault<Sys_User_RelicItemTemplate>();
			bool flag = sys_User_RelicItemTemplate == null;
			if (flag)
			{
				Player.SendMessage("该圣物不存在！");
			}
			else
			{
				int zfcount = RelicItemMgr.GetZFCount(sys_User_RelicItemTemplate.Quality, num, num2);
				bool flag2 = zfcount == 0;
				if (flag2)
				{
					Player.SendMessage("该词条不能增幅");
				}
				else
				{
					bool flag3 = this.CheckAndDeductZfNum(Player, num2, zfcount);
					if (flag3)
					{
						List<RelicProDataInfo> list = RelicItemMgr.ParseToList(sys_User_RelicItemTemplate.ProArr);
						list[num - 1] = RelicItemMgr.NewRelicProDataInfo(sys_User_RelicItemTemplate.Quality);
						sys_User_RelicItemTemplate.ProArr = RelicItemMgr.ConvertToJsonString(list);
						this.SendUserRelicItem(Player, 2);
						Player.EquipBag.UpdatePlayerProperties();
					}
				}
			}
		}

		// Token: 0x060061D9 RID: 25049 RVA: 0x001EB6E4 File Offset: 0x001E98E4
		public void SendEquipRelic(GamePlayer Player, GSPacketIn packet)
		{
			int num = packet.ReadInt();
			int num2 = packet.ReadInt();
			int RelicID = packet.ReadInt();
			Sys_User_RelicItemTemplate sys_User_RelicItemTemplate = Player.PlayerCharacter.RelicItem.Where((Sys_User_RelicItemTemplate i) => i.itemID == RelicID).FirstOrDefault<Sys_User_RelicItemTemplate>();
			bool flag = sys_User_RelicItemTemplate == null;
			if (flag)
			{
				Player.SendMessage("该圣物不存在！");
			}
			else
			{
				bool flag2 = num2 > Player.PlayerCharacter.RelicItemInfo.OpenCount;
				if (flag2)
				{
					Player.SendMessage("装备位置不存在！");
				}
				else
				{
					Player.PlayerCharacter.RelicItemInfo.RelicInfo = RelicItemMgr.GetRelicInfo(Player.PlayerCharacter.RelicItemInfo.RelicInfo, RelicID, num2 - 1, num);
					this.SendForcesBaseInfo(Player);
					Player.MainBag.UpdatePlayerProperties();
				}
			}
		}
	}
}
