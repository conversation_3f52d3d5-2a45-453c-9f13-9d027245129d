﻿#中文语言包
AlertDialog.Error:错误
AlertDialog.Info:提示
AlertDialog.ErrotTip:该功能暂时关闭
Crazytank.ONLY_ONE_CLIENT:同时只能打开一个客户端!
CrazyTank.CLOSE_OTHER_CLIENT:正在关闭其他客户端...
BaseStateCreator.LoadingTip:	模块正在加载中...
tank.auctionHouse.view.BaseStripView.Font:Arial
ok:确 定
get:领 取
sure:同 意
modify:修 改
close:关 闭
clear:清 除
cancel:取 消
money:点券
gift:礼金
gold:金币
medal:勋章
alert:警告:
load:加载
fail:失败
second:秒
yes:是
no:否
tips:提示
poorNote:您的点券不足，是否立即充值？
exp:经验
offer:功勋
attack:攻击
defence:防御 
agility:敏捷
luck:幸运 
damage:伤害
recovery:护甲
MaxHp:血量 
energy:体力 
day:天
hour:小时
minute:分钟
second:秒
club.contactConsortia.minLenLimit:少于4个字符不能发送
club.contactConsortia.maxLenLimit:最多只能输入30个字符
club.contactConsortiaName:公会名称
club.contactChairman:联系会长
ddt.vip.vipView.yearDiscountText:一次性开通三个月以上的VIP可成为紫金VIP,充值年费VIP享受8.8折优惠
chat.BugleInputFrameTitleString:个性喇叭
chat.BugleInputNull:输入框内容不能为空，请输入文字
chat.BugleInputFrameRemainString:剩余字数:
game.PlayerThumbnailTipItemText_0:查看资料
game.PlayerThumbnailTipItemText_1:加为好友
game.PlayerThumbnailTipItemText_2:悄 悄 话
littlegame.AwardScore:积分:
littlegame.MinLvNote:等级达到{0}级的玩家才能进入
littlegame.ScoreNote:等级达到{0}级的玩家才能进入
room.UnableToSwitchToAnotherViewer:不能从一个观战栏位切换到另一个观战栏位.
room.UnableToSwitchToAnotherViewerWhenIsReady:准备状态下不能切换到观战栏位.
ddt.ConnectServerError:连接游戏服务器失败！
tank.trainer.Welcome:       Hi, <font color='#a4ee91'><b>{0}</b></font> 为了帮助您更快的熟悉游戏操作，我们精心为您准备了<font color='#DC801B'><b> 30秒 </b></font>新手训练教程，完成教程可获得不菲的<font color='#DC801B'><b> 奖励 </b></font>噢~！
weekly.magazineTitle:弹弹时报
ddt.StartupResourceLoader.Error.LoadModuleError:加载{0}失败！"
chat.FastReplyCustomCountLimit:使用快捷发言设定达到最大限制.
chat.FastReplyCustomTextLengthLimit:自定义快捷回复的最大字符数为20.
chat.FastReplyDefaultStr:<自定义>
chat.fastRepley.Message0:我是路过打酱油的
chat.fastRepley.Message1:这也能打到,宽面条泪~
chat.fastRepley.Message2:不要小看弟，弟可是杀虫剂!
chat.fastRepley.Message3:准备!Ready3、2、1!
chat.fastRepley.Message4:我诅咒你!不中!不中!打不中!
wrongUnit:错误单位
ServerLinkError:链接游戏服务器失败
core.MarriedTipLatterTextHusband:{0}的老公
core.MarriedTipLatterTextWife:{0}的老婆
tank.game.danderStripTip.pow:POW
core.crossZone.PrivateChatToUnable:对方与您不在同一个区，无法密聊.
core.crossZone.ViewGoodInfoUnable:跨区物品无法查看.
core.crossZone.AddFriendUnable:跨区玩家无法添加为好友.
tank.MessageTip.GhostProp:给队友使用了
tank.MessageTip.AutoGuide:连续4回合没有命中对手，获得跟踪效果
tank.MessageTip.EmptyGrid:您的道具栏已满，无法获得
tank.calendar.signed:签到成功
tank.calendar.OutWeek:只能查看当日前后七天的活动。
tank.calendar.NoneActivity:没有活动。
tank.calendar.title:签到奖励(S)
tank.calendar.activity.time:{0}～{1}
tank.calendar.sign.title:领取累积签到奖励
tank.calendar.DailyAward:您已领取过每日奖励，下次领取时间{0}月{1}日24:00以后.
tank.calendar.grid.month0:JAN
tank.calendar.grid.month1:FEB
tank.calendar.grid.month2:MAR
tank.calendar.grid.month3:APR
tank.calendar.grid.month4:MAY
tank.calendar.grid.month5:JUN
tank.calendar.grid.month6:JUL
tank.calendar.grid.month7:AUG
tank.calendar.grid.month8:SEP
tank.calendar.grid.month9:OCT
tank.calendar.grid.month10:NOV
tank.calendar.grid.month11:DEC
tank.calendar.grid.today:{0}年{1}月{2}日 
tank.calendar.grid.week0:周日
tank.calendar.grid.week1:周一
tank.calendar.grid.week2:周二
tank.calendar.grid.week3:周三
tank.calendar.grid.week4:周四
tank.calendar.grid.week5:周五
tank.calendar.grid.week6:周六
tank.calendar.Activity.GetResult0:领奖成功,奖品已经发送到用户邮箱.
tank.calendar.Activity.GetResult1:未知错误.
tank.calendar.Activity.GetResult2:用户名不存在.
tank.calendar.Activity.GetResult3:领取物品失败.
tank.calendar.Activity.GetResult4:此号码不存在,请检查您的输入是否有误.
tank.calendar.Activity.GetResult5:此号码已经领过奖品,您不能重复领用.
tank.calendar.Activity.GetResult6:您已经在此活动中领取过奖品.
tank.calendar.Activity.GetResult7:活动未开始.
tank.calendar.Activity.GetResult8:活动已过期.
tank.calendar.Activity.GetResult9:领取失败.
tank.calendar.Activity.GetResult10:您无权进行此操作.
tank.calendar.Activity.GetResult11:激活码不能为空.
tank.calendar.Activity.GetResult12:超时,请过段时间再来领取
tank.calendar.Activity.GetResult13:活动不存在,请联系GM
tank.calendar.DailyAwardTitle:每日领取
tank.calendar.award.CountTitle:累计签到次数
tank.calendar.award.NagivCount:次
tank.calendar.LuckyNumBar.PayNote:您预测明天的幸运数字是：{0}<br>激活将扣除99点券，是否继续？
tank.calendar.LuckyNumBar.ChooseNote:预测明天的幸运数字，如果猜对可获得一天的经验、功勋翻倍，以及随机属性奖励。
tank.auctionHouse.controller.AuctionHouseController: 没有该类物品
tank.auctionHouse.controller.AuctionHouseListError:加载拍卖行列表失败！
tank.auctionHouse.view.AuctionBrowseView.sure: 您确认竞拍该物品吗?
tank.auctionHouse.view.AuctionBrowseView.AlertDialogInfo:提示：拍卖成功后的物品会变为绑定。
tank.auctionHouse.view.AuctionBrowseView.AlertDialogInfo1:拍卖成功后的物品会变为绑定
tank.auctionHouse.view.AuctionBrowseView.Auction: 竞拍价小于起拍价与增值之和
tank.auctionHouse.view.AuctionBrowseView.stipple:点券
tank.auctionHouse.view.AuctionBrowseView.Your: 你的余额不足
tank.auctionHouse.view.AuctionBrowseView.buy: 您确认一口价买下该物品吗?
tank.auctionHouse.view.AuctionBrowseView.buction: 竞拍金额输入
tank.auctionHouse.view.AuctionBrowseView.input:输入
tank.auctionHouse.view.AuctionBuyView.Auction: 竞拍价小于起拍价与增值之和
tank.auctionHouse.view.AuctionBuyView.Your: 你的余额不足
tank.auctionHouse.view.AuctionBuyView.price: 竞拍价:
tank.auctionHouse.view.AuctionBuyView.less: 小于起拍价与增值之和:
tank.auctionHouse.view.AuctionCellView.Object: 物品已过期
tank.auctionHouse.view.AuctionCellView.Sale: 物品已绑定,不能拍卖
tank.auctionHouse.view.AuctionDragInArea.this: 此物品已过期
tank.auctionHouse.view.AuctionRightView.Object: 物品
tank.auctionHouse.view.AuctionRightView.Total: (总数
tank.auctionHouse.view.AuctionSellLeftView.Choose: 请选择你需要拍卖的物品！
tank.auctionHouse.view.AuctionSellLeftView.8h: 8小时
tank.auctionHouse.view.AuctionSellLeftView.24h: 24小时
tank.auctionHouse.view.AuctionSellLeftView.48h: 48小时
tank.auctionHouse.view.AuctionSellLeftView.ChooseTwo: 请选择要拍卖的物品
tank.auctionHouse.view.AuctionSellLeftView.Price: 请填写起始价格
tank.auctionHouse.view.AuctionSellLeftView.PriceTwo:一口价不能低于或等于起始价
tank.auctionHouse.view.AuctionSellLeftView.storage: 保管费不够
tank.auctionHouse.view.AuctionSellView.cancel: 您确认取消本次拍卖吗?
tank.auctionHouse.view.AuctionSellView.Price: 此物品已参与竞价
tank.auctionHouse.view.AuctionSellView.Choose: 请选择物品
tank.auctionHouse.view.BagFrame.Choose: 请选择背包中的物品进行拍卖
calendar.view.goodsExchange.BagFrame.Choose: 请选择背包中的物品进行兑换
tank.auctionHouse.view.BrowseLeftListView:所有
tank.auctionHouse.view.BrowseLeftMenuView.All:全部
tank.auctionHouse.view.BrowseLeftMenuView.Hairdressing:美容
tank.auctionHouse.view.BrowseLeftMenuView.Arm:装备
tank.auctionHouse.view.BrowseLeftMenuView.Head:饰品
tank.auctionHouse.view.BrowseLeftMenuView.Prop:道具
tank.auctionHouse.view.BrowseLeftMenuView.Weapon:武器
choosecharacter.ChooseCharacterView.name:昵称中包含非法词汇
choosecharacter.ChooseCharacterView.space:昵称不能全为空格
choosecharacter.ChooseCharacterView.string:昵称中包含非法字符
choosecharacter.ChooseCharacterView.input:请输入昵称
tank.RenameFrame.Consortia.FailWord:名称中包含非法词汇
IM.privateChatFrame.failWord:信息中包含非法词汇
tank.RenameFrame.Consortia.space:名称不能全为空格
tank.RenameFrame.Consortia.string:名称中包含非法字符  
tank.RenameFrame.Consortia.input:内容不能为空
choosecharacter.ChooseSexPanel: 请先选择性别
tank.game.actions.ChangePlayerAction: ☎■■■■到你啦■■■■☎","☏□□□□到你啦□□□□☏
tank.game.actions.SelfPlayerWalkAction: 体力不足
tank.game.ArrowViewIII.fall: 轮到你行动时才可以使用
tank.game.ArrowViewIII.skill: 技能还需要{0}回合才能使用
tank.game.EnergyViewIII.proximal: 近战模式开启
tank.game.PropShortCutView.prop: 是否要删除该道具?
tank.game.RightPropView.prop: 此道具只能给自己使用
tank.game.ToolStripView.suicide1: 是否确定自杀?
tank.game.WebSpeedView.frame: 帧数:
tank.game.WebSpeedView.delay: 延迟:
tank.game.WebSpeedView.explain1:和游戏服务器通讯的平均时间。如果延迟一直很高（红色）的话，那么你的互联网连接可能有问题。
tank.game.WebSpeedView.explain2:游戏画面显示的流畅程度。如果画面的帧数较低，可以改变<FONT COLOR='#0099FF'>显示设置</FONT>来提高。
tnak.game.tryagain.title:请房主 <font color='#F6EE4B' >{0}</font> 选择是否续关
tnak.game.tryagain.value:续关需支付点券： <font color='#FF0000' ><b>{0}</b></font>
tank.game.BuffName11:在魔蚁之巢副本中，血量上限数值加成。
tank.game.BuffName12:攻击加强
tank.game.BuffName13:战斗觉醒
tank.game.BuffName14:狂暴一击
tank.game.BuffName15:超级体能
tank.game.BuffName16:愤怒释放
tank.game.BuffName17:全能战士
tank.game.BuffName18:生命之光
tank.game.BuffName19:希望护盾
tank.game.BuffName20:攻击增效
tank.game.BuffName21:先祖怒火
tank.game.BuffName22:寒冰庇护
tank.game.BuffName23:稳如泰山
tank.game.BuffName24:小强意志
tank.game.BuffName25:净化之源
tank.game.BuffName26:治疗祝福
tank.game.BuffName27:天赐防护
tank.game.BuffName31:神圣祝福
tank.game.BuffName32:神圣护盾
tank.game.BuffName101:上帝之手
tank.game.BuffName102:强袭
tank.game.BuffName103:致命一击
tank.game.BuffName104:强身术
tank.game.BuffName105:潜能爆发
tank.game.BuffName106:神行千里
tank.game.BuffName107:健体术
tank.game.BuffName108:增加效果回合
tank.game.BuffName109:掠夺
tank.game.BuffName110:贪婪
tank.game.BuffName111:神圣治愈
tank.game.BuffName112:笑里藏刀
tank.game.SelfMark.Skip:点击跳过本回合并增加怒气值20点

tank.game.BuffName215:战斗加强
tank.game.BuffName216:战斗加强
tank.game.BuffName217:战斗加强
tank.game.BuffName218:战斗加强
tank.game.BuffName219:战斗觉醒
tank.game.BuffName220:战斗觉醒
tank.game.BuffName221:战斗觉醒
tank.game.BuffName222:战斗觉醒
tank.game.BuffName223:狂暴一击
tank.game.BuffName224:狂暴一击
tank.game.BuffName225:狂暴一击
tank.game.BuffName226:狂暴一击
tank.game.BuffName227:超级体能
tank.game.BuffName228:超级体能
tank.game.BuffName229:超级体能
tank.game.BuffName230:超级体能
tank.game.BuffName231:愤怒释放
tank.game.BuffName232:愤怒释放
tank.game.BuffName233:愤怒释放
tank.game.BuffName234:愤怒释放
tank.game.BuffName235:全能战士
tank.game.BuffName236:全能战士
tank.game.BuffName237:全能战士
tank.game.BuffName238:全能战士
tank.game.BuffName239:生命之光
tank.game.BuffName240:生命之光
tank.game.BuffName241:生命之光
tank.game.BuffName242:生命之光
tank.game.BuffName243:希望护盾
tank.game.BuffName244:希望护盾
tank.game.BuffName245:希望护盾
tank.game.BuffName246:希望护盾
tank.game.BuffName247:攻击增效
tank.game.BuffName248:攻击增效
tank.game.BuffName249:攻击增效
tank.game.BuffName250:攻击增效
tank.game.BuffName251:先祖怒火
tank.game.BuffName252:先祖怒火
tank.game.BuffName253:先祖怒火
tank.game.BuffName254:先祖怒火
tank.game.BuffName255:寒冰庇护
tank.game.BuffName256:寒冰庇护
tank.game.BuffName257:寒冰庇护
tank.game.BuffName258:寒冰庇护
tank.game.BuffName259:稳如泰山
tank.game.BuffName260:稳如泰山
tank.game.BuffName261:稳如泰山
tank.game.BuffName262:稳如泰山
tank.game.BuffName263:小强意志
tank.game.BuffName264:小强意志
tank.game.BuffName265:小强意志
tank.game.BuffName266:小强意志
tank.game.BuffName267:净化之源
tank.game.BuffName268:净化之源
tank.game.BuffName269:净化之源
tank.game.BuffName270:净化之源
tank.game.BuffName271:治疗祝福
tank.game.BuffName272:治疗祝福
tank.game.BuffName273:治疗祝福
tank.game.BuffName274:治疗祝福
tank.game.BuffName275:天赐防护
tank.game.BuffName276:天赐防护
tank.game.BuffName277:天赐防护
tank.game.BuffName278:天赐防护
tank.game.BuffName279:神圣祝福
tank.game.BuffName280:神圣祝福
tank.game.BuffName281:神圣祝福
tank.game.BuffName282:神圣祝福
tank.game.BuffName283:神圣护盾
tank.game.BuffName284:神圣护盾
tank.game.BuffName285:神圣护盾
tank.game.BuffName286:神圣护盾
tank.game.BuffName287:神圣治愈
tank.game.BuffName288:神圣治愈
tank.game.BuffName289:神圣治愈
tank.game.BuffName290:神圣治愈
tank.game.BuffName400:疯狂的血
tank.game.BuffName401:疯狂的攻击
tank.game.BuffName402:超级生命
tank.game.BuffName403:超级攻击
tank.game.BuffName404:合金弹头
tank.game.BuffName405:远古祝福
tank.game.BuffName406:伤害增益
tank.game.BuffName407:疯狂的攻击

tank.game.BuffTip215:攻击命中时额外增加{0}点伤害。
tank.game.BuffTip216:攻击命中时额外增加{0}点伤害。
tank.game.BuffTip217:攻击命中时额外增加{0}点伤害。
tank.game.BuffTip218:攻击命中时额外增加{0}点伤害。
tank.game.BuffTip219:每次轮到自己回合激怒值额外增加{0}点。
tank.game.BuffTip220:每次轮到自己回合激怒值额外增加{0}点。
tank.game.BuffTip221:每次轮到自己回合激怒值额外增加{0}点。
tank.game.BuffTip222:每次轮到自己回合激怒值额外增加{0}点。
tank.game.BuffTip223:命中时出现暴击，则额外增加{0}点暴击伤害。
tank.game.BuffTip224:命中时出现暴击，则额外增加{0}点暴击伤害。
tank.game.BuffTip225:命中时出现暴击，则额外增加{0}点暴击伤害。
tank.game.BuffTip226:命中时出现暴击，则额外增加{0}点暴击伤害。
tank.game.BuffTip227:移动时不消耗体力，并且增加全属性{0}点。
tank.game.BuffTip228:移动时不消耗体力，并且增加全属性{0}点。
tank.game.BuffTip229:移动时不消耗体力，并且增加全属性{0}点。
tank.game.BuffTip230:移动时不消耗体力，并且增加全属性{0}点。
tank.game.BuffTip231:攻击命中时可额外增加{0}点怒气值。
tank.game.BuffTip232:攻击命中时可额外增加{0}点怒气值。
tank.game.BuffTip233:攻击命中时可额外增加{0}点怒气值。
tank.game.BuffTip234:攻击命中时可额外增加{0}点怒气值。
tank.game.BuffTip235:攻击、防御、敏捷、幸运属性各增加{0}点。
tank.game.BuffTip236:攻击、防御、敏捷、幸运属性各增加{0}点。
tank.game.BuffTip237:攻击、防御、敏捷、幸运属性各增加{0}点。
tank.game.BuffTip238:攻击、防御、敏捷、幸运属性各增加{0}点。
tank.game.BuffTip239:血量上限增加{0}点。
tank.game.BuffTip240:血量上限增加{0}点。
tank.game.BuffTip241:血量上限增加{0}点。
tank.game.BuffTip242:血量上限增加{0}点。
tank.game.BuffTip243:被击中时可减少{0}点伤害。
tank.game.BuffTip244:被击中时可减少{0}点伤害。
tank.game.BuffTip245:被击中时可减少{0}点伤害。
tank.game.BuffTip246:被击中时可减少{0}点伤害。
tank.game.BuffTip247:攻击命中时额外提高{0}%的伤害。
tank.game.BuffTip248:攻击命中时额外提高{0}%的伤害。
tank.game.BuffTip249:攻击命中时额外提高{0}%的伤害。
tank.game.BuffTip250:攻击命中时额外提高{0}%的伤害。
tank.game.BuffTip251:进入关卡时初始携带{0}点怒气。
tank.game.BuffTip252:进入关卡时初始携带{0}点怒气。 
tank.game.BuffTip253:进入关卡时初始携带{0}点怒气。 
tank.game.BuffTip254:进入关卡时初始携带{0}点怒气。
tank.game.BuffTip255:每回合受到的灼烧伤害可减少{0}点。
tank.game.BuffTip256:每回合受到的灼烧伤害可减少{0}点。
tank.game.BuffTip257:每回合受到的灼烧伤害可减少{0}点。
tank.game.BuffTip258:每回合受到的灼烧伤害可减少{0}点。
tank.game.BuffTip259:可在邪炎巨龙的怒吼攻击中屹立不动，并且增加全属性{0}点。
tank.game.BuffTip260:可在邪炎巨龙的怒吼攻击中屹立不动，并且增加全属性{0}点。
tank.game.BuffTip261:可在邪炎巨龙的怒吼攻击中屹立不动，并且增加全属性{0}点。
tank.game.BuffTip262:可在邪炎巨龙的怒吼攻击中屹立不动，并且增加全属性{0}点。  
tank.game.BuffTip263:防御属性提高{0}%。
tank.game.BuffTip264:防御属性提高{0}%。
tank.game.BuffTip265:防御属性提高{0}%。
tank.game.BuffTip266:防御属性提高{0}%。
tank.game.BuffTip267:每回合受到的中毒伤害可减少{0}点。
tank.game.BuffTip268:每回合受到的中毒伤害可减少{0}点。
tank.game.BuffTip269:每回合受到的中毒伤害可减少{0}点。
tank.game.BuffTip270:每回合受到的中毒伤害可减少{0}点。
tank.game.BuffTip271:天使的治疗效果可额外增加{0}点血量。
tank.game.BuffTip272:天使的治疗效果可额外增加{0}点血量。
tank.game.BuffTip273:天使的治疗效果可额外增加{0}点血量。
tank.game.BuffTip274:天使的治疗效果可额外增加{0}点血量。
tank.game.BuffTip275:能抵御拳击手的重拳攻击，并且增加全属性{0}点。
tank.game.BuffTip276:能抵御拳击手的重拳攻击，并且增加全属性{0}点。
tank.game.BuffTip277:能抵御拳击手的重拳攻击，并且增加全属性{0}点。
tank.game.BuffTip278:能抵御拳击手的重拳攻击，并且增加全属性{0}点。
tank.game.BuffTip279:攻击造成伤害的{0}%将转化为自己的生命值。
tank.game.BuffTip280:攻击造成伤害的{0}%将转化为自己的生命值。
tank.game.BuffTip281:攻击造成伤害的{0}%将转化为自己的生命值。
tank.game.BuffTip282:攻击造成伤害的{0}%将转化为自己的生命值。
tank.game.BuffTip283:将受到伤害的{0}%反弹给攻击你的人。
tank.game.BuffTip284:将受到伤害的{0}%反弹给攻击你的人。
tank.game.BuffTip285:将受到伤害的{0}%反弹给攻击你的人。
tank.game.BuffTip286:将受到伤害的{0}%反弹给攻击你的人。
tank.game.BuffTip287:救治中毒的小鸡士兵恢复血量额外增加{0}点。
tank.game.BuffTip288:救治中毒的小鸡士兵恢复血量额外增加{0}点。
tank.game.BuffTip289:救治中毒的小鸡士兵恢复血量额外增加{0}点。
tank.game.BuffTip290:救治中毒的小鸡士兵恢复血量额外增加{0}点。
tank.game.BuffTip400:增加{0}点生命值。
tank.game.BuffTip401:攻击力增加{0}。
tank.game.BuffTip402:增加{0}点生命值。
tank.game.BuffTip403:攻击力增加{0}。
tank.game.BuffTip404:大范围攻击，很牛...
tank.game.BuffTip405:攻击增加{0}。
tank.game.BuffTip406:伤害增加{0}。
tank.game.BuffTip407:伤害增加{0}。

tank.game.BuffTip11:血量上限增加{0}点。
tank.game.BuffTip12:攻击命中时额外增加{0}点伤害。
tank.game.BuffTip13:每次轮到自己回合激怒值额外增加{0}点。
tank.game.BuffTip14:命中时出现暴击，则额外增加{0}点暴击伤害。
tank.game.BuffTip15:移动时不消耗体力。
tank.game.BuffTip16:攻击命中时可额外增加{0}点怒气值。
tank.game.BuffTip17:攻击、防御、敏捷、幸运属性各增加{0}点。
tank.game.BuffTip18:血量上限增加{0}点。
tank.game.BuffTip19:被击时可减少{0}点伤害
tank.game.BuffTip20:攻击命中时额外提高{0}%的伤害。
tank.game.BuffTip21:进入关卡时初始携带{0}点怒气。
tank.game.BuffTip22:每回合受到的灼烧伤害可减少{0}点。
tank.game.BuffTip23:可在邪炎巨龙的怒吼攻击中屹立不动。
tank.game.BuffTip24:防御属性提高{0}%。
tank.game.BuffTip25:每回合受到的中毒伤害可减少{0}点。
tank.game.BuffTip26:天使的治疗效果可额外增加{0}点血量。
tank.game.BuffTip27:能抵御拳击手的重拳攻击。
tank.game.BuffTip31:每次攻击伤害的3%将转换为自己的生命值。
tank.game.BuffTip32:每次被攻击时，将承受伤害的5%反弹给攻击你的玩家，但是不会反弹死玩家。
tank.game.BuffTip101:受到的治疗效果提高{0}点。
tank.game.BuffTip102:伤害增加{0}点。
tank.game.BuffTip103:暴击伤害增加{0}点。
tank.game.BuffTip104:增加人物等级血量上限{0}%点。
tank.game.BuffTip105:提升攻防敏幸属性{0}点。
tank.game.BuffTip106:移动消耗体力降低{0}%。
tank.game.BuffTip107:体力值上限增加{0}点。
tank.game.BuffTip108:增加效果回合。
tank.game.BuffTip109:战斗获得功勋增加{0}倍。
tank.game.BuffTip110:战斗经验、翻牌金币获取增加{0}%。
tank.game.BuffTip111:使用加血道具时加血量增加{0}%。
tank.game.BuffTip112:攻击时可使对方怒气减少{0}%。


tank.game.BuffNameLucky:幸运数字“{0}”
tank.game.BuffTip40:战斗中{0}增加{1}点
tank.game.BuffTip41:战斗中经验x{0}倍
tank.game.BuffTip42:战斗中功勋x{0}倍
tank.game.BuffTip43:战斗经验两倍<br>战斗功勋两倍<br>战斗中血量增加{0}
tank.game.BuffTip44:战斗经验两倍<br>战斗功勋两倍<br>战斗中攻击增加{0}
tank.game.BuffTip45:战斗经验两倍<br>战斗功勋两倍<br>战斗中防御增加{0}
tank.game.BuffTip46:战斗经验两倍<br>战斗功勋两倍<br>战斗中幸运增加{0}
tank.game.BuffTip47:战斗经验两倍<br>战斗功勋两倍<br>战斗中护甲增加{0}
tank.game.BuffTip48:战斗经验两倍<br>战斗功勋两倍<br>战斗中伤害增加{0}
tank.game.BuffTip49:战斗中{0}增加{1}点

tank.invite.InviteView.request: 邀请
tank.invite.InviteView.list: 刷新列表
tank.invite.response.title:来自 “{0}” 的邀请
tank.invite.InviteView.close:关闭
tank.bagAndInfo.ColorShell.NoWeapon:您未装备武器，无法使用。
tank.room.ToShopConfirm:您将退出房间进入商城，确定要进入商城吗？
tank.room.RoomIIController.weapon: 没有装备武器不能开始游戏
tank.room.RoomIIController.reduceGains:此副本收益减少
tank.room.RoomIIController.all: 所有玩家准备才能开始游戏
tank.room.RoomIIController.balance: 组队不平衡，不能开始游戏
tank.room.RoomIIMapSet.room: 不允许设置房间信息
tank.room.RoomIIMapSet.room2: 修改房间信息
tank.room.RoomIIPlayerItem.new: 无
tank.room.RoomIIPlayerItem.unallowed: 不允许再操作
tank.room.RoomIIPlayerItem.make: 竞技中不允许再操作
tank.room.RoomIIPlayerItem.position: 至少保留一个位置
tank.room.RoomIIView2.kick: 你已被踢出房间
tank.room.RoomIIView2.owner: 您不是房主无权取消
tank.room.RoomIIView2.noallow: 你不是房主，你不允许设置!
tank.roomlist.RoomListIIBGView.start: 游戏已经开始
tank.roomlist.RoomListIIBGView.room: 房间人数已满。
tank.roomlist.RoomListIICreateRoomView.tank: 弹弹堂？弹弹堂！！吼吼
tank.roomlist.RoomListIICreateRoomView.go: 弹弹堂 go!go!!go!!!
tank.roomlist.RoomListIICreateRoomView.fire: 你轰我炸一决胜负
tank.roomlist.RoomListIICreateRoomView.happy: 开心弹弹堂，快乐无极限!
tank.roomlist.RoomListIICreateRoomView.time: 竞技房间固定回合时间：10秒
tank.roomlist.RoomListIICreateRoomView.name: 房间名不能为空
tank.roomlist.RoomListIICreateRoomView.string: 房间名存在非法字符
tank.roomlist.RoomListIICreateRoomView.set: 请设置你的房间密码
tank.roomlist.RoomListIICreateRoomView.set1:请选择战斗模式！
tank.roomlist.RoomListIICreateRoomView.set2:请选择房间模式！
tank.roomlist.RoomListIIFindRoomPanel.search: 查找房间
tank.roomlist.RoomListIIFindRoomPanel.id: 请输入房间ID号
tank.roomlist.RoomListIIPassInput.write: 请输入密码
tank.roomlist.siftAllFb:全部副本
tank.hall.ChooseHallView.sorry: 对不起,此功能暂未开放,敬请期待
tank.movement.MovementRightView.pass: 请输入卡密
tank.serverlist.ServerListPosView.choose: 请选择服务器
tank.serverlist.ServerListPosView.low: 您的等级过低，无法进入此服务器，请选择合适的频道进行游戏。
tank.serverlist.ServerListPosView.full: 人数已满，请选择其他频道!
tank.serverlist.ServerListPosView.maintenance: 该频道在维护中，请选择其他频道!
tank.serverlist.ServerListPosView.close: 关闭.........
tank.serverlist.ServerListView.login: 登录游戏服务器失败
shop.SetsTitle:激活梅恩兰德的祝福
shop.ShopIIBtnPanel.yellow: 金币和点券不足
shop.ShopIIBtnPanel.stipple: 点券不足
shop.ShopIIBtnPanel.gold: 金币不足
shop.ShopIIBtnPanel.buy: 确认购买？
shop.ShopIIModel.car: 购物车已满
shop.Shop.car: 购物车
shop.ShopIIPresentView.give: 请设置赠送对象
shop.ShopIIPresentView.space: 赠送对象不能全部为空格
shop.ShopIISaveFigurePanel.point: 点
shop.ShopIIShoppingCarItem.stipple: 点券
shop.ShopIIShoppingCarItem.day:天
shop.ShopIIShoppingCarItem.piece:件
shop.ShopIIShoppingCarItem.free:免费
shop.ShopIIShoppingCarItem.gold:金币
shop.ShopIIShoppingCarItem.forever:永久 
store.view.fusion.AccessoryDragInArea.overdue: 此物品已过期
store.view.fusion.LoadStrengthenListError:加载强化列表失败！
store.view.fusion.AccessoryDragInArea.type: 该类物品已满或类型不符!
store.view.fusion.AccessoryDragInArea.more: 您不能放入更多!
store.view.fusion.AccessoryDragInArea.move:物品已满或做为原装备无属性可转换!
store.view.fusion.TransferItemCell.object:该装备类型不符！
store.view.fusion.TransferItemCell.current:当前类型不符！
store.view.fusion.TransferItemCell.put:请放入相同部位的装备。
store.view.fusion.AccessoryFrame.add: 附加物品
store.view.fusion.AccessoryItemCell.overdue: 此物品已过期!
store.view.fusion.AccessoryItemCell.fusion: 此物品不可熔炼!
store.view.fusion.PreviewFrame.preview: 预览
store.view.fusion.PreviewFrame.lowRate:极低
store.view.fusion.PreviewFrame.false: 没有预览信息,请检查加入的熔炼物品与熔炼公式!
store.view.fusion.StoreIIFusionBG.error: 熔炼出错!
store.view.fusion.StoreIIFusionBG.your: 您的金币不足
store.view.fusion.StoreIIFusionBG.put: 请放满主熔炼物品及熔炼公式
store.view.fusion.StoreIIFusionBG.say:熔炼说明
store.view.fusion.StoreIIFusionBG.use:使用绑定的道具将会使您的物品<br>成为绑定状态，是否继续操作？
store.view.transfer.StoreIITransferBG.jade: 玉石
store.view.transfer.StoreIITransferBG.target: 目标装备
store.view.transfer.StoreIITransferBG.gold:您的金币不足。
store.view.transfer.StoreIITransferBG.point:您的点券不足，是否立即充值？
store.view.transfer.StoreIITransferBG.sure:是否互换两件装备的属性？<br><FONT SIZE='12' FACE='Arial' KERNING='2' COLOR='#FF0000'> 注：转换后两装备和角色绑定。</FONT>
store.view.transfer.StoreIITransferBG.BeforeHole:转换前{0}个孔吗？
store.view.transfer.StoreIITransferBG.AfterHole:转换后两个个孔吗？
store.view.transfer.StoreIITransferBG.put:请放入相同部位的装备。
store.view.transfer.StoreIITransferBG.move:转换说明
store.view.transfer.HoleBeforeLabel:强化孔宝珠
store.view.transfer.HoleAfterLabel:封印孔宝珠
store.view.transfer.HoleLabel:是否互换两件装备的强化、合成和选中的属性？<br><FONT SIZE='12' FACE='Arial' KERNING='2' COLOR='#FF0000'> 注：转换后两装备和角色绑定。</FONT>
store.view.transfer.EmptyItem:请继续放入装备或武器！
store.view.transfer.NoItem:您未放入装备或武器!
store.ComposeItemCell.type: 此类物品类型不符!
store.ComposeItemCell.up: 该装备已经合成到最高级.
store.StrengthItemCell.up: 该装备已经升级到最高级.
store.StrengthItemCell.ratiofalse:强化石等级太低，不能强化. 
store.StrengthItemCell.countlittle:强化石太少，必须3个以上才能强化.
store.StoreIIComposeBG.say:合成说明
store.StoreIIComposeBG.use:使用绑定的道具将会使您的装备<br>成为绑定状态，是否继续操作？
store.StoreIIStrengthBG.say:强化说明
store.StoreIIStrengthBG.use:使用绑定的道具将会使您的装备<br>成为绑定状态，是否继续操作？
store.Strength.Succes.ChatSay:恭喜您强化成功!
store.Transfer.Succes.ChatSay:恭喜您转换成功!
store.Embed.Succes.ChatSay:恭喜您镶嵌成功!
store.storeTip.hurt:伤{0}害{1}{2}
store.storeTip.chatHurt:武器伤害提升{0}点.
store.storeTip.Armor:护{0}甲{1}{2}
store.storeTip.chatArmor:装备护甲提升{0}点.
store.Embed.OpenHole.NoDrill:背包中没有{0}级开孔钻头。
store.Embed.OpenHole.LvError:该孔等级太低，无法镶嵌。
store.Embed.OpenHole.Opened:该孔已开启
store.Embed.OpenHole.NoTarget:未放入装备。
store.Embed.OpenHole.UnabledOpened:该孔不能开启
store.storeTip.openHole:新开插孔 
store.storeTip.AddHP:恢{0}复{1}{2}
store.storeTip.chatAddHP:副手恢复提升{0}点.
store.storeTip.subHurt:减{0}伤{1}{2}
store.storeTip.chatSubHurt:副手减伤提升{0}点.
store.storeTip.clothOpenDefense:防御
store.storeTip.weaponOpenAttack:攻击
store.storeTip.weaponOpenProperty:属性
store.storeTip.OpenDefense:新开防御宝珠插孔!
store.storeTip.OpenAttack:新开攻击宝珠插孔!
store.storeTip.OpenProperty:新开属性宝珠插孔!
tank.data.auctionHouse.AuctionGoodsInfo.short: 短
tank.data.auctionHouse.AuctionGoodsInfo.middle: 中
tank.data.auctionHouse.AuctionGoodsInfo.long: 长
tank.data.auctionHouse.AuctionGoodsInfo.very: 非常长
tank.data.channel.ChannelType.current: 当前
tank.data.channel.ChannelType.house: 公会
tank.data.player.SelfInfo.this: 此类物品不能被装备
tank.data.player.SelfInfo.object: 物品所需性别不同，不能装备
tank.data.player.SelfInfo.need: 您的等级过低，物品无法装备
tank.data.EmailInfo.test: 中华人民共和国中华人民共和国中华人民共和国中华人民共和国中华人民共和国保人
tank.data.EmailInfo.email: 邮件测试中。。。。。。。。。。。
tank.data.EmailInfo.random: 随便测试
tank.data.EquipType.weapon: 武器
tank.data.EquipType.leagueBadge: 灵宠
tank.data.EquipType.TEMPARMLET: 临时手鐲
tank.data.EquipType.tempTEMPRING: 临时戒指
tank.data.EquipType.tempweapon: 临时武器
tank.data.EquipType.tempHelp:临时副手
tank.data.EquipType.head: 帽子
tank.data.EquipType.glass: 眼镜
tank.data.EquipType.hair: 头发
tank.data.EquipType.face: 脸饰
tank.data.EquipType.clothing: 衣服
tank.data.EquipType.eye: 眼睛
tank.data.EquipType.bangle: 手鐲
tank.data.EquipType.finger: 戒指
tank.data.EquipType.prop: 道具
tank.data.EquipType.tool: 战斗道具
tank.data.EquipType.normal: 功能道具
tank.date.EquipType.bianyi: 圣物
tank.data.WebSpeedInfo.good: 良好
tank.data.WebSpeedInfo.find: 通畅
tank.data.WebSpeedInfo.bad: 极差
tank.manager.BallManager.bom: 炸弹元数据加载失败
tank.manager.CharTimerManager.say: 重复发言需间隔 3 秒
tank.manager.ChatManager.tool:没有相应道具，不能使用该功能
tank.manager.ItemManager.loader: 物品元数据加载失败
tank.manager.MailManager.email: 邮件打开失败，请稍后重试!
tank.manager.MailManager.delete: 邮件删除成功
tank.manager.MailManager.false: 邮件删除失败
tank.manager.MailManager.back: 退回邮件成功！
tank.manager.MailManager.return: 退回邮件失败！
tank.manager.MapManager.random:随机地图
tank.manager.MapManager.loader: 加载地图信息失败!
tank.manager.PlayerManager.one: 公会：<{0}> 已经通过了你的申请！
tank.consortia.club.CreatConsortiaFrame.titleText:创建公会
tank.manager.PlayerManager.your: 你所在的公会已经解散！！
tank.manager.PlayerManager.hit: 你已经被踢出公会！！
tank.manager.PlayerManager.hitSuccess: 踢出公会成功
tank.manager.PlayerManager.request: 公会邀请
tank.manager.PlayerManager.come: 邀请你进入&lt;{0}&gt;公会
tank.manager.PlayerManager.sure:确认加入
tank.manager.PlayerManager.refuse:拒绝加入
tank.manager.RoomManager.type: 暂时没有符合的房间类型!
tank.manager.RoomManager.break:对不起，断线了请刷新页面，重新登录！
tank.manager.RoomManager.false:对不起，断线了请刷新页面，重新登录！
tank.manager.StateManager.save: 保存文件，以便下次不再加载
tank.manager.TaskManager.loader: 加载任务列表失败,确定后重试!
tank.manager.TaskManager.task: 您的任务列表已满
tank.request.LoadServerListAction.server: 服务器维护中，请稍后再试!
ddt.UseGoodsFrame.title:打 开
tank.UseGoodsFrame.number:请输入使用数量。
tank.view.bagII.BreakGoodsView.split: 拆分
tank.view.bagII.BreakGoodsView.num: 数量
tank.view.bagII.BreakGoodsView.number: 拆分数量:
tank.view.bagII.BreakGoodsView.input:请输入拆分数量。
tank.view.bagII.BreakGoodsView.right: 数量不正确
tank.view.bagII.DeleteGoodsBtn.delete: 删 除
tank.view.bagII.DeleteGoodsBtn.sure: 确定删除此物品？
tank.view.bagII.GoodsTipPanel.fire:攻击
tank.view.bagII.GoodsTipPanel.recovery:防御
tank.view.bagII.GoodsTipPanel.agility:敏捷
tank.view.bagII.GoodsTipPanel.lucky:幸运
tank.view.bagII.GoodsTipPanel.need:所需等级
tank.view.bagII.GoodsTipPanel.man:所需性别:男
tank.view.bagII.GoodsTipPanel.woman:所需性别:女
tank.view.bagII.GoodsTipPanel.display:(强化失败不消失)
tank.view.bagII.GoodsTipPanel.may:可强化合成
tank.view.bagII.GoodsTipPanel.compose:可合成
tank.view.bagII.GoodsTipPanel.strong:可强化
tank.view.bagII.GoodsTipPanel.intensify:强化{0}级
tank.view.bagII.GoodsTipPanel.he:合成
tank.view.bagII.GoodsTipPanel.z:朱雀加攻击
tank.view.bagII.GoodsTipPanel.x:玄武加防御
tank.view.bagII.GoodsTipPanel.q:青龙加敏捷
tank.view.bagII.GoodsTipPanel.b:白虎加幸运
tank.view.bagII.GoodsTipPanel.use:永久有效
tank.view.bagII.GoodsTipPanel.less:还剩 
tank.view.bagII.GoodsTipPanel.time:有效时间 
tank.view.bagII.GoodsTipPanel.over:已过期
tank.view.bagII.GoodsTipPanel.hechengshi:合成石
tank.view.bagII.GoodsTipPanel.qianghuashi:强化石
tank.view.bagII.GoodsTipPanel.drill:开孔钻头
tank.view.bagII.GoodsTipPanel.drillnote:开孔钻头{0}级
tank.view.bagII.GoodsTipPanel.renwu:任务
tank.view.bagII.GoodsTipPanel.bangding:绑定类型：使用绑定
tank.view.bagII.GoodsTipPanel.zhuangbei:绑定类型：装备绑定
tank.view.cells.BagCell.detrude: 是否丢弃该物品?
tank.view.common.AddPricePanel.xufei2:    续费类型：
tank.view.common.AddPricePanel.xu:续 费
tank.view.common.AddPricePanel.sure: 是否确定删除该物品?
tank.view.common.AddPricePanel.pay: 确认支付?
tank.game.ToolStripView.friend:社交(F)
tank.game.ToolStripView.recentContact:最近联系人
tank.game.IM.custom:添加自定义组
tank.game.ToolStripView.face:表情
tank.game.ToolStripView.chat:快速聊天
tank.game.ToolStripView.set:设置(H)
tank.game.ToolStripView.sign:签到(S)
tank.game.ToolStripView.exit:退出
tank.game.ToolStripView.proplayer1:切换至竖向布局
tank.game.ToolStripView.proplayer2:切换至横向布局
tank.game.ToolStripView.transparent:透明(T)
tank.game.ToolStripView.suicide:自杀
tank.game.ToolStripView.task:任务(Q)
tank.roomlist.RoomListIIRoomBtnPanel.createRoom:创建游戏房间
tank.roomlist.RoomListIIRoomBtnPanel.fastjojn:快速加入组队战
tank.roomlist.RoomListIIRoomBtnPanel.fastjojnQj:快速加入夺旗战
tank.roomlist.RoomListIIRoomBtnPanel.findRoom:查找一个房间
tank.roomlist.RoomListIIRoomBtnPanel.waitRoomList:等待房间列表
tank.roomlist.RoomListIIRoomBtnPanel.allRoom:全部房间列表
tank.view.common.BellowStripViewII.supply: 充值
tank.view.common.BellowStripViewII.shop: 商城
tank.view.common.BellowStripViewII.bag: 背包(B)
tank.view.common.BellowStripViewII.email: 邮件(R)
tank.view.common.BellowStripViewII.back: 返回
tank.view.common.BellowStripViewII.exit: 退出
tank.view.common.BellowStripViewII.shortcut: 左键点击，将弹弹堂保存到桌面。
tank.view.common.FPSView.biaozhun: 标准:25FPS\n
tank.view.common.FPSView.dangqian: 当前:{0} FPS\n
tank.view.common.FPSView.xingneng: 性能:
FPSView.as.InviteAlertPanel.ruguo: 如果您在{0}秒没有反应，系统将默认拒绝！
FPSView.as.InviteAlertPanel.bianhao: 房间编号：
FPSView.as.InviteAlertPanel.ModeLabel: 游戏模式：
FPSView.as.InviteAlertPanel.name: 房间名称：
FPSView.as.InviteAlertPanel.mode: 对战模式：
FPSView.as.InviteAlertPanel.free:自由组队
FPSView.as.InviteAlertPanel.duoqi: 夺旗模式
FPSView.as.InviteAlertPanel.meifanying: 如果您在15秒没有反应，系统将默认拒绝！
FPSView.as.InviteAlertPanel.yaoqingni: 邀请您加入他的游戏房间
FPSView.as.InviteAlertPanel.map: 地图名称：
FPSView.as.InviteAlertPanel.huihetime: 回合时间：
FPSView.as.InviteAlertPanel.second: 秒
FPSView.as.InviteAlertPanel.nochoice: 未选
tank.view.scenechatII.PrivateChatIIView.privatename: 私聊
tank.view.scenechatII.PrivateChatIIView.nick:    玩家昵称:
tank.view.scenechatII.SceneChatIIController.littleb: ]使用小喇叭广播:
tank.view.scenechatII.SceneChatIIController.bigb: ]使用大喇叭广播:
tank.view.scenechatII.SceneChatIIController.sys:
tank.view.scenechatII.SceneChatIIController.sys0:[
tank.view.scenechatII.SceneChatIIController.say1:你对[
tank.view.scenechatII.SceneChatIIController.say2:]说：
tank.view.scenechatII.SceneChatIIController.say3:]悄悄地说：
tank.view.scenechatII.SceneChatIIController.say4:[{0}

tank.view.scenechatII.SceneChatIIModel.channel1: 当前
tank.view.scenechatII.SceneChatIIModel.channel2: 公会
tank.view.scenechatII.SceneChatIIModel.channel3: 大喇叭
tank.view.scenechatII.SceneChatIIModel.channel4: 小喇叭
tank.view.scenechatII.SceneChatIIModel.channel5: 组队
tank.view.scenechatII.SceneChatIIView.send: 发送信息
tank.view.scenechatII.SceneChatIIView.fast: 快捷回复
tank.view.scenechatII.SceneChatIIView.face: 表情
tank.view.im.AddFriendFrame.add: 添加好友
tank.view.im.AddFriendFrame.name: 输入昵称
tank.view.im.AddFriendFrame.qingwrite: 请输入用户昵称
tank.view.im.IMConsortiaList.online: 在线
tank.view.im.IMConsortiaList.zhanli: 暂离
tank.view.im.IMConsortiaList.dengji: 公会等级：
tank.view.im.IMConsortiaList.paiming: 公会排名：
tank.view.im.IMController.yitianjia: 已成功添加{0}至好友列表，开始私聊?
tank.view.im.IMController.hi: [{0}]已把你添加为好友,你是否也要添加对方为好友？
tank.view.im.IMController.yitianjiahaoyou: ]已把你添加为好友
tank.view.im.IMController.cannot: 不能添加自己为黑名单
tank.view.im.IMController.chongfu: 不能重复添加好友
tank.view.im.IMController.friend:已添加你为好友
tank.view.im.IMController.sameCityfriend:您被r加为同城好友!
tank.view.task.TaskCanAcceptView.nextInfo:下 一 条
tank.view.task.TaskCanAcceptView.finishTask:接受任务
tank.view.task.TaskCatalogContentView.dropTask:放弃任务
tank.view.task.TaskCatalogContentView.finishTask:完成任务
tank.view.task.TaskCatalogContentView.over:(完成)
tank.view.task.TaskCatalogContentView.tip:提示
tank.view.task.TaskCatalogContentView.dropTaskII:确定放弃此任务吗?
tank.view.task.TaskCatalogContentView.dropTaskIII:任务还没有完成
tank.view.task.TaskCatalogContentView.chooseYourAward:选择一样奖励后点击领取奖励按钮
tank.view.tutorial.EnterTutorialFrame.lodingTip:模块加载中...
tank.view.emailII.BagFrame.selectBag:请选择背包中的物品添加附件
tank.view.emailII.EmailIIdiamondview.distill:提示:玩家发送的附件物品收取后会变为绑定
tank.view.emailII.EmailIIDiamondView.emailTip:提示：付费邮件
tank.view.emailII.EmailIIDiamondView.deleteTip:需要扣除
tank.view.emailII.EmailIIDiamondView.money:点券
tank.view.emailII.EmailIIDiamondView.moneyDeficient:您的点券不足
tank.view.emailII.EmailIIStripView.sender:发件人:
tank.view.emailII.EmailIIStripView.validity:剩余时间:
tank.view.emailII.EmailIIStripView.day:天
tank.view.emailII.EmaillIIBagCell.isBinds:此物品已经绑定
tank.view.emailII.EmaillIIBagCell.RemainDate:此物品已经过期
tank.view.emailII.WritingView.sender:请填写收件人
tank.view.emailII.WritingView.annex.tip:添加附件
tank.view.emailII.WritingView.NickName:不能给自己发邮件
tank.view.emailII.WritingView.topic:请填写邮件主题
tank.view.emailII.WritingView.contentLength:您输入内容不得超过300字
tank.view.emailII.WritingView.annex:付费邮件必须添加附件
tank.view.emailII.WritingView.money_txt:请输入点券金额！
tank.view.emailII.LoadSendInfoError:加载已发邮件信息失败！
tank.view.emailII.LoadMailAllInfoError:加载所有邮件信息失败！
tank.view.emailII.WritingView.tip:提示
tank.view.emailII.WritingView.isEdit:是否取消当前邮件编辑?
tank.view.im.IMFriendList.online:在线
tank.view.im.IMFriendList.offline:暂离
tank.view.im.IMFriendList.addFriend:添加好友
tank.view.im.IMFriendList.InviteFriend:邀请社区好友
tank.view.im.IMFriendItem.delete:删除
tank.view.im.IMFriendItem.deleteFriend:确定要删除该好友？
shop.ShopIITryDressView.repeal:还原上步形象
shop.ShopIITryDressView.returnToBegin1:去掉身上装扮
shop.ShopIITryDressView.returnToBegin2：回到原始形象
road.ui.controls.TabPanel.zhi: index的值应该在0到
road.ui.controls.TabPanel.zhijian: 之间
tank.game.ToolStripView.limit:血量小于30%，不能使用自杀功能
tank.room.RoomIIPlayerItem.chat:私        聊
tank.room.RoomIIPlayerItem.look:查        看
tank.room.RoomIIPlayerItem.friend:加为好友
tank.room.RoomIIPlayerItem.copy:复制名称
tank.view.scenechatII.SceneChatIIView.selfChat:不可以和自已聊天.
tank.view.bagII.DeleteGoodsBtn.break:拆分
tank.room.RoomIIPlayerItem.addFriend:加为好友
tank.room.RoomIIPlayerItem.view:查看
tank.room.RoomIIPlayerItem.exitRoom:踢出房间
tank.view.bagII.BreakGoodsView.wrong2:请输入数量
shop.ShopIITryDressView.hideHat:帽子
tank.task.TaskPannelPosView.loginPath:弹弹堂_官方游戏登陆
shop.ShopIISaveFigurePanel.gold: 金币不足,{0}G
shop.ShopIISaveFigurePanel.stipple: 点券不足,{0}点
shop.ShopIISaveFigurePanel.medal: 勋章不足,{0}个
shop.ShopIIBtnPanel.present: 确定赠送？
tank.menu.PrivateChat:发送信息
tank.menu.ViewInfo:查看资料
tank.menu.AddFriend:添加好友
tank.menu.CopyName:复制名称
tank.menu.AddBlack:加入黑名单
tank.menu.Up:升职
tank.menu.Down:降职
tank.menu.Invite:邀请入会
tank.menu.ChatAllow:禁言
tank.menu.ChatDisable:解禁
tank.menu.ClubName:公会:
tank.menu.GongXun:功勋:{0}
tank.menu.Level:等级:{0}
tank.menu.WinCount:总胜:{0}
tank.menu.LostCount:总负:{0}
tank.menu.Winrate:胜率:{0}
tank.room.UpdateGameStyle:您的小队加入了公会、自由双排队列。
tank.manager.PlayerManger.friendOnline:上线了
tank.manager.PlayerManger.friendOffline:下线了
tank.data.player.FightingPlayerInfo.your:您获得了
tank.game.GameView.player: 玩家[{0}]已退出
tank.view.chat.ChatInput.time1:您说话太快，请休息一会!
tank.view.chat.ChatInput.time2:您输入太快
tank.room.PickupPanel.ChangeStyle:暂时没有合适的公会参与!<br>是否同时加入自由战等待？
tank.consortia.club.consortiaList:公会列表
tank.consortia.club.searchTxt:请输入公会名称
tank.consortia.club.text:这小子很懒,什么也没留下!
tank.consortia.club.CreatConsortiaFrame.inputPlease:请输入您要创建的公会名
tank.consortia.club.CreatConsortiaFrame.yourGrade:您的等级低于12级
tank.consortia.consortiadiplomatism.ConsortiaDiplomatismView.searchTxt:请输入公会名称
tank.consortia.consortiadiplomatism.ConsortiaDiplomatismView.inputPlease:请输入搜索条件
tank.consortia.consortiadiplomatism.ConsortiaDiplomatismView.text:这小子很懒,什么也没留下!
tank.consortia.myconsortia.frame.AlienationConsortiaFrame.titleText:转让公会
tank.consortia.myconsortia.frame.LoadMyconsortiaInfoError:加载公会条件信息失败!
tank.consortia.myconsortia.frame.LoadMemberInfoError:加载公会会员列表信息失败!
tank.consortia.myconsortia.frame.LoadMyconsortiaLevelError:加载公会等级信息失败！
tank.consortia.myconsortia.frame.LoadMyconsortiaListError:加载公会列表信息失败！
tank.consortia.myconsortia.frame.LoadApplyRecordError:加载公会申请记录信息失败！
tank.consortia.myconsortia.frame.LoadDutyListError:加载公会职位列表信息失败！
tank.consortia.myconsortia.frame.LoadEnemyInfoError:加载公会敌对信息失败!
tank.consortia.myconsortia.frame.AlienationConsortiaFrame.inputName:请输入玩家昵称:
tank.consortia.myconsortia.frame.AlienationConsortiaFrame.info:提示:只能转让给本公会玩家
tank.consortia.myconsortia.frame.DeleteMemberFrame.titleText:开除成员
tank.consortia.myconsortia.frame.DeleteMemberFrame.inputName:请输入玩家昵称:
tank.consortia.myconsortia.frame.DeleteMemberFrame.delete:不能开除同级别或级别比你高的成员
tank.consortia.myconsortia.frame.DeleteMemberFrame.success:操作成功
tank.consortia.myconsortia.frame.DisbandConsortiaFrame.titleText:解散公会
tank.consortia.myconsortia.frame.DisbandConsortiaFrame.sure:请在输入框中输入“Disband”以确认。
tank.consortia.myconsortia.frame.ExitConsortiaFrame.titleText:退出公会
tank.consortia.myconsortia.frame.ExitConsortiaFrame.quit:请在输入框中输入“Quit”以确认。
tank.consortia.myconsortia.frame.MyConsortiaAfficheFrame.titleText:公会公告
tank.consortia.myconsortia.frame.MyConsortiaAfficheFrame.long:当前输入文本过长
tank.consortia.myconsortia.frame.MyConsortiaAfficheFrame.input:请输入公告内容！
tank.consortia.myconsortia.frame.MyConsortiaDeclarationFrame.titleText:公会宣言
tank.consortia.myconsortia.frame.MyConsortiaDeclarationFrame.long:当前输入文本过长
tank.consortia.myconsortia.frame.MyConsortiaDeclarationFrame.input:请输入公会宣言
tank.consortia.myconsortia.frame.MyConsortiaJobItem.null:名称不能为空!
tank.consortia.myconsortia.frame.MyConsortiaJobItem.diffrent:不同权限的职位名不能相同
tank.consortia.myconsortia.frame.MyConsortiaJobItem.duty:职位名含非法字符!
tank.consortia.myconsortia.frame.MyConsortiaRightsFrame.titleText:职位管理
tank.consortia.myconsortia.frame.MyConsortiaTax.titleText:公会捐献
tank.consortia.myconsortia.frame.MyConsortiaTax.money:您目前拥有 {0} 点券，请输入捐献的数量。
tank.consortia.myconsortia.frame.MyConsortiaTax.input:输入的点券数必须大于或等于2
tank.consortia.myconsortia.frame.MyConsortiaTax.info:提示
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.titleText:公会升级
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.okLabel:升 级
#tank.consortia.myconsortia.frame.MyConsortiaUpgrade.sure:是否确定升级公会等级？
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.gold:金币不足!
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.explainTxt:您的公会已升到最高等级,目前人数上限为 {0}人
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.null:无
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.upgrade:升级后可提升公会人数上限,目前人数上限为 {0}人
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.nextLevel:公会 {0}级，人数上限为 {1}人
tank.consortia.myconsortia.frame.RecruitMemberFrame.titleText:招收成员
tank.consortia.myconsortia.frame.RecruitMemberFrame.tipTxt:请输入玩家昵称:
tank.consortia.myconsortia.MyConsortiaAuditingApplyList.titleText:审核批准
tank.consortia.myconsortia.MyConsortiaAuditingApplyList.okLabel:招收成员
tank.consortia.myconsortia.MyConsortiaInfoPane.week:每周将从公会财富中扣除
tank.consortia.myconsortia.MyConsortiaInfoPane.time:下次扣除时间：
tank.consortia.myconsortia.MyConsortiaView.titleText:公会说明
tank.consortia.myconsortia.MyConsortiaMemberInfoItem.offlineTxt:在 线
tank.consortia.myconsortia.AtLeastChoose:请您至少选择一个条目
tank.manager.ChatManagerII.cannot:不能与自己私聊！
tank.manager.ChatManagerII.you:你没有加入任何公会！
tank.manager.ChatManagerII.now:你现在没有在队伍中
tank.manager.HYellowGlowButtonManager.Error:名字{0}重复
tank.manager.PlayerManager.msg:你所在的公会已解散
tank.manager.PlayerManager.invite:邀请通过！
tank.manager.PlayerManager.success:成功加入<{0}>公会
tank.manager.PlayerManager.disband:你所在的公会已解散
tank.manager.PlayerManager.delect:你已经被<{0}>移除出公会
tank.manager.PlayerManager.leave:你已离开公会
tank.manager.PlayerManager.upgrade:恭喜您所属的公会升级成功，公会等级上升为{0}级，成员上限提升到{1}人
tank.manager.PlayerManager.up:升级成为
tank.manager.PlayerManager.upsuccess:升级成员成功！
tank.manager.PlayerManager.upfalse:升级成员失败！
tank.manager.PlayerManager.downsuccess:降级成员成功！
tank.manager.PlayerManager.downfalse:降级成员失败！
tank.manager.PlayerManager.consortia:<{0}>将 <{1}>移除公会
tank.manager.PlayerManager.leaveconsortia:<{0}>离开公会
tank.menu.RightMenuPanel.btnChat.label:解禁
tank.view.bagII.KeySetFrame.titleText:点击选择图标设置数字快捷键
tank.view.bagII.KeySetFrame.autocheck:自动补齐
tank.view.bagII.KeySetFrame.autocheck.tips:打开该功能系统将为您自动填\n补空余快捷键上的道具
tank.view.ChannelList.FastMenu.titleText:更 多(T)
tank.view.chat.ChannelListSelectView.big:大喇叭
tank.view.chat.ChannelListSelectView.small:小喇叭
tank.view.chat.ChannelListSelectView.private:私聊
tank.view.chat.ChannelListSelectView.consortia:公会
tank.view.chat.ChannelListSelectView.ream:组队
tank.view.chat.ChannelListSelectView.current:当前
tank.view.chat.ChatInput.usernameField.text:告诉[{0}]: 
chat.FriendList:好友列表
chat.Expression:表情
chat.Friend:社交
chat.FastReply:快速回复
chat.Send:发送
tank.view.chat.ChatInputView.choose:请选择玩家再进行私聊
tank.view.chat.ChatInputView.channel:本频道30秒内只能发言一次
tank.view.chat.FaceTextField.sendto:发送给[
tank.view.chat.FaceTextField.stealthily:悄悄对你说： 
tank.view.chat.FaceTextField.send:发送给
chat.Lock:点击锁定
chat.UnLock:点击解锁
chat.Clear:清屏
chat.ScrollUp:滚轮向上
chat.ScrollDown:滚轮向下
chat.Bottom:到底
chat.Function:聊天
tank.view.chat.SelectPlayerChatView.name:请输入玩家昵称。
tank.view.common.BuggleView.small:]使用小喇叭广播:
tank.view.common.BuggleView.big:]使用大喇叭广播:
tank.view.common.BuggleView.cross:]使用跨区喇叭广播:
tank.view.common.ConsortiaIcon.middle:中立
tank.view.common.ConsortiaIcon.enemy:敌对
tank.view.common.ConsortiaIcon.self:公会全体成员
tank.view.common.RoomIIPropTip.infinity:(无限)
tank.view.common.RoomIIPropTip.consume:消耗体力
tank.view.common.RoomIIPropTip.Energy:<b>消耗：</b><font color='#f2c834'>{0}体力</font>
tank.view.common.RoomIIPropTip.Psychic:<b>消耗：</b><font color='#f2c834'>{0}灵力</font>
tank.view.common.RoomIIPropTip.Gold:<b>价格：</b><font color='#E0D06E'>{0}金币</font>
tank.view.common.RoomIIPropTip.Description:<b>功能：</b>\r{0}
tank.view.im.AddBlackListFrame.titleText:添加黑名单
tank.view.im.AddBlackListFrame.btnText:加入黑名单
tank.view.im.AddBlackListFrame.chat:在聊天栏内点击玩家即可获取玩家昵称
tank.view.im.IMBlackItem.sure:确认要将此人从黑名单中移除？
tank.view.im.IMGourp.sure:确认要将自定义组【{0}】删除？
tank.view.im.IMController.success:操作成功
tank.view.im.IMController.cannotAddSelfFriend:不能把自己加为好友
tank.view.im.IMController.thisplayer:该玩家存在黑名单中
tank.view.im.IMController.thisone:该玩家目前存在于黑名单，<br>是否添加对方为好友？
tank.view.im.LoadFriendsListError:加载社区好友列表失败！
tank.view.task.TaskCanAcceptView.new:新任务
tank.view.task.TaskCatalogContentView.new:任务内容
tank.view.task.TaskCatalogView.new:任务
tank.view.quest.newquest.Title:接受新任务
tank.view.quest.bubble.TankLink:[主线]
tank.view.quest.bubble.BranchLine:[支线]
tank.view.quest.bubble.Daily:[日常]
tank.view.quest.bubble.Act:[活动]
tank.view.quest.bubble.VIP:[VIP]
tank.view.quest.newquest.Subtitle:您已接受新任务，请点击查看。
tank.game.ArrowViewIII.big:透视镜[T]
tank.game.ArrowViewIII.alpha:调节游戏界面透明度
tank.game.ArrowViewIII.send:传送[F]
tank.game.ArrowViewIII.give:将自己传送到本回合的攻击落点每2轮战斗只可使用1次
tank.game.GameView.team:队友在行动，您可以使用右边道具给队友助威
tank.game.ToolStripView.itemTemplateInfo.Name:必杀技[B]
tank.game.ToolStripView.itemTemplateInfo.Description:点击愤怒火焰即可发动必杀技与三叉戟导弹和原子弹道具同时使用无效
tank.game.ToolStripView.itemTemplateInfo.Name2:第二必杀技[R]
tank.game.ToolStripView.itemTemplateInfo.Description2:点击愤怒火焰即可发动第二必杀技与三叉戟导弹同时使用无效
tank.room.RoomIIMapSetPanel.room:房间设置
tank.roomlist.RoomListIICreateRoomView.titleText:创建房间
tank.roomlist.RoomListIICreateRoomView.contest:竞技房不可选择夺旗战
tank.roomlist.RoomListIIFindRoomPanel.room.info:您输入的房间号不存在
tank.tofflist.view.TofflistLeftCurrentCharcter.cdr:会长
tank.tofflist.view.TofflistLeftInfo.no:无公会
Crazytank.load:加载语言包失败
Crazytank.TankStartApp.login:登陆失败   
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.titleTxt:公会升级
tank.view.movement.MovementLeftView.action:活动
tank.manager.PlayerManager.player:<{0}>加入公会
tank.game.ToolStripView.start: 游戏开始{0}分钟后，才可以使用自杀功能
tank.game.ToolStripView.cannotExit:游戏开始2分钟后才可使用退出功能。
tank.data.MovementInfo.begin:从{0}开始
tank.data.MovementInfo.date:{0}年{1}月{2}日
tank.menu.TotalCount:局数:{0} 
shop.ShopIIBtnPanel.message.free:免费物品不可赠送
tank.manager.PlayerManager.contributionSelf:你向公会捐献{0}点财富
tank.manager.PlayerManager.contributionOther:<{0}>向公会捐献{1}点财富
tank.manager.PlayerManager.youUpgrade:你被<{0}>提升为<{1}>
tank.manager.PlayerManager.upgradeSelf:你将<{0}>提升为<{1}>
tank.manager.PlayerManager.upgradeOther:<{0}>将<{1}>提升为<{2}>
tank.manager.PlayerManager.youDemotion:你被<{0}>降职为<{1}>
tank.manager.PlayerManager.demotionSelf:你将<{0}>降职为<{1}>
tank.manager.PlayerManager.demotionOther:<{0}>将<{1}>降职为<{2}>
tank.consortia.myconsortia.MyConsortiaAuditingApplyList.cancelLabel:取    消
choosecharacter.ChooseCharacterView.pass: 恭喜！角色名可以使用！
choosecharacter.ChooseCharacterView.lost: 角色名已被注册！
tank.view.common.AddPricePanel.xufei1:此物品已过期，请续费
hours:小时
tank.consortia.club.CreatConsortiaFrame.yourGold:您的金币不足
tank.game.LeftPlayerCartoonView.Name:蓄力，快捷键[P]
tank.game.LeftPlayerCartoonView.Description:跳过一轮攻击，并积攒一定的怒气值。
tank.view.comon.lack:您的点券不足，是否立即充值？
tank.view.enthrall.turnSwitch:防沉迷开关关闭
tank.view.enthrall.CIDInfoSetFail:验证失败
tank.view.enthrall.CIDInfoSetSuccess:验证成功
tank.view.enthrall.CIDInfoFalse:身份证信息有误，请重新填写
tank.view.enthrallCheckFrame.checkBtn:开始验证
tank.view.enthrallCheckFrame.checkCode:验证码
tank.view.enthrallCheckFrame.checkTitle:实名验证
tank.view.enthrallCheckFrame.checkAlert:身份证格式不对，请重新填写！
shop.ShopIIBtnPanel.lackGold:金币不足
shop.ShopIIBtnPanel.fullBagAlert:购买失败，背包已满
tank.consortia.club.CreatConsortiaFrame.consortiaName:公会名不能含有非法信息
tank.view.changeColor.lackCard:此次变色需消耗{0}礼金，请问是否继续？
tank.view.changeColor.sexAlert:性别不符
tank.view.buff.PayBuff.Note:点击激活梅恩兰德的祝福
tank.view.buff.PayBuff.Name:梅恩兰德的祝福
tank.view.buff.freeCard:激活该功能1天将扣除{0}点券，是否继续？
tank.view.buff.doubleExp:激活该功能将扣除{0}点券，是否继续？
tank.view.buff.preventKick:激活该功能30天将扣除{0}点券，是否继续？
tank.view.buff.addPrice:对该功能续费将扣除{0}点券，是否继续？
tank.gameover.DisableGetCard:您所在小队中无人对敌方造成伤害，不能翻牌。
tank.view.buffInfo.outDate:您的{0}将于{1}分钟后到期
tank.serverlist.ServerListPosView.your:您等级过高，请选择合适的服务器进行游戏。
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.consumeTxt:{0} 财富{1}；  {2} 金币
crazytank.clickboard:我在玩弹弹堂，感觉很不错，你也一起来玩啊~
crazytank.copyOK:地址复制成功，粘贴至QQ或MSN发送给好友吧
tank.auctionHouse.view.BrowseLeftMenuView.specialties:特殊
tank.auctionHouse.view.BrowseLeftMenuView.qianghua1:强化石1级
tank.auctionHouse.view.BrowseLeftMenuView.qianghua2:强化石2级
tank.auctionHouse.view.BrowseLeftMenuView.qianghua3:强化石3级
tank.auctionHouse.view.BrowseLeftMenuView.qianghua4:强化石4级
tank.auctionHouse.view.BrowseLeftMenuView.zhuque1:朱雀石1级
tank.auctionHouse.view.BrowseLeftMenuView.zhuque2:朱雀石2级
tank.auctionHouse.view.BrowseLeftMenuView.zhuque3:朱雀石3级
tank.auctionHouse.view.BrowseLeftMenuView.zhuque4:朱雀石4级
tank.auctionHouse.view.BrowseLeftMenuView.xuanwu1:玄武石1级
tank.auctionHouse.view.BrowseLeftMenuView.xuanwu2:玄武石2级
tank.auctionHouse.view.BrowseLeftMenuView.xuanwu3:玄武石3级
tank.auctionHouse.view.BrowseLeftMenuView.xuanwu4:玄武石4级
tank.auctionHouse.view.BrowseLeftMenuView.qinglong1:青龙石1级
tank.auctionHouse.view.BrowseLeftMenuView.qinglong2:青龙石2级
tank.auctionHouse.view.BrowseLeftMenuView.qinglong3:青龙石3级
tank.auctionHouse.view.BrowseLeftMenuView.qinglong4:青龙石4级
tank.auctionHouse.view.BrowseLeftMenuView.baihu1:白虎石1级
tank.auctionHouse.view.BrowseLeftMenuView.baihu2:白虎石2级
tank.auctionHouse.view.BrowseLeftMenuView.baihu3:白虎石3级
tank.auctionHouse.view.BrowseLeftMenuView.baihu4:白虎石4级
BrowseLeftMenuView.zhuque:朱雀石
BrowseLeftMenuView.xuanwu:玄武石
BrowseLeftMenuView.qinglong:青龙石
BrowseLeftMenuView.baihu:白虎石
BrowseLeftMenuView.wuqisp:武器碎片
BrowseLeftMenuView.fuwuqisp:副武器碎片
BrowseLeftMenuView.freakCard:怪物卡
BrowseLeftMenuView.equipCard:武器卡
choosecharacter.ChooseCharacterView.check_txt:可输入中英文或数字，长度不超过14个字符
choosecharacter.ChooseCharacterView.setCheckTxt:恭喜!角色名可以使用.
choosecharacter.LoadCheckName.m:检测失败！
choosecharacter.LoadChooseFigure.trace:加载注册装备信息
church.churchScene.frame.ModifyDiscriptionFrame.spare:剩余
church.churchScene.frame.PresentFrame.confirm:该操作将扣除
church.churchScene.menu.MenuPanel.menuClick:婚礼进行中，请稍后
church.churchScene.scene.SceneMap.useFire:礼花效果已关闭
church.churchScene.SceneControler.timerComplete:您的房间将在10分钟后关闭，请注意续费。
church.churchScene.SceneControler.timerComplete.msg:您的房间将在10分钟后关闭，请注意续费。
church.churchScene.SceneControler.startWedding.valid:礼堂剩余时间不足举行婚礼，请及时续费
church.churchScene.SceneControler.startWedding.spouse:你的情侣不在场，不能开始典礼
church.churchScene.SceneControler.startWedding.isStarted:再次举行婚礼需扣除500点券，是否继续？
church.churchScene.SceneControler.startWedding.more:再次举行仪式
church.churchScene.SceneControler.startWedding.Money:点券少于100，不能赠送
church.churchScene.SceneMask.chatMsg.msg:婚礼倒计时 
church.churchScene.SceneMask.click:仪式进行中，无法移动
church.churchScene.SceneUI.switchVisibleFireList:烟花资源正在加载中！请稍候
church.churchScene.SceneUI.info:提示：
church.churchScene.SceneView.stopWeddingMovie:典礼现在结束
church.churchScene.MoonLightScene:月光场景
church.churchScene.WeddingMainScene:结婚主场景
church.weddingRoom.frame.AddWeddingRoomFrame.titleText:参加婚礼
church.weddingRoom.frame.AddWeddingRoomFrame.into:进 入
church.weddingRoom.frame.AddWeddingRoomFrame.time:0小时0分
church.weddingRoom.frame.AddWeddingRoomFrame.minute:分
church.weddingRoom.frame.AddWeddingRoomFrame.hour:时
church.weddingRoom.frame.AddWeddingRoomFrame.day:天
church.weddingRoom.frame.AddWeddingRoomFrame.notenoughday:不足一天
church.weddingRoom.frame.AddWeddingRoomFrame.exceedmounth:超过一个月
church.weddingRoom.frame.AddWeddingRoomFrame.note:注：若对方离线时间超过1个月，离婚只扣除999点券
church.weddingRoom.frame.AddWeddingRoomFrame.frameInfo:由于您的伴侣XXXX已经超过1个月未上线，所以您若现在决定离婚只需要<cr999点券cr>。
church.weddingRoom.frame.CreateRoomFrame.titleText:举办婚礼
church.weddingRoom.frame.CreateRoomFrame._remark_txt:在这个新旧交替，举世欢腾，万众瞩目的日子里，英俊潇洒的{0}和漂亮温柔的{1}的结婚大典将在这里举行。这将是一个因心与心的结合让寒冷变得火热的日子，一个令一对新人刻骨铭心、终生难忘的日子！在此，我们热烈欢迎前来参加庆典的朋友们，来一起分享我们的幸福与喜悦！
church.weddingRoom.RoomListBtnPanel.clickListener:您还未结婚，不可离婚
church.view.weddingRoomList.DivorcePromptFrame.yes:决定离婚
church.view.weddingRoomList.DivorcePromptFrame.no:以后再说
church.view.weddingRoomList.frame.WeddingUnmarryViewTip:离婚
church.weddingRoom.WeddingRoomControler.addRoom:创建房间失败
church.weddingRoom.WeddingRoomControler.showCreateFrame:您还未结婚，不可举行婚礼
church.weddingRoom.WeddingRoomControler.leaveRoom:是否确定离开当前婚礼房间
civil.frame.CivilRegisterFrame.married:已婚
civil.frame.CivilRegisterFrame.marry:未婚
civil.frame.CivilRegisterFrame.checkBox:公开个人装备信息
civil.frame.CivilRegisterFrame.text:很期待和你做朋友，快来和我联系吧！
civil.frame.CivilRegisterFrame.titleText:登记信息
civil.frame.CivilRegisterFrame.modify:修改信息
civil.frame.CivilRegisterFrame.infoError:交友中心玩家信息失败
ddt.data.analyze.MyAcademyPlayersAnalyze:我的师徒信息失败
civil.register.NicknameLabel:名称：
civil.register.MatrimonyLabel:婚姻状态：
civil.register.IntroductionLabel:个人介绍：
civil.register.NewMemberNotify:注：登记交友中心需要10000金币。
civil.register.infoAutoEntered:信息自动录入
tank.consortia.club.ConsortiaClubView.applyJoinClickHandler:该公会暂时不允许申请加入
tank.consortia.club.ConsortiaClubView.cellDoubleClick:请升级公会保管箱
tank.consortia.club.ConsortiaClubView.cellDoubleClick.msg:保管箱已满
tank.consortia.club.ConsortiaClubView.cellOpen:等级不足.
tank.consortia.consortiabank.ConsortiaBankView.titleText:个人保管箱
tank.consortia.consortiashop.ConsortiaShopItem.checkMoney:公会商城等级不够
tank.consortia.consortiashop.ConsortiaShopItem.Money:您的点券不足,是否立即充值?
tank.consortia.consortiashop.ConsortiaShopView.titleText:公会商城
tank.consortia.consortiashop.ConsortiaShopView.skill:公会技能{0}级
tank.consortia.myconsortia.frame.AlienationConsortiaFrame.NickName:您不能将公会转让给自己!
tank.consortia.myconsortia.frame.AlienationConsortiaFrame.Grade:接受转让的成员等级要求"12级"以上!
tank.consortia.myconsortia.frame.ConsortiaAssetManagerFrame.titleText:公会设施管理
tank.consortia.myconsortia.frame.ConsortiaAssetManagerFrame.okFunction:确认修改!
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.consortiaLevel:公会已升到最高等级
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.consortiaShopLevel:公会商城已升到最高等级
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.pleaseUpgrade:请升级公会
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.bank:公会保管箱已升到最高等级
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.skill:公会技能已升到最高等级
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.store:公会铁匠铺已升到最高等级
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.CONSORTIASHOPGRADE.explainTxt:升级后可购买更高级的商品
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.skill.explainTxt:升级后可使用更高级的技能
grade:级
consortia.upgrade:公会等级
consortia.Money:财富
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.consortiaShopUpgrade:公会商城升级
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.contentUpgrade:升级后可提升保管箱容量，目前可储存数量为{0}格
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.contentSmith:保管箱{0}级，可储存数量{1}格
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.appealTxt:公会等级{0}级
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.consortiaSmithUpgrade:公会保管箱升级
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.consortiaSkillUpgrade:公会技能升级
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.success:升级后可提升强化合成成功率，目前提升成功率
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.storeSuccess:公会铁匠铺{0}级，提升成功率{1}
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.storeUpgrade:公会铁匠铺升级
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.condition:<FONT SIZE='12' FACE='Arial' KERNING='2' COLOR='#ff0000'>(条件不足)</FONT>
tank.consortia.myconsortia.MyConsortiaMemberList.tipArr.name:会员名称
tank.consortia.myconsortia.MyConsortiaMemberList.tipArr.duty:公会职务
tank.consortia.myconsortia.MyConsortiaMemberList.tipArr.level:会员等级
tank.consortia.myconsortia.MyConsortiaMemberList.tipArr.time:会员下线时间
tank.consortia.myconsortia.MyConsortiaMemberList.tipArr.search:搜索会员
tank.consortia.myconsortia.MyConsortiaView.consortiaStore:公会铁匠铺
tank.manager.ChurchRoomManager.loadingMap:正在加载结婚场景地图
tank.manager.ItemManager.falseLoading:物品类型数据加载失败
tank.manager.ItemManager.cigaretteAsh: 烟花
tank.manager.ItemManager.aid: 辅助
tank.manager.ItemManager.gift: 礼品
tank.manager.ItemManager.Description:烟花啊烟花
tank.manager.PlayerManager.isInvent.msg:你成功加入<{0}>公会
tank.manager.PlayerManager.consortiaShop:您将公会商城升级到{0}级
tank.manager.PlayerManager.consortiaShop2:你所在的公会商城升级到{0}级
tank.manager.PlayerManager.consortiaStore:您将公会铁匠铺升级到{0}级
tank.manager.PlayerManager.consortiaStore2:你所在的公会铁匠铺升级到{0}级
tank.manager.PlayerManager.consortiaSmith:您将公会保管箱升级到{0}级
tank.manager.PlayerManager.consortiaSkill:您将公会技能升级到{0}级
tank.manager.PlayerManager.consortiaSmith2:你所在的公会保管箱升级到{0}级
tank.manager.PlayerManager.consortiaSkill2:你所在的公会技能升级到{0}级
tank.manager.PlayerManager.IsMarried:您已存在婚姻关系
tank.manager.PlayerManager.notAllow:不允许同性求婚
tank.manager.PlayerManager.notOtherLvWoo:不允许向10级以下玩家求婚
tank.manager.PlayerManager.notLvWoo:10级以下玩家不允许求婚
tank.manager.PlayerManager.married:对方已经结婚了
tank.manager.PlayerManager.youMarried:您已存在婚姻关系
tank.manager.PlayerManager.isApplicant:同意了你的求婚
tank.manager.PlayerManager.refuseMarry:拒绝了你的求婚
tank.manager.PlayerManager.youAndOtherMarried:与 {0} 玩家成为夫妻
tank.manager.PlayerManager.divorce:您已经离婚了
tank.manager.SocketManager.copyRight:版本
tank.manager.TaskManager.ChannelListSelectView:您领取了新任务，请及时查看。
tank.view.bagII.baglocked.BagLockedGetFrame.titleText:解除锁定
tank.view.bagII.baglocked.BagLockedGetFrame.titleText2:清除二级密码
tank.view.bagII.baglocked.BagLockedGetFrame.input:请输入您的二级密码
tank.view.bagII.baglocked.BagLockedHelpFrame.titleText:二级密码说明
tank.view.bagII.baglocked.BagLockedSetFrame.tip1:密码最少6个字符，最长不得超过14个字符。
tank.view.bagII.baglocked.BagLockedSetFrame.tip2:请输入密码。
tank.view.bagII.baglocked.BagLockedSetFrame.tip3:两次密码不一致，请重新输入。
tank.view.bagII.baglocked.BagLockedSetFrame.titleText:二级密码设置
tank.view.bagII.baglocked.BagLockedUpdateFrame.oldPassWord:请输入原始密码。
tank.view.bagII.baglocked.BagLockedUpdateFrame.newPassWord:请输入新密码。
tank.view.bagII.baglocked.BagLockedUpdateFrame.wrong:两次密码不一致，请重新输入。
tank.view.bagII.baglocked.BagLockedUpdateFrame.titleText:修改二级密码
tank.view.bagII.BagIIView.bagLockedBtn:二级密码设置
tank.view.bagII.BagIIView.level:等级不足.
delet:删 除
tank.view.bagII.GoodsTipPanel.free:免伤
tank.view.bagII.GoodsTipPanel.life:生命
tank.view.bagII.GoodsTipPanel.baseLife:基础血量
tank.view.bagII.GoodsTipPanel.advance:提高
tank.view.bagII.GoodsTipPanel.reply:恢复
tank.view.changeColor.ChangeColorLeftView.glass:眼镜
tank.view.changeColor.ChangeColorLeftView.suit:套装
tank.view.changeColor.ChangeColorLeftView.Wings:装饰
tank.view.chat.FaceTextField.copy:复制
tank.view.common.church.DialogueAgreePropose.okLabel:举行婚礼
tank.view.common.church.DialogueAgreePropose.cancelLabel:以后举行
songti:宋体
accept:接 受
refuse:拒 绝
tank.view.common.church.ProposeResponseFrame.titleText:
tank.view.common.church.ProposeResponseFrame.msg:你拒绝了<{0}>的求婚！
tank.view.common.AddPricePanel.label:         此物品即将过期，请续费
tank.view.common.CheckCodePanel.titleText:验证码
tank.view.continuation.ContinuationItem.initCom:请选择续费时间
tank.view.emailII.EmailIIStripSended.sender_txt:收件人:
tank.view.emailII.EmailIIStripSended.sender_txtMember:收件人:公会全体成员
reBack_btn.label:退 信
reply_btn.label:回 复
write_btn.label:写邮件
tank.view.emailII.ReadingView.deleteSelectListener:请至少选择一封邮件
send:发 送
tank.view.emailII.WritingView.backTime:邮件返还时间:1小时
tank.view.emailII.WritingView.backTime2:邮件返还时间:6小时
tank.view.tutorial.EnterTutorialFrame.loaderComplete:请稍后进入新手帮助
tank.game.DanderStrip.tip:POW代表玩家的怒气值，玩家攻击，蓄力，被攻击都会积累一定的怒气值，当怒气值满的时候，可以使用威力强大的必杀技
duoqi:夺 旗
nextPage:下一页
lastPage:最后一页
prePage:上一页
firstPage:第一页
tank.roomlist.RoomListIICreateRoomView.DESCRIPTION:竞技模式随机分配对手，奖励较丰富，是游戏的主要战斗模式。
tank.roomlist.RoomListIICreateRoomView.DESCRIPTION2:训练模式可以选择参赛对手，奖励较少，适合玩家间的训练切磋。
shop.DataError.NoGood:数据表有误，找不到商品：
shop.ShopIISaveFigurePanel.okBtn.label:支 付
shop.ShopIISaveFigurePanel.cancelBtn.label:不支付
store.StoreIIComposeBG.consortiaRate_txt:公会加成 {0}%
store.ShowSuccessRate.basallevelTxt:基础值
store.ShowSuccessRate.luckyTxt:幸运符
store.ShowSuccessRate.sociatyTxt:公会加成
MarryIcon.hubby:的老公 
MarryIcon.wife:的老婆 
tank.data.EquipType.suit: 套装
tank.data.EquipType.necklace: 项链
tank.data.EquipType.wing: 翅膀
tank.data.EquipType.decorate: 装饰
tank.data.EquipType.paopao: 泡泡
church.churchScene.scene.MoonSceneMap.lipao:礼炮正在燃放中,请稍候.
hurch.weddingRoom.frame.CreateRoomFrame.name_txt:{0}与{1}的结婚大典
yu:与
dehunli:的婚礼
church.churchScene.scene.SceneMap.lihua:礼花效果已关闭
church.churchScene.frame.ModifyRoomInfoFrame.titleText:房间设置
ranfang:燃 放
tank.consortia.myconsortia.MyConsortiaMemberList.tipArr.total:会员向公\n会捐献的财\n富值和任\n务奖励的\n财富之和
tank.consortia.myconsortia.MyConsortiaMemberList.tipArr.getOrLost:会员在公\n会战中获\n得的财富值
gongxun: 功勋
ge:个
ConsortiaShopItem.gongXunbuzu:功勋不足.
Crazytank.share:分享给MSN/QQ好友
Crazytank.collection:收藏地址
Crazytank.supply:点券充值
tank.view.common.BellowStripViewII.channel: 更多(T)
tank.common.fastMenu.hasnotOfficialSite:暂未开放
tank.common.fastMenu.hasnotForum:暂未开放
tank.manager.enthrallRemind1:您今日累计在线时间已满1小时
tank.manager.enthrallRemind2:您今日累计在线时间已满2小时
tank.manager.enthrallRemind3:系统检测您是属于未成年用户或者未进行实名认证，且今日累计游戏时间已满3小时，现在进入疲劳阶段，游戏收益将降为正常值的50%，如果您还未进行实名认证，请您尽快完善实名资料。弹弹堂提醒您合理安排游戏时间，做适当身体活动保持健康。
tank.manager.enthrallRemind4:您今日累计在线时间已经满5小时，为了您的健康，您的收益已降为零，次日恢复正常。
tank.manager.enthrallRemind5:您今日累计在线时间已满5小时，为了您的健康，请尽快下线休息。
community:社区好友
tank.view.bagII.fushion:熔炼
tank.view.bagII.couldFushion:可熔炼
tank.view.dailyconduct.clubDayly:公会日常
tank.view.dailyconduct.dayly:日常任务
tank.view.dailyconduct.completeProcess:完成进度
tank.view.dailyconduct.daylyAndCompleteProcess:日常任务\n完成进度:
tank.view.dailyconduct.clubDaylyAndCompleteProcess:公会日常任务\n完成进度:
tank.view.dailyconduct.DailyTaskItem.complete:[完成]
tank.view.dailyconduct.DailyTaskItem.uncomplete:[未完成]
tank.view.dailyconduct.DailyTaskItem.accomplish:<b><FONT SIZE='13' FACE='Arial'  KERNING='2' COLOR='#00FF00' >完成</FONT></b>
tank.view.dailyconduct.DailyTaskItem.noAccomplish:<b><FONT SIZE='13' FACE='Arial'  KERNING='2' COLOR='#FF0000' >未完成</FONT></b>
tank.view.dailyconduct.dailyFrameTitle:每日引导

#2.10version
tank.data.auctionHouse.AuctionGoodsInfo.tshort:30分钟以内
tank.data.auctionHouse.AuctionGoodsInfo.tmiddle:30分钟到2小时
tank.data.auctionHouse.AuctionGoodsInfo.tlong:2小时到12小时
tank.data.auctionHouse.AuctionGoodsInfo.tvery:大于12小时
store.view.fusion.StoreIIFusionBG.useNoExtends:熔炼后合成属性将不再继承，是否继续？<br><FONT SIZE='12' FACE='Arial' KERNING='2' COLOR='#FF0000'>注：生成物品的有效期为主熔炼物品中最短的有效期。</FONT>
tank.gameover.NotEnoughPayToTakeCard:您的点券不足，无法进行翻牌。
tank.room.RoomIIView2.noplacetoinvite:房间没有空余的位置
tank.view.changeColor.lackCard1:此次转换需消耗{0}点券，请问是否继续？
tank.room.RoomIIView2.notenoughmoney.title:提示
tank.room.RoomIIView2.notenoughmoney.content:您的点券不足，是否立即充值？
tank.room.RoomIIView2.confirmtostartgame:此操作将扣除您100点券，是否继续？
continue:继续
shop.ShopIISaveFigurePanel.gift: 礼金不足,{0}
tank.data.EquipType.offhand: 副手
tank.view.bagII.GoodsTipPanel.ciclehole:属性插孔
tank.view.bagII.GoodsTipPanel.recthole:防御插孔
tank.view.bagII.GoodsTipPanel.trianglehole:攻击插孔
tank.view.bagII.GoodsTipPanel.unknowhole:未知插孔
tank.view.bagII.GoodsTipPanel.holerequire:(强化{0}级开启)
tank.view.bagII.GoodsTipPanel.melting:熔炼
tank.view.bagII.GoodsTipPanel.canmelting:可熔炼
tank.view.bagII.GoodsTipPanel.holeenable:(已激活)
tank.view.bagII.GoodsTipPanel.holeLv:({0}级孔)
tank.dungeon.help.hited:已击杀：{0}/{1}
tank.dungeon.tryagain.title:再玩一次
tank.dungeon.tryagain.content:   是否再次挑战本关卡<br><FONT COLOR='#FF0000' SIZE='13'>选择再试一次将扣除房主500点券</FONT>
tank.invite.InvitePlayerItem.levelInadequate:该玩家的等级和房间设置的等级不符.
tank.roomlist.RoomListIIPveBGView.levelCheck:您的等级和该房间要求等级不符，请选择适合您级别段的探险房间.
tank.view.common.shortcutBuyItem:购买{0}将扣除{1}点券，是否继续？
tank.gameover.payConfirm.title:购买1次翻牌机会
tank.gameover.payConfirm.contentCommon:本次购买翻牌将扣除您486点券（成为VIP可优惠49点券），\n确定购买吗？
tank.gameover.payConfirm.contentVip:尊贵的VIP会员，本次购买翻牌只需扣除您\n437点券（优惠49点券），确定购买吗？
tank.missionsettle.dungeon.leaveConfirm.title:离开副本
tank.missionsettle.dungeon.leaveConfirm.contents:<font color='#ff0000'>副本尚未结束，中途退出将无法获得通关后的珍贵奖励！</font>
tank.missionsettle.explore.leaveConfirm.title:离开战斗
tank.room.RoomIIView2.affirm:确定
tank.room.RoomIIView2.clewContent:副本难度较大，适合<FONT COLOR='#FF0000'> 2</FONT> 人以上团队挑战，<br>确定要单人闯关吗？
tank.game.ArrowViewIII.deputyweapon.title:副手[R]
tank.game.ArrowViewIII.deputyweapon.description:副手武器
tank.room.difficulty.simple:简单
tank.room.difficulty.normal:普通
tank.room.difficulty.hard:困难
tank.room.difficulty.hero:英雄
tank.room.difficulty.all:全部
tank.room.difficulty.none:无
tank.game.prop.effect.seal:被封印状态下不可使用道具
tank.roomlist.RoomListIICreatePveRoomView.tank:勇者大冒险！
tank.roomlist.RoomListIICreatePveRoomView.go:团队作战，乐趣无限！
tank.roomlist.RoomListIICreatePveRoomView.fire:挑战极限，欢乐无边！
tank.room.RoomMapSetPanelDuplicate.choiceMap:请选择地图
tank.room.RoomMapSetPanelDuplicate.choicePermissionType:请选择房间难度
tank.room.RoomIIView2.setRoomInfo:请设置房间信息
tank.view.task.Taskstatus.onProgress:（未完成）
tank.manager.PlayerManager.gradeLow:{0}级以上玩家才能进入远征码头
tank.gameover.takecard.getgold:您获得了{0}金币
tank.gameover.takecard.money:点券
tank.gameover.takecard.gifttoken:礼金
tank.game.gameplayer.proplist.full:道具栏已满,拾取失败.
tank.view.shortcutforge.tip:快捷铁匠铺
tank.view.equipretrieve.tip:物品精炼
tank.view.equipretrieve.success:物品精炼成功
tank.view.equipretrieve.helpTip:物品精炼说明
tank.view.equipretrieve.bagHasFull:背包已满，所生成的物品以邮箱发给您
tank.view.equipretrieve.countlack:物品数量不足
tank.view.equipretrieve.needsixteen:等级达到16级即可开启物品精炼
tank.dialog.showbugleframe.ok:确认购买
tank.dialog.showbugleframe.bigbugletitle:大喇叭快速购买
tank.dialog.showbugleframe.smallbugletitle:小喇叭快速购买
tank.dialog.showbugleframe.texpQuickBuy:修炼药水快速购买
tank.view.ReworkNameView.consortiaReworkName:公会名字更改
tank.view.ReworkNameView.reworkName:名字更改
tank.view.ReworkNameView.okLabel:确认修改
tank.view.ReworkNameView.inputName:请输入昵称
tank.view.ReworkNameView.consortiaInputName:请输入新的公会名称
tank.view.ReworkNameView.reworkNameComplete:改名成功，新名字将在24小时内启用。
tank.view.bagII.GoodsTipPanel.over1: 物品已过期,将于{0}天
tank.view.bagII.GoodsTipPanel.over2: 后删除,请及时续费
tank.view.bagII.GoodsTipPanel.over3: 物品已过期,将于{0}小
tank.view.bagII.GoodsTipPanel.over4: 时后删除,请及时续费
tank.view.bagII.GoldDirections: 点券：可通过充值获得
tank.view.bagII.GiftDirections: 礼金：可通过任务获得
tank.view.bagII.MoneyDirections:金币：可通过战斗获得
tank.view.bagII.MedalDirections:勋章：可通过任务获得，上限个数999
tank.view.userGuide.AnswerView.answerFail:回答错误，请重试。
tank.view.userGuide.Common.progress:目标达成
tank.view.DefyAfficheView.cancel:取消
tank.view.DefyAfficheView.affiche:战报公告
tank.view.DefyAfficheView.afficheInfo:胜利简直易如反掌~不服!那就接着来!
tank.view.DefyAfficheView.afficheCaput:的队伍战胜
tank.view.DefyAfficheView.afficheLast:并发表获胜宣言:
tank.view.DefyAfficheView.afficheTitText:请输入获胜宣言(还可输入{0}个字)
tank.view.DefyAfficheView.afficheInfoText:此操作将扣除500点券，并发布全服公告。
tank.view.DefyAfficheView.hint:<FONT SIZE='16' FACE='Arial' KERNING='2' COLOR='#FF0000'>此操作将扣除500点券，是否继续？</FONT>
tank.view.personalinfoII.luckyday:[幸运日加成]
tank.view.personalinfoII.recovery:护甲
tank.view.personalinfoII.recoveryDetail:降低战斗时你所受到的伤害。
tank.view.personalinfoII.hp:血量
tank.view.personalinfoII.hpDetail:你的生命值，归0后你将进入灵魂状态。
tank.view.personalinfoII.attact:攻击
tank.view.personalinfoII.attactDetail:增加战斗时你所能造成的伤害，但会略微降低你的攻击速度。
tank.view.personalinfoII.defense:防御
tank.view.personalinfoII.defenseDetail:减少战斗时你所受到的伤害。
tank.view.personalinfoII.agility:敏捷
tank.view.personalinfoII.agilityDetail:会提高你的攻击速度，还会略微提高你的体力。
tank.view.personalinfoII.luck:幸运
tank.view.personalinfoII.luckDetail:提高你的暴击伤害和暴击率，并且会忽视对方部分防御。
tank.view.personalinfoII.energy:体力
tank.view.personalinfoII.energyDetail:移动和使用道具会消耗的能量。
tank.view.personalinfoII.damage:伤害
tank.view.personalinfoII.damageDetail:战斗时你所能造成的基本伤害。
tank.view.ClientDownloading.Tip:使用客户端登\n陆,游戏更流畅!经验\n更高!奖励更丰厚！
tank.view.DownloadingView.titleText:客户端下载
tank.view.bagII.BagIIView.bagFinishingBtn:整理我的背包
tank.view.store.matteTips:如果您道具栏内有与插槽相同的宝珠，\n就可拖动宝珠至插槽中进行镶嵌
tank.view.GoldInadequate:金币不足，是否购买金币箱？
tank.game.GameViewBase.Hint:<FONT SIZE='16' FACE='Arial' KERNING='2' COLOR='#0077bc'><B>您的无限道具卡已过期，点击右侧道具栏可进行快捷续费！</B></FONT>
tank.game.GameViewBase.HintTitle:温馨提示
tank.view.continuation.contiuationFailed:货币不足，续费失败
tank.view.continuation.contiuationTitle:快捷续费
tank.view.store.matteHelp.title:镶嵌说明
tank.view.store.title:铁匠铺
tank.view.store.matte.notOpen:该孔未开启
tank.view.store.matte.notType:类型不符
tank.view.store.matte.LevelWrong:该孔等级太低，无法镶嵌。
store.view.transfer.StoreIITransferBG.lijinbuzu:您的礼金不足，可以使用点券续费
tank.view.store.matte.goldQuickBuy:快速购买
tank.view.common.levelRange:级别段：
tank.view.common.roomLevel:房间难度：
tank.view.common.duplicateName:副本名称：
tank.view.common.gameLevel:游戏难度：
tank.view.common.freeFight:自由战
tank.view.common.guildFight:公会战
tank.view.common.exploreFight:探险战
tank.view.common.bossFight:Boss战
tank.view.common.duplicateFight:副本战
tank.gameover.clientGainEXP:客户端用户经验获得
tank.gameover.times:倍
shop.view.madelLack:勋章不足
shop.view.lackCoin:货币不足,购买失败
shop.view.present:赠送
tank.auctionHouse.view.pleaseInputOnThere:请输入物品名称
tank.auctionHouse.view.cloth:服装
tank.auctionHouse.view.beauty:美容
tank.auctionHouse.view.cards:卡牌类
tank.auctionHouse.view.prop:其他
tank.auctionHouse.view.sphere:宝珠
tank.auctionHouse.view.rarechip:稀有碎片
tank.auctionHouse.view.jewelry:首饰
tank.auctionHouse.view.offhand:副武器
tank.auctionHouse.view.triangle:三角宝珠
tank.auctionHouse.view.round:圆形宝珠
tank.auctionHouse.view.square:方形宝珠
tank.auctionHouse.view.auctioned:该物品已有人竞拍
tank.manager.congratulateGain:恭喜{0}开启{1}获得
tank.manager.selectDuplicate:请选择副本
tank.manager.loadShopItemsFailed:加载商店物品失败
tank.view.bagII.baglocked.setting:设置密码
tank.view.bagII.baglocked.unlock:解除锁定
tank.view.bagII.baglocked.modify:修改密码
tank.view.bagII.baglocked.delete:删除密码
tank.view.bagII.baglocked.guide:二级密码设置向导
tank.view.bagII.baglocked.next:下一步
tank.view.bagII.baglocked.diffrent:密码与确认密码不一致！
tank.view.bagII.baglocked.alertInfo:密码长度应在6-14个字符之内!
tank.view.bagII.baglocked.setted:你已经设置了密码保护问题
tank.view.bagII.baglocked.deletePassword:二级密码删除
tank.view.bagII.baglocked.sureToDelete:确定删除
tank.view.bagII.baglocked.inputOriginalPassword:请输入原密码
tank.view.bagII.baglocked.resetting:二级密码补登
tank.view.bagII.baglocked.immediatelyComplete:立即完善
tank.view.bagII.baglocked.inputAnswer:请输入密码保护问题答案
tank.view.bagII.baglocked.sureInfo:二级密码保护信息确认
tank.view.bagII.baglocked.complete:完成
tank.view.bagII.baglocked.preview:上一步
tank.view.bagII.baglocked.answerDiffer:密码保护答案不一致!
tank.view.bagII.baglocked.passwordSetting:二级密码保护信息设置
tank.view.bagII.baglocked.selectQustion:请选择密码保护问题
tank.view.bagII.baglocked.inputCompletely:请填写完整的信息
tank.view.bagII.baglocked.cantRepeat:两次密码保护问题不能重复
tank.view.bagII.baglocked.modifyTitle:二级密码修改
tank.view.bagII.baglocked.modifyBtn:修改
tank.view.bagII.baglocked.originalNull:原密码不能为空
tank.view.bagII.baglocked.newNull:新密码不能为空
tank.view.bagII.baglocked.newDiffer:新密码不一致
tank.view.bagII.baglocked.newTooLong:新密码长度太长
tank.view.bagII.baglocked.newTooShort:新密码长度太短
tank.view.bagII.cantAddPrice:该物品不能续费
tank.view.bagII.sexErr:所需性别不对
tank.view.bagII.dressed:已装备
tank.game.smallmap.simple:简单
tank.game.smallmap.normal:普通
tank.game.smallmap.difficulty:困难
tank.game.smallmap.hero:英雄
tank.view.HealStone.ErrorGrade:装备该圣石需要等级{0}级及以上
tank.game.prop.Error1:轮到你行动时才可以使用
tank.game.prop.NotAttacking:轮到你行动时才可以使用
tank.game.prop.NotCoolDown:技能还需要{0}回合才能使用
tank.game.prop.LockState:被封印状态下不可使用道具
tank.game.prop.EmptyEnergy:体力不足
tank.game.prop.EmptyPsychic:灵力不足
tank.game.prop.SoulPropOverFlow:一回合最多使用两个道具
tank.game.PsychicBar.Title:灵力值:
tank.game.PsychicBar.Content:消耗灵力值使用道具辅助队友。
tank.game.mapGenerated:已生成地图
tank.game.cantUseItem:被封印状态下不可使用道具
tank.gametrainer.study.fengongbogu:粉红啵咕
tank.gametrainer.view.freshmanQustion:新手问答
tank.gametrainer.view.agree:同意
tank.missionresult.leave:离开
tank.room.explanation:关卡说明
tank.room.statistics:战斗统计
tank.room.moreThanOne:各方人数大于一人
tank.room.somebodyInvalidate:房间内有玩家和选择的级别段不符
tank.roomlist.lessLevel:你的等级未到{0}级，不能进入该副本房间
tank.roomlist.cantBeChallenged:12级以下玩家无法被挑战
tank.roomlist.notGotoIntoRoom:{0}级以下玩家不能进入{1}房间
tank.roomlist.challenge:挑战
tank.roomlist.friendOffline:好友不在线
tank.roomlist.joinDuplicateQuickly:优先选择有任务的副本进行快速组队
tank.roomlist.joinBossBattleQuickly:快速加入Boss战
tank.roomlist.joinExploreBattleQuickly:快速加入探险模式的战斗
tank.roomlist.joinBattleQuickly:快速加入竞技模式的战斗
tank.loginstate.loginURLFail:登录链接失效
tank.loginstate.chooseCharacter:角色选择
tank.loginstate.inputUserName:请输入用户名！
tank.loginstate.CharacterNameNull:角色名不能为空！
tank.loginstate.guildNameNull:公会名不能为空！
tank.loginstate.characterNameExist:角色名<{0}>已存在，请修改角色名，在角色名未修改之前，该角色无法进入游戏
tank.loginstate.characterModify:角色名修改
tank.loginstate.inputCharacterName:请输入角色名！
tank.loginstate.guildNameExist:公会名<{0}>已存在，请修改公会名，在公会名未修改之前，将无法进入游戏
tank.loginstate.guildNameModify:公会名修改
tank.loginstate.inputInvalidate:名称中包含非法字符
tank.loginstate.allNull:名称不能全为空格
tank.loginstate.inputNickName:请输入昵称！
shop.view.restore:还原初始形象
shop.view.cantPresent:礼金或勋章类物品不能赠送
shop.view.giftLack:礼金不足
store.states.hatOpenProperty:强化成功，您的帽子开启了一个新的属性插槽
store.states.hatOpenDefense:强化成功，您的帽子开启了一个新的防御插槽
store.states.clothOpenProperty:强化成功，您的衣服开启了一个新的属性插槽
store.states.clothOpenDefense:强化成功，您的衣服开启了一个新的防御插槽
store.states.weaponOpenAttack:强化成功，您的武器开启了一个新的攻击插槽
store.states.weaponOpenProperty:强化成功，您的武器开启了一个新的属性插槽
store.states.embedTitle:镶嵌提示
store.embem.HoleTip.Level:级:
store.view.Compose.buyCompose:合成石快速购买
store.view.fusion.buyFormula:公式快速购买
store.view.shortcutBuy.buyBtn:购买
heiti:黑体
store.view.shortcutBuy.shouldPay:您总共需要支付 ：
store.view.strength.unpare:石头与装备不符
store.view.strength.typeUnpare:石头类型不一致
store.view.strength.noneSymble:您未使用神恩符，强化失败会降低\n强化等级，是否继续？
store.view.strength.FiveLevelTip:装备的强化等级达到5,此后强化失败会降低强化\n等级！使用神恩符可以避免降级。
store.view.transfer.info:物品已满或类型不相同!
store.openHole.NOstone:您背包中没有高级开孔钻头或初级开孔钻头
store.openHole.btnTipData:开启插孔需要开孔钻头
store.openHole.OpenTipData:{0}级：{1}/{2}\n消耗开孔钻头{3}级升级
tank.view.buffControl.buffButton.freeCard:无限道具卡
civil.frame.CivilRegisterFrame.checkIntro:您输入的内容中含有无意义字符，交友信息未录入。
tank.view.bagII.GoodsTipPanel.holeactive:(强化{0}级激活)
tank.view.bagII.baglocked.operationTimesOut:操作次数超过今日上限
tank.view.dailyconduct.gain:领 取
tank.gametrainer.data.TrainerModel.answer1:1、如何移动角色？
tank.gametrainer.data.TrainerModel.answer4:2、如何发射炮弹？
tank.gametrainer.data.TrainerModel.answer6:按住空格键发射。
tank.view.common.BuyGiftBagButton.initliziItemTemplate.excellent:优惠礼包
tank.view.common.BuyGiftBagButton.initliziItemTemplate.info:包含强化石4级x3,神恩符x1,幸运符25%x1(以上物品皆绑定)。现在购买则享受有85折优惠特权,仅需4599点券,您还在等什么？赶快来强化吧！
tank.invite.InvitePlayerItem.cannot:{0}级以下玩家无法被邀请
tank.view.bagII.baglocked.question1:你父亲的姓名是?
tank.view.bagII.baglocked.question2:你的学号(或工号)是?
tank.view.bagII.baglocked.question3:你的小学校名是?
tank.view.bagII.baglocked.question4:你的中学校名是?
tank.view.bagII.baglocked.question5:你的生日是?
tank.view.bagII.baglocked.question6:你配偶的姓名是?
tank.view.bagII.baglocked.customer:自定义
tank.tofflist.view.lastUpdateTime:最后更新:
tank.view.common.newAnwserTitle:新手问答
tank.view.common.newAnwser1:提高装备属性。
tank.view.common.newAnwser2:提高自身的血量。
tank.gametrainer.view.wrongAnwser:回答错误，请重试
tank.view.chatsystem.sendTo:发送给
tank.view.chatsystem.privateSayToYou:悄悄对你说: 
store.view.strength.buyGift:购买优惠礼包将花费4599点券，是否继续？
tank.data.QualityType.cucao: 粗糙
tank.data.QualityType.putong: 普通
tank.data.QualityType.youxiu: 优秀
tank.data.QualityType.jingliang: 精良
tank.data.QualityType.zhuoyue: 卓越
tank.data.QualityType.chuanshuo: 传说
tank.data.QualityType.shenqi: 神器
tank.consortia.myconsortia.frame.MyConsortiaAfficheFrame:您输入的内容中含有无意义字符，公告信息未录入。
tank.view.common.DeadTipDialog.title:灵魂参与战斗
tank.view.common.DeadTipDialog.btn:我知道了

#2.20version
tank.view.bagII.SellGoodsBtn.sure:确定出售此物品？\n价格：{0}
tank.view.bagII.SellGoodsBtn.CantSellEquip:无法出售此高价值物品。
tank.view.bagII.SellGoodsBtn.NoSell:此物品不能被删除
tank.game.GameView.getgoodstip.broadcast:恭喜{0}在{1}中获得
tank.game.GameView.getgoodstip:[{0}]在{1}中获得
tank.dialog.showbugleframe.crossbugletitle:跨区大喇叭快速购买
tank.game.GameView.unexpectedBattle:战斗
tank.game.GameView.dungeonBattle:副本
tank.room.duplicate.notOpen:该副本尚未开放！
tank.view.chat.ChannelListSelectView.cross:跨区公告
tank.view.chat.ChannelListSelectView.crossBugle:跨区喇叭
tank.view.chatFormat.cross:对方与您不在同一个区，无法密聊
tank.game.BloodStrip.HP:血量
tank.game.actions.kill:击败
tank.game.actions.turn:回合
tank.game.PowerStrip.energy:体力值： 
tank.game.deputyWeapon.canNotUse:您的天使之赐本场战斗使用次数已用尽
tank.view.common.InviteAlertPanel.pass:关卡:
tank.menu.FightPower:战斗力:{0} 
tank.manager.GradeExaltClewManager:恭喜您升级啦！
tank.hall.ChooseHallView.hotWellAlert:温泉提示
tank.view.ConsortiaReworkNameView.consortiaNameModify:公会名更改
tank.view.ConsortiaReworkNameView.consortiaNameAlert:可输入中英文或数字，长度不得超过12个字符
tank.view.ConsortiaReworkNameView.consortiaNameAlert1:您没有加入任何公会，不能使用公会改名卡
tank.view.ConsortiaReworkNameView.consortiaNameAlert2:您不是会长，不能使用公会改名卡
tank.view.ConsortiaReworkNameView.consortiaNameAlert3:请输入新的名称
tank.view.ConsortiaReworkNameView.consortiaNameModifySuccess:改名成功，新公会名将在24小时内启用。
tank.view.ConsortiaReworkNameView.consortiaNameAlert4:恭喜!公会名称可以使用.
tank.view.ConsortiaReworkNameView.consortiaNameAlert5:公会名中包含非法词汇
tank.view.ConsortiaReworkNameView.consortiaNameAlert6:公会名称不能为空格
tank.view.ConsortiaReworkNameView.consortiaNameAlert7:请输入名称
tank.manager.crossdisable:跨区喇叭暂未开放
tank.game.deputyWeapon.canNotUse2:您的啵咕盾牌本场战斗使用次数已用尽
tank.game.deputyWeapon.canNotUse3:您的巴罗夫的盾牌本场战斗使用次数已用尽
tank.view.emailII.ReadingView.useHelp:使用说明
tank.game.BloodStrip.tip:当血量减少到0的时候，将成为鬼魂状态，升级可以增加你的血量上限
tank.gameover.gainDoubleEXP:双倍经验卡经验获得2倍
tank.game.PowerStrip.tip:玩家移动，使用道具将会消耗一定的体力值，当体力值为0的时候，玩家不能移动，不能使用道具。

#2.30version
tank.fightLib.AwardBox.FullBag:但背包空间不足,物品暂存到邮箱。
tank.fightLib.FirstGetPass:恭喜通关！
tank.fightLib.questionInfo:当前共{0}题，答对{1}题即可通过 \n已答对{2}/{1}题,剩余{3}题。
tank.room.RoomIIView.cross.kuaqu:切换至跨区战斗状态
tank.room.RoomIIView.cross.benqu:切换至本区战斗状态
tank.fightLib.Lesson.Message1:请先通过测量屏距课程。
tank.fightLib.Lesson.Message2:请先通过65度课程。
tank.fightLib.Lesson.17Grade:需要达到17级才可进入{0}课程的中级难度。
tank.fightLib.Lesson.20Grade:需要达到20级才可进入{0}课程的高级难度。
tank.fightLib.AngleMessage:不在规定角度内，击中无效。
tank.command.fightLibCommands.script.MeasureScree.EasyMeasureScreenScript.command1:欢迎进入测量屏距初级课程，在这里您将学习到最基础的屏距测量知识。
tank.command.fightLibCommands.script.MeasureScree.EasyMeasureScreenScript.command2:欢迎进入测量屏距初级课程，尝试拖动小地图的白框。
tank.command.fightLibCommands.script.MeasureScree.EasyMeasureScreenScript.command3:很好，就是这样。白框左右两端的距离是地图中一个屏幕标准宽度，我们称为1屏。
tank.command.fightLibCommands.script.MeasureScree.EasyMeasureScreenScript.command5:白框的长度为1屏，白线之间的距离为1距，1屏=10距。
tank.command.fightLibCommands.script.MeasureScree.EasyMeasureScreenScript.command7:现在我们来演示一下，我们与靶子之间的距离。
tank.command.fightLibCommands.script.MeasureScree.EasyMeasureScreenScript.command9:动画显示的屏距5距，如果您看明白了，下面我们开始初级目测屏距的训练。
tank.command.fightLibCommands.script.MeasureScree.EasyMeasureScreenScript.command10:下面我们开始进行初级测屏距的训练。
tank.command.fightLibCommands.script.MeasureScree.NomalMeasureScreenScript.command1:欢迎进入测量屏距中级课程，上节课我们学习了1屏以内的屏距测量方法，那么超过1屏的距离怎么测呢？
tank.command.fightLibCommands.script.MeasureScree.NomalMeasureScreenScript.command2:本节课程练习超出10距的目测屏距。如果超出10距，采用两次测量相加的方法。下面来演示下~
tank.command.fightLibCommands.script.MeasureScree.NomalMeasureScreenScript.command4:现在我们来看一下，在白框的右端边线的位置，也就是1屏的距离。记住这个位置。
tank.command.fightLibCommands.script.MeasureScree.NomalMeasureScreenScript.command5:再测量两个红点之间的距离为3，两次测量的数据相加为13就是现在的屏距了。如果您明白的话就开始中级测屏距的训练~
tank.command.fightLibCommands.script.MeasureScree.NomalMeasureScreenScript.command6:下面我们开始进行中级测屏距的训练。
tank.command.fightLibCommands.script.MeasureScree.DifficultMeasureScreenScript.command1:之前我们学习了比较规则的屏距测量，那么遇见非整数距该怎么算？
tank.command.fightLibCommands.script.MeasureScree.DifficultMeasureScreenScript.command2:本节课程练习非整数距目测屏距。如果屏幕不是整数距，可以按就近的整数距计算。来看个实例吧~
tank.command.fightLibCommands.script.MeasureScree.DifficultMeasureScreenScript.command4:现在我们来看一下，您与靶子之间的距离在10~11之间，并且离10距比较近，那我们就把它当做10距来计算
tank.command.fightLibCommands.script.MeasureScree.DifficultMeasureScreenScript.command5:下面我们开始进行高级测屏距的训练。
tank.command.fightLibCommands.script.MeasureScree.EasyTwentyDegree.command1:首先我来说一下20度打法适合的武器：
tank.command.fightLibCommands.script.MeasureScree.EasyTwentyDegree.command2:现在我们来说一下20度打法的优缺点。优点就是受风力影响很小。缺点就是受地形、高差影响比较大，有些位置会打不到。
tank.command.fightLibCommands.script.MeasureScree.EasyTwentyDegree.command4:结合我们之前学习的课程，我来说明一下，角度为20度，相隔为1距时，发射力度为10，相隔为2距时，发射力度为19，以此类推，相隔为10距时，发射力度为54。
tank.command.fightLibCommands.script.MeasureScree.EasyTwentyDegree.command6:我们首先将角度调整到20度，然后按照这个力度表试验一下。
tank.command.fightLibCommands.script.MeasureScree.EasyTwentyDegree.command7:恭喜你打中了，现在您做好进行初级训练的准备了吗？
tank.command.fightLibCommands.script.MeasureScree.NormalTwentyDegree.command1:欢迎来到20度打法中级课程，在这里我们要训练的内容是超过1屏时，20度的打法，首先我们来看一下超过1屏的力度表。
tank.command.fightLibCommands.script.MeasureScree.NormalTwentyDegree.command2:本节课程训练超过一屏的20度打法，如果您准备好了，那我们开始进行中级训练吧！
tank.command.fightLibCommands.script.MeasureScree.DifficultTwentyDegree.command1:欢迎来到20度打法高级场，在之前的训练中，我们给出的都是整数的屏距，如果遇到非整数的屏距，怎么办？
tank.command.fightLibCommands.script.MeasureScree.DifficultTwentyDegree.command2:我们可以对屏距先进行估算，并计算出所需近似力度，然后对力度进行一些微调。如果您明白了，那我们开始进行高级训练吧！
tank.command.fightLibCommands.script.MeasureScree.DifficultTwentyDegree.command3:本节课程训练超过一屏的20度打法，如果您准备好了，那我们开始进行高级训练吧！
tank.command.fightLibCommands.script.HighThrow.EasyHighThrow.command1:首先我来说一下高抛打法适合的武器：
tank.command.fightLibCommands.script.HighThrow.EasyHighThrow.command2:现在我们来说一下高抛打法的优缺点。优点就是受高地形影响较小，缺点就是受风力和高差影响比较大。
tank.command.fightLibCommands.script.HighThrow.EasyHighThrow.command3:高抛打法是一种力度相对不变，调整角度的打法。在无风状态下，发射力度95，发射角度为90-（与目标的屏距）。
tank.command.fightLibCommands.script.HighThrow.EasyHighThrow.command5:我们首先将角度调整到90度附近，然后按照刚才所说的方法，试验一下。
tank.command.fightLibCommands.script.HighThrow.EasyHighThrow.command6:恭喜您打中了，现在您做好进行初级训练的准备了吗？
tank.command.fightLibCommands.script.HighThrow.NormalHighThrow.command1:这节课我们要训练在有风的情况下，如何计算发射角度。
tank.command.fightLibCommands.script.HighThrow.NormalHighThrow.command2:本节课程介绍有风情况下高抛打法。具体的计算方法为：顺风角度计算：90度-屏距+风力×2；逆风角度计算：90度-屏距-风力×2。
tank.command.fightLibCommands.script.HighThrow.DifficultHighThrow.command1:在之前的课程中，我们都是在整数屏距与风力情况下训练的，如果遇到非整数的屏距与风力，怎么办呢？
tank.command.fightLibCommands.script.HighThrow.DifficultHighThrow.command2:我们可以对角度按照屏距、风力先进行四舍五入，算出所需近似角度，然后通过对力度进行一些微调，来校正角度带来的误差。如果您明白了，那我们开始进行高级训练吧！
tank.command.fightLibCommands.script.SixtyDegree.DifficultSixtyDegreeScript.command1:在之前的训练中，我们给出的都是整数的屏距，如果遇到非整数的屏距，怎么办呢？
tank.command.fightLibCommands.script.SixtyDegree.DifficultSixtyDegreeScript.command2:本节课程训练非整数距65度打法。打法和前面的课程类似，需要计算近似力度，然后对力度进行微调。如果您明白了，那就开始训练吧！
tank.command.fightLibCommands.script.SixtyDegree.EasySixtyDegreeScript.command1:首先我来说一下65度打法适合的武器：
tank.command.fightLibCommands.script.SixtyDegree.EasySixtyDegreeScript.command2:现在我们来说一下65度打法的优缺点。优点就是受地形、高差影响比较小，缺点就是受风力影响比较大，而且对力度要求比较精确。
tank.command.fightLibCommands.script.SixtyDegree.EasySixtyDegreeScript.command3:下面我们来详细讲解一下打法。先看一下65度打法的力度表。
tank.command.fightLibCommands.script.SixtyDegree.EasySixtyDegreeScript.command4:结合我们之前学习的课程，我来说明一下，在无风状态下，角度为65度，相隔为1距时，发射力度为13，相隔为2距时，发射力度为21，以此类推，相隔为20距时，发射力度为85。
tank.command.fightLibCommands.script.SixtyDegree.EasySixtyDegreeScript.command6:我们首先将角度调整到65度，然后按照这个力度表试验一下。
tank.command.fightLibCommands.script.SixtyDegree.EasySixtyDegreeScript.command7:恭喜您打中了，现在您做好进行初级训练的准备了吗？
tank.command.fightLibCommands.script.SixtyDegree.NormalSixtyDegreeScript.command1:欢迎来到65度打法中级场，在这里我们要训练的课程有风的情况下，要如何计算。在计算好力度的基础上，我们还要根据风速计算发炮角度。
tank.command.fightLibCommands.script.SixtyDegree.NormalSixtyDegreeScript.command2:本节课程介绍有风情况下65度打法。具体的计算方法为：顺风角度计算：65度+风力×2；逆风角度计算：65度-风力×2。如果您明白了，那我们开始进行中级训练吧！
tank.command.fightLibCommands.script.HighGap.EasyHighGap.command1:之前我们学习都是如何打同一水平线的敌人，但是实战中，经常出现高度差，这时候就需要考虑到高差的影响。
tank.command.fightLibCommands.script.HighGap.EasyHighGap.command2:对于高差没有固定的公式来计算，但是可以通过对力度的调整来，一般来说，对方位置比你低，就需要在计算出力度的基础上减少一些力度。
tank.command.fightLibCommands.script.HighGap.EasyHighGap.command3:高差打法没有固定的公式计算，对方位置比你低，需要相应减少开炮力度；对方位置比你高，需要相应增加力度。如果您明白了，那我们开始进行初级训练吧！
tank.command.fightLibCommands.script.HighGap.NormalHighGap.command1:本节课我们进行高差打法的中级训练。
tank.command.fightLibCommands.script.HighGap.DifficultHighGap.command1:本节课我们进行高差打法的高级训练。
tank.command.fightLibCommands.script.ChallengeScript:能到达这里的都是万中选一的勇士，在这里你将接受最严酷的挑战。准备好了吗？那就惦记开始挑战吧！
tank.command.fightLibCommands.script.MeasureScree.startTrain:开始训练
tank.command.fightLibCommands.script.MeasureScree.watchAgain:再看一遍
tank.command.fightLibCommands.script.MeasureScree.understood:明白了
tank.fightLib.ChooseFightLibTypeView.selectFightLibInfo:请选择课程
tank.fightLib.ChooseFightLibTypeView.selectDifficulty:请选择课程难度
tank.hall.ChooseHallView.fifteenGrade:15级以上玩家才能进入训练场
shop.ShopIIModel.countOver:此商品已售完
tank.view.ChannelList.FastMenu.exit:关 闭
tank.game.ToolStripView.isExit:确定要退出当前战斗？
tank.game.ToolStripView.isExitLib:确定要退出当前训练？
tank.view.im.IMController.shifou: 确定添加该用户为好友？
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.sure:确定升级公会等级？
church.churchScene.SceneUI.menuClick:确定现在举行婚礼？
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.CONSORTIASHOPGRADE:确定升级公会商城等级?
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.CONSORTIASTOREGRADE:确定升级公会保管箱等级?
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.CONSORTIASMITHGRADE:确定升级公会铁匠铺等级?
tank.consortia.myconsortia.frame.MyConsortiaUpgrade.CONSORTIASKILLGRADE:确定升级公会技能等级?
are.you.sure.to.marry:确定现在举行婚礼？
tank.room.RoomIIView2.leaveRoom:确定离开当前房间？
tank.missionsettle.explore.leaveConfirm.contents:确定离开当前战斗？
tank.gametrainer.data.TrainerModel.answer3:用手移动显示器。
tank.gametrainer.data.TrainerModel.answer5:用意念支配。
tank.view.common.newQuestion1:1、强化的作用是什么？
tank.view.common.newQuestion2:2、必杀技与三叉戟导弹能同时作用吗？
tank.view.common.newAnwser3:能。
tank.view.common.newAnwser4:不能。
tank.view.im.IMController.issure:确定要将此人添加到黑名单？
tank.consortia.myconsortia.frame.MyConsortiaTax.sure:确定捐献财富？
tank.view.emailII.EmailIIStripView.delectEmail: 您删除的邮件内含有附件，删除后<br>    附件无法找回，是否删除？
tank.gametrainer.data.TrainerModel.answer2:按小键盘的←、→键。
tank.hotSpring.room.time.minute:分钟
tank.hotSpring.room.config.title:房间设置
tank.hotSpring.room.create.title:创建房间
tank.hotSpring.room.defaultName:泡泡温泉，休闲休闲
tank.view.bagII.BagIIView.BindsInfo:该操作会使物品成为绑定状态，<BR><FONT SIZE='16' FACE='Arial' KERNING='2' COLOR='#FF0000'>    确认继续？</FONT>
tank.fightLib.FightLibAwardView.exp:经验
tank.gameover.ExperienceView:夫妻组队经验获得1.5倍
tank.command.fightLibCommands.script.FightLibGuideScripit.welcome:欢迎来到训练场，在这里我们为您 \n安排了一系列课程来提升您的作战能力。
store.view.ShortcutBuyCell.lingju:凝聚
store.view.ShortcutBuyCell.jiezi:戒指
store.view.ShortcutBuyCell.shouzhuo:手镯
store.view.ShortcutBuyCell.baozhu:宝珠
store.view.ShortcutBuyCell.zhuque:朱雀
store.view.ShortcutBuyCell.xuanwu:玄武
store.view.ShortcutBuyCell.qinglong:青龙
store.view.ShortcutBuyCell.baihu:白虎
store.view.ShortcutBuyCell.Stone1:朱雀
store.view.ShortcutBuyCell.Stone2:玄武
store.view.ShortcutBuyCell.Stone3:青龙
store.view.ShortcutBuyCell.Stone4:白虎
shop.ShopIIModel.GoodsNumberLimit: 没有更多的商品可以购买
church.churchScene.frame.ModifyDiscriptionFrame.word:字符
tank.hotSpring.money:点券
tank.hotSpring.gold:金币
tank.hotSpring.comfirm:进入房间需花费10000金币，你确定要进入？
tank.room.duplicate.previewTitle:黑暗堡垒
tank.game.map.smallMapView.slow:系统检测到您的游戏运行不够流畅\n是否关闭游戏特效来加快游戏速度
store.view.strength.clew2:<FONT SIZE='16' FACE='Arial' KERNING='2' COLOR='#FF0000'><B>武器成功强化到十级后，属性\n将不可转换，是否强化？</B></FONT>
tank.manager.PlayerManager.pass:<{0}>公会批准了您的申请
tank.view.common.NPCPairingDialog.title:遭遇强敌
tank.view.common.NPCPairingDialog.accept:接受
tank.view.common.NPCPairingDialog.refuse:拒绝
tank.view.common.NPCPairingDialog.content:遭遇未知强敌，是否要接受挑战。
tank.room.RoomMapSetPanelDuplicate.clew:您的等级未到{0}级，不可创建该副本
tank.view.im.IMBlackList.add:加入黑名单
tank.game.smallmap.simple1:初级
tank.game.smallmap.normal1:中级
tank.game.smallmap.difficulty1:高级
tank.fightLib.Award.GetMessage:你获得了
tank.fightLib.FightLibQuestionFrame.reAnswer:重新测试
tank.fightLib.FightLibQuestionFrame.viewTutorial:去看教程
tank.hall.ChooseHallView.campaignLabAlert:训练场
tank.hall.ChooseHallView.auction:拍卖场
tank.hall.ChooseHallView.church:结婚礼堂
tank.hall.ChooseHallView.civil:交友中心
tank.hall.ChooseHallView.consortia:公会俱乐部
tank.hall.ChooseHallView.hotWell:温泉
tank.hall.ChooseHallView.roomList:游戏大厅
tank.hall.ChooseHallView.shop:购物中心
tank.hall.ChooseHallView.tofflist:名人堂
tank.hall.ChooseHallView.dungeon:远征码头
tank.hall.ChooseHallView.master:师徒圣殿
tank.hall.ChooseHallView.openBuildTip:恭喜你开启{0}功能!
store.view.strength.clew:<FONT SIZE='16' FACE='Arial' KERNING='2' COLOR='#FF0000'><B>使用五级强化石可以使强化几率更高哦,\n  通过熔炼4级强化石就可以获得啦！</B></FONT>
tank.auctionHouse.view.BrowseLeftMenuView.qianghua5:强化石5级

2.35version
tank.consortia.myconsortia.MyConsortiaMemberInfoItem.long:很 久
tank.consortia.myconsortia.MyConsortiaMemberInfoItem.month:1个月以上
tank.hotSpring.room.load.error:房间信息加载失败，请重新进入
tank.menu.fireConfirm:您确定要开除“{0}”?
tank.timeBox.awardsInfo:奖励
tank.timebox.second:秒
tanl.timebox.tipMes:倒计时结束后 ,您将获得丰厚的物品奖励
tanl.timebox.LimitAwardTip:本次活动将在倒计时为零时结束，请弹友们赶紧参加吧
tank.game.GameView.RouletteBattle:宝箱抽奖
tank.dialog.showbugleframe.rouletteKey:钥匙快速购买
tank.view.rouletteView.title:梦幻轮盘
tank.view.store.consortiaRateI:你还没有加入公会
tank.view.store.consortiaRateII:公会贡献度:{0}/{1}
tank.view.store.consortiaRateIII:需要公会铁匠铺等级大于0
tank.view.store.consortiaRateIV:通过强化额外获得的成功率提升
tank.view.effort.EffortCategoryTitleItem.FULL:总 览
tank.view.effort.EffortCategoryTitleItem.INTEGRATION:其 他
tank.view.effort.EffortCategoryTitleItem.PART:角 色
tank.view.effort.EffortCategoryTitleItem.TASK:任 务
tank.view.effort.EffortCategoryTitleItem.DUNGEON:副 本
tank.view.effort.EffortCategoryTitleItem.FIGHT:战 斗
tank.view.effort.EffortCategoryTitleItem.HONOR:奖 励
tank.view.effort.EffortRigthItemView.honorName:奖励:
tank.view.effort.EffortRigthItemView.honorNameII:称号
tank.view.effort.EffortRigthItemView.honorNameIII:和
tank.view.effort.EffortRigthItemView.honorNameIV:奖励物品:
tank.view.effort.EffortRigthItemView.honorNameV:奖励称号:
tank.view.effort.EffortRigthItemView.achievementPoint:成就点数:
tank.view.effort.EffortPullDodnMenu.FULL:全部
tank.view.effort.EffortPullDodnMenu.ACQUIRE:已完成
tank.view.effort.EffortPullDodnMenu.INCOMPLETE:未完成
tank.view.rouletteview.close:确定退出梦幻轮盘
tank.view.im.IMController.addBlackList:您的黑名单人数超过100人，请删除部分后再进行添加
tank.view.im.IMController.addFriend:您的好友人数超过{0}人，请删除部分后再进行添加
tank.consortia.myconsortia.MyConsortiaMemberList.tipArr.battle:会员的战斗力
tank.consortia.myconsortia.MyConsortiaMemberList.tipArr.contribute:成员对公会贡\n献总值
tank.menu.fire:开除
tank.consortia.myconsortia.systemWord:请点击此处，修改公告
tank.consortia.myconsortia.frame.MyConsortiaInfoPanel.week:/周
tank.view.rouletteview.quit:梦幻轮盘正在转动,无法关闭

2.40version
baglocked.SetPassFrame1.titText1:1/3: 二级密码设置
baglocked.SetPassFrame1.inputTextInfo1:请输入密码:
baglocked.SetPassFrame1.inputTextInfo2:(密码长度为6~14位)
baglocked.SetPassFrame1.inputTextInfo3:请再次输入密码:
baglocked.SetPassFrame1.inputTextInfo4:温馨提示:请牢记你的二级密码!
baglocked.SetPassFrame2.titText2:2/3: 密码保护信息设置
baglocked.SetPassFrame2.question1:问题一:
baglocked.SetPassFrame2.answer1:答案一:
baglocked.SetPassFrame2.inputTextInfo1:(答案长度最多为40位)
baglocked.SetPassFrame2.textInfo2_7:以上信息一经填写不可更改或删除。为了避免遗忘，请填写真实信息，这将有助于您通过回答问题快速找回二级密码。
baglocked.SetPassFrame2.question2:问题二:
baglocked.SetPassFrame2.answer2:答案二:
baglocked.SetPassFrame3.titText3:3/3: 密码保护信息确认
baglocked.SetPassFrame3.textInfo3_7:以上是您刚刚设置的密保信息，请依次做出回答，以便进行确认。
baglocked.BagLockedGetFrame.Text4:请输入二级密码进行解锁
baglocked.UpdatePassFrame.Text5:请输入旧密码:
baglocked.UpdatePassFrame.Text6:请输入新密码:
baglocked.UpdatePassFrame.Text7:请再次输入新密码:
baglocked.DelPassFrame.operationAlertInfo:您今天还可以进行{0}次操作
tank.view.chat.ChatFacePannel.face1:微笑
tank.view.chat.ChatFacePannel.face2:可怜
tank.view.chat.ChatFacePannel.face3:色
tank.view.chat.ChatFacePannel.face4:惊呆
tank.view.chat.ChatFacePannel.face5:得意
tank.view.chat.ChatFacePannel.face6:流泪
tank.view.chat.ChatFacePannel.face7:害羞
tank.view.chat.ChatFacePannel.face8:闭嘴
tank.view.chat.ChatFacePannel.face9:大哭
tank.view.chat.ChatFacePannel.face10:呲牙
tank.view.chat.ChatFacePannel.face11:难过
tank.view.chat.ChatFacePannel.face12:调皮
tank.view.chat.ChatFacePannel.face13:酷
tank.view.chat.ChatFacePannel.face14:恐吓
tank.view.chat.ChatFacePannel.face15:大笑
tank.view.chat.ChatFacePannel.face16:犯困
tank.view.chat.ChatFacePannel.face17:寒...
tank.view.chat.ChatFacePannel.face18:疑问
tank.view.chat.ChatFacePannel.face19:嘘~
tank.view.chat.ChatFacePannel.face20:暴打
tank.view.chat.ChatFacePannel.face21:汗
tank.view.chat.ChatFacePannel.face22:奋斗
tank.view.chat.ChatFacePannel.face23:不屑
tank.view.chat.ChatFacePannel.face24:发怒
tank.view.chat.ChatFacePannel.face25:抓狂
tank.view.chat.ChatFacePannel.face26:晕啊
tank.view.chat.ChatFacePannel.face27:再见
tank.view.chat.ChatFacePannel.face28:摇头
tank.view.chat.ChatFacePannel.face29:罗嗦
tank.view.chat.ChatFacePannel.face30:呆滞
tank.view.chat.ChatFacePannel.face31:放电
tank.view.chat.ChatFacePannel.face32:呕吐
tank.view.chat.ChatFacePannel.face33:惊恐
tank.view.chat.ChatFacePannel.face34:强
tank.view.chat.ChatFacePannel.face35:弱
tank.view.chat.ChatFacePannel.face36:鼓掌
tank.view.chat.ChatFacePannel.face37:握手
tank.view.chat.ChatFacePannel.face38:猪头
tank.view.chat.ChatFacePannel.face39:咬人
tank.view.chat.ChatFacePannel.face40:强壮
tank.view.chat.ChatFacePannel.face41:亲亲
tank.view.chat.ChatFacePannel.face42:示爱
tank.view.chat.ChatFacePannel.face43:心碎
tank.view.chat.ChatFacePannel.face44:炸弹
tank.view.chat.ChatFacePannel.face45:凸
tank.view.chat.ChatFacePannel.face46:错误
tank.view.chat.ChatFacePannel.face47:正确
tank.view.chat.ChatFacePannel.face48:打小人
daily.view.DetList.getBtnText1:领　取
daily.view.DetList.getBtnText2:已领取
feedback.view.FeedbackSubmitFrame.title:弹弹堂意见反馈区
feedback.view.FeedbackSubmitSp.comBoxText:请选择问题类型
feedback.view.FeedbackSubmitSp.submitBtnText:提交
feedback.view.SystemsAnalysis:您的问题正在处理，请稍候再提交！
feedback.view.ThankReferQuestion:您的问题已提交，感谢您的支持！
feedback.view.MaxReferTimes:今日提交次数已达上限。
feedback.view.SystemBusy:系统繁忙，请稍后再试。
feedback.view.question_type:请选择问题类型。
feedback.view.question_title:请填写问题标题。
feedback.view.question_content:请填写详细描述。
feedback.view.question_LessThanEight:详细描述不能少于8个字符。
feedback.view.user_full_name:请填写玩家姓名。
feedback.view.user_phone:请填写玩家手机。
feedback.view.complaints_title:请填写投诉标题。
feedback.view.complaints_source:请填写投诉来源。
feedback.view.charge_order_id:请填写充值订单号。
feedback.view.charge_method:请填写充值方式。
feedback.view.charge_moneys:请填写充值金额。
feedback.view.activity_name:请填写活动名称。
feedback.view.goods_get_method:请填写物品获取途径。
feedback.view.goods_get_date:请填写物品获取时间。
feedback.view.report_url:请填写举报名称或网址。
feedback.view.report_user_name:请填写举报用户名称。

feedback.view.problemCombox_text0:咨询游戏问题
feedback.view.problemCombox_text1:咨询活动相关
feedback.view.problemCombox_text2:物品道具异常消失
feedback.view.problemCombox_text3:盗号受理类
feedback.view.problemCombox_text4:充值卡类
feedback.view.problemCombox_text5:建议反馈
feedback.view.problemCombox_text6:BUG信息反馈
feedback.view.problemCombox_text7:举报非法
feedback.view.problemCombox_text8:服务投诉
feedback.view.problemCombox_text9:其它

feedback.view.csTelNumber:客服电话：{0}
feedback.view.infoText:最少输入8个字，还可以输入{0}字
feedback.view.infoDateText:例：2010-09-17
feedback.view.delPostsBtnText:结贴
feedback.view.continueSubmitBtnText:继续提交
feedback.view.submitAssessmentBtnText:提交评价

feedback.view.explainTextAreaText:<p align="center"><b>事故补偿规则</b></p><br><b>1.补偿期限</b><br>a)我们可以为玩家恢复最近15天内丢失的道具。<br>b)丢失时间超过15天的道具将可能无法进行恢复。<br><br><b>2.恢复内容</b><br>a)在玩家申报表单内，经核实确为玩家拥有的绑定且进行过合成或者强化操作的道具可以补偿给玩家。<br>b)在玩家申报表单内，经核实确为玩家拥有的绑定珍稀道具可以补偿给玩家。<br><br><b>3.不可被恢复的</b><br>a)未被绑定的道具。<br>b)被转换或被转手出售的道具。<br>c)游戏中的货币，包括但不仅限于点券、礼金。<br>d)游戏中消耗品（如强化材料、合成材料、卡片、经验药水、勋章、礼包）。<br><br><b>4.可追回的</b><br>a)可以明确判定盗号者藏脏的账号。<br>b)我们将对盗号者藏脏的账号进行封停，并且将藏脏账号上剩余的道具根据实际情况返还给被盗号的玩家。<br><br><b>5.需要全部补偿的</b><br>a)因为游戏程序本身原因造成玩家道具丢失的，经过核实属实的需按照玩家的表单进行恢复。

room.view.chooseMap.DungeonChooseMapView.dungeonModeDescription:该模式由若干个关卡组成，击杀最终Boss有可能获得各种稀有的宝物。
im.imList.blackTitle:黑名单
im.CMFriendList.title:已经加入弹弹堂的好友
im.CMFriendList.titleII:暂未加入弹弹堂的好友
trainer.view.QuestionView.tip:真厉害！下面有两个问题，巩固一下在本课中所学到的东西吧。
trainer.view.QuestionView.award:回答正确，您可获得:
ddt.view.common.QuestionGoogsView.questionDescription:这个礼包被智慧之神的仆从封印，需要答对他们的问题才能获得里面的东西。\n注：答错题后礼包将会消失！
ddt.view.common.QuestionGoogsView.questionCorrect:答对题目数
ddt.view.common.QuestionGoogsView.title:答题礼包

ddt.auctionHouse.notesTitle:拍卖说明
ddt.room.boguTip:您开启了一个新的难度
ddt.room.boguNewEnemy:新的敌人:
ddt.room.boguBoss:啵咕国王
ddt.room.boguMoreGoods:更多掉落:
ddt.room.boguGoods1:啵咕盾牌
ddt.room.boguGoods2:3级合成石
ddt.room.boguGoods3:啵咕帽
ddt.view.common.church.ProposeResponse:，向你求婚！
ddt.room.boguGoods4:2级宝珠
tank.view.caddy.FullBag:背包已满，请清理背包.
tank.view.caddy.title:魔罐开启
tank.view.caddy.start:开始
tank.view.caddy.end:停止
tank.view.caddy.close:关闭
tank.view.caddy.lookTrophy:查看奖励
tank.view.caddy.closeNode:请确认是否将获得物品放入背包!
tank.view.caddy.BagFull:背包已满，请清理背包。
tank.view.caddy.putInBag:放入背包
tank.view.caddy.sellAll:全部出售
tank.view.caddy.showChat:您通过魔罐发现了{0},并返还天使魔锤x1.
tank.view.caddy.sellAllNode:确定是否全部出售?<br>总价格:
tank.view.caddy.convertedAll:您确定将开出来的道具替换成{0}点积分
tank.view.caddy.exchangeAll:<p align="center">您可兑换{0}个天使魔锤</p>\n<FONT SIZE='16' COLOR='#FF0000'>注：每10点积分兑换一个天使魔锤</FONT>
tank.view.caddy.exchangeText1:您的魔锤积分小于10点，不可以兑换
tank.view.caddy.exchangeText:兑换
tank.view.caddy.opennAll:确定打开所有卡盒?
tank.view.caddy.dontClose:魔罐正在转动,无法关闭
tank.view.caddy.cardBoxDontClose:正在开启神秘卡盒中,不能关闭界面
tank.view.caddy.awardsPoint:<FONT SIZE='12' FACE='宋体' COLOR='#993300'><B>·</B></FONT>
tank.view.caddy.awardsGong:恭喜
tank.view.caddy.openCaddy:开启魔罐获得
tank.view.caddy.openCardBox:开启神秘卡盒
tank.view.caddy.quickDate:该类商品已下架,现在无法购买
tank.view.caddy.quickBuyKey:快速购买天使魔锤
tank.view.caddy.quickBoxKey:快速购买{0}
tank.view.caddy.EmptyBox:{0}数量不足。
tank.view.caddy.EmptyKey:锤子数量不足。
tank.view.daddy.SilverAward:若您再开启4个天使魔罐就可获得华丽的银罐一个！
tank.view.daddy.GoldAward:若您再开启17个天使魔罐就可获得耀眼的金罐一个！
tank.view.bead.quickNoBuy:请至少选择一类物品
tank.view.bead.title:开启宝珠
tank.view.bead.buyNode:您背包中没有该类宝珠,是否购买该类宝珠?
tank.view.bead.buyNoCardBox:您背包中没有神秘卡盒，是否购买神秘卡盒？
tank.view.bead.buyNoCardBox1:您背包中没有随机卡盒
tank.view.bead.dontClose:正在开启宝珠,无法关闭
tank.view.bead.quickNode:请至少选择一类宝珠
tank.view.bead.quickNumber:请选择购买数量
tank.view.award.Player:玩家
tank.view.award.Attack:开启攻击宝珠
tank.view.award.Defense:开启防御宝珠
tank.view.award.Attribute:开启属性宝珠
tank.view.award.auto:连续开启
tank.view.bead.opendGetAwards:开启属性宝珠获得
tank.view.bead.AttackBead:开启攻击宝珠获得
tank.view.bead.DefanceBeak:开启防御宝珠获得
tank.view.caddy.boxNameCaddy:魔罐
tank.view.caddy.boxNameBead:宝珠
tank.view.caddy.boxNameGift:红包
tank.view.offer.noOffer:功勋不足,购买失败
tank.view.offer.noConsortia:您没有加入任何公会。
tank.game.GameView.CaddyBattle:开启魔罐
tank.game.GameView.beadBattle:开启宝珠
tank.game.GameView.GiftBattle:开启功勋红包
tank.game.GameView.cardBoxBattle:开启神秘卡盒
tank.game.GameView.cardBoxBattle1:开启随机卡盒
tank.view.offer.dontClose:正在开启功勋红包,无法关闭
tank.view.offer.opendGetAwards:开启功勋红包获得
tank.view.card.title:开启卡盒
tank.view.card.noCard:卡盒已用完
tank.view.card.moreCard:该卡牌数量达到上限，不可再打开卡牌盒子
tank.view.card.noHaveCard:无此套卡
tank.view.card.HaveCard:装备成功
tank.view.card.dontClose:正在开启卡盒中,不能关闭界面.
tank.game.ToolStripView.isExitPVP:确定要退出当前战斗？<br><FONT SIZE='12' FACE='Arial' COLOR='#FF0000'> 退出后将扣除胜率、经验及功勋。</FONT>
im.IMLookupItem.CMFriendInfo:不能删除社区好友
im.IMLookupItem.ConsortiaPlayerInfo:不能删除公会好友
store.StoreIIComposeBG.consortiaSimthLevel:公会铁匠铺等级
store.StoreIIComposeBG.StrengthPercentageStrip:幸运符可提高强化成功率神恩符可使失败后不降级 
store.StoreIIComposeBG.ComposePercentageStrip:幸运符可提高合成成功率
store.StoreIIComposeBG.StrengthenStonStrip:强化石所提供的成功率。
store.StoreIIComposeBG.ComposeStonStrip:合成石所提供的成功率。
store.StoreIIComposeBG.LuckSignStrip:幸运符所提供的加成成功率。
store.StoreIIComposeBG.VIPAddStrip:VIP所提供的加成成功率。
store.StoreIIComposeBG.NoVIPAddStrip:你还没有开通VIP。
store.StoreIIComposeBG.ConsortiaAddStrip:公会铁匠铺所提供的加成成功率。
store.StoreIIComposeBG.RichesIsNotEnough:公会贡献值不够
store.StoreIIComposeBG.AllNumStrip:基础值，幸运符，公会，VIP加成的总和。
store.StoreIIComposeBG.noVIPAllNumStrip:基础值，幸运符，公会加成的总和。
tank.view.chat.ChatInputView.friend:好友
church.view.churchFire.ChurchFireView:金币：
im.IMView.inviteBtnText:邀请加入
im.IMView.inviteInfo:已通过社区消息发送邀请
im.IMView.inviteInfo1:存在非法字符，无法发送！
tank.store.embedCell.close3:强化孔，强化3级开启
tank.store.embedCell.close6:强化孔，强化6级开启
tank.store.embedCell.close9:强化孔，强化9级开启
tank.store.embedCell.close12:强化孔，强化12级开启
tank.store.embedCell.closeOpenHole:封印孔，消耗开孔钻头升级
tank.store.embedCell.attack:攻击宝珠插孔，您可以将攻击宝珠放入其中。
tank.store.embedCell.attribute:属性宝珠插孔，您可以将属性宝珠放入其中。
tank.store.embedCell.defense:防御宝珠插孔，您可以将防御宝珠放入其中。
tank.store.embedCell.Hole:{0}级属性宝珠插孔，您可以将S{0}级或S{0}级以下属性宝珠放入其中。

im.IMView.infoText:您暂时没有加入公会

gotopage.view.GotoPageView.bagStoreAlert:请关闭商城后再打开铁匠铺。

ddt.loader.loadingActivityInformationFailure:加载活动信息失败！
ddt.loader.LoadingBombMetadataFailure:加载炸弹元数据失败！
ddt.loader.LoadingChestsListFailure:加载宝箱物品列表失败！
ddt.loader.LoadingLoginFailedRewardInformation:加载登录奖励信息失败！
ddt.loader.LoadingCopyMapsInformationFailure:加载副本地图信息失败！
ddt.loader.LoadingAchievementTemplateFormFailure:加载成就模板表失败！
ddt.loader.LoadingExpressionResourcesFailure:加载表情资源失败！
ddt.loader.LoadingBuddyListFailure:加载好友列表失败！
ddt.loader.LoadingManualContentFailure:加载指南内容失败！
ddt.loader.LoadingManualSearchTagsFailure:加载指南标签失败！
ddt.loader.LoadingItemTypeFailure:加载物品类型失败！
ddt.loader.LoadingGoodsTemplateFailure:加载物品模板失败！
ddt.loader.LoadingBadgeInfoFailure:加载工会徽章模板失败！
ddt.loader.LoadingAnnouncementFailure:加载走马灯公告失败！
ddt.loader.LoadMapInformationFailure:加载地图信息失败！
ddt.loader.LoadingOpenMapListFailure:加载开启地图列表失败！
ddt.loader.LoadingTaskListFailure:加载任务列表失败！
ddt.loader.FailedToRegister:注册失败！
ddt.loader.LoadingRoleListFailure:加载角色列表失败！
ddt.loader.LoadingServerListFailure:加载服务器列表失败！
ddt.loader.LoadingStoreItemsFail:加载商店物品失败！
ddt.loader.LoadingGoodsAdditionFail:加载物品小加成失败！
ddt.loader.TheClassificationOfGoodsLoadingShopFailure:加载商店物品分类表失败！
ddt.loader.ShopDisCountRealTimesFailure:加载实时更新折扣商品表失败！
ddt.loader.LoadingTestFailure:加载题库失败！
ddt.loader.LoadingChestsInformationFailure:加载宝箱信息失败！
ddt.loader.LoadingGuildMembersListFailure:加载公会成员列表失败！
ddt.loader.LoadingComplainInformationFailure:加载投诉信息失败！
ddt.loader.LoadingNoviceRewardFailure:加载新手奖励失败！
ddt.loader.LoadingWeaponBallListFormFailure:加载WeaponBallList失败！
ddt.loader.LoadingTexpExpFailure:加载修炼经验表失败！
ddt.loader.LoadingBadLuckFailure:加载衰神表失败！
ddt.loader.LoadingLikeFriendTemplateFailure:加载同城好友失败！

ddt.enthrall.invalidID:您输入的身份证号码无效!
ddt.enthrall.emptyName:请输入您的姓名!

2.4.5version
ddt.manager.playerManager.hushandOnline:你的老公[{0}]上线了
ddt.manager.playerManager.wifeOnline:你的老婆[{0}]上线了
ddt.email.readingEmail.payEmail:该邮件为付费邮件,请点击附件物品获取

civil.view.CivilRightView.info:请输入要搜索的名字
store.fusion.preview.LowRate:较低
store.showTips.compose.dontCompose:您未放入装备或武器!
store.showTips.compose.dontComposeI:您未放入装备或武器!
store.showTips.compose.dontComposeII:您未放入合成石!
store.showTips.transfer.dontTransferI:您未放入装备或武器!
store.showTips.transfer.dontTransferIII:您未放入原装备或武器!
store.showTips.transfer.dontTransferIIII:您未放入目标装备或武器!
store.showTips.transfer.dontTransferII:装备上没有可交换的属性!
store.showTips.strength.dontStrengthI:您未放入装备或武器!
store.showTips.strength.dontStrengthII:您未放入强化石!
store.showTips.strength.dontStrengthIII:装备强化等级高于9级,未放入神恩符不能强化!
store.showTips.fusion.noEquip:您未放入装备或道具!
store.showTips.fusion.notSameStone:需放入同类型物品
store.showTips.fusion.notEnoughStone:需放入4份同类型物品
store.showTips.fusion.nofusonStone:您未放入熔炼公式!

ddt.view.vote.title:问卷调查
ddt.view.vote.loadXMLError:加载投票信息失败！
ddt.view.vote.choseOne:请选择至少一个答案

room.roomRightPropView.bagFull:每次战斗只能携带3个贴身道具
room.roomRightPropView.uppropTip:单击可将道具丢弃
room.roomRightPropView.downpropTip:点击图标可贴身带入战斗
ddt.vip.vipIcon.otherVipTip:VIP：{0}级
ddt.vip.vipIcon.clickOpen:点击此处开通VIP
ddt.vip.vipIcon.notVipCoin:VIP币不足，无法开启！
ddt.vip.vipIcon.notVip:你还没成为VIP，不能进行抽奖！
ddt.vip.vipIcon.reduceVipExp:VIP经验以每天{0}点经验减少
ddt.vip.vipIcon.upGradDays:还有{0}天，升入{1}级
ddt.vip.vipFrame.ConfirmTitle:确认开通
ddt.vip.vipFrame.openVipBtn:立即开通
ddt.vip.helpFrame.title:VIP专享特权
ddt.vip.vipFrame.title:VIP充值
ddt.vip.vipView.oneMonth:1个月
ddt.vip.vipView.threeMonth:3个月
ddt.vip.vipView.sixMonth:6个月
ddt.vip.vipView.oneYear:1年
ddt.vip.vipView.twoYear:2年
ddt.vip.vipView.threeYear:3年
ddt.vip.vipView.other:其他
ddt.vip.vipView.months:个月
ddt.vip.vipView.payMoneyShow:您应支付<FONT COLOR='#FF0000'>{0}</FONT>点券(<FONT COLOR='#CC6600'>1</FONT>元 = <FONT COLOR='#CC6600'>100</FONT>点券)
ddt.vip.vipView.checkName:两次输入的角色名称不一致
ddt.vip.vipIcon.upGradFull:您已是最高级VIP
ddt.vip.vipView.noMoney:您的点券不足,请充值
ddt.vip.vipView.upRule:成长规则
ddt.vip.vipView.help:VIP说明
ddt.vip.vipView.confirmforSelf:您确认要开通{0}的VIP会员？<br/>(本次开通需扣除您{1}点券)
ddt.vip.vipView.confirmforOther:您确认要为{0}开通{1}的会员？<br/>(本次开通需扣除您{2}点券)
ddt.vip.vipView.finish:请完善信息
ddt.vip.vipView.expired:您的VIP会员还有{0}天到期,请及时续费
ddt.vip.vipView.expiredToday:您的VIP会员还有{0}小时到期,请及时续费
ddt.vip.vipView.expiredHour:您的VIP会员有效期不足1小时,请及时续费
ddt.vip.vipView.expiredTrue:您的VIP会员已过期!
ddt.vip.vipView.RenewalNow:马上续费!
ddt.vip.vipView.EquipRenewal:装备续费
ddt.vip.vipView.checkOtherInput24:输入的月数不能大于24
ddt.vip.vipView.checkOtherInput0:输入的月数不能为0
ddt.vip.vipView.vipLevel.explain:<b>一、弹弹堂VIP会员：</b><br/>    "弹弹堂VIP会员"的推出，将为VIP玩家提供更贴心的服务及尊荣特<br/>权，等级越高，特权越强。<br/><br/><b>二、弹弹堂VIP等级成长体系规则：</b><br/>    1、VIP等级成长体系目前包含9个阶段，即VIP1- VIP9，开通VIP一<br/>个月的有效期为31天。<br/>    2、	当玩家成功开通VIP成为会员以后，VIP图标被点亮，VIP成长<br/>值以10点/天增长，累计达到一定数额，VIP等级才会逐步上升，上限为<br/>VIP9。<br/>    3、	当玩家为非VIP会员时，VIP图标熄灭，玩家将不再享受VIP特<br/>权，且VIP成长值以5点/天下降，VIP等级也将随之下降，下限为VIP1。<br/><br/><b>三、弹弹堂VIP成长值组成：</b><br/>    成长值由开通/续费成长值与非VIP成长值两部分组成：<br/>              <FONT COLOR='#FF0000'>成长值=开通/续费成长值+非VIP成长值</FONT>
ddt.vip.loadVip.error:加载vip性息失败！
ddt.vip.loadVip.error:加载返回投票是否开启失败！
ddt.vip.dueTime.tip:还有{0}天升级至VIP{1}
ddt.vip.vipFrame.upValue0:成长值: 15点/天
ddt.vip.vipFrame.upValue1:成长值：10点/天
ddt.vip.vipFrame.upValue2:成长值：-5点/天
ddt.vip.vipFrame.upValue3:成长值：0点/天
ddt.vip.vipFrame.youarenovip:您还不是VIP
ddt.vip.vipView.checkOtherInput:请添入一个小于24的月数
ddt.vip.vipView.cueDateScript:您已领取过本周VIP礼包，下次领取时间{0}月{1}日00:00以后
bag.changeColor.tips.name:外套
bag.changeColor.tips.armName:武器
im.IMController.deleteRecentContactsInfo:是否删除最近联系人
bagAndInfo.info.PlayerInfoEffortHonorView.selecting:选择称号
bagAndInfo.bag.UnableUseColorShell:强化等级10级及以上武器方可使用此道具.
ddt.vip.VipStatusView.VIPFeatureDescription:<FONT COLOR='#FF0000' ><b>1、VIP特色名称显示</b></FONT><br/>    游戏中VIP玩家的名称会显示红色。<br/><FONT COLOR='#FF0000'><b>2、VIP通关经验加成特权</b></FONT><br/>    VIP玩家在竞技战、公会战、副本通关过后，均可享受经验加成的特权。经验加成只针对双倍经验卡才有效。加成的比例随VIP等级增加而增加，即1.1倍-1.9倍。<br/><FONT COLOR='#FF0000'><b>3、VIP温泉场景特权</b></FONT><br/>    VIP玩家在温泉中享有专属套装，泡温泉时间比非VIP玩家多一个小时，且获得的经验也会更高。<br/><FONT COLOR='#FF0000'><b>4、VIP副本翻牌特权</b></FONT><br/>    VIP玩家副本通关过后，在第三次翻牌机会中可以享受9折的优惠。<br/><FONT COLOR='#FF0000'><b>5、VIP好友上限特权</b></FONT><br/>    VIP玩家比非VIP玩家的好友上限增加150人，将随着不同等级递增：VIP LV1加50人，VIP LV5加50人，VIP LV9加50人，累计150人）。<br/><FONT COLOR='#FF0000'><b>6、VIP公会作战功勋特权</b></FONT><br/>    VIP玩家在公会战败以后，当扣除的功勋大于1时，享受扣除功勋减半的特权。<br/><FONT COLOR='#FF0000'><b>7、VIP礼包特权奖励</b></FONT><br/>    VIP玩家每周可以领取一个VIP礼包。<br/><FONT COLOR='#FF0000'><b>8、VIP专属任务特权</b></FONT><br/>    VIP玩家将获得属于VIP的专属日常任务，完成后即可领取勋章奖励。<br/><FONT COLOR='#FF0000'><b>9、成就系统尽显VIP尊贵</b></FONT><br/>    随着玩家的VIP等级上升可以完成相关成就，并给予称号奖励。
ddt.vip.VipStatusView.RewardDescription: 双倍经验卡X1，双倍功勋卡X1
bagAndInfo.bag.sortBagClick.isSegistration:你是否想在背包整理后进行物品叠加？
bagAndInfo.bag.sortBagClick.isSegistration2:你是否想整理卡牌背包？
bagAndInfo.consortionBag.sortBagClick.isSegistration:是否对个人保管箱整理后进行物品叠加？
ddt.view.academyCommon.academyRequest.AcademyRequestMasterFrame.account:本人诚心拜师，师傅在上请收下我吧！
ddt.view.academyCommon.academyRequest.AcademyRequestMasterFrame.submitLabel:我要拜师
ddt.view.academyCommon.academyRequest.AcademyRequestMasterFrame.title:拜师
ddt.view.academyCommon.academyRequest.AcademyRequestMasterFrame.explainText:留下你的拜师宣言吧！
ddt.view.academyCommon.academyRequest.AcademyRequestApprenticeFrame.account:拜我为师吧，跟着师傅有肉吃!
ddt.view.academyCommon.academyRequest.AcademyRequestApprenticeFrame.submitLabel:我要收徒
ddt.view.academyCommon.academyRequest.AcademyRequestApprenticeFrame.title:收徒
ddt.view.academyCommon.academyRequest.AcademyRequestApprenticeFrame.explainText:留下你的收徒承诺吧！
ddt.view.academyCommon.academyRequest.AcademyRegisterFrame.academyHonorLabel:师徒称号：
ddt.view.academyCommon.recommend.AcademyApprenticeMainFrame.title:推荐拜师
ddt.view.academyCommon.recommend.AcademyMasterMainFrame.title:推荐收徒
ddt.view.academyCommon.myAcademy.myAcademyItem.MyAcademyMasterItem.online:在线
ddt.view.academyCommon.myAcademy.myAcademyItem.MyAcademyMasterItem.offline:不在线
ddt.goodsTip.date1:还剩{0}天{1}时{2}分开启
ddt.goodsTip.date2:还剩{0}时{1}分开启
ddt.view.academyCommon.academyIcon.MasterIcon:我的师傅
ddt.view.academyCommon.academyIcon.ApprenticeIcon:我的徒弟
ddt.view.academyCommon.academyIcon.AcademyIconTip.master:{0}的师傅
ddt.view.academyCommon.academyIcon.AcademyIconTip.Apprentice:{0}的徒弟
ddt.view.academyCommon.academyIcon.AcademyIconTip.masterExplanation:还缺{0}个徒弟
ddt.view.academyCommon.academyIcon.AcademyIconTip.1:一
ddt.view.academyCommon.academyIcon.AcademyIconTip.2:二
ddt.view.academyCommon.academyIcon.AcademyIconTip.3:三
ddt.view.academyCommon.academyIcon.AcademyIconTip.ApprenticeExplanation:还没有师傅
ddt.view.academyCommon.academyIcon.AcademyIconTip.ApprenticeNull:点击查看师徒福利
ddt.view.academyCommon.myAcademy.MyAcademyMasterItem.remove:解除关系将受到20000金币和在24小时收徒等\n待时间的惩罚，是否要将徒弟{0}逐出师门？
ddt.view.academyCommon.myAcademy.MyAcademyApprenticeItem.remove:解除关系将受到10000金币和24小时拜师等待时间的惩罚，且16级以上\n不可以再拜师，是否要将师傅{0}解雇？
ddt.view.academyCommon.myAcademy.MyAcademyApprenticeItem.removeII:师徒情分来之不易，且16级以上不可以再拜师，是否要将师傅{0}解雇？
ddt.view.academyCommon.myAcademy.MyAcademyApprenticeItem.removeIII:师徒情分来之不易，是否要将徒弟{0}逐出师门？

ddt.view.academyCommon.recommend.AcademyApprenticeMainFrame.checkBoxBtnInfo:今天不再推荐提醒
ddt.view.academyCommon.academyRequest.AcademyAnswerMasterFrame.AnswerMaster:你希望{0}成为你的徒弟吗？
ddt.view.academyCommon.academyRequest.AcademyAnswerApprenticeFrame.AnswerApprentice:你希望{0}成为你的师傅吗？
ddt.view.academyCommon.graduate.ApprenticeGraduate:出师
ddt.view.academyCommon.graduate.ApprenticeGraduate.title:恭喜你出师了！
ddt.view.academyCommon.graduate.ApprenticeGraduate.explain:现在你也可以带徒弟了，奖励很丰厚哦！
ddt.view.academyCommon.graduate.MasterGraduate:出徒
ddt.view.academyCommon.graduate.MasterGraduate.title:恭喜您的徒弟{0}出师了！
ddt.view.academyCommon.graduate.MasterGraduate.explain:您可以再收一个徒弟，最多同时可收3个徒弟！
ddt.manager.showAcademyPreviewFrame.masterFree:师徒福利
ddt.manager.AcademyManager.warning:收徒时等级必须比对方大5级
ddt.manager.AcademyManager.warningI:拜师时等级必须比对方小5级
ddt.manager.AcademyManager.warningII:玩家等级必须大于5级才可以收入门下
ddt.manager.AcademyManager.warningIII:徒弟必须小于20级
ddt.manager.AcademyManager.warningIIII:只有等级大于等于20级才可以收徒
ddt.manager.AcademyManager.warning6:5级以上才可以拜师
ddt.manager.AcademyManager.warning7:对方等级大于16级，不可以收他为徒了！
ddt.manager.AcademyManager.warning8:只有等级大于5级且小于17级才可以拜师！
ddt.manager.AcademyManager.warning9:17~19级玩家不可以登记拜师哦！
ddt.manager.AcademyManager.warning10:你现在正在房间中,是否进入师徒圣殿？
ddt.room.roomStart.academy.warning1:只有师徒身份才能进入师徒副本!
ddt.room.roomStart.academy.warning2:队伍成员必须要有一个师傅!
ddt.room.roomStart.academy.warning3:队伍成员只能有一个师傅!
ddt.room.roomStart.academy.warning4:最少需要1名师傅和1名徒弟!
ddt.manager.AcademyManager.WarningApprenticeState:你已经有师傅了！
ddt.manager.AcademyManager.WarningMasterFullState:只有徒弟数小于3人才能收徒，先去解除师徒关系吧！
im.IMView.myAcademyBtnTips:我的师徒
im.IMView.socialContact:发状态
ddt.view.academyCommon.recommend.RecommendPlayerCellView.tips:点击可以查看装备
ddt.manager.academyManager.Freezes:距离上次解除师徒关系超过24小时才可再拜师
ddt.manager.academyManager.FreezesII:距离上次解除师徒关系超过24小时才可再收徒
ddt.manager.playerManager.masterState:您的师傅[{0}]上线了
ddt.manager.playerManager.ApprenticeState:您的徒弟[{0}]上线了
academy.view.AcademyMemberListView.searchTxt:请输入玩家名称
ddt.view.academyCommon.register.TitleTxt:登记信息
ddt.view.academyCommon.register.TitleTxtII:修改信息
ddt.view.academyCommon.register.introductionFieldTxt:一段相遇，两处轻狂。桃李红墙外，寄语短天涯长。
ddt.data.analyze.AcademyMemberListAnalyze.info:很抱歉！暂时没有合适的徒弟，请稍后再试！
ddt.data.analyze.AcademyMemberListAnalyze.infoI:很抱歉！暂时没有合适的师傅，请稍后再试！
academy.view.AcademyMemberListView.amendBtnTips:修改拜师或收徒信息
academy.view.AcademyMemberListView.registerBtnTips:登记拜师或收徒信息
ddt.manager.AcademyManager.alertSubmit:去师徒圣殿
hall.HallStateView.academyInfo:该功能暂未开放，6级再来吧！
im.IMView.academyInfo:只有等级大于5级且小于17级才可以拜师
ddt.view.academyCommon.register.introductionLabel:寄语：
academy.view.AcademyMemberListView.registerInfo:您已拜师，不得在师徒圣殿登记
academy.view.AcademyMemberListView.registerInfoII:您的徒弟已经到达3个，不得在师徒圣殿登记
academy.view.AcademyMemberListView.registerInfoIII:无对应搜索结果
academy.view.success:操作成功，请等待对方回应！

bagAndInfo.bag.sortBagClick.isSegistration:你是否想在背包整理后进行物品叠加？
ddt.giftSystem.loadRecord.error:加载礼物记录失败!
ddt.giftSystem.GiftBannerView.giftLvel:LV{0}魅力值
ddt.giftSystem.GiftBannerView.giftShow:收到      件礼物
ddt.giftSystem.RecordParent.receivedShow:共收到 <FONT COLOR='#FF0000' >{0}</FONT> 份礼物
ddt.giftSystem.RecordParent.sendedShow:共送出 <FONT COLOR='#FF0000' >{0}</FONT> 份礼物
ddt.giftSystem.RecordItem.receivedHeadTxt:收到
ddt.giftSystem.RecordItem.sendedHeadTxt:您送给了
ddt.giftSystem.RecordItem.receivedGiftName:的{0}
ddt.giftSystem.RecordItem.giftCount:X {0}个
ddt.giftSystem.RebackMenu.reback:回  赠
ddt.giftSystem.RebackMenu.check:查  看
ddt.giftSystem.GiftGoodItem.charm:魅力:
ddt.giftSystem.GiftGoodItem.money:点券:
ddt.giftSystem.GiftGoodItem.free:剩余:
ddt.giftSystem.RebackMenu.alert:为好友<FONT COLOR='#FF0000' >{0}</FONT>选择礼物吧!
ddt.giftSystem.ClearingInterface.title:赠送礼物
ddt.view.SnsFrame.titleText:
ddt.view.SnsFrame.inputTextI:谢谢DDTV……谢谢亲爱的朋友们！！
ddt.view.SnsFrame.inputTextII:今儿个真高兴！
ddt.view.SnsFrame.inputTextIII:不是俺不想低调~天道酬勤啊
ddt.view.SnsFrame.inputTextIV:少壮不努力，老大徒伤悲！
ddt.view.SnsFrame.inputTextV:和朋友们在一起，什么困难都不怕！
ddt.view.SnsFrame.inputTextVI:防御UP，攻击UP，幸运UP，敏捷UP，快乐UP.
ddt.view.SnsFrame.inputTextVII:魅力不是我想要，想要就能要！
ddt.view.SnsFrame.inputTextVIII:千里狙击，我能，无限可能！
ddt.view.SnsFrame.shareBtnText:分享
ddt.view.SnsFrame.visibleBtnText:下次分享不再提醒
ddt.view.SnsFrame.snsInviteBtnTipData:上线邀请
ddt.view.SnsFrame.inputTextText:好久没看到你上线了，有空上线一起玩吧！
ddt.giftSystem.MyGiftItem.own:拥有<FONT COLOR='#FF0000'>{0}</FONT>个
ddt.giftSystem.ClearingInterface.inputName:请输入赠送对象名字
ddt.giftSystem.ClearingInterface.moreThanZero:礼物数量不能为0
ddt.giftSystem.ClearingInterface.canNotYourSelf:赠送对象不能是您自己
ddt.giftSystem.ClearingInterface.noEnoughMoney:您的余额不足，请充值
ddt.giftSystem.GiftShopView.chooseGiftForFriend:为好友选份礼物吧!
ddt.FriendDropListCell.noFriend:无好友搜索结果
ddt.giftSystem.GiftShopView.chooseGiftForFriend:为好友选份礼物吧!
ddt.view.NovicePlatinumCard.titleText:新手白金卡
im.CMFriendList.switchBtn1.tipData1:好友动态发送时提示
im.CMFriendList.switchBtn1.tipData2:好友动态发送时不提示
im.CMFriendList.switchBtn2.tipData1:好友动态开启
im.CMFriendList.switchBtn2.tipData2:好友动态关闭
IM.InviteDialogFrame.info:想要体验竞技的乐趣么？快来弹弹堂{0}区，和我一起体验真人射击对战的乐趣吧！
im.InviteDialogFrame.Title:邀请
im.InviteDialogFrame.name:对TA发出邀约
im.InviteDialogFrame.send:发送
im.SnsFrame.success:分享成功!
ddt.giftSystem.giftView.helpFrameTitle:礼品盒说明
ddt.giftSystem.ClearingInterface.limit:免费礼物一次只能赠送一个
ddt.giftSystem.GiftGoodItem.freeLimit:礼物全部抢购一空，请下次关注！
ddt.cardSystem.CardCell.cannotMoveCard.Same:此卡牌已在装备上
ddt.cardSystem.CardCell.cannotMoveCardMain:此卡牌不能移动到主卡位上
ddt.cardSystem.CardCell.cannotMoveCardOther:此卡牌不能移动到副卡位上
ddt.cardSystem.CardCell.notOpen:此卡位尚未解除封印
ddt.cardSystem.CardCell.differentSets:卡牌不能移动到不属于他的套卡中
ddt.cardSystem.loadfail.setsSortRule:加载套卡排列规则失败!
ddt.cardSystem.loadfail.setsProperties:加载套卡属性失败!
ddt.cardSystem.loadfail.PropertyList:加载套卡详细属性列表失败!
ddt.cardSystem.cardsTipPanel.type:类型:
ddt.cardSystem.cardsTipPanel.typeDetail:{0} ({1})
ddt.cardSystem.cardsTipPanel.basePropText:基础属性:
ddt.cardSystem.cardsTipPanel.level:等级
ddt.cardSystem.cardsTipPanel.attack:攻击: {0}   ({1})
ddt.cardSystem.cardsTipPanel.defends:防御: {0}   ({1})
ddt.cardSystem.cardsTipPanel.agility:敏捷: {0}   ({1})
ddt.cardSystem.cardsTipPanel.lucy:幸运: {0}   ({1})
ddt.cardSystem.cardsTipPanel.guard:护甲: {0}   ({1})
ddt.cardSystem.cardsTipPanel.damage:伤害: {0}   ({1})
ddt.cardSystem.cardsTipPanel.setsName:套卡: {0}   ({1}/{2})
ddt.cardSystem.cardsTipPanel.equip:装备{0}张:   
ddt.giftSystem.GiftGoodItem.freeLimit:礼物全部抢购一空，请下次关注！
ddt.trainer.view.ExplainOne.explain:增加1次攻击
ddt.trainer.view.ExplainTen.explain:增加伤害10%
ddt.cardSystem.UpGradeFrame.title:卡牌升级
ddt.cardSystem.UpGradeFrame.explain0:升级至1星，每次需消耗一张卡牌，增加5-10点生长值
ddt.cardSystem.UpGradeFrame.explain1:升级至2星，每次需消耗两张卡牌，增加5-10点生长值
ddt.cardSystem.UpGradeFrame.explain2:升级至3星，每次需消耗三张卡牌，增加5-10点生长值
ddt.cardSystem.UpGradeFrame.moreCards:卡牌数量不足，不能升级
ddt.cardSystem.PropResetFrame.help.title:洗点说明
ddt.cardSystem.PropResetFrame.title:卡牌洗点
ddt.cardSystem.PropResetFrame.oldProp:原属性
ddt.cardSystem.PropResetFrame.newProp:新属性
ddt.cardSystem.PropResetFrame.count:{0} 张
ddt.cardSystem.PropResetFrame.prop:({0})
ddt.cardSystem.PropResetFrame.Attack:攻击: {0}
ddt.cardSystem.PropResetFrame.Defence:防御: {0}
ddt.cardSystem.PropResetFrame.Agility:敏捷: {0}
ddt.cardSystem.PropResetFrame.Luck:幸运: {0} 
ddt.cardSystem.PropResetFrame.Damage:伤害: {0}
ddt.cardSystem.PropResetFrame.Guard:护甲: {0}
ddt.cardSystem.PropResetFrame.explain:说明：每次洗点需要消耗5张该卡牌，当卡牌数量不足时，您也可以花费300点卷完成一次洗点
ddt.cardSystem.PropResetFrame.noReplace:不替换
ddt.cardSystem.PropResetFrame.reset:洗点
ddt.cardSystem.PropResetFrame.resetagain:再次洗点
ddt.cardSystem.PropResetFrame.replace:替换
ddt.cardSystem.PropResetFrame.EmptyCard:您的卡牌数量不足3张，是否确认消耗300点券完成洗点？
ddt.cardSystem.CardBoxTipPanel.place:卡位: {0}
ddt.cardSystem.CardsTipPanel.main:主卡
ddt.cardSystem.CardsTipPanel.vice:副卡
ddt.cardSystem.CardBoxTipPanel.type:类别: {0}
ddt.cardSystem.CardBoxTipPanel.sets:套卡: {0}
ddt.trainer.view.ExplainTen.explain:增加伤害10%
ddt.trainer.view.ExplainPowerThree.three:发射3束炮弹
ddt.trainer.view.ExplainPowerThree.power:发射致命一击
ddt.trainer.view.ExplainPowerThree.ps:注：这两个道具不可同时使用
ddt.trainer.view.ExplainPlane.explain:将自己传送到目标位置
ddt.trainer.view.ExplainTwoTwenty.two:增加2次攻击
ddt.trainer.view.ExplainTwoTwenty.twenty:增加伤害20%
ddt.trainer.view.ExplainTwoTwenty.ps:可与其他道具组合使用，伤害更高
ddt.trainer.view.ExplainThreeFourFive.tip:使用后增加更多攻击伤害
ddt.trainer.view.ExplainThreeFourFive.ps:注：与其他道具组合使用伤害更高
ddt.functionLimitTip:该功能暂未开放，{0}级再来吧！
ddt.trainer.store.open:铁匠铺开启后可以使用该功能!
ddt.room.cantOpenMuti:6级可以点击此处增加己方房间人数
ddt.playerManager.addFriend.isRefused:对方设置了拒绝加好友.
room.view.roomView.MatchRoomView.challengeTip:开启了挑战功能，可以加入大厅挑战房间列表或点击\n他人名称后在界面中点击“发起挑战”按钮
shop.manager.ShopBuyManager.textImg:购买数量
shop.view.ShopRankingView.NoSearchResults:无搜索结果
im.playerState.awayReply:你好,现在有事不在,一会再和你联系.
im.playerState.busyReply:现在忙,一会联系.
im.playerState.noDisturbReply:神马迷茫中,火星回来联系.
im.playerState.shoppingReply:正在血拼中,一会联络.
im.playerState.away:离开
im.playerState.offline:离线
im.playerState.busy:忙碌中
im.playerState.noDisturb:请勿打扰
im.playerState.online:在线
im.playerState.shopping:购物
ddt.consortia.event.contribute:{0} 公会成员{1}捐献{2}点公会财富
ddt.consortia.event.contributeGM:{0} GM捐献{1}点公会财富
ddt.consortia.event.quite:{0} {1}开除公会成员{2}！
ddt.consortia.event.join:{0} {1}招收新成员{2}！
ddt.consortia.event.quit:{0} {1}退出公会
im.playerState.shopping:购物
ddt.view.bossbox.AwardsView.TheNextTimeText:请保持在线至{0}，您将获得以下奖励：
shop.view.ShopRankingView.shopSearchText:输入物品名称
shop.view.ShopRankingView.PleaseEnterTheKeywords:请输入关键字
im.imView.giftBtnTip:礼品盒
ddt.giftSystem.openGiftBtn.text:等级达到16级即可开启礼品盒
ddt.giftSystem.openCardBtn.text:20级可开启卡牌
ddt.texpSystem.openTexpBtn.text:等级达到10级即可开启修炼系统
ddt.userGuild.boxTip:还剩{0}小时{1}分钟开启
tank.view.changeColor.ChangeColorLeftView.wing:装饰
ddt.cardSystem.cardCollectView.title:卡牌收藏册
ddt.cardSystem.cardProp.unknown:属性未知
ddt.cardSystem.preview.propExplain:套卡属性说明:
ddt.cardSystem.preview.setProp1:任意装备
ddt.cardSystem.preview.setProp2:张该套卡牌可生成:
ddt.cardSystem.upgradeFrame.ownCardNum:<FONT COLOR='#06f906' >{0}</FONT> 
ddt.cardSystem.upgradeFrame.rule:卡牌升到<FONT COLOR='#f8f806' >{0}</FONT>级<br>每消耗<FONT COLOR='#f8f806' >{1}</FONT>张卡牌<br>增加<FONT COLOR='#f8f806' >{2}-{3}</FONT>点成长值
ddt.cardSystem.upgradeFrame.upSuccess:恭喜！增加{0}点成长值
ddt.cardSystem.upgradeFrame.isMaxLevel:此卡牌已是最高级，无法升级
ddt.cardSystem.loadfail.setsUpgradeRule:加载卡牌升级规则失败！
ddt.cardSystem.loadfail.propIncreaseRule:加载卡牌升级属性变化规则失败！
ddt.cardSystem.getNewCard.name:恭喜您获得:{0}
ddt.cardSystem.useCardBox.tweenty:您还不足20级，不能打开卡牌盒子
game.view.propContainer.ItemContainer.energy:按空格键发炮
game.view.arrow.ArrowView.energy:按空格键传送
ddt.cardSystem.cardEquip.openVice3:解除3号副卡位封印
ddt.cardSystem.cardEquip.openVice4:解除4号副卡位封印
ddt.cardSystem.cardEquip.open3:<b>解除封印条件</b>：拥有任意<FONT COLOR='0xff0000' >1</FONT>张<FONT COLOR='0xff0000' >20</FONT>级卡牌
ddt.cardSystem.cardEquip.open4:<b>解除封印条件</b>：拥有任意<FONT COLOR='0xff0000' >3</FONT>张<FONT COLOR='0xff0000' >20</FONT>级卡牌或<FONT COLOR='0xff0000' >1</FONT>张<FONT COLOR='0xff0000' >30</FONT>级卡牌
ddt.cardSystem.cardEquip.congratulate:恭喜您增加了一个卡牌装备位置！
ddt.cardSystem.cardEquip.cannotOpen3:符合条件卡牌不足，无法开启卡牌位
ddt.cardSystem.cardEquip.cannotOpen4:符合条件卡牌不足，无法开启卡牌位
ddt.cardSystem.upgrade.explain:卡牌升级说明
ddt.cardSystem.cardView.explain:卡牌说明
shop.view.ShopRightView.getSimpleAlert.title:免费领取
shop.view.ShopRightView.getSimpleAlert.msg:每天您可以进行一次免费领取，是否确认？
ddt.roomLoading.tips0:战斗内单击“Enter”键可快捷聊天
ddt.roomLoading.tips1:卡牌可通过自身消耗进行升级
ddt.roomLoading.tips2:拜师/收徒可获得额外奖励
ddt.cardSyste.bagItem.unkwon:未知
ddt.tryonSystem.title:请选择你最想要的奖励
ddt.cardSystem.bagView.openCardBox.level:您未到达20级，不可打开卡牌盒子
ddt.consortion.levelTip.explain:说　明：
ddt.consortion.levelTip.nextLevel:下一级：
ddt.consortion.levelTip.requirements:要　求：
ddt.consortion.levelTip.consumption:消　耗：
ddt.consortion.loadEventList.fail:加载公会事件失败！
ddt.consortion.loadUseCondition.fail:加载公会条件信息失败！
cityWideFrame.ONSTitle:TA和你可能在同一城市
cityWideFrame.ONSAlertInfo:<font size='14px' COLOR='#000000'>已经添加</font><font size='14px' COLOR='#D38753'>r</font><font size='14px' COLOR='#000000'>为同城好友!</font>
ddt.exitPrompt.btInfoTextI:还有<font size='18px' COLOR='#20d8ff'>r</font>个未完成
ddt.exitPrompt.btInfoTextII:<font COLOR='#00cc33'>全部完成</font>
ddt.exitPrompt.btInfoTextIII:还有<font size='18px' COLOR='#20d8ff'>r</font>封未读
ddt.exitPrompt.btInfoTextIV:<font COLOR='#00cc33'>全部读取</font>
ddt.exitPrompt.prompt:你确定要退出游戏吗
ddt.exitPrompt.alotofTask:多次
ddt.exitPrompt.copyThisUrl:地址已保存
tank.view.im.microcobol.tip:状态
socialContact.microcobol.infoTextI:请输入内容
socialContact.microcobol.infoTextII:超出<font size='24px'>r</font>字
socialContact.microcobol.infoTextIII:还能输入<font size='24px' >r</font>字
socialContact.microcobol.succeed:分享成功
socialContact.microcobol.infoTextTooLong:字数超出限制
journal.view.JournalItem.content1:获得物品：{0}{1}{2}{3}获得物品获得物品获得物品获得物品获得物品获得物品！
journal.view.JournalItem.content2:XX成功强化到{0}级！
journal.view.JournalItem.content3:与{0}结婚，成为甜蜜夫妻！
journal.view.JournalItem.content4:与{0}在结婚礼堂举办婚礼！
journal.view.JournalItem.content5:成为{1}公会会长！
journal.view.JournalItem.content6:成为拥有特权的VIP！
journal.view.JournalItem.content7:获得称号：{0}！
journal.view.JournalItem.content8:获得成就：{0}！
journal.view.JournalItem.content9:升级到{0}级！
journal.view.JournalItem.content10:共经历{0}场PVP战斗！
journal.view.JournalItem.content11:获得经验{0}！
journal.view.JournalItem.content12:获得勋章 {0} 个！
journal.view.JournalItem.content13:获得功勋{0}！
journal.view.JournalItem.content14:收到{0}份礼物！
journal.view.JournalItem.content15:交到{0}个新的朋友！
journal.view.JournalItem.content16:共完成主线任务：{0}个！
journal.view.JournalItem.content17:共完成支线任务：{0}个！
journal.view.JournalItem.content18:共完成日常任务：{0}个！
journal.view.JournalItem.content19:共完成活动任务：{0}个！
journal.view.JournalItem.content20:共完成VIP任务：{0}个！
journal.view.JournalItem.content21:徒弟{0}出师啦！
journal.view.JournalItem.content22:拜了{0}为师！
journal.view.JournalItem.content23:收{0}为徒！
journal.view.JournalItem.content24:{0}卡升级到{1}级！
journal.view.JournalItem.content25:加入了{0}公会！
journal.view.JournalItem.content26:与{0}离婚，往事不可追！
journal.view.JournalItem.content27:VIP升到{0}级！
journal.view.JournalItem.contentTxtNum:<FONT SIZE='14' FACE='宋体' COLOR='#993300'><B>{0}.</B></FONT>
journal.view.JournalItem.contentTxtContent:<FONT SIZE='14' FACE='宋体' COLOR='#FF0000'><B>{0}</B></FONT>
ddt.consortion.mailFrame.title:写公会邮件
ddt.consortion.mailFrame.all:公会全体成员
ddt.consortion.mailFrame.noEnagth:公会财富不足1000点
ddt.consortion.mailFrame.success:公会邮件发送成功
ddt.consortion.mailFrame.fail:公会邮件发送失败
ddt.consortion.pollFrame.title:会长选举
ddt.consortion.pollload.error:加载公会选举列表失败！
ddt.consortion.pollFrame.pleaseSelceted:请选择一位候选人
ddt.consortion.pollFrame.helpFrame.title:会长转移说明
ddt.consortion.pollFrame.continueDay:注:选举还剩{0}天
ddt.consortion.skillFrame.title:公会技能
ddt.consortion.skillInfo.loadError:公会技能信息加载失败!
ddt.consortion.skillTip.validity:有效期:{0}
ddt.consortion.skillItem.oneDay:1天
ddt.consortion.openSkillFrame.title:技能开启
ddt.consortion.SkillFrame.confirm:是否确定关闭此技能
ddt.consortion.SkillFrame.info:会长或者副会长才能开启此技能
ddt.consortion.skillItem.click.open:公会技能等级不足
ddt.consortion.skillItem.click.enough1:公会财富不足，是否现在捐献公会财富?
ddt.consortion.skillItem.click.enough2:个人贡献不足，是否现在捐献公会财富?
ddt.consortion.skillItem.click.enough3:勋章不足
ddt.consortion.pollFrame.success:投票成功
ddt.consortion.pollFrame.fail:投票失败
ddt.consortion.pollFrame.double:您已参与过投票
ddt.consortion.playerTip.notInvite:7级以下玩家不能加入公会

#房间内人物表情关键词列表
room.roomPlayerActionKey.HAPPY:haha|hehe|^ ^|^_^|噗哧|呼呼|呵呵|哈哈
room.roomPlayerActionKey.LUAGH:ahaha|ahahaha|ahahah|ahahahaha|ahahahahah|ahahahahaha|hahahaha|hahahahaha|hahahahahahahah|啊哈哈|啊哈哈哈哈|啊哈哈哈哈哈|哈哈哈哈|木哈哈|哇哈哈|XD|笑死我了
room.roomPlayerActionKey.NAUGHTY:嘻嘻|xixi|嘎嘎|gaga
room.roomPlayerActionKey.SAD:5555|呜呜|我哭|哭
room.roomPlayerActionKey.SARROWFUL:5555|555|呜呜呜呜
room.roomPlayerActionKey.LOOK_AWRY:切|戚|额|切~
room.roomPlayerActionKey.ANGRY:可恶|怒|啊啊|擦|日|杀
room.roomPlayerActionKey.SULK:啧|烦|烦死了|烦死了！|烦死了!|郁闷
room.roomPlayerActionKey.COLD:寒|怕怕|冷|好冷|好冷啊
room.roomPlayerActionKey.DIZZY:晕|昏|倒|yun|hun|dao
room.roomPlayerActionKey.SUPRISE:这……|雷|汗|汗……|汗…|汗…|囧|orz|囧rz
room.roomPlayerActionKey.SICK:恶心|恶心!|恶…|恶……|呸|呸！|我呸！
room.roomPlayerActionKey.SLEEPING:呼噜……|zzz…|zzz|zzz……
room.roomPlayerActionKey.OH_MY_GOD:天哪！|天哪|天哪！|omg|哇|哇哇哇|wa
room.roomPlayerActionKey.LOVE:么么|亲亲|亲|kiss|啵|么么~|么么～|美女|美眉|帅哥～|帅哥|好厉害！|好厉害|好厉害!
room.roomPlayerActionKey.PRIDE:嘿嘿|哼哼|heihei|hengheng
ddt.view.skillFrame.effect:效果:
ddt.consortion.skillCell.btnPersonal.rich:贡献
ddt.consortion.skillFrame.alertFrame.title:开启同类技能
ddt.consortion.skillFrame.alertFrame.content:若开启此技能，已开启的同类的技能会被关闭，是否确认开启?
ddt.consortion.skillFrame.alertFrame.personal.riches:个人贡献不足，无法开启此技能
ddt.consortion.skillFrame.alertFrame.consortion.riches:公会财富不足，无法开启此技能
ddt.tryonSystem.tryon:请选择奖励物品
ddt.bagInfo.notSociaty:无公会点击操作无效
ddt.bagInfo.notDesignation:未获得成就称号点击操作无效
ddt.dailyRecord.title:弹弹日志
ddt.dailyRecord.content1:<FONT COLOR='#820300'><B>{0}</B></FONT>    获得物品:<FONT COLOR='#b51609'><B>{1}</B></FONT>！<br/>
ddt.dailyRecord.content2:<FONT COLOR='#820300'><B>{0}</B></FONT>    <FONT COLOR='#b51609'><B>{1}</B></FONT>成功强化到<FONT COLOR='#b51609'><B>{2}</B></FONT>级！<br/>
ddt.dailyRecord.content3:<FONT COLOR='#820300'><B>{0}</B></FONT>    与<FONT COLOR='#b51609'><B>{1}</B></FONT>结婚，成为甜蜜夫妻！<br/>
ddt.dailyRecord.content4:<FONT COLOR='#820300'><B>{0}</B></FONT>    与<FONT COLOR='#b51609'><B>{1}</B></FONT>在结婚礼堂举办婚礼！<br/>
ddt.dailyRecord.content5:<FONT COLOR='#820300'><B>{0}</B></FONT>    成为<FONT COLOR='#b51609'><B>{1}</B></FONT>公会会长！<br/>
ddt.dailyRecord.content6:<FONT COLOR='#820300'><B>{0}</B></FONT>    成为拥有特权的<FONT COLOR='#b51609'><B>VIP</B></FONT>！<br/>
ddt.dailyRecord.content7:<FONT COLOR='#820300'><B>{0}</B></FONT>    获得称号:<FONT COLOR='#b51609'><B>{1}</B></FONT>！<br/>
ddt.dailyRecord.content8:<FONT COLOR='#820300'><B>{0}</B></FONT>    获得成就:<FONT COLOR='#b51609'><B>{1}</B></FONT>！<br/>
ddt.dailyRecord.content9:<FONT COLOR='#820300'><B>{0}</B></FONT>    升级到<FONT COLOR='#b51609'><B>{1}</B></FONT>级！<br/>
ddt.dailyRecord.content10:<FONT COLOR='#820300'><B>{0}</B></FONT>    共经历<FONT COLOR='#b51609'><B>{1}</B></FONT>场PVP战斗！<br/>
ddt.dailyRecord.content11:<FONT COLOR='#820300'><B>{0}</B></FONT>    获得经验<FONT COLOR='#b51609'><B>{1}</B></FONT>！<br/>
ddt.dailyRecord.content12:<FONT COLOR='#820300'><B>{0}</B></FONT>    获得勋章<FONT COLOR='#b51609'><B>{1}</B></FONT>个！<br/>
ddt.dailyRecord.content13:<FONT COLOR='#820300'><B>{0}</B></FONT>    获得功勋<FONT COLOR='#b51609'><B>{1}</B></FONT>！<br/>
ddt.dailyRecord.content14:<FONT COLOR='#820300'><B>{0}</B></FONT>    收到<FONT COLOR='#b51609'><B>{1}</B></FONT>份礼物！<br/>
ddt.dailyRecord.content15:<FONT COLOR='#820300'><B>{0}</B></FONT>    交到<FONT COLOR='#b51609'><B>{1}</B></FONT>个新的朋友！<br/>
ddt.dailyRecord.content16:<FONT COLOR='#820300'><B>{0}</B></FONT>    共完成主线任务:<FONT COLOR='#b51609'><B>{1}</B></FONT>个！<br/>
ddt.dailyRecord.content17:<FONT COLOR='#820300'><B>{0}</B></FONT>    共完成支线任务:<FONT COLOR='#b51609'><B>{1}</B></FONT>个！<br/>
ddt.dailyRecord.content18:<FONT COLOR='#820300'><B>{0}</B></FONT>    共完成日常任务:<FONT COLOR='#b51609'><B>{1}</B></FONT>个！<br/>
ddt.dailyRecord.content19:<FONT COLOR='#820300'><B>{0}</B></FONT>    共完成活动任务:<FONT COLOR='#b51609'><B>{1}</B></FONT>个！<br/>
ddt.dailyRecord.content20:<FONT COLOR='#820300'><B>{0}</B></FONT>    共完成vip任务:<FONT COLOR='#b51609'><B>{1}</B></FONT>个！<br/>
ddt.dailyRecord.content21:<FONT COLOR='#820300'><B>{0}</B></FONT>    出师啦！<br/>
ddt.dailyRecord.content22:<FONT COLOR='#820300'><B>{0}</B></FONT>    徒弟<FONT COLOR='#b51609'><B>{1}</B></FONT>出师啦！<br/>
ddt.dailyRecord.content23:<FONT COLOR='#820300'><B>{0}</B></FONT>    拜了<FONT COLOR='#b51609'><B>{1}</B></FONT>为师！<br/>
ddt.dailyRecord.content24:<FONT COLOR='#820300'><B>{0}</B></FONT>    收<FONT COLOR='#b51609'><B>{1}</B></FONT>为徒！<br/>
ddt.dailyRecord.content25:<FONT COLOR='#820300'><B>{0}</B></FONT>    <FONT COLOR='#b51609'><B>{1}</B></FONT>升级到<FONT COLOR='#b51609'><B>{2}</B></FONT>级！<br/>
ddt.dailyRecord.content26:<FONT COLOR='#820300'><B>{0}</B></FONT>    加入了<FONT COLOR='#b51609'><B>{1}</B></FONT>公会！<br/>
ddt.dailyRecord.content27:<FONT COLOR='#820300'><B>{0}</B></FONT>    与<FONT COLOR='#b51609'><B>{1}</B></FONT>离婚，往事不可追！<br/>
ddt.dailyRecord.content28:<FONT COLOR='#820300'><B>{0}</B></FONT>    VIP升到<FONT COLOR='#b51609'><B>{1}</B></FONT>级！<br/>

gotopage.view.LeagueShowFrame.title:联 赛
gotopage.view.LeagueShowFrame.scoreBtnText:积分{0}

ddt.quest.collectInfo.phone:手机号码：
ddt.quest.collectInfo.email:邮箱地址：
ddt.quest.collectInfo.validate:验证码：
ddt.quest.collectInfo.validateSend:验证码发送成功,可能会有数分钟延迟。
ddt.quest.collectInfo.dataError:输入无效，请重新输入。
ddt.quest.collectInfo.validateComplete:验证成功。
ddt.quest.collectInfo.validateFail:验证码有误。
ddt.quest.collectInfo.validateError:输入验证码有误，请重新输入
ddt.quest.collectInfo.noMail:请输入邮箱地址
ddt.quest.collectInfo.noPhone:请输入手机号码
ddt.quest.collectInfo.noValidate:请输入验证码。
ddt.quest.collectInfo.phoneNumberError:输入号码无效，请重新输入

ddt.quest.collectInfo.notValidMailAddress:邮箱地址无效
ddt.quest.collectInfo.notUsualMailAddress:不是常见的邮件地址
ddt.quest.collectInfo.validMailAddress:邮件地址有效
ddt.roomList.roomListBG.full:所有模式
ddt.roomList.roomListBG.Athletics:竞技模式
ddt.roomList.roomListBG.challenge:挑战模式
tank.view.common.BellowStripViewII.downLoadClient:点击下载登录器到桌面
ddt.view.academyCommon.academyRequest.state:对方不在线,请稍后再试
IMControl.addNullFriend:请输入名称
calendar.view.ActiveState.StartNot:活动尚未开始
calendar.view.ActiveState.OnMark:活动尚有{0}结束
calendar.view.ActiveState.TimeOut:活动已经结束
calendar.view.GoodsExchangeView.title:请将兑换的物品放入下面方框内
calendar.view.GoodsExchangeView.explain:提交物品后，活动奖励会以邮件的形式发送给您，请注\n意查收
calendar.view.goodsExchange.warning:物品类型不符合
calendar.view.goodsExchange.warningI:强化等级不够
calendar.view.goodsExchange.warningII:熔炼等级不够
calendar.view.goodsExchange.warningIII:请放入需要兑换的物品
calendar.view.GoodsExchangeView.explain:提交物品后，活动奖励会直接放到你的背包，如果背包\n满了会以邮件的形式发送给您，请注意查收
bagAndInfo.sellFrame.explainTxt:是否确定要出售 <FONT COLOR='#ff0000'>{0}</FONT> 个\n价格:
texpSystem.view.TexpView.currLv:当前等级：
texpSystem.view.TexpView.currEffect:当前效果：
texpSystem.view.TexpView.upEffect:下级效果：
texpSystem.view.TexpView.currExp:当前经验：
texpSystem.view.TexpView.upExp:升级经验：
texpSystem.view.TexpView.texpExp:修炼经验
texpSystem.view.TexpView.texp:修炼
texpSystem.view.TexpView.hp:血量
texpSystem.view.TexpView.att:攻击
texpSystem.view.TexpView.def:防御
texpSystem.view.TexpView.spd:敏捷
texpSystem.view.TexpView.luk:幸运
texpSystem.view.TexpView.up:恭喜您！{0}修炼等级提升！
texpSystem.view.TexpView.tipContent:等级: <FONT COLOR='#ea1a3c'>{0} 级</FONT>\n提升{1}值: <FONT COLOR='#ea1a3c'>{2}</FONT>
texpSystem.view.TexpInfoTip.content:<FONT COLOR='{0}'>{1}:</FONT> <FONT COLOR='#ffffff'>{2}</FONT> ({3}级)
texpSystem.view.TexpInfoTip.title:修炼属性
texpSystem.view.TexpView.texpTip:提升角色{0}值
texpSystem.view.TexpView.helpTitle:修炼说明
texpSystem.view.TexpView.refreshTaskTip:刷新修炼任务需要消耗 100 点券,确定刷新?
texpSystem.view.TexpCell.typeError:该位置只能放入修炼药水！
texpSystem.view.TexpCell.selectType:请选择修炼类型！
texpSystem.view.TexpCell.empty:您未放入修炼药水！
texpSystem.view.TexpCell.lvToplimit:该修炼等级已达上限！
texpSystem.view.TexpCell.noGrade:修炼系统10级开启！
texpSystem.view.TexpCell.texpCountToplimit:您今天可使用修炼药水的数量已到上限！
pageInterface.yourturn:轮到你出手了 
quest.QuestInfoPanelView.necessaryText:可选任务目标
quest.QuestInfoPanelView.notNecessaryText:必要任务目标
pageInterface.yourturn:轮到你出手了
IM.MaxCustom:最多只能创建10组自定义组
IM.addCustom.success:"{0}"添加成功
IM.addCustom.fail:"{0}"添加失败
IM.deleteCustom.success:分组<{0}>删除成功
IM.deleteCustom.fail:分组<{0}>删除失败
IM.alertCustom.success:自定义组修改成功
IM.alertCustom.fail:自定义组修改失败
IM.PrivateFrame.title:与"{0}"的对话
IM.PrivateFrame.send:发送
IM.PrivateFrame.record:记录
consortion.view.selfConsortia.SearchMemberFrame:搜索会员
consortion.view.selfConsortia.SearchMemberFrame.default:请输入会员名称
consortion.view.selfConsortia.SearchMemberFrame.warning:没有找到昵称与{0}字符相匹配的成员
consortion.view.selfConsortia.SearchMemberFrame.warningII:您搜索的目标不在查询列表中
consortion.view.selfConsortia.SearchMemberFrame.warningIII:最多输入14个字符或者7个汉字
IM.ChatFrame.level:等级：
IM.ChatFrame.warning:交谈中请勿轻信汇款、中奖信息、陌生电话，勿使用外挂软件
IM.ChatFrame.recordFrame.title:消息记录
IM.ChatFrame.recordFrame.pageWord:第        页/{0}页
IM.messagebox.title:消息盒子（G）
IM.addFirend.repet:组名已存在
IM.other.noEnough.five:对方还未开启此聊天功能
IM.privateChatFrame.send.tipdata:Enter发送
tank.gameover.takecard.score:积分
tank.littlegame.scorelack:积分不足
cardSystem.preview.descript.level:(0级/10级/20级/30级)

consortia.task.releasetitle:公会使命
consortia.task.releaseTable:发布使命
consortia.task.cancelTable:取消使命
consortia.task.resetTable:重新获取
consortia.task.okTable:确认发布
consortia.task.resetContent:重新获取使命需要支付点券500点,确定获取?<p align="center"><br><FONT SIZE='12' FACE='Arial' KERNING='2' COLOR='#FF0000'>注：重新获取公会使命会清空当前使命进度。</FONT></p>
consortia.task.OKContent:发布使命需要支付财富{0}点,确定发布?
consortia.task.lasttime:距本次使命结束下次开始还有{0}分{1}秒
consortia.task.released:使命已发布!
consortia.task.havetaskNoRelease:此次使命已消耗过公会财富，点击发布不会重复扣除
consortia.task.donateOK:捐献成功
consortia.task.plaese:请输入捐献的数量
consortia.task.stopTable:公会使命已经结束
consortia.donateMEDAL:捐献
consortia.task.noMedal:您没有勋章
cardSystem.preview.descript.level:(0级/10级/20级/30级)
ActivityState.confirm.content:你确定要充入10000点券？
consortion.buyBadgeFrameTitle:购买公会徽章
core.badgeTip.description:公会等级需大于等于{0}级，只有会长有购买权利
consortia.buyBadge.levelTooLow:公会等级不够
EliteGame.loadScoreRank.fail:加载积分排名信息失败！
email.complain.lan:邮件举报
BaseRoomView.getout.Timeout:30秒后将被请出房间
room.scoreRoom.isInRoom:您已在一个游戏房间内，不能创建精英赛房间
eliteGame.readyFrame.title:16强争霸赛
eliteGame.readyFrame.firstLine:现在进入16强争霸赛时间
eliteGame.readyFrame.alert:请在{0}秒内完成准备，进入房间
eliteGame.readyFrame.readyOther:正在等待对方准备...
eliteGame.readyFrame.readyOk:准备好了

store.fusion.donMoveGoods:正在连续熔炼物品,不能移动物品!
store.fusion.autoSplit.inputNumber:输入熔炼数量
loading.asset:资源文件
shop.buyAvatarFail:部分商品无法快捷购买！
email.complain.confim:确定举报此邮件为垃圾邮件？

tank.trainer.fightAction.newHandTip1:开炮方向反了，调整方向再打哦！
tank.trainer.fightAction.newHandTip2:力量太大了，试试用更小的力量开炮
tank.trainer.fightAction.newHandTip3Small:力量有些大，再小点试试
tank.trainer.fightAction.newHandTip3Large:力量有些小，再大点试试
tank.trainer.fightAction.newHandTip4:炮弹对您和队友同样会造成伤害，调整位置与角度，将炮口对准敌人！
tank.trainer.fightAction.newHandTip5:在位置不佳的情况下，可以使用传送。
tank.trainer.fightAction.newHandTip6:生命值过低，请使用在战斗道具箱中购买的生命恢复！
tank.roulette.helpView.tltle:规则说明

ddt.vip.vipView.getAwardsByLVText:请根据VIP等级领取对应的礼包

tank.view.Texp.bugbuff:点击购买梅恩兰德的祝福
tank.auctionHouse.view.wishBead:祝福宝珠
tank.view.WishBead.title:装备铸金
wishBead.wishBeadHelp.say:铸金说明
wishBead.StrengthenLevel:Gold
wishBead.GoodsTipPanel.txt1:装备成功铸金，黄金祝福还剩
wishBead.GoodsTipPanel.txt2:天
wishBead.GoodsTipPanel.txt3:小时
wishBead.lostEquip:请放入需要铸金的装备
wishBead.noBead:祝福宝珠数量不足
wishBead.notCanWish:该物品无法铸金
wishBead.equipIsGold:该物品已经铸金成功，无需继续铸金
wishBead.remainTimeShort:装备有效期过低
wishBead.help:正在铸金，不能进行此操作
wishBead.result:恭喜铸金成功

tank.roulette.tipInfo:恭喜您获得{0}倍充值奖励，充值返点不超过最高返点额度哦！

tank.leavaPageManager.chargeMoney.error:加载充值页面有错！
tank.leavaPageManager.chargeMoney.fail:充值失败！请联系GM阿九进行充值.

#3.6VIP 优化
tank.vip.rechargeAlertTitle:升级到VIP{0}之后您将享有：
tank.vip.rechargeAlertEndTitle:升级到VIP{0}您将享有：
tank.vip.rechargeAlertContent1:1、每周可领取“{0}”
tank.vip.rechargeAlertContent2:2、战斗经验加成将提升至{0}倍；
tank.vip.rechargeAlertContent3:3、强化成功率额外加成{0}；
tank.vip.rechargeAlertContent4:4、战斗失败将不扣除功勋；
tank.vip.rechargeAlertContent5:5、每天温泉时间增加{0}个小时，并拥有温泉专属装扮；
tank.vip.rechargeAlertContent6:6、第三次副本翻牌享受{0}折优惠；
tank.vip.rechargeAlertContent7:7、每天开启VIP币次数为{0}次
tank.vip.rechargeAlertContent8:8、如一次性开通{0}个月VIP服务将升级为{1}，成长值将以每天{2}点增加。 
tank.vip.rechargeAlertContent1Param0:低级VIP礼包
tank.vip.rechargeAlertContent1Param1:中级VIP礼包
tank.vip.rechargeAlertContent1Param2:高级VIP礼包  
tank.vip.rechargeAlertContent3Param:25%
tank.vip.rechargeAlertContent5Param:1
tank.vip.rechargeAlertContent6Param:8.8
tank.vip.rechargeAlertContent8Param1:3
tank.vip.rechargeAlertContent8Param2:紫金VIP
tank.vip.rechargeAlertContent8Param3:15
 
tank.shop.view.labelTips:适度游戏，理性消费

tank.serviceTextTips:官方客服QQ号：{0}
 
ddt.pets.rePetName:改名
ddt.pets.release:放生
ddt.pets.revert:还原
ddt.pets.feed:喂养
ddt.pets.feedNoFood:你未放入宠物粮食
ddt.pets.rePetNameTitle:宠物改名
ddt.pets.reInputPetName:请输入宠物昵称
ddt.pets.revertPetAlertMsg:你确定还原宠物<FONT COLOR='#FF0000'>{0}</FONT>么？还原后宠物会变为1级，\n并获得宠物精华液。
ddt.pets.revertPetCostMsg:还原宠物需要花费<FONT COLOR='#FF0000'>{0}</FONT>点券，确定还原宠物？
ddt.pets.hungerMsg:主人我饿了！不吃饱会影响心情的哦！
ddt.pets.expLimitMsg:经验快到达上限了！
ddt.pets.hasCropMature:农场有农作物已经成熟了，快去收吧！
ddt.pets.growthText:成长
ddt.pets.petHapyyStatus:当前快乐度
ddt.pets.petHapyyheart:心情
ddt.petsBag.petStatus:愤怒||不满||满意||快乐
ddt.pets.petUnFight:宠物不能出战 
ddt.pets.petHappyDesc:宠物可以发挥{0}属性.
ddt.pets.skillTipLost:消耗
ddt.pets.skillTipDesc:功能
ddt.pets.skillTipMagicValue:{0}魔法值 
ddt.pets.skillTipMagicValue1:{0}怒气值
ddt.pets.skillTipMagicValue2:{0}%最大体力值
ddt.pets.foodAmountSelect:宠物粮数量
ddt.pets.foodAmountTipText:粮食数量：
ddt.pets.upgradeNeedFoodAmount:宠物还需要喂食<FONT COLOR='#FF0000'>{0}</FONT>个宠物粮才可以升级。
ddt.pets.hungerNeedFoodAmount:宠物还需要喂食<FONT COLOR='#FF0000'>{0}</FONT>个宠物粮快乐度才会满。
ddt.pets.attactDetail:可以提高主人的攻击属性。
ddt.pets.defenseDetail:可以提高主人的防御属性。
ddt.pets.agilityDetail:可以提高主人的敏捷属性。
ddt.pets.luckDetail:可以提高主人的幸运属性。
ddt.pets.hp:血量
ddt.pets.hpDetail:可以提高主人血量上限。
ddt.pets.rePetNameAlertContonet:本次操作消耗 ：    点券
ddt.pets.petSkill:宠物技能
ddt.petEquipLevel:装备等级：{0}

ddt.petsBag.readme:宠物说明
ddt.petsBag.LevAction:宠物{0}级可开启
ddt.petsBag.VipAction:VIP4级时,可开启

ddt.petbag.petExpProgressTip:还能获得{0}经验
core.petskillTip.balltype0:不能叠加必杀
core.petskillTip.balltype1:不能叠加道具、必杀
core.petskillTip.balltype2:不能叠加道具、必杀，使用后回合结束
core.petskillTip.balltype3:不能叠加必杀
game.petskill.hasUsedPetskill:本回合你已经使用过一次宠物技能
core.petskillTip.activeSkill:主动技
core.petskillTip.passiveSkill:被动技
core.petMptip.description:魔法值每次轮到自己回合都会增长，不同技能消耗的魔法值不同。
ddt.petbags.text.petGrowUptipTitleName:成长值
ddt.pet.refreshNeed:249

ddt.farm.newPet.SkillTxt:技能
ddt.farm.newPet.Come:稀有宠物来袭！
ddt.farm.newPet.Desc:快去领养中心领养一只幼火红蚁吧！
ddt.farm.newPet.LvTxt:等级
tank.game.petmp.mp:魔法值
ddt.vip.PrivilegeViewItem.PetFifthSkill:宠物第五技能格
ddt.petSystem.openPetBtn.text:等级达到25级即可开启宠物系统
ddt.pets.fightSkill:战斗技能   

tank.game.actions.cooldown:冷却时间
tank.game.PowerStrip.energy:体力值：

ddt.farms.releasePet:是否确定放生宠物<FONT COLOR='#FF0000'>{0}</FONT>？\n放生的宠物将会被删除。

tank.data.EquipType.food:食物
tank.data.EquipType.seed:种子
tank.data.EquipType.manure:化肥 
tank.data.EquipType.vegetable:蔬菜
tank.data.EquipType.petEgg:宠物蛋

tank.gameVerify.title:下面第{0}行第{1}个字是？

tank.view.bagII.changeSexAlert:确定要改变性别吗？改变后角色已有夫妻关系将解除。
tank.view.bagII.changeSexAlert.success:变性成功，请重新登录。
tank.view.bagII.changeSexAlert.failed:变性失败

tank.specialGift.lotteryTip:活动未开启
wonderfulActivityManager.btnTxt12:· 迷宫寻宝
treasureHunting.start:迷宫宝藏活动已开启，点击精彩活动查看。
treasureHunting.end:迷宫宝藏活动已关闭！
treasureHunting.alert.title:是否消耗{0}点券购买神秘卡盒X{1},同时进行{2}次寻宝
treasureHunting.alert.ensureSellAll:确认出售全部物品
treasureHunting.alert.ensureGetAll:确认把所有物品放入背包
treasureHunting.alert.MovieUncomplete:迷宫寻宝正在进行，无法关闭
treasureHunting.tip1:点券不足
treasureHunting.tip2:绑定点券不足
treasureHunting.tip3:请选择您想要迷宫类型
treasureHunting.tip4:最多只能选取四种类型的迷宫
treasureHunting.tip5:请结束寻宝再退出
treasureHunting.tip6:请结束寻宝再使用积分兑换
treasureHunting.tip7:积分开启只可在常规寻宝下进行
treasureHunting.msg:你获得
treasureHunting.count:成功寻宝{0}次！
treasureHunting.huntingNow:寻宝中。。。
treasureHunting.lastTimeTxt:最后更新  
treasureHunting.exchangeAll:<p align="center">您可使用积分寻宝<FONT SIZE='16' COLOR='#FF0000'>{0}</FONT>次</p>\n<FONT SIZE='14' COLOR='#FF0000'>注：每30点积分兑换一次寻宝机会（如果背包已满，系统会自动停止寻宝）</FONT>
treasureHunting.notEnough:您的积分不足30点，不可兑换
treasureHunting.over:活动已结束
treasureHunting.recordTxt:在寻宝中
treasureHunting.exHunt:需要至少选择四种迷宫类型才可开启高级寻宝
mysteriousRoulette.noTicket:寻宝次数不足！

tank.calendar.Activity.BackButtonText:返回
tank.calendar.GoodsExchangeView.actTimeText:活动时间：
tank.calendar.GoodsExchangeView.haveGoodsExplainText:已有物品：
tank.calendar.GoodsExchangeView.changeGoodsExplainText:选择兑换物品：
tank.calendar.GoodsExchangeView.changeGoodsCountText:输入兑换数量：
tank.calendar.ActivityDetail.TimeFieldTitle:活动时间：
tank.calendar.ActivityDetail.AwardFieldTitle:活动奖励：
tank.calendar.ActivityDetail.ContentFieldTitle:活动内容：

treasureHunting.timesText:购买卡盒          个
treasureHunting.huntTimesText:搜寻宝藏          /<font color="#BAFD00">{0}</font>次
treasureHunting.buyCardBoxTips:是否花费{0}点券购买{1}个卡盒?
chat.task.Notconsortia:工会领地不能领奖！
chat.task.NotBattle:战斗中不能领奖!
ddt.pyramid.autoOpenCount.endBtnMsg:自动开启中...
ddt.bagandinfo.buffBuf:开启
treasureHunting.huntingAlert:是否花费{0}点券寻宝{1}次?

ddt.caddy.badluck.sortTitletxt:排名
ddt.caddy.badluck.nameTitletxt:名称
ddt.caddy.badluck.numberTitletxt:数量
ddt.caddy.badluck.goodsNameTxt:物品名称
caddy.badLuck.rankingText。text:排  名
caddy.badLuck.nameText.text:名  称
caddy.badLuck.propertyText.text:道具奖励
caddy.badLuck.regulationText1.text:1. 1个幸运彩石兑换1个经验礼包。
caddy.badLuck.regulationText2.text:2. 2个幸运彩石兑换1个1级经药水。
caddy.badLuck.regulationText3.text:3. 4个幸运彩石兑换1个2级经药水。
caddy.badLuck.regulationText4.text:4. 8个幸运彩石兑换1个3级经药水。
caddy.badLuck.regulationText5.text:5. 排名1~10的弹友且彩石数量大于100个，可获得奖励。
caddy.badLuck.regulationText6.text:注:以活动结束最终排名,送出以下奖励。
caddy.badLuck.regulationText7.text:10名以后玩家获得奖励规则。
treasureHunting.recordTxt:在寻宝中
ringStation.view.person.noRank:未上榜

newTitleView.currentTitleTxt:选中称号
newTitleView.hasTitleTxt:拥有称号
newTitleView.allTitleTxt:全部称号
newTitleView.propertyTxt1:攻击：{0}\n
newTitleView.propertyTxt2:防御：{0}\n
newTitleView.propertyTxt3:敏捷：{0}\n
newTitleView.propertyTxt4:幸运：{0}\n
newTitleView.propertyTxt5:有效期：{0}天
newTitleView.titleTxt:称号管理
newTitleView.useTitleSuccessTxt:使用{0}成功
newTitleView.hideBtnTipTxt:隐藏称号，称号不再显示，称号属性依然生效。
newTitleView.hasnoTitleTxt:未获得
newTitleView.selectTitleTxt:请先选中一个拥有的称号


#小贱添加完美图腾
ddt.totemSystem.openTotemBtn.text:等级达到{0}级即可开启弹王图腾系统
ddt.totem.sevenProperty:攻击,防御,敏捷,幸运,生命,伤害,护甲
ddt.totem.rightView.lvTxt:LV:{0}
ddt.totem.rightView.titleTxt1:激活
ddt.totem.rightView.titleTxt2:当前拥有
ddt.totem.rightView.titleTxt3:当前图腾属性
ddt.totem.rightView.tipTxt:注：荣誉可通过击杀世界BOSS和活动获得
ddt.totem.rightView.honorTipTxt:荣誉：可通过击杀世界BOSS获得
ddt.totem.totemWindow.propertyLvTxt:Lv:{0} {1}
ddt.totem.honorOrExpUnenough:点卷或荣誉不足
ddt.totem.totemPointTip.propertyNameTxt:激活属性：
ddt.totem.totemPointTip.possibleNameTxt:激活成功率：
ddt.totem.totemPointTip.possibleValueTxt:必成,高,较高,较低,低
ddt.totem.totemPointTip.statusNameTxt:激活状态：
ddt.totem.totemPointTip.statusValueTxt:已激活,未激活
ddt.totem.totemPointTip.honorTxt:消耗荣誉：{0}
ddt.totem.totemPointTip.expTxt:消耗点卷：{0}
ddt.totem.totemPointTip.lvAddPropertyTxt:Lv{0}.{1}+{2}
ddt.totem.totemPointTip.currentPropertyTxt:当前属性
ddt.totem.totemChapterTip.numListTxt:一,二,三,四,五
ddt.totem.totemChapterTip.titleListTxt:魔蚁女王,邪恶母鸡,强悍波谷,愤怒邪神,黑暗神牛
ddt.totem.totemChapterTip.titleTxt:第{0}章:{1}(<FONT COLOR='#00FF06' SIZE='14'>+{2}</FONT>)
ddt.totem.totemChapterTip.addValue:<FONT COLOR='#fffab1' SIZE='14'> (+{0})</FONT>
ddt.totem.totemChapterTip.descTxt:升阶属性提升{0}%
ddt.totem.totemChapterTip.nameTxt:第{0}章属性：
ddt.totem.honorUpFrame.tip1:消耗      点券进行荣誉升华，获得      荣誉
ddt.totem.honorUpFrame.titleTxt:荣誉提升
ddt.totem.honorUp.cannot:今日可以提升的荣誉次数已用完
ddt.totem.honorUp.success:您获得{0}荣誉
ddt.totem.activateProtectTipTxt:开启保护后成功率提升到100%
ddt.totem.activateProtectTipTxt2:开启保护后每次点亮图腾将消耗1000点券，\n成功率为必成时不扣除费用。
ddt.totem.upGrade.needGood:荣誉精华
ddt.totem.upGrade.fail:升阶材料不足!
ddt.totem.upGrade.totemNoActive:当前图腾章节未激活！
tank.totem.maxLevelTxt:图腾已经达到最高等级！
ddt.consortiaBattle.buyConfirm.noAlertTxt:本次登录不再提示

#小贱添加完美世界BOSS
worldboss.totalInfo.time:挑战剩余时间
worldboss.totalInfo.yourself:我的积分
worldboss.tickets.propInfo:进入挑战需要消耗{0}boss门票, 确定是否挑战?
worldboss.tickets.none:您的门票不足，不能进入.
worldboss.ranking.title:世界boss积分排名
worldboss.ranking.num:排名
worldboss.ranking.name:昵称
worldboss.ranking.socre:积分
worldboss.resurrectView.prop:等待复活时间
worldboss.buff.tip:点击购买，挑战boss时可额外获得\n{0}攻击力和{1}生命值
worldboss.revive.propNoMoney:您的点券不足，无法立即复活
worldboss.revive.propMoney:立即复活需要消耗{0}点券，是否立即复活
worldbossRoom.ranking.proploading:获取排名中...
worldboss.buff.buy:购买后将扣除{0}点券,是否继续？
worldboss.buyBuff.Nomoney:您的点券不足,无法购买
worldboss.buyBuff.yourHaveMoneny:当前拥有点券
worldboss.buyBuff.FrameTitle:挑战技能
worldboss.buyBuff.eachMoney:{0}点券/{1}次
worldboss.buyBuff.selectedText:购买
worldboss.buyBuff.haveBuy:已购买
worldboss.buyBuff.allBuy:全部选中
worldboss.buyBuff.NotAgain:以后不再提示
worldboss.buyBuff.setShowSucess:设置成功
worldboss.room.leaveroom:确定要离开世界boss战场吗？
wordlboss.room.tipText:点击可购买各种战斗技能，让你拥有超强的战斗力对战世界boss
worldboss.buff.noSelected:请选择您要购买的物品
worldboss.buff.limit:(下线或者切换频道将会被自动清除)
worldboss.buyBuff.eachItem:{0}{1}/{2}次
worldboss.buyBuff.lackItem:沒有足夠的{0}!
worldboss.buyBuff.autoBuy:自动购买
worldboss.buyBuff.autoBuyConfirm:选择自动购买模式后，每次进入BOSS战斗时将自动购买BUFF\n并扣除相应的点卷。下面每次不再提示勾选。
worldboss.buyBuff.buyBuffSuccessTip:自动购买{0}成功，扣除{1}点卷！
worldboss.buyBuff.buyBuffFailTip:点卷不足，购买BUFF失败
worldboss.buyBuff.moneyTip:您目前拥有
worldboss.room.roomFull:房间已满，请稍候再试！


#小贱添加完美精彩活动
wonderfulActivityManager.btnTxt1:· 充值反馈	
wonderfulActivityManager.btnTxt2:· 消费反馈	
wonderfulActivityManager.btnTxt3:· 藏宝农场	
wonderfulActivityManager.btnTxt4:· 天使赐福	
wonderfulActivityManager.btnTxt5:· 幸运转盘	
wonderfulActivityManager.btnTxt6:· 飞升首储反馈	
wonderfulActivityManager.btnTxt7:· 活动期间已充值  {0}点券	
wonderfulActivityManager.btnTxt8:· 活动期间已消费  {0}点券
wonderfulActivityManager.btnTxt8_tip:有效消费：除拍卖场购买、赠送红包、结婚返礼、邮件交易以及公会贡献活动加码以外的点券消费	
wonderfulActivityManager.btnTxt9:激情团购
wonderfulActivityManager.btnTxt10:英雄人物
wonderfulActivityManager.btnTxt11:新服优惠	
wonderfulActivityManager.btnTxt12:迷宫寻宝
wonderfulActivityManager.btnTxt13:· 赛亚之神
wonderfulActivityManager.btnTxt14:· 神秘转盘
wonderfulActivityManager.btnTxt15:累计登录
wonderfulActivityManager.btnTxt16:兑换活动
wonderfulActivityManager.btnTxt17:· 变废为宝
wonderfulActivityManager.btnTxt18:邪神募捐
wonderful.accumulative.alreadyPayTxt:当前已充值点券数量：
wonderful.accumulative.nextPrizeNeedTxt:领取下级奖励需再充值点券：
wonderfulActivity.startTip1:充值反馈活动已开启	
wonderfulActivity.startTip2:消费反馈活动已开启	
wonderfulActivity.startTip3:充值反馈有可领取的奖励。|立即领取
wonderfulActivity.startTip4:消费反馈有可领取的奖励。|立即领取
wonderfulActivity.startTip41:神庙升级活动已开启.
wonderfulActivity.startTip42:活力消耗活动已开启.
wonderfulActivity.startTip43:卡牌洗点活动已开启.
wonderfulActivity.startTip44:潜能激活活动已开启.
wonderfulActivity.startTip45:扭蛋活动已开启.
wonderfulActivity.startTip46:藏宝图活动已开启.
wonderfulActivity.startTip47:打造升级活动已开启.
wonderfulActivity.startTip48:.宠物突破活动已开启
wonderfulActivity.startTip49:.道具使用活动已开启
wonderfulActivity.getRewardTip:只能在游戏广场领取奖励
wonderfulActivity.close:活动已结束
wonderfulActivity.strength.nameTxt:任意装备强化+{0}
wonderfulactivity.accumulativelogin.txt:累计登录7天才能选择神器
wonderfulactivity.accumulativelogin.txt2:请选择一件神器
wonderfulActivityManager.tittle:精彩活动	
wonderfulActivityManager.d:天	
wonderfulActivityManager.h:小时	
wonderfulActivityManager.m:分钟	
wonderfulActivityManager.s:秒	
wonderfulActivity.startTip18:充值回馈活动已开启.
wonderfulActivity.startTip19:消费回馈活动已开启.
wonderfulActivity.startTip24:坐骑达人升级活动已开启.
wonderfulActivity.startTip25:坐骑达人技能升级活动已开启.
wonderfulActivity.awardTip18:充值回馈有可领取的奖励。|立即领取
wonderfulActivity.awardTip19:消费回馈有可领取的奖励。|立即领取
wonderfulActivity.awardTip24:坐骑达人有可领取的奖励。|立即领取
wonderfulActivity.awardTip25:坐骑达人有可领取的奖励。|立即领取=======
wonderfulActivity.awardTip24:坐骑达人升级活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip25:坐骑达人技能升级活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip26:疯狂冲级活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip27:强化达人活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip28:合成之王活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip29:至尊新人王活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip30:宝珠大亨活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip31:图腾先锋活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip32:修炼高手活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip33:超级卡牌活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip34:缤纷战魂活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip35:拉风坐骑活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip36:宠物进化活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip37:全民超级VIP活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip38:全民宠物精英活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip39:每日有礼活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip41:神庙升级活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip42:活力消耗活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip43:卡牌洗点活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip44:潜能激活活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip45:扭蛋活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip46:藏宝图活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip47:打造升级活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip48:宠物突破活动有可领取的奖励。|立即领取
wonderfulActivity.awardTip49:道具使用活动有可领取的奖励。|立即领取
wonderfulActivity.npc.clickTip:您的点击次数太频繁，请稍等！
wonderfulActivity.above:及以上
wonderfulActivity.choosePrizeFirst:请选择所要领取的奖励
wonderfulActivity.oneOffInTimeTip:活动期间每个角色只能参与一次首充活动，奖励只可领取一次，一定要直接选择领取最高的奖励哦。
wonderfulActivity.startTip17:一次性充值活动已开启.
wonderfulActivity.awardTip17:一次性充值活动有可领取的奖励。|立即领取
wonderful.rookieRank.title1:名次
wonderful.rookieRank.title2:昵称
wonderful.rookieRank.title3:历史最高战斗力
wonderful.daySupplyAwardTotal:本轮募捐需要充值：{0}点券
wonderful.daySupplyAwardTime:{0}小时
wonderful.daySupplyAwardLackTime:<1小时
wonderful.daySupplyAwardGreaterTime:{0}天
wonderful.daySupplyAwardDay:第{0}天
ddt.wonderfulActivity.limitActivityTime:您的点击速度过快，请30秒以后重新进行尝试
carnival.closeTimeTxt:活动剩余时间
carnival.getTimeTxt:领取时间：
carnival.awardCountTxt:奖励剩余：
carnival.awardBuyCountTxt2:限购{0}
carnival.awardBuyCountTxt1:每日限购{0}
carnival.buyGiftTypeTxt1:点券/个
carnival.buyGiftTypeTxt2:绑点/个
carnival.awardBuyCountTxt:限购{0}
carnival.awardGiftBuyTxt:确认消耗{0}点券，购买一个礼包
carnival.descTxt1:等级达到{0}级
carnival.descTxt2:全身强化{0}级
carnival.descTxt3:合成属性+{0}
carnival.rookie.descTxt1:战斗力第{0}名
carnival.rookie.descTxt2:战斗力第{0}-{1}名
carnival.rookie.descTxt3:战斗力达到{0}
carnival.descTxt5:全身宝珠{0}级
carnival.descTxt6:图腾{0}级
carnival.descTxt7:修炼总等级达到{0}
carnival.descTxt8:卡牌总等级达到{0}
carnival.descTxt9:战魂总品阶达到{0}
carnival.descTxt10:坐骑达到{0}级{1}星
carnival.descTxt11:宠物进化到{0}级
carnival.descTxt16:达到VIP{0},全区VIP{0}达到{1}人,VIP{0}人数每增加{0}人
carnival.descTxt17:获得{0}只{1}星宠,全区共有{0}只{1}星宠,全区每增加{0}只{1}星宠
carnival.descTxt18:使用{0}个{1},获得{0}个{1},种植{0}个{1}
carnival.clickTip:点击太快了,请休息一下！
carnival.descTxt21:消耗{0}活力
carnival.descTxt22:{0}张卡牌洗出\n全max属性
carnival.descTxt24:通过终点{0}次
carnival.descTxt27_1:参与{0}次单次扭蛋
carnival.descTxt27_2:参与{0}次十连扭
carnival.descTxt26:宠物突破{0}级
consumeRank.outOfRank:我的排名：未上榜  
consumeRank.outOfRankLabel:已消费{0}点券，再消费{1}点券可上榜
consumeRank.myRank:我的排名：
consumeRank.rankLabel:再消费{0}点券可提升名次
consumeRank.checkConsume:查看有效消费
consumeRank.helpTxt:1.有效消费：游戏中一切会增加VIP经验的点券消费均为有效消费。\n2.排名前10，且消费点券数量大于 {0} 才可获得奖励。
consumeRank.over:活动已结束
rechargeRank.outOfRank:我的排名：未上榜
rechargeRank.outOfRankLabel:已储值{0}点券，再储值{1}点券可上榜
rechargeRank.myRank:我的排名：
rechargeRank.rankLabel:再储值{0}点券可提升名次
rechargeRank.checkConsume:领奖条件
rechargeRank.helpTxt:1.只有在活动期间内储值才计入排行。\n2.排名前10的奖励必须储值达到{0}点券才可获得。
rechargeRank.over:活动已结束
wholePeople.vip.descTxt1:达到VIP{0}
wholePeople.vip.descTxt2:全区VIP{0}达到{1}人
wholePeople.vip.descTxt3:VIP{0}人数每增加{1}人
wholePeople.vip.tipTxt:达到VIP{0}可以领取奖励
wholePeople.pet.descTxt1:获得{0}只{1}星宠
wholePeople.pet.descTxt2:全区有{0}只{1}星宠
wholePeople.pet.descTxt3:每增加{0}只{1}星宠
wholePeople.pet.descTxt4:获得{0}只{1}星神宠
wholePeople.pet.descTxt5:全区有{0}只{1}星神宠
wholePeople.pet.descTxt6:每增加{0}只{1}星神宠
wholePeople.pet.tipTxt2:自己至少拥有{0}只{1}星宠
wholePeople.pet.tipTxt3:自己至少拥有{0}只{1}星宠
wholePeople.pet.tipTxt5:自己至少拥有{0}只{1}星神宠
wholePeople.pet.tipTxt6:自己至少拥有{0}只{1}星神宠

#小贱添加纹章
store.StoreBagView.EquipmentTitleText:装备	
shop.ShopRightView.TopBtn.equipment:装备
store.StoreBagView.ItemTitleText:道具	
store.StoreBagView.EquipmentTip.StrengthText:首先请双击选择您要强化的装备	
store.StoreBagView.EquipmentTip.EmbedText:首先请双击选择您要镶嵌的装备	
store.StoreBagView.EquipmentTip.ComposeText:首先请双击选择您要合成的装备	
store.StoreBagView.EquipmentTip.TransferText:请双击选择您要转换的装备	
store.StoreBagView.EquipmentTip.FusionText:请双击选择您要熔炼的物品	
tank.emblem.tempLoadFail:纹章模板表加载失败！
tank.emblem.scrollTempLoadFail:纹章卷轴模板表加载失败！
tank.emblem.noEquip:请先选择镶嵌的装备！
tank.emblem.insertFail:装备类型和纹章类型不一致！
tank.emblem.togetherEnough:放置纹章已满,请先进行聚合操作！
tank.emblem.togetherTypeFalse:放置纹章与前面纹章类型不一致！
tank.emblem.togetherNotEnough:放置纹章数量不足！
tank.emblem.togetherProtected:您确定消耗{0}点券保护纹章聚合？
tank.emblem.insertEnough:镶嵌纹章已满！
tank.emblem.proNameTxt:火攻,风攻,土攻,水攻,火抗,风抗,土抗,水抗,技能概率
tank.emblem.proNameTxt2:火攻,风攻,土攻,水攻,光攻,暗攻,火抗,风抗,土抗,水抗,光抗,暗抗
tank.emblem.emblemTxt:纹章
tank.emblem.selectEmblemTxt:然后双击选择您要镶嵌的纹章
tank.emblem.scrollTxt:卷轴
tank.emblem.selectScrollTxt:首先双击选择您要选择的卷轴
tank.emblem.sellEmblem:确认出售{0}，获得{1}圣晶
tank.emblem.makeTips:消耗物品数量不足!
tank.emblem.selectAlert:请选择打造纹章的类型!
tank.auction.emblem1:纹章卷轴
tank.auction.emblem2:纹章材料
tank.emblem.saleAlert:无法出售锁住的纹章
tank.emblem.lockAlert:该纹章已经锁定，不能进行该操作！
tank.emblem.proNameTxtType1:火攻
tank.emblem.proNameTxtType2:风攻
tank.emblem.proNameTxtType3:土攻
tank.emblem.proNameTxtType4:水攻
tank.emblem.proNameTxtType5:火抗
tank.emblem.proNameTxtType6:风抗
tank.emblem.proNameTxtType7:土抗
tank.emblem.proNameTxtType8:水抗
tank.emblem.proNameTxtType20:光攻
tank.emblem.proNameTxtType21:暗攻
tank.emblem.proNameTxtType30:光抗
tank.emblem.proNameTxtType31:暗抗
tank.emblem.proGrowNameTxtType1:火攻成长
tank.emblem.proGrowNameTxtType2:风攻成长
tank.emblem.proGrowNameTxtType3:土攻成长
tank.emblem.proGrowNameTxtType4:水攻成长
tank.emblem.proGrowNameTxtType5:火抗成长
tank.emblem.proGrowNameTxtType6:风抗成长
tank.emblem.proGrowNameTxtType7:土抗成长
tank.emblem.proGrowNameTxtType8:水抗成长
tank.emblem.proGrowNameTxtType20:光攻成长
tank.emblem.proGrowNameTxtType21:暗攻成长
tank.emblem.proGrowNameTxtType30:光抗成长
tank.emblem.proGrowNameTxtType31:暗抗成长


#啵咕大冒险
boguAdventure.view.gameOver:您已经完美的通关，重新开始可进行新一局游戏!
boguAdventure.view.hpNotEnough:生命值不足无法行动,复活可继续冒险!
boguAdventure.view.awardFrameText:注:重复走过的格子是无效的
boguAdventure.view.successfulWalkCount:成功走过{0}个格子
boguAdventure.view.openAwardError:你打开的格子数量与条件不符!
boguAdventure.view.awardComplete:你本轮已领取完所有奖励!
boguAdventure.view.affirmGet:确认要领取礼包？
boguAdventure.view.reviveText:生命值不足，是否消耗{0}点券进行复活?
boguAdventure.view.resetText:您本次还有奖励未领取,是否继续操作?
boguAdventure.view.resetAffirmText:重新开始需要{0}点券，确认要重新开始么?
boguAdventure.view.findMineText:使用探雷器需要{0}点券，确认要使用么?
boguAdventure.view.notSignOpenCell:不能标记已经打开的格子!
boguAdventure.view.walkTip:此格不能行走!
boguAdventure.view.isSign:此格已标记，请取消标记后再次尝试!
boguAdventure.view.acquireAward:你已领取完所有奖励，请从新开始游戏!
boguAdventure.view.signBtnTip:标记过的格子是不能走的，再次点击标记可取消(快捷键F)
boguAdventure.view.findMineBtnTip:自动探测啵咕周围格子中的一颗炸弹
boguAdventure.view.reviveBtnTip:复活后生命值回满，可继续当前冒险
boguAdventure.view.resetBtnTip:开始新一局的冒险
boguAdventure.view.notRevive:本次活动的重新开始次数已经用完
boguAdventure.view.limitRevive:剩余       次免费重新开始
boguAdventure.view.notFineMine:你的周围没有雷
boguAdventure.view.notStart:你还没有开始扫雷!
boguAdventure.view.activityOver:弹弹扫雷已经结束!
boguAdventure.view.notReset:你还没有开始大冒险，无须重新开始
boguAdventure.view.move:啵咕正在移动中,不能进行其他操作
boguAdventure.view.notReturn:啵咕正在移动中,不能退出
boguAdventure.view.freeFineMine:免费      次
boguAdventure.view.activityDate:活动时间: {0} 至 {1}



#欧皇秘宝
ddt.pairBox.main.timeTxt:活动剩余时间:{0}-{1}
ddt.pairBox.main.shopMoneyTxt:非酋印记: {0}
ddt.pairBox.card.resetTxt:是否花费{0}点券重置牌堆?
ddt.pairBox.card.txt1:不处于活动期间,不可进行此操作
ddt.pairBox.card.txt2:是否以{0}点券进行一次翻牌？
ddt.pairBox.card.txt3:将消耗{0} 点券
ddt.pairBox.card.txt4:当前拥有卷轴数 {0}
ddt.pairBox.card.txt5:可开启 {0} 张卡牌
ddt.pairBox.card.txt6:卷轴数不足
ddt.pairBox.card.txt7:消耗非酋印记数量：
ddt.pairBox.card.txt8:非酋印记不足
ddt.pairBox.card.txt9:每日限购{0}/{1}
ddt.pairBox.card.txt10:剩余时间：{0}天{1}小时
ddt.pairBox.card.txt11:本次界面不再提示
ddt.pairBox.card.txt12:已全部配对成功，请重置！
ddt.pairBox.card.txt13:{0}点券快捷购买


#图鉴
avatarCollection.selectOne:请至少勾选一套图鉴
avatarCollection.propertyNameTxt:攻击,防御,敏捷,幸运,伤害,护甲,血量
avatarCollection.propertyTipTitleTxt:集1/2图鉴效果,集齐图鉴效果
avatarCollection.rightView.descTxt:1.需要拥有对应服饰才能激活该图鉴。\n2.收集图鉴可获得属性，收集数量达到\n一半获得1/2属性，集齐获得完整属性。\n3.收集效果过期后可消耗荣誉续时。
avatarCollection.itemTip.typeNameTxt:类型
avatarCollection.itemTip.typeValueTxt:{0}图鉴
avatarCollection.helpTxt:图鉴说明
avatarCollection.itemTip.activityStatusTxt:激活状态：
avatarCollection.itemTip.needGoldTxt:激活消耗：{0}金币
avatarCollection.itemTip.placeTxt:产地：
avatarCollection.itemTip.activityTxt:已激活,未激活
avatarCollection.itemTip.placeValueStrTxt:商城、活动,副本
avatarCollection.timeView.txt:属性有效期：
avatarCollection.activeItem.promptTxt:确认消耗{0}金币激活当前图鉴(激活后物品绑定)？
avatarCollection.delayConfirmFrame.titleTxt:图鉴属性续时
avatarCollection.delayConfirmFrame.dayNameTxt:续时天数：
avatarCollection.delayConfirmFrame.promptTxt:当前勾选了{1}套属性\n续时一共需要消耗<FONT COLOR='#FF0000'>{0}</FONT>点券。
avatarCollection.delayConfirmFrame.noEnoughHonor:点券不足
avatarCollection.doActive.donnotHas:当前没有获得该{0}
avatarCollection.buyConfirm.tipTxt:确定以{0}点券购买当前{1}并激活图鉴？
avatarCollection.noTime.tipTxt:您的部分图鉴属性已经失效。|立即激活
avatarCollection.pay.tipTxt:请在行走大厅进行操作！
avatarCollection.select.decorate:服饰
avatarCollection.select.weapon:武器
avatarCollection.select.petTalent:天赋
avatarCollection.bagFull:背包已满，无法购买激活.

#吉普赛
tank.game.GameView.gypsyShopBought:恭喜玩家[{0}]在吉普赛商人处购得珍贵物品
tank.game.GameView.gypsyHonourConfirm:确认消耗{0}荣誉刷新一次商店物品？
tank.game.GameView.gypsyRmbConfirm:确认消耗{0}点券刷新一次商店物品？
tank.game.GameView.gypsyRMBTicketConfirm:确认消耗{0}点券购买？
tank.game.GameView.gypsyHelpTitle:帮助说明
gypsy.refresh.details:每日免费刷新时间 {0}
gypsy.open:吉普赛神秘商店开启。
gypsy.close:吉普赛神秘商店关闭。
gypsy.upOpen:吉普赛神秘商店尚未开启。
gypsy.honourNotEnough:荣誉不足，刷新失败!
ddt.consortiaBattle.buyConfirm.noAlertTxt:本次登录不再提示
gypsy.rmbNotEnough:点券不足，刷新失败!

#魔法衣橱
tank.magicWardrobe.showTis:点击查看部件
tank.ddthonor.loadMagicWardrobeError:加载魔法衣橱数据表失败

#BOSS战
tank.room.openBossTip.text:开启BOSS战将扣除 {0}点券.
ddt.dungeonRoom.bossBtn.tiptext:VIP7级可开启BOSS战

#批量打开
ddt.bag.item.openBatch.titleStr:批量打开
ddt.bag.item.openBatch.promptStr:请输入打开的数量

#VIP保管箱
tank.view.bagII.bank.exchangeText:切换
tank.vipbank.cellDoubleClick.msg:vip保管箱已满
tank.bagAndInfo.vipbank.sortBagClick:是否对vip保管箱整理后进行物品叠加？
tank.vipbank.txt1:需要清空灰色格子的道具才可以进行整理。

#月光宝盒
tank.moonlightTreasure.txt1:开启{0}次
tank.moonlightTreasure.txt2:{0}点券/{1}个
tank.moonlightTreasure.txt3:今日还可以使用礼金限购{0}个
tank.moonlightTreasure.txt4:宝盒碎片
tank.moonlightTreasure.txt5:宝盒碎片不足!
tank.moonlightTreasure.txt6:月光宝盒积分≥{0}才可上榜跨区排行榜前十
ddt.common.clickTooOften:点击频率过高
tank.moonlightTreasure.txt8:取出后将无法撤销，是否确认取出？
tank.moonlightTreasure.txt7:回收后将无法撤销，是否确认回收？
ddt.condiscount.view.time:活动时间：{0}-{1}
tank.moonlightTreasure.txt9:兑换时间：{0}-{1}


#绑定邮箱
ddt.mailBind.mailtips:请输入您的邮箱.
ddt.mailBind.codeTips:请输入邮箱验证码.

ddt.mailBind.mailtips:请输入您的邮箱.
ddt.mailBind.codeTips:请输入邮箱验证码.
ddt.mailBind.mailAddressError:邮箱验证失败!
ddt.mainBind.noReadAndAgree.msg:请先阅读用户协议!


#农场
ddt.farms.shopPayButton2:兑换
ddt.systemOpenPrompt.farmCropRipe:您的作物已经\n成熟了。
ddt.farms.convertPet:确认将<FONT COLOR='#FF0000'>{0}星宠物</FONT>转化为宠物之源及宠物精华液，\n转化后的宠物将被删除？
ddt.farms.fastForwardInfo:是否消耗{0}点券将作物成熟时间减少30分钟。
ddt.farms.fastForwardAllInfo:是否消耗{0}点券减少所有作物成熟时间30分钟。
ddt.farms.noFastForwardInfo:当前没有可加速的作物
ddt.farms.playerGiftPacksText:确认赠送中秋作物包，成功赠送后将扣除\n背包内1个未绑定中秋作物包？
ddt.farms.playerSpeakInfo:点我，赠送中秋作物包吧！
ddt.farms.refresh:刷新	
ddt.farms.desc:宠物说明	
ddt.farms.refreshPetsAlert:刷新宠物	
ddt.farms.helper.buyTxt:购买确定	
ddt.farms.helper.buyTxt1:道具不足是否到农场商城购买	
ddt.farms.releasePet:是否确定放生宠物<FONT COLOR='#FF0000'>{0}</FONT>么?放生后宠物将会消失，并且不能获得宠物精华液。	
ddt.farms.refreshPetsAlertContonet:刷新需要支付：       或者宠物刷新券一张.	
ddt.farms.refreshPetsNOAlert:今天不再提醒	
ddt.farms.adoptPetsAlertTitle:领养确定	
ddt.petsBag.readme:宠物说明	
ddt.petsBag.formTitleTxt:宠物形态
ddt.petsBag.LevAction:宠物{0}级可开启	
ddt.petsBag.VipAction:VIP11级时,可开启	
ddt.farms.adoptPetsAlertContonet:是否确定领养宠物？	
ddt.farms.refreshPetsAlertTitle:刷新确定	
ddt.farms.refreshPetsAlertContonetI:你已经刷新到四星宠物还要继续刷新吗？	
ddt.farms.refreshPetsLastTimes:后刷新	
ddt.farms.shopPayButton:购 买	
ddt.farms.shopPayAlert:您总共需要支付： 	
ddt.farms.petRefreshVolume:宠物刷新券	
ddt.farms.shopPayTitle:确认购买	
ddt.farms.composeComfirmNumPnlTitle:确认合成数量	
ddt.farms.confirmHelperMoneyAlertFrame.selectOneText:7天	
ddt.farms.confirmHelperMoneyAlertFrame.selectTwoText:一个月	
ddt.farms.confirmHelperMoneyAlertFrame.payMoneyShow:{0}点券	
ddt.farms.confirmComposeFoodAlertFrame.promptText:请选择需要合成的食品	
ddt.farm.farmNameTxt:{0}的农场	
ddt.farm.spreadTitle:田地扩充	
ddt.farm.spreadTime:扩充时间	
ddt.farm.spreadTime1:一周	
ddt.farm.spreadTime2:一个月	
ddt.farm.spreadTime3:扩充全部	
ddt.farm.spread.payMoney:您总共需要支付
ddt.farm.spread.Money:{0}点券	
ddt.farmHouse.readme:宠物食品合成说明	
ddt.farm.gainField.fail:收取农作物失败	
ddt.farm.gainField.success:收取农作物成功	
ddt.farm.payField.fail:开垦土地失败	
ddt.farm.payField.success:开垦土地成功	
ddt.farm.DoMature.fail:催熟失败	
ddt.farm.DoMature.success:催熟成功	
ddt.farm.seeding.fail:种植失败	
ddt.farm.seeding.success:种植成功	
ddt.farm.goFarm.internal:切换农场需要2秒间隔时间	
ddt.farm.farmHouse.click:仓库功能暂不开放	
ddt.farm.steal.farm:偷菜成功	
ddt.farm.goods.name:名称：{0}\n{1}	
ddt.farm.goods.grown:数量 {0}/{1}	
ddt.farm.goods.mini:成熟时间：{0}分钟	
ddt.farm.goods.houer:成熟时间：{0}小时{1}分钟	
ddt.farm.field.noGrown:尚未成熟，不能收割	
ddt.farm.helperList.fieldIndexText:田地序号	
ddt.farm.helperList.seedIDText:作物名称	
ddt.farm.helperList.fertilizerIDText:化肥	
ddt.farm.helperList.isAutoText:状态	
ddt.farm.helperList.pleaseSelect:请选择	
ddt.farm.helperList.yesAuto:{0}号田地设置为自动种植	
ddt.farm.helperList.noAuto:{0}号田地设置取消自动种植	
ddt.farm.helperItem.selectSeedFail:所选种子数为0个，不能设置为自动种植	
ddt.farm.helperItem.pleaseSelectSeed:请选择种子，再进行此操作	
ddt.fram.helperItem.Text:无	
ddt.fram.helperItem.Text1:({0})	
ddt.fram.helperItem.Text2:操作成功	
ddt.farm.helper.SetTxt3:请选择种子数量	
ddt.farm.helper.SetTxt4:请选择化肥数量	
ddt.farm.helper.SetTxt6:您背包无此类种子	
ddt.farm.helper.SetTxt7:您背包无此类化肥	
ddt.farm.helperItem.pleaseSelectSeedI:请选择化肥，再进行此操作	
ddt.farms.killCropComfirmNumPnlTitle:确认铲除作物	
ddt.farms.comfirmKillCropMsg:您确定要铲除{0}吗？	
ddt.farms.comfirmKillCropMsg2:您确定要铲除{0}吗？\n铲除作物操作会停止农场助手	
ddt.farm.helperItem.selectFertilizerFail:所选肥料数为0个，请先购买肥料	
ddt.farm.killCrop.success:铲除作物成功	
ddt.farm.helperShow.text1:使用前一定要看功能说明哦！~亲！	
ddt.farm.helperSelSeed.text:请选择你想种植的种子	
ddt.farm.helperSeltime.text:请选择你需要托管时间	
ddt.farm.helperNeedSeed.text:需要消耗种子：	
ddt.farm.helperGetSeed.text:获得宠物粮食：	
ddt.farm.helperNeedTime.text:剩余收获时间：	
ddt.farm.StartBtn.text:开  启	
ddt.farm.CloseBtn.text:停  止	
ddt.farm.beginFrame.title:开启确定	
ddt.farms.stopHelperComfirm:确认关闭助手	
ddt.farms.fieldBlockSeedTips:农场助手开启中...	
ddt.farms.stopHelperComfirmText:你确定要关闭农场助手吗？	
ddt.farm.beginFrame.expText:确定开始助手功能？	
ddt.farm.beginFrame.expText1:您缺少{0}个种子，是否花费{1}	
ddt.farm.beginFrame.expText2:快捷购买并开启？	
ddt.farm.beginFrame.expText3:（开启助手会自动铲除田地现有作物）	
ddt.farm.beginFrame.expText4:绑定点券不足！	
ddt.farm.helpBtn.text:？功能说明	
ddt.farm.killCrop.fail:铲除作物失败	
ddt.farm.helper.help.readme:种植助手说明	
ddt.farm.hepper.help.Reset:重置	
ddt.farm.hepper.help.Set:助手设置	
ddt.farms.helperMoneyComfirmPnlTitle:助手续费	
ddt.farms.helperMoneyComfirm.timeText:续费时间	
ddt.farms.helperMoneyComfirm.payText:您总共需要支付：	
ddt.farms.helperMoneyComfirmPnlSuccess:助手续费成功	
farm.componse.selected.me:请选择需要合成的食物	
ddt.Farm.friendList.item.enterFriend:只能进入好友农场
ddt.goFarm.need20:农场25级开放！	
ddt.farm.spread.discount:({0}折)	
ddt.farm.petScore:积分	
ddt.farm.possessScore:拥有积分:	
ddt.farm.affirmExchange:确认兑换
ddt.pets.pose1:主人你需要我的帮助么？	
ddt.pets.pose2:宠物粮食不但可以增加我的经验还可以增加我的快乐度。	
ddt.pets.pose3:升级之后的我会更加强大。	
ddt.farms.newPetsComew:新宠物小龙~~~\n\n来袭！！！	
ddt.farms.adoptItemAlertTitle:领取确定	
ddt.farms.adoptItemsAlertContonet:是否确定领取物品？	
ddt.farm.helperShow.text2:助手到期时间：	
ddt.farm.newPet.SkillTxt:技能	
ddt.farm.newPet.Come:稀有宠物来袭！	
ddt.farm.newPet.Desc:快去领养中心领养一只幼火红蚁吧！	
ddt.farm.newPet.LvTxt:等级:60	
ddt.farm.scoreNotEnough:您的积分不够！		
ddt.farms.petFreeRefresh:免费刷新({0})	
farm.viewx.farmBuyExpFrame.title:购买粮食	
farm.viewx.farmBuyExpFrame.explain:消耗    <FONT COLOR='#FF0000'>{0}</FONT>        点券,可购买 <FONT COLOR='#FCC036'>{1}</FONT> 个中级宠物粮,\n使用后提升宠物经验  <FONT COLOR='#FF0000'>{2}</FONT>  点.并增加快乐度	
farm.viewx.FarmBuyExpFrame.warning:今日的购买次数已经耗尽！
ddt.farm.arrange2:该好友土地可修整次数已达上限	
ddt.farm.arrange1:您今天已经修整过该好友的土地	
ddt.farm.arrange0:您已成功帮助该好友修整土地	
ddt.farm.arrange.tips:您今天已经修整过该好友的土地	
farm.seed1:初级作物	
farm.seed2:中级作物	
farm.seed3:高级作物	
farm.seed4:丰收之神	
ddt.farms.releasePet2:确定放生<FONT COLOR='#FF0000'>{0}星宠物</FONT>吗？放生后宠物将会消失，并且不能获得宠物精华液。	
ddt.farms.releasePet3:转化为宠物之源将扣除{0}点券	
farm.tree.nameText:时光神木
farm.tree.upgradeWater.checkBoxText:爱心全部注入
farm.tree.upgradeCondenser.checkBoxText:阳光全部注入
farm.tree.upgradeWaterBtn.tipsText:浇灌到一定等级后需要聚光才能继续成长
farm.tree.upgradeCondenserBtn.tipsText:聚光完成后可继续提升神木等级
task.taskView.quickUse.titleText:快捷使用
farm.tree.callPoultry.titleText:神木召唤
farm.tree.callPoultry.infoText:请选择怪物等级：
farm.friendListPanel.poultryText:养怪
farm.friendListPanel.stealBtnText:偷菜
farm.farmTree.waterNotNeedTipsTxt:爱心值不足！
farm.farmUpgrade.callBtnTxt:可召唤出蝶妖
farm.farmUpgrade.loveNumTipsTxt:爱心：用于浇灌时光神木
farm.farmUpgrade.waterUpgrade:浇灌成功,神木等级提升到{0}级!
farm.farmUpgrade.condenserUpgrade:聚光成功,神木将进入下一成长阶段!
farm.farmPoultry.fightTipsTxt:农场蝶妖正在战斗中
farm.farmTree.others:这是别人家的树……
farm.farmPoultry.chatBallText:我需要更多的爱心，才能获得重生的力量。
ddt.farm.spread.prosperity:繁荣度不足
ddt.farm.spread.describe:扩充需要繁荣度<FONT COLOR='#FF0000'>{0}</FONT>，是否确定扩地？

ddt.treasure.giveUp:今日您还可获得一次额外探宝机会看，您确定要放弃吗？	
ddt.treasure.over:探宝已结束，不可再进行操作	
ddt.treasure.warning:请好友修整五次还可额外获得一次探宝机会	
ddt.treasure.warning1:操作太频繁请稍后再试	

ddt.goodsTip.avatarState:<FONT COLOR='#ff0a0a'>图鉴(已激活)</FONT>,<FONT COLOR='#cfcfcf'>图鉴(未激活)</FONT>
ddt.goodsTip.titleState:<FONT COLOR='#ff0a0a'>称号(已激活)</FONT>,<FONT COLOR='#cfcfcf'>称号(未激活)</FONT>

//刻印
mark.chipProTemplateLoadError:刻印系统属性数据模板加载失败
mark.mark.hammerTemplateLoadError:刻印系统锤炼模版数据加载失败
mark.transferTemplateLoadError:刻印系统炼化模版数据加载失败
mark.pro31:攻击
mark.pro32:防御
mark.pro33:敏捷
mark.pro34:幸运
mark.pro37:血量
mark.pro36:伤害
mark.pro35:护甲
mark.pro101:魔攻
mark.pro102:魔抗
mark.pro1:暴击
mark.pro3:破甲
mark.pro7:暴伤
mark.pro9:速度
mark.pro5:必杀
mark.realValue:<font color="#ffa65d">{0}</font><font color="#1afa10">  +{1}</font>
mark.realBigValue:<b><font color="#ffe400">{0}</font><font color="#1afa10">  +{1}</font></b>
mark.percentValue:<font color="#ffa65d">{0}</font><font color="#1afa10">  +{1}%</font>
mark.error1:操作的刻印碎片不存在
mark.error2:此碎片已经被装备不可再装备
mark.error3:此碎片已经卸载不可再卸载
mark.error4:卖出的碎片列表为空
mark.error5:魂晶不足，无法进行锤炼
mark.error6:锤炼已达最高级，无法进行锤炼
mark.error7:选择的水晶不存在
mark.error8:此碎片不存在此属性
mark.error9:魂晶不足，无法进行炼化
mark.error10:炼化石不足，无法炼化
mark.suitPor:<font color="#ffa65d">{0}(<font color="#fffa00">{1}</font>)</font>
mark.hammer:刻印锤炼
mark.force:刻印增幅
mark.transfer:刻印炼化
mark.qualityName:+{0}  {1}  -{2}
mark.chipTipName:+{0} {1}
mark.property:{0}+{1}
mark.propertyP:{0}+{1}%
mark.propertyRange:{0}+{1}~{2}
mark.propertyForce:{0}+{1}<font color="#00ff00">(+{2})</font>
mark.propertyNext:+{0}
mark.propertyHammerRate:成功率：{0}%
mark.chip:刻印碎片
mark.crystal:增幅水晶
mark.characterRank:按品质排序
mark.defaultRank:默认排序
mark.starLvRank:按星级排序
mark.cancel:是否放弃本次炼化结果
mark.sell:是否出售全部选中碎片，获得{0}魂晶
mark.propertyName:{0}：
mark.propertyValue:<b>+{0}</b>
mark.suitName:{0}件套：
mark.openTips:{0}级开启（{1}级前获得的刻印碎片会存储在刻印背包中）
mark.noProps:<b><font color="#fffa00">暂无属性</font></b>
mark.noSuits:<b><font color="#fffa00">当前无套装效果</font></b>
mark.openForceTips:刻印锤炼到12级或以上才能开启
mark.openTransferTips:两星或以上碎片可进行炼化
mark.propsTipTile:刻印属性
mark.propCnt:({0}/6)
mark.tipBaseValue:<b><font color="#ffffff">{0}</font></b>
mark.tipHammerValue:<b><font color="#00ff00">(+{0})</font></b>
mark.tipAddValue:<b><font color="#0000ff">(+{0})</font></b>
mark.tipValue:{0}{1}{2}
mark.tipBaseName:<b><font color="#ffffff">{0}：</font></b>
mark.tipAddName:<b><font color="#ed9029">{0}：</font></b>
mark.bind:<b><font color="#ffffff">绑定</font></b>
mark.noBind:<b><font color="#bcbbbb">未绑定</font></b>
mark.transferAlert:当前选中属性已被增幅，炼化后将消失
mark.type1:虚空力量
mark.type2:指挥艺术
mark.type3:船长祝福
mark.type4:巫师赞礼
mark.type5:啵咕印记
mark.type6:邪神印记
mark.type7:堡垒印记
mark.type8:龙巢印记
mark.type9:运动印记
mark.type10:小鸡印记
mark.type11:时空印记
mark.equipScheme.txt0:方案一
mark.equipScheme.txt1:方案二
mark.equipScheme.txt2:方案三
mark.equipScheme.txt3:方案四
mark.equipScheme.txt4:方案五
mark.equipScheme.txt5:方案六
mark.equipScheme.txt6:方案七
mark.equipScheme.txt7:方案八
mark.equipScheme.txt8:方案九
mark.equipScheme.txt9:方案十
mark.equipScheme.add:新增方案
mark.switchScheme.faildMsg:背包空间不足，切换方案失败！
mark.sell.failMsg:该刻印已保存在方案内，不可出售!

tank.game.GameView.TreasureRoomViewConfirm1:是否花费{0}绑定点券购买魂晶盒X{1}，送探宝次数X{2}？
tank.game.GameView.TreasureRoomViewConfirm2:是否花费{0}点券购买魂晶盒X{1}，送探宝次数X{2}？
tank.game.GameView.countdownText:距离下次免费 {0}（{1}/{2}）
tank.game.GameView.TreasureRoomFrameTitle:藏宝室
catalog.chipTemplateLoadError:刻印系统图录碎片模版数据加载失败
catalog.focusLoadError:刻印系统图录条件效果模版数据加载失败
tank.mark.remainTimeTxt:(还剩{0}次)
tank.mark.hasNoTurn:您没有探宝次数！
tank.mark.cangbaoNotOpen:藏宝室功能暂时关闭！
mark.resetName.not:方案名字不合法或命名重复！
mark.resetName.tipTxt:方案重命名
tank.mark.leftview.switchTips:切换方案将自动保存所作变更
tank.mark.checkProtectTips:激活保护后,锤炼失败不消耗魂晶。
tank.mark.protectMoneyTips:激活保护锤炼，每次消耗{0}点券，锤炼失败不消耗魂晶!
tank.mark.allUnload:确认卸载当前方案的所有刻印?

catalogView.pro1:攻击
catalogView.pro2:防御
catalogView.pro3:敏捷
catalogView.pro4:幸运
catalogView.pro5:血量
catalogView.pro6:伤害
catalogView.pro7:护甲
catalogView.pro8:魔攻
catalogView.pro9:魔抗
catalogView.pro10:暴击
catalogView.pro11:破甲
catalogView.pro12:暴伤
catalogView.pro13:速度
catalogView.pro14:必杀
catalogView.countTxtMsg:({0}/{1})
catalogView.claimNameMsg0:{0}号碎片
catalogView.claimNameMsg1:基石碎片
catalogView.claimNameMsg2:碎片数量
catalogView.claimNameMsg3:{0}碎片
catalogView.activationAndTotalMsg:已组合：{0}/{1}




#葫芦瓶
GourdExpBottle.gourdHelpTipView.tipsText:当前经验储存进度:{0}/{1}
GourdExpBottle.gourdHelpTipView.titleMsg:注意事项
GourdExpBottle.gourdHelpTipView.precautionsMsg:葫芦瓶的经验储存功能可手动关闭，关闭功能后葫芦瓶将消失，此前储存的经验不再返还，若需再次使用经验储存功能，请使用新的葫芦瓶。确认关闭功能后不可撤销，请谨慎操作。
GourdExpBottle.gourdHelpTipView.stopButtonText:关闭功能
GourdExpBottle.gourdHelpTipView.stopMsg:关闭功能后未满的葫芦瓶将消失，
GourdExpBottle.gourdHelpTipView.useTips:使用经验瓶将获得<font color="#ff0000">大量经验</font>，是否确认使用？
tank.consortia.club.CreatConsortiaFrame.yourGrade:您的等级低于{0}级
panicBuying.vipLvlLimit:需要VIP等级达到{0}级
DecExpBottle.gourdHelpTipView.useTips:使用{0}后，将<font color="#ff0000">扣除{1}经验</font>，不产生降级，是否确认使用？


#啵咕转盘
tank.purrturntable.txt1:碎片
tank.purrturntable.txt2:转盘碎片
tank.purrturntable.txt3:点券/绑定点券
tank.purrturntable.txt4:今日免费次数剩余{0}次
tank.purrturntable.txt5:绑券限购：{0}/{1}
tank.purrturntable.txt6:扭扭币绑点限购次数不足
tank.purrturntable.txt7:普通追加成功
tank.purrturntable.txt8:普通追加失败
tank.purrturntable.txt9:保护追加成功
tank.purrturntable.txt10:保护追加失败
tank.purrturntable.txt11:您的扭扭币不足，是否立即购买？
tank.purrturntable.txt12:碎片不足，购买失败
tank.purrturntable.txt13:超出购买上限
tank.purrturntable.txt14:请等待动画播放完成
tank.purrturntable.txt15:成功购买了{0}扭扭币
tank.purrturntable.txt16:您还未选择奖励
tank.purrturntable.txt17:普通追加失败，随机会扣除1~2星，本轮结束，是否继续追加？
tank.purrturntable.txt18:本次登录不再提示
tank.purrturntable.txt20:您的扭扭币不足.


#神庙
home.temple.levelTxt:{0}级
home.temple.rectTipText0:拥有一星10级祝福可获得该属性
home.temple.rectTipText1:拥有二星10级祝福可获得该属性
home.temple.rectTipText2:拥有三星10级祝福可获得该属性
home.temple.rectTipText3:拥有四星10级祝福可获得该属性
home.temple.rectTipText4:拥有五星10级祝福可获得该属性
home.temple.rectTipText5:拥有六星10级祝福可获得该属性
home.temple.circleTipText0:一星祝福
home.temple.circleTipText1:二星祝福
home.temple.circleTipText2:三星祝福
home.temple.circleTipText3:四星祝福
home.temple.circleTipText4:五星祝福
home.temple.circleTipText5:六星祝福
home.temple.upgradeText:恭喜您获得了{0}星{1}级祝福。
home.temple.upgradeExpNumberText:献祭成功，增加{0}点经验！
home.temple.promoteTipText0:攻击升华
home.temple.promoteTipText1:防御升华
home.temple.promoteTipText2:敏捷升华
home.temple.promoteTipText3:幸运升华
home.temple.promoteTipText4:血量升华
home.temple.promoteTipText5:护甲升华
home.temple.promoteNotGoodsTips:道具数量不足！
HomeTemple.Handle.Immolation.ItemNotEnough:数量不足！
NecklaceStrength.Success



#快乐圣诞
tank.menu.christmasTiTle:冰雪圣诞																	
christmas.makingSnowmenTiTle:堆雪人																	
christmas.chooseRoom.chooseRoomTextLG:提示：雪地全天开放,每天可免费进入雪地时长为30分钟,时间不足时可在商城购买"冰雪圣诞时间卡"继续进入雪地。							
christmas.makingSnowmen.completeTxt.LG:完成度：																	
christmas.makingSnowmen.addSnowBnt.LG:添加雪块																	
christmas.makingSnowmen.selectedCheckButton.LG:双倍添加																
christmas.makingSnowmen.conditionTxt.LG:条件																	
christmas.makingSnowmen.rewardTxt.LG:奖励																	
christmas.list.countTxt.LG:堆{0}个雪人																	
christmas.curInfo.notfit:雪块不足																	
christmas.curInfo.upgradeExp:提升了{0}完成度																	
christmas.curInfo.succe:成功堆了{0}个雪人！																	
christmas.room.leaveroom:确定要离开雪地吗？																	
christmas.christmas.readme:冰雪圣诞说明																	
christmas.snowpack.double:  此次双倍添加雪块将扣除{0}点券,是否继续？															
christmas.snowpack.doubleMoney:(每一雪块消耗{0}点券可获得双倍效果)														
christmas.flushTimecut:{0}小时{1}分		
christmas.snowPacks.unfinished2:圣诞老爷爷将在每天 <FONT COLOR='#FF0000'>14：00</FONT> 后给大家限量派发圣诞礼物,每天堆完 <FONT COLOR='#FF0000'>3</FONT> 个雪人即可领取,数量有限,先到先得哦。		
christmas.snowPacks.unfinished:圣诞老爷爷正在派送礼物哦,每堆满 <FONT COLOR='#FF0000'>3</FONT> 个雪人就能领取奖励,你今天还有{0}次领取机会,数量有限先到先得哦！					
christmas.snowPacks.complete:恭喜您获得了圣诞老人的礼物，点击确定后发送圣诞惊喜礼包到玩家邮箱。												
christmas.snowPacks.full:您今天已经领了好多礼物了哦，留点给小伙伴们吧，明天再来哦！														
christmas.addNum.txt.LG:添加雪块数量：																	
christmas.receiveBtn.tip:领取8级礼包后开启																	
christmas.list.poorTxt.LG:(还差{0}个)																	
christmas.list.countTxt.last.LG:再堆{0}个雪人																	
christmas.poortTxt.OK.LG:(可以领取)																	
christmas.listItem.num:您当前的雪人不足以领取礼包																	
christmas.room.snowSpeakText:圣诞惊喜礼包快被\n小伙伴们抢光了，\n赶紧来领吧！														
christmas.curInfo.notfitNum:输入的雪块数量请大于0																	
christmas.buy.playingSnowman.volumes:免费进入雪地时间已经结束，是否花费{0}点券获得30分钟活动时间？												
christmas.room.santaSpeakFiveSecondsText:小伙伴们，抓紧时间堆雪人领大奖哦！
christmas.room.santaSpeakFiveSecondsText2:雪地的圣诞老人每天14：00会给大家派发礼物，你不要错过了哟！												
christmas.Icon.NoEnter:副本进入等级为十级！																	
christmas.room.packs.isFull:圣诞惊喜礼包已经被小伙伴们抢光了,明天抓住机会来领取吧！	
christmas.snowPacks.notOpen:"圣诞爷爷将在每晚<FONT COLOR='#FF0000'>{0}</FONT>后给大家限量派发<FONT COLOR='#FF0000'>300</FONT>份圣诞礼物，每天堆完<FONT COLOR='#FF0000'>25</FONT>个雪人即可领取，数量有限，先到先得哦！"	
chirstmas.addslowBt.txt:"添加雪块"	
chirstmas.doubleAddTipTxt:双倍添加雪块优先扣除绑定点券	

#项链
bagAndInfo.bag.NecklacePtetrochemicalView.title:项链炼化
bagAndInfo.bag.NecklacePtetrochemicalView.info:生命提升+{0}%
bagAndInfo.bag.NecklacePtetrochemicalView.Warning:紫晶石数量不足
bagAndInfo.bag.NecklacePtetrochemicalView.WarningI:项链已经炼化到最高级
bagAndInfo.bag.NecklacePtetrochemicalView.goodTip:(炼化{0})
bagAndInfo.bag.NecklacePtetrochemicalView.goodTip:(炼化{0})
bagAndInfo.bag.NecklacePtetrochemicalView.goodTipII:(+{0}%)
tank.necklace.des:注：炼化经验加成暂时没有加成。（炼化效果永久有效）
tank.necklace.artificeAddTxt:(炼化经验加成{0}%)
tank.necklace.pro0:攻击：
tank.necklace.pro1:防御：
tank.necklace.pro2:敏捷：
tank.necklace.pro3:幸运：
tank.necklace.pro4:血量：
tank.necklace.castMax:项链精铸已满级！
tank.necklace.castNotEnough:精铸材料不足！
tank.necklace.castLevle:(精铸{0})
tank.necklace.castOpenLevel:炼化12级开启

#护身符
tank.data.EquipType.equipAmulet:护身符
tank.equipAmulet.upgradeTip:距离100%升级成功还差 <FONT COLOR='#ffeb0d'>{0}</FONT> 次
tank.equipAmulet.phase:洗炼阶段:{0}
tank.equipAmulet.phaseTip:(升级护身符，提升洗炼阶段可以<FONT COLOR='#ffd21e'>增加洗炼属性最大值</FONT>)
tank.equipAmulet.grade:洗炼等级:{0}
tank.equipAmulet.gradeTip:(洗炼次数越多，洗炼等级越高，洗炼出现最小值越大)
tank.equipAmulet.power:战斗力:{0}
tank.equipAmulet.consumeStive:消耗粉尘：<FONT COLOR='#ffd21e'>{0}/{1}</FONT>
tank.equipAmulet.consumeMoney:消耗点券：<FONT COLOR='#ffd21e'>{0}</FONT>
tank.equipAmulet.fullGrade:当前护身符等级已满
tank.equipAmulet.notUpgradeGoods:没有升级所需要的物品
tank.equipAmulet.upgradeComplete:护身符成功升到{0}级，累计消耗{1}道具{2}个！
tank.equipAmulet.upgradeFail:护身符升级失败
tank.equipAmulet.notStive:当前粉尘不足，是否购买?
tank.equipAmulet.buyStiveCount:当天购买粉尘次数已满
tank.equipAmulet.buyStiveTip:是否消耗{0}点券,获得{1}点粉尘,今日还可购买{2}次.
tank.equipAmulet.buyFreeStive:每天首次购买免费，是否免费获得50点粉尘?
tank.equipAmulet.activateTip:本次洗炼需要消耗{0}粉尘,{1}点券,是否继续?
tank.equipAmulet.activateComplete:护身符洗炼成功
tank.equipAmulet.buyStiveComplete:购买成功
tank.equipAmulet.notReplace:当前没有新属性可替换
tank.equipAmulet.replaceComplete:替换成功
tank.equipAmulet.notEquipAmulet:未装备护身符
tank.equipAmulet.notActivateAmulet:未洗炼
tank.equipAmulet.notPropertyLock:当前没有属性可以锁定
tank.equipAmulet.activatePhase:当前洗炼阶段:{0}
tank.equipAmulet.activatePhaseTip:{0}上限:{1}
tank.equipAmulet.activateGrade:当前洗炼等级:{0}
tank.equipAmulet.activateGradeTip:{0}下限:{1}
tank.equipAmulet.activateGradeCount:升级洗炼次数：{0}/{1}
tank.equipAmulet.dated:当前装备护身符已过期
tank.equipAmulet.closeFrameTip:当前有洗炼属性未替换,是否放弃当前洗炼属性?
tank.equipAmulet.lockPropertyTip:最多只能锁定1个属性
tank.equipAmulet.maxGrade:Max
tank.equipAmulet.powerReplaceTip:洗炼新属性战斗力低于原属性战斗力，是否替换为新属性？
tank.equipAmulet.starTip:{0}星
tank.equipAmulet.propertyList:攻击,防御,敏捷,幸运,伤害,护甲
ddtKingGrade.grade:Lv.{0}



#启灵
equipGhost.ratioTxt:成功率：{0}%
equipGhost.ratioLowTxt:成功率：较低
equipGhost.material1:您未放入装备或武器!
equipGhost.material2:您未放入启灵珠!
equipGhost.upLevel:该装备已经启灵到最高级了!
equipGhost.tip:(+{0})
goodTip.propertyStr:{0}
equipGhost.luck:启灵符
equipGhost.stone:启灵珠
tank.equipGhost.tips:幸运值越高成功概率越高，幸运值满时启灵必定成功！
tank.equipGhost.lucky:幸运值:
store.Strength.BuyButtonText:购买

#打造
storeFine.suit.type:石,铜,银,金,玉
storeFine.effect.titleText:{0}装完整效果:
storeFine.tips.itemText:[{0}]装全属性:
storeFine.tips.titleText:[套装打造效果]
storeFine.effect.contentText:攻击+,防御+,敏捷+,幸运+,伤害+,护甲+,
storeFine.cell.titleText:帽子,眼镜,头发,脸饰,衣服,眼睛,套装,装饰,项链,婚戒,戒指,戒指,手镯,手镯
storeFine.cell.selectTips:请依次进行打造，当前可打造装备:{0}
storeFine.accomplish:套装已经升到了最满级!
storeFine.forgeTips:材料不足!
storeFine.forgeFail:打造失败!
storeFine.forge.state1:已完成
storeFine.forge.state2:打造中
storeFine.forge.state3:未打造
storeFine.forge.detail:注：每种套装打造出5件/10件/14件时，能大幅度提升属性。


#潜能
ddt.latentEnergy.frameTitle:装备潜能
ddt.latentEnergy.helpTitle:潜能说明
ddt.latentEnergy.oldProNoTxt:未激活
ddt.latentEnergy.proListTxt:攻击,防御,敏捷,幸运
ddt.latentEnergy.tipItemTxt:潜能激活   {0}+{1}
ddt.latentEnergy.tipRemainDateTxt:（潜能还剩{0}）
ddt.latentEnergy.noEnoughItem:能量石不足
ddt.latentEnergy.noNewProperty:无新属性可以替换
ddt.latentEnergy.bindTipTxt:潜能激活将会使您的装备成为绑定状态，是否继续操作？
forgeMainView.latentEnergy.equipTipTxt:首先请双击选择您要激活的装备
forgeMainView.latentEnergy.itemTipTxt:然后请双击选择您要使用的道具
tank.invalidTip.latentEnergy:您的<{0}>的潜能效果已失效。|前往激活潜能


#附魔
tank.store.enchant.tabTxt1:附魔
tank.store.enchant.tabTxt2:注灵
tank.store.enchant.magicAttrack:伤害  {0}%
tank.store.enchant.magicDefend:护甲  {0}%
tank.store.enchant.spiritMax:注灵已到满级！
tank.store.enchant.spiritNotEnough:注灵材料不足！
tank.store.enchant.spiritNotEquip:请选择注灵首饰！
store.enchantSpirit.use:如果背包中有绑定的材料会使您的<br>装备成为绑定状态，是否继续操作？
forgeMainView.enchant.equipTipTxt:首先请双击选择您要附魔/注灵的装备
enchant.valueTxt:附魔值
enchant.descTxt:附魔效果将在首饰熔炼等级+3后生效
enchant.levelTxt:{0}阶{1}级附魔
evolutionchant.levelTxt:进化等级Lv.{0}
enchant.addMagicAttackTxt:伤害：
enchant.addMagicDenfenceTxt:护甲：
enchant.noEquip:您未放入装备！
enchant.noSouleStone:您未放入灵魂石！
enchant.cannotUp: 该装备已经附魔到最高级.
enchant.help.title: 附魔说明
enchant.succes.txt:恭喜您附魔成功!


#恶魔宝藏
tank.devilTurn.loadGoodsItemError:加载恶魔转盘物品表失败
tank.devilTurn.loadBoxConvertError:加载恶魔转盘宝箱兑换表失败
tank.devilTurn.loadPointShopError:加载恶魔转盘积分兑换表失败
tank.devilTurn.loadRankAewardError:加载恶魔转盘排行奖励表失败
tank.devilTurn.sacrifice:购买灵魂石
tank.devilTurn.freeSacrifice:免费获取
tank.devilTurn.hasTurnCount:剩余次数：{0}次
tank.devilTurn.freeSacrificeTips:距离下次免费{0}（{1}/{2}）
tank.devilTurn.notFreeSacrificeTips:今日免费探险次数已用完
tank.devilTurn.notBoxConvert:当前宝珠不足，无法兑换
tank.devilTurn.notMallGetGift:当前次数不足，无法领取
tank.devilTurn.notOpenBox:当前宝箱不足
tank.devilTurn.lotteryTips:是否花费{0}点券,购买{1}个灵魂石并赠送{2}次探险？
tank.devilTurn.lotteryRunning:转盘转动中不可进行该操作!
tank.devilTurn.activateDate:活动时间:{0}
tank.devilTurn.diceTips:是否花费{0}点券继续投掷？
tank.devilTurn.diceContinueTips:本次继续消耗{0}点券/绑定点券
tank.devilTurn.abandonTips:放弃后将不可获得任何奖励，确认放弃？
tank.devilTurn.getTips:领取奖励后，将不可获得最高倍率奖励，确认领取？
tank.devilTurn.shopBuyText:消耗{0}：\n\n还可购买：
tank.devilTurn.shopBuyFail:{0}不足，购买失败!
tank.devilTurn.diceRuuning:正在掷骰子中，不能进行该操作!
tank.devilTurn.currentRate:当前倍率: x{0}
tank.devilTurn.activityFinish:活动结束
tank.devilTurn.expireDate:{0}天{1}时{2}分
tank.devilTurn.maxPhase:已经达到最高级别
tank.devilTurn.activityOver:本期恶魔宝藏活动已结束，请下期再参与
tank.devilTurn.activityClose:本期恶魔宝藏兑换时间已结束，请下期再参与
tank.devilTurn.notHasCount:您抽奖次数已用完！

tank.carnival.tip1:惊喜大奖可以获得奖池中5%的物品！
tank.carnival.tip2:幸运大奖可以获得奖池中20%的物品！
tank.carnival.tip3:至尊大奖可以获得奖池中50%的物品！！！
tank.carnival.tip4:福袋可以用来兑换道具哦~
tank.carnival.tip5:活动时间：{0}至{1}
tank.carnival.txt1:当前已有{0}人参加
tank.carnival.txt2:还剩{0}次即可开启大奖
tank.carnival.txt3:{0}{1}抽中了{2}获得{3}
tank.carnival.txt4:{0}{1}获得{2}
tank.carnival.tet5:惊喜大奖,幸运大奖,至尊大奖,超级大奖
tank.carnival.txt6:本轮抽奖已经结束！
tank.carnival.txt7:现在正在抽取{0}大奖
tank.carnival.txt8:福袋数量不足！
tank.carnival.tet9:惊喜大奖,幸运大奖,至尊大奖,福袋
tank.carnival.tet10:恭喜您抽中{0}

tank.compose.alert1:请放入需要铭刻的装备！
tank.compose.alert2:请选择需要铭刻的合成石！
tank.compose.alert3:合成石已经铭刻到最高等级！
tank.compose.alert4:请放入合成石提高铭刻概率！



#自选
tank.rewardSelect.txt1:已选择{0}/{1}个
tank.rewardSelect.txt2:已选数目小于可选数目，请继续选择！
tank.rewardSelect.txt3:已选数目大于可选数目！
tank.rewardSelect.txt4:从以下奖励中选择{0}个



#年兽
catchBeast.view.Title:新年活动
catchBeast.view.damageInfo:总伤害值：      {0}万       {1}万       {2}万      {3}万      {4}万
catchBeast.view.progressTips:造成的总伤害值为{0}
catchBeast.view.buyBuffTips:点击购买直接增加30000伤害及30000血量
catchBeast.view.buyBuffInfoText:是否消耗{0}点券直接增加30000伤害及30000血量
catchBeast.view.challengeInofText:是否立即挑战，购买BUFF可增加总伤害值获得更高奖励哦。
catchBeast.view.challengeTips:点击挑战年兽，可获得丰厚奖励
catchBeast.view.careInfo:注：购买BUFF可大幅提高伤害值，获得更好的奖励哦！
catchBeast.view.challengeNum:({0})


#洗髓
ddt.pets.washBone.cancelWash:取消本次操作
ddt.pets.washBone.alertConfigMsg:洗髓后宠物原成长值不再保留,确定继续进行洗髓操作?
ddt.pets.washBone:洗髓
ddt.pets.washBone.starEnoughfailtMsg:5星宠物才可进行洗髓操作!
ddt.pets.washBone.goodEnoughMsg:洗髓丹数量不足，洗髓失败!
ddt.pets.washBone.petHighGradeMsg:宠物{0}评级为{1}，确认洗髓？
ddt.pets.washBone.titleName:宠物洗髓
ddt.pets.washBone.proBloodGrow:血 量: +{0}
ddt.pets.washBone.proAttackGrow:攻 击: +{0}
ddt.pets.washBone.proDefenceGrow:防 御: +{0}
ddt.pets.washBone.proAgilityGrow:敏 捷: +{0}
ddt.pets.washBone.proLuckGrow:幸 运: +{0}
ddt.pets.washBone.proGrowTxt:成长值
ddt.pets.washBone.proMaxTxt:最大值
ddt.pets.washBone.descTxt:注:洗髓后宠物可以重新获得新的属性成长值，每次洗髓消耗一定数量洗髓丹.
ddt.pets.washBone.proLockMaxCountMsg:最多只允许锁定四条属性!
ddt.petbags.text.petGrowUptipTitleName:成长值
#战魂
ddt.gemstone.curInfo.title1:当前战魂属性
ddt.gemstone.curInfo.title2:当前部位战魂属性
ddt.gemstone.curInfo.descriptTxt:1、 装备更换战魂属性依然存在。 \n2、 对应位置没有装备也能升级战魂，战魂属性也存在。
ddt.gemstone.curInfo.pdescriptTxt1:当前等级：
ddt.gemstone.curInfo.pdescriptTxt2:下一等级：
ddt.gemstone.curInfo.pdescriptTxt3:提升属性：
ddt.gemstone.curInfo.pZbdescriptTxt:战魂等级总和：
ddt.gemstone.curInfo.pZbdescriptTxt1:战魂属性总和：
ddt.gemstone.curInfo.gemstoneKind:战魂种类
ddt.gemstone.curInfo.gemstoneEffect:战魂介绍
ddt.gemstone.curInfo.redGemstone:红色战魂（攻击）
ddt.gemstone.curInfo.bluGemstone:蓝色战魂（生命）
ddt.gemstone.curInfo.gesGemstone:绿色战魂（敏捷）
ddt.gemstone.curInfo.yelGemstone:黄色战魂（幸运）
ddt.gemstone.curInfo.purpleGemstone:紫色战魂（血量）
ddt.gemstone.curInfo.redGemstoneAtc:红色战魂({0}品) 攻击+{1}
ddt.gemstone.curInfo.bluGemstoneDef:蓝色战魂({0}品) 防御+{1}
ddt.gemstone.curInfo.gesGemstoneAgi:绿色战魂({0}品) 敏捷+{1}
ddt.gemstone.curInfo.yelGemstoneLuk:黄色战魂({0}品) 幸运+{1}
ddt.gemstone.curInfo.purpleGemstoneLuk:紫色战魂({0}品) 血量+{1}
ddt.gemstone.curInfo.goldenGemstoneAtc:(金魂) 攻击+{0}
ddt.gemstone.curInfo.goldenGemstoneDef:(金魂) 防御+{0}
ddt.gemstone.curInfo.goldenGemstoneAgi:(金魂) 敏捷+{0}
ddt.gemstone.curInfo.goldenGemstoneLuk:(金魂) 幸运+{0}
ddt.gemstone.curInfo.redGemstoneAtc2:红色战魂({0}品)
ddt.gemstone.curInfo.bluGemstoneDef2:蓝色战魂({0}品)
ddt.gemstone.curInfo.greGemstoneAgi2:绿色战魂({0}品)
ddt.gemstone.curInfo.yelGemstoneLuk2:黄色战魂({0}品)
ddt.gemstone.curInfo.goldenGemstone:(金魂)
ddt.gemstone.curInfo.AtcAdd:攻击 +{0}
ddt.gemstone.curInfo.DefAdd:防御 +{0}
ddt.gemstone.curInfo.AgiAdd:敏捷 +{0}
ddt.gemstone.curInfo.LukAdd:幸运 +{0}
ddt.gemstone.curInfo.GoldenAddAttack: 造成伤害 +2%
ddt.gemstone.curInfo.GoldenReduceDamage: 减伤 +2%
ddt.gemstone.curInfo.nextLevel:下一级效果
ddt.gemstone.curInfo.inactive:未激活
ddt.gemstone.curInfo.fullLevel:已满级
ddt.gemstone.curInfo.effDesc:1、战魂的最高等级为5级\n2、更好部位上的装备不会影响战魂的属性加成\n3、部位上没有装备，战魂加成属性同样生效
ddt.gemstone.curInfo.figGetTxt:战魂丹获取
ddt.gemstone.curInfo.shopTxt:商城购买
ddt.gemstone.curInfo.othersTxt:其他途径
ddt.gemstone.curInfo.notEquip:您还没选择装备部位
ddt.gemstone.curInfo.maxLevel:已经升到最高等级
ddt.gemstone.curInfo.notfit:战魂丹不足
ddt.gemstone.curInfo.succe:升级成功
ddt.gemstone.curInfo.noeq:所选择的部位没装备
ddt.gemstone.curInfo.upgradeExp:升级了{0}经验
ddt.gemstone.obtain.road:1、通关勇士秘境。	2、商城购买。
ddt.gemstone.obtain.effect:战魂丹的作用
ddt.gemstone.obtain.effectdescrip2:增加战魂经验，提升战魂属性。
ddt.gemstone.obtain.effDescri:1、每个战魂丹可以给战魂增加10点经验，战魂升级可提升战魂的属性。\n2、勾选全部注入可以简化操作，快捷购买窗可在商城购买战魂。
ddt.gemstone.obtain.num:数量:
ddt.gemstone.obtain.lijuan:点券:
ddt.gemstone.upview.hair:头发
ddt.gemstone.upview.face:脸饰
ddt.gemstone.upview.eye:眼睛
ddt.gemstone.upview.suit:套装
ddt.gemstone.upview.decorate:装饰
ddt.gemstone.upview.txt1:
ddt.gemstone.upview.txt2:
ddt.gemstone.upview.txt3:全部注入
ddt.gemstone.upview.txt4:战魂升级：
ddt.gemstone.upview.txt5:请选需要进行提升的装备
ddt.gemstone.upview.txt6:(鼠标点击放入战魂库中)
ddt.gemstone.alertFrame.title:输入战魂丹数量
ddt.gemstone.alertFrame.descript:请输入要注入的战魂丹数量：
gemstone.limitLevel.tipTxt:战魂系统{0}级开放
#宠物装备
tank.data.EquipType.petHead:宠物帽子
tank.data.EquipType.petTool:宠物武器
tank.data.EquipType.petClothing:宠物衣服
tank.data.EquipType.armShell:觉醒星级
#铭刻
tank.compose.alert1:请放入需要铭刻的装备！
tank.compose.alert2:请选择需要铭刻的合成石！
tank.compose.alert3:合成石已经铭刻到最高等级！
tank.compose.alert4:请放入合成石提高铭刻概率！



#保管箱
home.homeBankFrame.bankName0:①号仓库
home.homeBankFrame.bankName1:②号仓库
home.homeBankFrame.bankName2:③号仓库
home.homeBankFrame.bankName3:④号仓库
home.homeBankFrame.bankName4:⑤号仓库


tank.forthactive.txt1:累积充值{0}/{1}点券
tank.forthactive.txt2:使用{0}折券
tank.forthactive.txt3:未使用折扣券
tank.forthactive.txt4:虚空仓库
tank.forthactive.txt5:取出物品
tank.forthactive.txt9:你确定消耗{0}点券加入{1}{2}折拼团？
tank.forthactive.txt13:定金{0}点券
tank.forthactive.txt8:{0}后结束拼单
tank.forthactive.txt11:{0}发起拼单
tank.forthactive.txt14:尾款{0}点券
tank.forthactive.txt10:{0}邀请你加入{1}折团购，是否接受？
tank.forthactive.txt6:虚空仓库正在搬运物品中，请稍后操作！
tank.forthactive.txt15:全款{0}点券
tank.forthactive.txt7:{0}发起{1}折拼单
tank.forthactive.txt16:第{0}期：{1}开启付定金，{2}后结束定金支付，开启尾款
tank.forthactive.txt17:第{0}期：{1}开启尾款支付，{2}后结束尾款支付，未付尾款的玩家将会退回定金
tank.forthactive.txt18:第{0}期：{1}开启原价购买，{2}后结束预售
tank.forthactive.txt19:确认消耗{0}点券支付定金
tank.forthactive.txt12:钥匙数量不足！
tank.forthactive.txt20:确认消耗{0}点券支付尾款
tank.forthactive.txt21:确认消耗{0}点券全价购买
tank.forthactive.txt22:待支付尾款
tank.forthactive.txt26:是否花费{0}点券开启{1}订单？
tank.forthactive.txt32:尾款倒计时：
tank.forthactive.txt28:拼单成功
tank.forthactive.txt30:确定消耗{0}点券抢购{1}？
tank.forthactive.txt29:拼单失败
tank.forthactive.txt25:是否花费{0}点券进行快捷邀请？
tank.forthactive.txt27:是否花费{0}点券加入{1}{2}折订单团
tank.forthactive.txt31:定金倒计时：
tank.forthactive.txt24:玩家{0}发起{1}{2}折扣拼团，是否立即拼单？
tank.forthactive.txt23:已支付尾款
tank.forthactive.txt33:全款倒计时：
tank.forthactive.bag0:虚空仓库1
tank.forthactive.bag1:虚空仓库2
tank.forthactive.bag2:虚空仓库3
tank.forthactive.bag3:虚空仓库4
tank.forthactive.bag4:虚空仓库5
tank.forthactive.bag5:虚空仓库6

tank.firstrecharge.txt1:是否花费{0}金豆购买商品？
tank.firstrecharge.txt3:本周还可购买{0}
tank.firstrecharge.txt4:首冲券赠送
tank.firstrecharge.txt5:和我一起首充吧！
tank.firstrecharge.txt6:完成{0}首冲，可获得由{1}提供的{2}*1
tank.firstrecharge.txt2:金豆不足！
tank.firstrecharge.txt8:打开可获得
tank.firstrecharge.txt9:还需充值{0}点券
tank.firstrecharge.txt7:账号,本周,本月
tank.firstrecharge.txt1:是否花费{0}金豆购买商品？
tank.firstrecharge.txt3:本周还可购买{0}
tank.firstrecharge.txt4:首冲券赠送
tank.firstrecharge.txt5:和我一起首充吧！
tank.firstrecharge.txt6:完成{0}首冲，可获得由{1}提供的{2}*1
tank.firstrecharge.txt2:金豆不足！
tank.firstrecharge.txt8:打开可获得
tank.firstrecharge.txt9:还需充值{0}点券
tank.firstrecharge.txt7:账号,本周,本月
#海盗迷岛 
ddt.dungeonRoom.todayEnterNum:每日次数为三次,用尽之后不可进入第二天刷新！

#幸运转盘
tank.view.surpriseRoulette.quit:龙神转盘正在转动,请勿关闭
tank.view.surpriseRoulette.close:关闭幸运转盘,每次打开无论开启与否都将消耗一个宝箱
tank.view.RouletteBoxKey.Null:需要使用钥匙打开转盘.
tank.view.surpriseRoulette.Null:需要使用钥匙打开转盘.

#副本首杀
wonderfulActivityManager.btnTxt19:· 今日首杀-副本首杀
tank.data.EquipType.leagueBadge: 灵宠
<!-- 战令 -->
zhanling.tips1:您已成功开启普通战令!
zhanling.tips2:您已成功开启高级战令!
zhanling.tips3:您已成功开启超级战令!
#矿洞寻宝
tank.kongfuMeeting.txt18:可兑换:{0}
ddtMoney:礼金	
-----PSone1:原绑定点券-------
fashion.myRank:我的排名：{0}
fashion.noRank:我的排名：未上榜
tank.juneHoliday.txt2:我的积分：{0}
tank.caveloot.txt1:活动开始时间:
tank.caveloot.txt2:将消耗 {0} {1} 进行 {2} 次矿洞寻宝,是否确定？
tank.caveloot.txt3:挖矿{0}/{1}次奖励
tank.caveloot.txt4:全服进度达到{0}%
tank.caveloot.txt5:将该数量的物品放入背包(位置不足则以邮件方式收取)
tank.caveloot.txt6:1折算以上物品{0}个 可获得{1}个{2}矿石
tank.caveloot.txt7:免费寻宝
tank.caveloot.txt8:连续{0}次{1}矿洞寻宝没有获得({2})
tank.caveloot.txt9:进行{0}次{1}连挖矿
tank.caveloot.txt10:暂无临时物品
tank.caveloot.txt11:每日前三次免费{0}/3
tank.caveloot.txt12:将{0}背包内的所有临时物品进行折算，将获得{1}{2}矿石.是否确认？
tank.caveloot.txt13:是否确认将{0}背包内的所有临时物品放入背包(位置不足则以邮件方式收取)
tank.caveloot.txt14:消耗货币:{0}
tank.caveloot.txt15:挖矿<FONT COLOR='#fffab8'>{0}</FONT> /<FONT COLOR='#fffab8'>{1}</FONT>次奖励
tank.caveloot.txt16:挖矿<FONT COLOR='#ff0000'>{0}</FONT> /<FONT COLOR='#fffab8'>{1}</FONT>次奖励
tank.caveloot.txt17:勾选可以在寻宝中自动点击挖矿
tank.caveloot.txt18:可兑换:不限购
tank.caveloot.txt19:等级须达到{0}或者{1}级以上且贵宾等级达到{2}级
tank.caveloot.txt20:我正在看着你~
tank.caveloot.help:1.可以使用绑定点券参与初级矿洞，使用点券参与高级矿洞。\n2.初级矿洞每日前三次挖矿免费。\n3.获得的道具会存放在临时背包，可以选择折算矿石或者拾取至背包。\n4.单击临时背包的道具可以自由选择数量进行拾取或者折算矿石。\n5.初级矿洞每次挖矿会获得1积分，高级矿洞每次挖矿会获得2积分，积分可以参与积分排行。\n6.挖矿获得的矿石可以在矿洞商城购买道具。\n7.部分稀有道具会有保底奖励（每种仅限一次），累计指定次数未获得稀有道具，即可免费获得一次稀有道具。\n8.根据个人和全服挖矿次数，还可以领取挖矿进度奖励。\n9.活动结束后有一天展示期，可以进行奖励领取和商店兑换，无法继续挖矿。\n10.积分排行前十的玩家如果积分不足5000分，则只能领取到11名的奖励。
tank.caveloot.mineType1:初级
tank.caveloot.mineType2:高级
ddt.cardSystem.Groove.Exp:{0} / {1}

#圣物
tank.forceRelic.txt1:攻击
tank.forceRelic.txt2:防御
tank.forceRelic.txt3:{0}阶
tank.forceRelic.txt4:势力等级达到{0}解锁
tank.forceRelic.txt5:<FONT COLOR='#696969'>{0}</FONT>
tank.forceRelic.txt6:圣物
tank.forceRelic.txt7:修炼消耗:{0}
tank.forceRelic.txt8:{0}{1} 或
tank.forceRelic.txt9:{0}{1} 
tank.forceRelic.txt11:实际生效{0}
tank.forceRelic.txt13:是否消耗{0}个{1}进行升级？
tank.forceRelic.txt12: <FONT COLOR='{0}'>{1}(max:{2})</FONT>
tank.forceRelic.txt14:该圣物已装备
tank.forceRelic.txt15:同类型圣物最多装备{0}个
tank.forceRelic.txt16:当前没有可放置圣物的装备孔位
tank.forceRelic.txt17:是否解锁该圣物装备孔位？
tank.forceRelic.txt10:已满级
tank.forceRelic.txt18:根据宝册最低等级提升额外固定总宝册属性
tank.forceRelic.txt19:进度获取途径:\n1、获取对应宝册品质的圣物\n2、对应宝册品质的圣物进阶
tank.forceRelic.txt20:{0}圣物宝册{1}级
tank.forceRelic.txt21:{0}个
tank.forceRelic.txt22:圣物套装：
tank.forceRelic.txt23:数据错误
tank.forceRelic.txt24:(未激活)
tank.forceRelic.txt25:(已激活)
tank.forceRelic.txt26:是否消耗{0}个{1}进行升阶？
tank.forceRelic.txt27:是否消耗{0}个{1}进行词条增幅？
tank.forceRelic.txt28:{0}级
tank.forceRelic.txt29:粗糙,普通,优秀,精良,卓越,传说
tank.forceRelic.txt30:<FONT COLOR='{0}'>{1}</FONT>
tank.forceRelic.txt31:品质:{0}
tank.forceRelic.txt32:请选择需要增幅的词条
tank.forceRelic.txt33:打开势力圣物界面
tank.forceRelic.txt35:可用货币不足
tank.forceRelic.txt36:{0}数量不足，无法升级圣物！
tank.forceRelic.txt34:势力圣物\n1、装备不同圣物生效的属性不同；\n2、初始开放1个圣物孔，根据势力等级开放剩余圣物孔；\n3、圣物分为：攻击类型、防御类型，每种类型装备3个；\n4、每个圣物有1条基础属性词条；\n5、升级圣物，可以提升基础属性生效百分比；\n6、圣物进阶，可以解锁进阶属性/技能；\n7、圣物增幅，可以提升进阶属性/技能生效百分比\n8、装备3个攻/防圣物激活套装技能\n9、根据装备圣物的品质决定套装技能生效百分比；\n10、圣物可以通过月度副本、消耗圣物令兑换获得；\n11、重复圣物会自动分解成对应圣物的精粹，用来进阶/增幅\n12、当圣物进阶满级后，再次获得重复圣物自动分解成通用圣物；\n13、普通~精良圣物：普通通用圣物。卓越~传说圣物：稀有通用圣物；\n14、每获得一个圣物会点亮一个圣物图册，进阶圣物可以提升图册等级，获得属性提升；
tank.forceRelic.proType1:攻击
tank.forceRelic.proType2:防御
tank.forceRelic.proType3:敏捷
tank.forceRelic.proType4:幸运
tank.forceRelic.proType5:魔攻
tank.forceRelic.proType6:魔抗
tank.forceRelic.proType8:伤害
tank.forceRelic.proType7:护甲
tank.forceRelic.proType10:暴击
tank.forceRelic.proType9:血量
tank.forceRelic.proType11:韧性
tank.forceRelic.proType12:破甲
tank.forceRelic.proType13:免伤
tank.forceRelic.proType14:必杀
tank.forceRelic.proType16:爆伤
tank.forceRelic.proType17:守护
tank.forceRelic.proType18:速度
tank.forceRelic.proType15:必抗
tank.forceRelic.proType20:真伤
tank.forceRelic.proType19:体力
tank.forceRelic.proType21:真防
tank.forceRelic.proType22:火攻
tank.forceRelic.proType24:风攻
tank.forceRelic.proType23:火抗
tank.forceRelic.proType26:土攻
tank.forceRelic.proType28:水攻
tank.forceRelic.proType25:风抗
tank.forceRelic.proType29:水抗
tank.forceRelic.proType30:光攻
tank.forceRelic.proType27:土抗
tank.forceRelic.proType32:暗攻
tank.forceRelic.proType100:技能
tank.forceRelic.suitDesc2:当血量高于50%时，发起攻击20%概率触发使本回合攻击伤害的{0}%转化为血量，若血量以满则获得对应血量的护盾，护盾最大为玩家基础血量的30%，护盾存在回合2，技能CD5;
tank.forceRelic.proType31:光抗
tank.forceRelic.proType33:暗抗
tank.forceRelic.suitDesc1:玩家血量低于50%时，玩家发起攻击{0}%概率触发奇袭，受击方减伤越高承受越高额外伤害，技能CD5；
tank.forceRelic.txt36:暂未开放,敬请期待
tank.forcesBattle.txt43:<FONT COLOR='#fffab8'>{0}</FONT> /<FONT COLOR='#fffab8'>{1}</FONT>
tank.forcesBattle.txt44:<FONT COLOR='#ff0000'>{0}</FONT> /<FONT COLOR='#fffab8'>{1}</FONT>
tank.forcesBattle.skillT.txt12:Lv{0}