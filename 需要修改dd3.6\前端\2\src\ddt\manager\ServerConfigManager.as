package ddt.manager
{
   import ddt.data.ServerConfigInfo;
   import ddt.data.analyze.ServerConfigAnalyz;
   import flash.utils.Dictionary;
   import road7th.data.DictionaryData;
   import road7th.utils.DateUtils;
   
   public class ServerConfigManager
   {
      
      private static var _instance:ServerConfigManager;
      
      private static var privileges:Dictionary;
      
      public static const MARRT_ROOM_CREATE_MONET:String = "MarryRoomCreateMoney";
      
      public static const MISSION_RICHES:String = "MissionRiches";
      
      public static const VIP_EXP_NEEDEDFOREACHLV:String = "VIPExpNeededForEachLv";
      
      public static const HOT_SPRING_EXP:String = "HotSpringExp";
      
      public static const FIRSTRECHARGE_RETURN:String = "FirstChargeReturn";
      
      public static const VIP_PRIVILEGE:String = "VIPPrivilege";
      
      public static const PET_SCORE_ENABLE:String = "IsOpenPetScore";
      
      public static const PRIVILEGE_CANBUYFERT:String = "8";
      
      private var _serverConfigInfoList:DictionaryData;
      
      public function ServerConfigManager()
      {
         super();
      }
      
      public static function get instance() : ServerConfigManager
      {
         if(_instance == null)
         {
            _instance = new ServerConfigManager();
         }
         return _instance;
      }
      
      public function get RelicGroupSkill() : Array
      {
         var _loc1_:String = "1,3,20,1,1,2,2,3|2,3,30,1,2,3,4,5";
         if(this._serverConfigInfoList.hasKey("RelicGroupSkill"))
         {
            _loc1_ = this._serverConfigInfoList["RelicGroupSkill"].Value as String;
         }
         return _loc1_.split("|");
      }
      
      public function get RelicOpenEquipPos() : Array
      {
         var _local_1:String = "1,2,3,4,5,6";
         if(this._serverConfigInfoList.hasKey("RelicOpenEquipPos"))
         {
            _local_1 = this._serverConfigInfoList["RelicOpenEquipPos"].Value as String;
         }
         return _local_1.split(",");
      }
      
      public function get RelicBuffSubstatPrice() : Array
      {
         var _local_1:String = "1,1,20,20,0|1,2,30,30,0|1,3,40,40,0|2,1,20,20,0|2,2,30,30,0|2,3,40,40,0|3,1,20,0,20|3,2,30,0,30|3,3,40,0,40|4,1,20,0,20|4,2,30,0,30|4,3,40,0,40|5,1,20,0,20|5,2,30,0,30|5,3,40,0,40";
         if(this._serverConfigInfoList.hasKey("RelicBuffSubstatPrice"))
         {
            _local_1 = this._serverConfigInfoList["RelicBuffSubstatPrice"].Value as String;
         }
         return _local_1.split("|");
      }
      
      public function get RelicAdvanceToUpgrade() : Array
      {
         var _local_1:String = "0,5|1,8|2,10|3,10";
         if(this._serverConfigInfoList.hasKey("RelicAdvanceToUpgrade"))
         {
            _local_1 = this._serverConfigInfoList["RelicAdvanceToUpgrade"].Value as String;
         }
         return _local_1.split("|");
      }
      
      internal function getPlayerItemCount(itemId:String) : int
      {
         return 0;
      }
      
      public function get RelicUpgradeItem() : Array
      {
         var _local_1:String = "386298,50";
         if(this._serverConfigInfoList.hasKey("RelicUpgradeItem"))
         {
            _local_1 = this._serverConfigInfoList["RelicUpgradeItem"].Value as String;
         }
         return _local_1.split(",");
      }
      
      public function get weaponShellNormalAdd() : int
      {
         var _local_1:int = 0;
         if(this._serverConfigInfoList.hasKey("WeaponShellNormalAdd"))
         {
            _local_1 = int(this._serverConfigInfoList["WeaponShellNormalAdd"].Value.toString());
         }
         return _local_1;
      }
      
      public function getserverConfigInfo(_arg_1:ServerConfigAnalyz) : void
      {
         this._serverConfigInfoList = _arg_1.serverConfigInfoList;
      }
      
      public function get serverConfigInfo() : DictionaryData
      {
         return this._serverConfigInfoList;
      }
      
      public function get weddingMoney() : Array
      {
         return this.findInfoByName("MarryRoomCreateMoney").Value.split(",");
      }
      
      public function get MissionRiches() : Array
      {
         return this.findInfoByName("MissionRiches").Value.split("|");
      }
      
      public function get VIPExpNeededForEachLv() : Array
      {
         return this.findInfoByName("VIPExpNeededForEachLv").Value.split("|");
      }
      
      public function get HotSpringExp() : Array
      {
         return this.findInfoByName("HotSpringExp").Value.split(",");
      }
      
      public function findInfoByName(_arg_1:String) : ServerConfigInfo
      {
         return this._serverConfigInfoList[_arg_1];
      }
      
      public function getFirstRechargeRebateAndValue() : Array
      {
         var _local_1:Object = this.findInfoByName(FIRSTRECHARGE_RETURN);
         if(Boolean(_local_1))
         {
            return this.findInfoByName(FIRSTRECHARGE_RETURN).Value.split("|");
         }
         return [1,998];
      }
      
      public function get TreasureHuntCrossAwardRankConf() : Array
      {
         var _local_1:Array = [];
         if(this._serverConfigInfoList.hasKey("LuckStoneRankReward"))
         {
            _local_1 = this._serverConfigInfoList["LuckStoneRankReward"].Value.split("|");
         }
         return _local_1;
      }
      
      public function get emblemTotherRate() : Array
      {
         var _local_1:String = "500,600";
         if(this._serverConfigInfoList.hasKey("EmblemComposeRandom"))
         {
            _local_1 = this._serverConfigInfoList["EmblemComposeRandom"].Value;
         }
         return _local_1.split(",");
      }
      
      public function get eblemTotherProtectMoney() : int
      {
         var _local_1:int = 1000;
         if(this._serverConfigInfoList.hasKey("EmblemComposeMoney"))
         {
            _local_1 = int(this._serverConfigInfoList["EmblemComposeMoney"].Value);
         }
         return _local_1;
      }
      
      public function get boguAdventureFreeResetTimes() : int
      {
         var _local_1:int = 2;
         if(this._serverConfigInfoList.hasKey("BombFreeAutoResetTimes"))
         {
            _local_1 = int(this._serverConfigInfoList["BombFreeAutoResetTimes"].Value);
         }
         return _local_1;
      }
      
      public function get boguAdventureOpenLevel() : int
      {
         var _local_1:int = 20;
         if(this._serverConfigInfoList.hasKey("BombEnterLevel"))
         {
            _local_1 = int(this._serverConfigInfoList["BombEnterLevel"].Value);
         }
         return _local_1;
      }
      
      public function get boguAdventureAward() : Array
      {
         var _local_1:* = null;
         var _local_2:String = "1-1,11906|2-2,11906|3-3,11906|4-4,11906|5-5,11906|6-10,11906|11-200,11906|201-500,11906|501-1000,11906|1000-1000,11906";
         if(this._serverConfigInfoList.hasKey("BombAreaAward"))
         {
            _local_2 = this._serverConfigInfoList["BombAreaAward"].Value;
         }
         _local_1 = _local_2.split("|");
         return _local_1 || [];
      }
      
      public function get boguAdventureBeginTime() : String
      {
         var _local_1:* = null;
         if(this._serverConfigInfoList.hasKey("BombStartTime"))
         {
            _local_1 = this._serverConfigInfoList["BombStartTime"].Value;
         }
         return _local_1.substr(0,_local_1.length - 3);
      }
      
      public function get boguAdventureEndTime() : String
      {
         var _local_1:* = null;
         if(this._serverConfigInfoList.hasKey("BombEndTime"))
         {
            _local_1 = this._serverConfigInfoList["BombEndTime"].Value;
         }
         return _local_1.substr(0,_local_1.length - 3);
      }
      
      public function get PairBoxPriceConfig() : Array
      {
         var _local_1:String = "1,1,1";
         if(this._serverConfigInfoList.hasKey("PairBoxPriceConfig"))
         {
            _local_1 = this._serverConfigInfoList["PairBoxPriceConfig"].Value;
         }
         return _local_1.split(",");
      }
      
      public function get PairBoxLimitBuy() : int
      {
         var _local_1:int = 20;
         if(this._serverConfigInfoList.hasKey("PairBoxLimitBuy"))
         {
            _local_1 = int(this._serverConfigInfoList["PairBoxLimitBuy"].Value);
         }
         return _local_1;
      }
      
      public function get gyShopRefreshPrice() : int
      {
         var _local_1:int = 0;
         if(this._serverConfigInfoList.hasKey("MysteryShopMoneyRefreshValue"))
         {
            _local_1 = int(this._serverConfigInfoList["MysteryShopMoneyRefreshValue"].Value);
         }
         return _local_1;
      }
      
      public function get WarOrderPrice() : Array
      {
         var _local_1:String = "1,8,1000,800|2,88,5000,8800|3,688,15000,68800";
         if(this._serverConfigInfoList.hasKey("WarPassPriceConfig"))
         {
            _local_1 = this._serverConfigInfoList["WarPassPriceConfig"].Value;
         }
         return _local_1.split("|");
      }
      
      public function get WarOrderPetSetInfo() : Array
      {
         var _local_1:String = "1,140403_1120623_46465,150103_1120624_46466,190103_1120625_46467,160103_1120626_46468,200203_1121483_46469,230203_1123310_46470|2,140403_1120623_46471,150103_1120624_46472,190103_1120625_46473,160103_1120626_46474,200203_1121483_46475,230203_1123310_46476|3,140403_1120623_46477,150103_1120624_46477,190103_1120625_46477,160103_1120626_46477,200203_1121483_46477,230203_1123310_46477";
         if(this._serverConfigInfoList.hasKey("WarPassPetConfig"))
         {
            _local_1 = this._serverConfigInfoList["WarPassPetConfig"].Value;
         }
         return _local_1.split("|");
      }
      
      public function get WarPassPetShowConfig() : Array
      {
         var _local_1:String = "550,500,1|2750,2500,1|11000,2500,1|275,6,1|1375,6,1|5500,6,1";
         if(this._serverConfigInfoList.hasKey("WarPassPetShowConfig"))
         {
            _local_1 = this._serverConfigInfoList["WarPassPetShowConfig"].Value;
         }
         return _local_1.split("|");
      }
      
      public function get WarPassSeasonNeedCharge() : int
      {
         var _local_1:String = "50000";
         if(this._serverConfigInfoList.hasKey("WarPassSeasonNeedCharge"))
         {
            _local_1 = this._serverConfigInfoList["WarPassSeasonNeedCharge"].Value;
         }
         return int(_local_1);
      }
      
      public function get limitWarPassPrice() : int
      {
         var _local_1:String = "50000";
         if(this._serverConfigInfoList.hasKey("WarPassSeasonPrice"))
         {
            _local_1 = this._serverConfigInfoList["WarPassSeasonPrice"].Value;
         }
         return int(_local_1);
      }
      
      public function get limitWarPassBuyLvPrice() : int
      {
         var _local_1:String = "50000";
         if(this._serverConfigInfoList.hasKey("WarPassSeasonBuyLevelPrice"))
         {
            _local_1 = this._serverConfigInfoList["WarPassSeasonBuyLevelPrice"].Value;
         }
         return int(_local_1);
      }
      
      public function get limitWarPassCurSeasonID() : int
      {
         var _local_1:String = "1";
         if(this._serverConfigInfoList.hasKey("WarPassSeasonID"))
         {
            _local_1 = this._serverConfigInfoList["WarPassSeasonID"].Value;
         }
         return int(_local_1);
      }
      
      public function get limitWarPassSeasonEndID() : int
      {
         var _local_1:String = "0";
         if(this._serverConfigInfoList.hasKey("WarPassSeasonEndID"))
         {
            _local_1 = this._serverConfigInfoList["WarPassSeasonEndID"].Value;
         }
         return int(_local_1);
      }
      
      public function get limitWarPassSeasonEndTime() : Date
      {
         return DateUtils.getDateByStr(this.findInfoByName("WarPassSeasonEndTime").Value);
      }
      
      public function get limitWarPassSeasonValidDays() : int
      {
         var _local_1:String = "45";
         if(this._serverConfigInfoList.hasKey("WarPassSeasonValidDays"))
         {
            _local_1 = this._serverConfigInfoList["WarPassSeasonValidDays"].Value;
         }
         return int(_local_1);
      }
      
      public function get limitWarPassQuitConfig() : String
      {
         var _local_1:String = "0-0,66|1-14,50|15-30,0";
         if(this._serverConfigInfoList.hasKey("WarPassQuitConfig"))
         {
            _local_1 = this._serverConfigInfoList["WarPassQuitConfig"].Value;
         }
         return _local_1;
      }
      
      public function get vipBankBagConf() : Array
      {
         var _local_1:String = "2,40|4,60|6,80|8,100";
         if(this._serverConfigInfoList.hasKey("VIP11BagVIPLevelToBagCount"))
         {
            _local_1 = this._serverConfigInfoList["VIP11BagVIPLevelToBagCount"].Value;
         }
         return _local_1.split("|");
      }
      
      public function get moonlightCountRewards() : Array
      {
         var _local_1:String = "20,14562,1,1|100,14562,2,1|500,14562,2,1|1000,14562,2,1|1500,14562,2,1|3000,14562,2,1";
         if(this._serverConfigInfoList.hasKey("MoonlightBoxRewardsTemplate"))
         {
            _local_1 = this._serverConfigInfoList["MoonlightBoxRewardsTemplate"].Value as String;
         }
         return _local_1.split("|");
      }
      
      public function get moonlightDrawCountConfig() : Array
      {
         var _local_1:String = "1,200,500";
         if(this._serverConfigInfoList.hasKey("MoonlightBoxOpenTimes"))
         {
            _local_1 = this._serverConfigInfoList["MoonlightBoxOpenTimes"].Value as String;
         }
         return _local_1.split(",");
      }
      
      public function get moonlightBuyPriceConfig() : Array
      {
         var _local_1:String = "1,200|100,20000|500,100000";
         if(this._serverConfigInfoList.hasKey("MoonlightBoxPrice"))
         {
            _local_1 = this._serverConfigInfoList["MoonlightBoxPrice"].Value as String;
         }
         return _local_1.split("|");
      }
      
      public function get moonlightBuyLimit() : int
      {
         var _local_1:int = 100;
         if(this._serverConfigInfoList.hasKey("MoonlightBoxLimitBuy"))
         {
            _local_1 = int(this._serverConfigInfoList["MoonlightBoxLimitBuy"].Value);
         }
         return _local_1;
      }
      
      public function get MoonlightBoxCrossReward() : Array
      {
         var _local_1:String = "1-20,46127|21-100,46128";
         if(this._serverConfigInfoList.hasKey("MoonlightBoxCrossReward"))
         {
            _local_1 = String(this._serverConfigInfoList["MoonlightBoxCrossReward"].Value);
         }
         return _local_1.split("|");
      }
      
      public function get MoonlightBoxMinScoreTool() : int
      {
         var _local_1:int = 1000;
         if(this._serverConfigInfoList.hasKey("MoonlightBoxMinScoreTool"))
         {
            _local_1 = int(this._serverConfigInfoList["MoonlightBoxMinScoreTool"].Value);
         }
         return _local_1;
      }
      
      public function get signInDobuleBeginTime() : String
      {
         var _local_1:String = "2018-06-12 00:00:00";
         if(this._serverConfigInfoList.hasKey("SignRateBeginTime"))
         {
            _local_1 = this._serverConfigInfoList["SignRateBeginTime"].Value as String;
         }
         return _local_1;
      }
      
      public function get signInDobuleEndTime() : String
      {
         var _local_1:String = "2018-6-20 23:59:59";
         if(this._serverConfigInfoList.hasKey("SignRateEndTime"))
         {
            _local_1 = this._serverConfigInfoList["SignRateEndTime"].Value as String;
         }
         return _local_1;
      }
      
      public function get mailBindOpenState() : Boolean
      {
         var _local_1:Boolean = false;
         if(this._serverConfigInfoList.hasKey("MailBindOpen"))
         {
            _local_1 = Boolean(this._serverConfigInfoList["MailBindOpen"].Value);
         }
         return _local_1;
      }
      
      public function get mailBindAward() : Array
      {
         var _local_1:Array = [];
         if(this._serverConfigInfoList.hasKey("MailBindAwardConfig"))
         {
            _local_1 = this._serverConfigInfoList["MailBindAwardConfig"].Value.split("|");
         }
         return _local_1;
      }
      
      public function get petScoreEnable() : Boolean
      {
         var _local_1:ServerConfigInfo = this.findInfoByName(PET_SCORE_ENABLE);
         if(Boolean(_local_1))
         {
            return _local_1.Value.toLowerCase() != "false";
         }
         return false;
      }
      
      public function getPrivilegeMinLevel(_arg_1:String) : int
      {
         var _local_2:Object = null;
         var _local_3:int = 0;
         var _local_4:Array = null;
         var _local_5:String = null;
         var _local_6:String = null;
         if(privileges == null)
         {
            _local_2 = this.findInfoByName(VIP_PRIVILEGE);
            _local_3 = 1;
            _local_4 = String(_local_2.Value).split("|");
            privileges = new Dictionary();
            for each(_local_5 in _local_4)
            {
               for each(_local_6 in _local_5.split(","))
               {
                  privileges[_local_6] = _local_3;
               }
               _local_3++;
            }
         }
         return int(privileges[_arg_1]);
      }
      
      public function get markOpenLevel() : int
      {
         var _local_1:int = 10;
         if(this._serverConfigInfoList.hasKey("EngraveLimitLevel"))
         {
            _local_1 = int(this._serverConfigInfoList["EngraveLimitLevel"].Value);
         }
         return _local_1;
      }
      
      public function get MarkEquipSchemePrice() : Array
      {
         var _local_1:Array = null;
         if(this._serverConfigInfoList.hasKey("AddEngraveEquipSchemeCostMoney"))
         {
            _local_1 = (this._serverConfigInfoList["AddEngraveEquipSchemeCostMoney"].Value as String).split(",");
         }
         return _local_1;
      }
      
      public function get MarkHammerPrice() : int
      {
         var _local_1:int = 1000;
         if(this._serverConfigInfoList.hasKey("EngraveTemperMoney"))
         {
            _local_1 = int(this._serverConfigInfoList["EngraveTemperMoney"].Value);
         }
         return _local_1;
      }
      
      public function get markSealInjectCostArr() : Array
      {
         var _local_1:String = "1,100|2,200|3,300|4,400|5,500|6,600";
         if(this._serverConfigInfoList.hasKey("SigilRanProUseSoulCrystalConfig"))
         {
            _local_1 = String(this._serverConfigInfoList["SigilRanProUseSoulCrystalConfig"].Value);
         }
         return _local_1.split("|");
      }
      
      public function get markSealCompoundCostArr() : Array
      {
         var _local_1:String = "1,100|2,200|3,300|4,400|5,500|6,600";
         if(this._serverConfigInfoList.hasKey("SpellsUpgradeUseSoulCrystalConfig"))
         {
            _local_1 = String(this._serverConfigInfoList["SpellsUpgradeUseSoulCrystalConfig"].Value);
         }
         return _local_1.split("|");
      }
      
      public function get markSealProRangeArr() : Array
      {
         var _local_1:String = "0.2,0.4,0.6,0.8,0.9,1";
         if(this._serverConfigInfoList.hasKey("Sigil3"))
         {
            _local_1 = String(this._serverConfigInfoList["Sigil3"].Value);
         }
         return _local_1.split(",");
      }
      
      public function get getEngraveVaults() : Array
      {
         var _local_1:String = null;
         var _local_2:Array = [];
         if(this._serverConfigInfoList.hasKey("EngraveVaultsConfig"))
         {
            _local_1 = String(this._serverConfigInfoList["EngraveVaultsConfig"].Value);
            _local_2.push((_local_1.split("|")[0] as String).split(","));
            _local_2.push((_local_1.split("|")[1] as String).split(","));
         }
         return _local_2;
      }
      
      public function get getEngraveVaultsFreeTimes() : int
      {
         var _local_1:int = 0;
         if(this._serverConfigInfoList.hasKey("EngraveVaultsFreeTimes"))
         {
            _local_1 = int(this._serverConfigInfoList["EngraveVaultsFreeTimes"].Value);
         }
         return _local_1;
      }
      
      public function get EngraveSaleStarConfig() : int
      {
         var _local_1:int = 200;
         if(this._serverConfigInfoList.hasKey("EngraveSaleStarConfig"))
         {
            _local_1 = int(this._serverConfigInfoList["EngraveSaleStarConfig"].Value);
         }
         return _local_1;
      }
      
      public function get chrismasTypeConfig() : int
      {
         var _local_1:int = 2;
         if(this._serverConfigInfoList.hasKey("FestivalTypeConfig"))
         {
            _local_1 = int(this._serverConfigInfoList["FestivalTypeConfig"].Value);
         }
         return _local_1;
      }
      
      public function get EngraveSaleTemperConsumeConfig() : int
      {
         var _local_1:int = 60;
         if(this._serverConfigInfoList.hasKey("EngraveSaleTemperConsumeConfig"))
         {
            _local_1 = int(this._serverConfigInfoList["EngraveSaleTemperConsumeConfig"].Value);
         }
         return _local_1;
      }
      
      public function get equipAmuletBuyDustMax() : int
      {
         var _local_1:int = 20;
         if(this._serverConfigInfoList.hasKey("AmuletBuyDustMax"))
         {
            _local_1 = int(this._serverConfigInfoList["AmuletBuyDustMax"].Value);
         }
         return _local_1;
      }
      
      public function get equipAmuletActiveMoney() : int
      {
         var _local_1:int = 20;
         if(this._serverConfigInfoList.hasKey("AmuletActiveMoney"))
         {
            _local_1 = int(this._serverConfigInfoList["AmuletActiveMoney"].Value);
         }
         return _local_1;
      }
      
      public function get equipAmuletBuyDustConfig() : Array
      {
         var _local_1:String = "30|150|150";
         if(this._serverConfigInfoList.hasKey("AmuletBuyDustCountAndNeedMoney"))
         {
            _local_1 = String(this._serverConfigInfoList["AmuletBuyDustCountAndNeedMoney"].Value);
         }
         return _local_1.split("|");
      }
      
      public function get devilTurnCfgBox() : Array
      {
         var _local_1:Array = [];
         if(this._serverConfigInfoList.hasKey("DevilTreasureCfgBox"))
         {
            _local_1 = this._serverConfigInfoList["DevilTreasureCfgBox"].Value.split("|");
         }
         return _local_1;
      }
      
      public function get devilTurnTemplateID() : int
      {
         var _local_1:int = 0;
         if(this._serverConfigInfoList.hasKey("DevilTreasureTemplateID"))
         {
            _local_1 = int(this._serverConfigInfoList["DevilTreasureTemplateID"].Value);
         }
         return _local_1;
      }
      
      public function get devilTurnBeginDate() : String
      {
         var _local_1:* = null;
         if(this._serverConfigInfoList.hasKey("DevilTreasureBeginDate"))
         {
            _local_1 = String(this._serverConfigInfoList["DevilTreasureBeginDate"].Value);
         }
         return _local_1;
      }
      
      public function get devilTurnEndDate() : String
      {
         var _local_1:* = null;
         if(this._serverConfigInfoList.hasKey("DevilTreasureEndDate"))
         {
            _local_1 = String(this._serverConfigInfoList["DevilTreasureEndDate"].Value);
         }
         return _local_1;
      }
      
      public function get devilTurnLotteryOneCost() : int
      {
         var _local_1:int = 0;
         if(this._serverConfigInfoList.hasKey("DevilTreasureOneCost"))
         {
            _local_1 = int(this._serverConfigInfoList["DevilTreasureOneCost"].Value);
         }
         return _local_1;
      }
      
      public function get devilTurnLotteryTenCost() : int
      {
         var _local_1:int = 0;
         if(this._serverConfigInfoList.hasKey("DevilTreasureTenCost"))
         {
            _local_1 = int(this._serverConfigInfoList["DevilTreasureTenCost"].Value);
         }
         return _local_1;
      }
      
      public function get devilTurnTotalJackpot() : int
      {
         var _local_1:int = 0;
         if(this._serverConfigInfoList.hasKey("DevilTreasurePrizePoolMax"))
         {
            _local_1 = int(this._serverConfigInfoList["DevilTreasurePrizePoolMax"].Value);
         }
         return _local_1;
      }
      
      public function get devilTurnFreeLotteryCount() : int
      {
         var _local_1:int = 0;
         if(this._serverConfigInfoList.hasKey("DevilTreasureFreeLotteryCount"))
         {
            _local_1 = int(this._serverConfigInfoList["DevilTreasureFreeLotteryCount"].Value);
         }
         return _local_1;
      }
      
      public function get devilTurnOpenLevelLimit() : int
      {
         var _local_1:int = 20;
         if(this._serverConfigInfoList.hasKey("DevilTreasLevelLimit"))
         {
            _local_1 = int(this._serverConfigInfoList["DevilTreasLevelLimit"].Value);
         }
         return _local_1;
      }
      
      public function get CarnivalConvertItem() : Array
      {
         var _local_1:String = "1,11901-100,100|2,11901-50,70|3,11901-30,40|4,11901-100,120|5,11901-50,150";
         if(this._serverConfigInfoList.hasKey("CarnivalConvertItem"))
         {
            _local_1 = this._serverConfigInfoList["CarnivalConvertItem"].Value;
         }
         return _local_1.split("|");
      }
      
      public function get CarnivalStartDate() : String
      {
         var _local_1:String = "2021-01-01 00:00:00";
         if(this._serverConfigInfoList.hasKey("CarnivalBeginTime"))
         {
            _local_1 = this._serverConfigInfoList["CarnivalBeginTime"].Value;
         }
         return _local_1;
      }
      
      public function get CarnivalEndDate() : String
      {
         var _local_1:String = "2021-01-01 00:00:00";
         if(this._serverConfigInfoList.hasKey("CarnivalEndTime"))
         {
            _local_1 = this._serverConfigInfoList["CarnivalEndTime"].Value;
         }
         return _local_1;
      }
      
      public function get activityEnterNum() : int
      {
         if(this._serverConfigInfoList.hasKey("QXGameLimitCount"))
         {
            return this._serverConfigInfoList["QXGameLimitCount"].Value;
         }
         return 0;
      }
      
      public function get FightSpiritLevelAddDamage() : Array
      {
         var _local_1:Object = this.findInfoByName("FightSpiritLevelAddDamage");
         if(Boolean(_local_1))
         {
            return _local_1.Value.split("|");
         }
         return [];
      }
      
      public function get goldGemstoneLevel() : Array
      {
         var _local_1:* = 0;
         var _local_2:Array = [];
         _local_1 = 0;
         while(_local_1 < this.FightSpiritLevelAddDamage.length)
         {
            _local_2.push(this.FightSpiritLevelAddDamage[_local_1].split(",")[0]);
            _local_1++;
         }
         return _local_2;
      }
      
      public function get dayActiveWorshipLimit() : int
      {
         var _local_1:int = 10;
         if(this._serverConfigInfoList.hasKey("LuckSignActivePoint"))
         {
            _local_1 = int(this._serverConfigInfoList["LuckSignActivePoint"].Value);
         }
         return _local_1;
      }
      
      public function get dayActiveResetTotal() : int
      {
         var _local_1:int = 1;
         if(this._serverConfigInfoList.hasKey("EveryDayActiveResetTargetCount"))
         {
            _local_1 = int(this._serverConfigInfoList["EveryDayActiveResetTargetCount"].Value);
         }
         return _local_1;
      }
      
      public function get UltimateLuxuryInviteMoney() : int
      {
         var _local_1:int = 10;
         if(this._serverConfigInfoList.hasKey("UltimateLuxuryInviteMoney"))
         {
            _local_1 = int(this._serverConfigInfoList["UltimateLuxuryInviteMoney"].Value);
         }
         return _local_1;
      }
      
      public function get UltimateLuxuryDiscount() : int
      {
         var _local_1:int = 100;
         if(this._serverConfigInfoList.hasKey("UltimateLuxuryDiscount"))
         {
            _local_1 = int(this._serverConfigInfoList["UltimateLuxuryDiscount"].Value);
         }
         return _local_1;
      }
      
      public function get UltimateLuxuryDeposit() : Array
      {
         var _local_1:String = "3,1,1,1,3,5";
         if(this._serverConfigInfoList.hasKey("UltimateLuxuryDeposit"))
         {
            _local_1 = this._serverConfigInfoList["UltimateLuxuryDeposit"].Value;
         }
         return _local_1.split(",");
      }
      
      public function get dayActiveRewardTemplate() : String
      {
         var _local_1:String = "30,11901,10,30,1|60,11902,20,30,1|90,-1100,15,30,1|120,11905,10,30,1|150,11906,100,30,1";
         if(this._serverConfigInfoList.hasKey("EveryDayActiveRewardTemplate"))
         {
            _local_1 = String(this._serverConfigInfoList["EveryDayActiveRewardTemplate"].Value);
         }
         return _local_1;
      }
      
      public function get dayActiveComplateTaskNeedMoney() : Array
      {
         var _local_1:String = "100,200,300,400";
         if(this._serverConfigInfoList.hasKey("EveryFinishPoints"))
         {
            _local_1 = this._serverConfigInfoList["EveryFinishPoints"].Value;
         }
         return _local_1.split(",");
      }
      
      public function get pyramidTopMinMaxPoint() : Array
      {
         var _local_1:Array = null;
         var _local_2:ServerConfigInfo = this.findInfoByName("PyramidTopPoint");
         if(Boolean(_local_2))
         {
            _local_1 = _local_2.Value.split("|");
            return new Array(_local_1[0],_local_1[_local_1.length - 1]);
         }
         return new Array(0,0);
      }
      
      public function get godCardDailyFreeCount() : int
      {
         if(Boolean(this._serverConfigInfoList["GodCardDailyFreeCount"]))
         {
            return int(this._serverConfigInfoList["GodCardDailyFreeCount"].Value);
         }
         return 0;
      }
      
      public function get godCardOpenOneTimeMoney() : int
      {
         if(Boolean(this._serverConfigInfoList["GodCardOpenOneTimeMoney"]))
         {
            return int(this._serverConfigInfoList["GodCardOpenOneTimeMoney"].Value);
         }
         return 0;
      }
      
      public function get godCardOpenFiveTimeMoney() : int
      {
         if(Boolean(this._serverConfigInfoList["GodCardOpenFiveTimeMoney"]))
         {
            return int(this._serverConfigInfoList["GodCardOpenFiveTimeMoney"].Value);
         }
         return 0;
      }
      
      public function get petQualityConfig() : Array
      {
         var qualityArr:Array = this.findInfoByName("PetQualityConfig").Value.split("|");
         var result:Array = qualityArr.sort((function():Function
         {
            var sortCompare:* = function(_arg_1:int, _arg_2:int):int
            {
               if(_arg_1 >= _arg_2)
               {
                  return 1;
               }
               return -1;
            };
            return sortCompare;
         })());
         return result;
      }
      
      public function get petWashCost() : DictionaryData
      {
         var _local_4:int = 0;
         var _local_0:String = "0,40|1,80|2,120|3,160|4,200";
         if(this._serverConfigInfoList.hasKey("PetWashCost"))
         {
            _local_0 = String(this._serverConfigInfoList["PetWashCost"].Value);
         }
         var _local_2:* = null;
         var _local_3:Array = _local_0.split("|");
         var _local_1:DictionaryData = new DictionaryData();
         _local_4 = 0;
         while(_local_4 < _local_3.length)
         {
            _local_2 = _local_3[_local_4].split(",");
            _local_1.add(_local_2[0],_local_2[1]);
            _local_4++;
         }
         return _local_1;
      }
   }
}

