package com.demonsters.debugger
{
   import flash.utils.ByteArray;
   
   public class MonsterDebuggerData
   {
      
      private var _id:String;
      
      private var _data:Object;
      
      public function MonsterDebuggerData(_arg_1:String, _arg_2:Object)
      {
         super();
         this._id = _arg_1;
         this._data = _arg_2;
      }
      
      public static function read(_arg_1:ByteArray) : MonsterDebuggerData
      {
         var _local_2:MonsterDebuggerData = new MonsterDebuggerData(null,null);
         _local_2.bytes = _arg_1;
         return _local_2;
      }
      
      public function get id() : String
      {
         return this._id;
      }
      
      public function get data() : Object
      {
         return this._data;
      }
      
      public function get bytes() : ByteArray
      {
         var _local_2:ByteArray = new ByteArray();
         var _local_3:ByteArray = new ByteArray();
         _local_2.writeObject(this._id);
         _local_3.writeObject(this._data);
         var _local_1:ByteArray = new ByteArray();
         _local_1.writeUnsignedInt(_local_2.length);
         _local_1.writeBytes(_local_2);
         _local_1.writeUnsignedInt(_local_3.length);
         _local_1.writeBytes(_local_3);
         _local_1.position = 0;
         _local_2 = null;
         _local_3 = null;
         return _local_1;
      }
      
      public function set bytes(_arg_1:ByteArray) : void
      {
         var _local_2:ByteArray = new ByteArray();
         var _local_3:ByteArray = new ByteArray();
         try
         {
            _arg_1.readBytes(_local_2,0,_arg_1.readUnsignedInt());
            _arg_1.readBytes(_local_3,0,_arg_1.readUnsignedInt());
            this._id = _local_2.readObject() as String;
            this._data = _local_3.readObject() as Object;
         }
         catch(e:Error)
         {
            _id = null;
            _data = null;
         }
         _local_2 = null;
         _local_3 = null;
      }
   }
}

