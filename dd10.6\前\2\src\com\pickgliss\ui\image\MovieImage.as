package com.pickgliss.ui.image
{
   import com.pickgliss.utils.ClassUtils;
   import flash.display.MovieClip;
   
   public class MovieImage extends Image
   {
      
      public function MovieImage()
      {
         super();
      }
      
      public function get movie() : MovieClip
      {
         return _display as MovieClip;
      }
      
      override public function setFrame(_arg_1:int) : void
      {
         super.setFrame(_arg_1);
         this.movie.gotoAndStop(_arg_1);
         if(_width != Math.round(this.movie.width))
         {
            _width = Math.round(this.movie.width);
            _changedPropeties["width"] = true;
         }
      }
      
      override protected function resetDisplay() : void
      {
         if(Bo<PERSON>an(_display))
         {
            removeChild(_display);
         }
         _display = ClassUtils.CreatInstance(_resourceLink);
      }
   }
}

