package bagAndInfo.amulet.vo
{
   public class EquipAmuletPhaseVo
   {
      
      public var Phase:int;
      
      public var grade:int;
      
      public var Expend:int;
      
      public var LockPrice:int;
      
      public var Crit:int;
      
      public var Tenacity:int;
      
      public var SunderArmor:int;
      
      public var AvoidInjury:int;
      
      public var Kill:int;
      
      public var WillFight:int;
      
      public var ViolenceInjury:int;
      
      public var Guard:int;
      
      public var Speed:int;
      
      public function EquipAmuletPhaseVo()
      {
         super();
      }
      
      public function get property1() : int
      {
         return this.Crit;
      }
      
      public function get property2() : int
      {
         return this.Tenacity;
      }
      
      public function get property3() : int
      {
         return this.SunderArmor;
      }
      
      public function get property4() : int
      {
         return this.AvoidInjury;
      }
      
      public function get property5() : int
      {
         return this.Kill;
      }
      
      public function get property6() : int
      {
         return this.WillFight;
      }
      
      public function get property7() : int
      {
         return this.ViolenceInjury;
      }
      
      public function get property8() : int
      {
         return this.Guard;
      }
      
      public function get property9() : int
      {
         return this.Speed;
      }
   }
}

