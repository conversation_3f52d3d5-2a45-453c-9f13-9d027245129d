package braveDoor.data
{
   public class DuplicateInfo
   {
      
      private var _id:int;
      
      private var _page:int;
      
      private var _x:int;
      
      private var _y:int;
      
      private var _backStyle:String;
      
      private var _name:String;
      
      private var _filterString:String;
      
      private var _awardGoodID:int;
      
      public function DuplicateInfo()
      {
         super();
      }
      
      public function get awardGoodID() : int
      {
         return this._awardGoodID;
      }
      
      public function set awardGoodID(_arg_1:int) : void
      {
         this._awardGoodID = _arg_1;
      }
      
      public function get filterString() : String
      {
         return this._filterString;
      }
      
      public function set filterString(_arg_1:String) : void
      {
         this._filterString = _arg_1;
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      public function set name(_arg_1:String) : void
      {
         this._name = _arg_1;
      }
      
      public function get backStyle() : String
      {
         return this._backStyle;
      }
      
      public function set backStyle(_arg_1:String) : void
      {
         this._backStyle = _arg_1;
      }
      
      public function get y() : int
      {
         return this._y;
      }
      
      public function set y(_arg_1:int) : void
      {
         this._y = _arg_1;
      }
      
      public function get x() : int
      {
         return this._x;
      }
      
      public function set x(_arg_1:int) : void
      {
         this._x = _arg_1;
      }
      
      public function get page() : int
      {
         return this._page;
      }
      
      public function set page(_arg_1:int) : void
      {
         this._page = _arg_1;
      }
      
      public function get id() : int
      {
         return this._id;
      }
      
      public function set id(_arg_1:int) : void
      {
         this._id = _arg_1;
      }
   }
}

