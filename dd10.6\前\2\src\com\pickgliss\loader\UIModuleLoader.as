package com.pickgliss.loader
{
   import com.pickgliss.events.LoaderResourceEvent;
   import com.pickgliss.events.UIModuleEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.ComponentSetting;
   import ddt.GlobalData;
   import flash.events.EventDispatcher;
   
   [Event(name="uiModuleComplete",type="com.pickgliss.events.UIModuleEvent")]
   [Event(name="uiModuleError",type="com.pickgliss.events.UIModuleEvent")]
   [Event(name="uiMoudleProgress",type="com.pickgliss.events.UIModuleEvent")]
   public class UIModuleLoader extends EventDispatcher
   {
      
      private static var _instance:UIModuleLoader;
      
      private var _loadingLoaders:Vector.<BaseLoader>;
      
      private var _queue:Vector.<String>;
      
      public function UIModuleLoader()
      {
         super();
         this._queue = new Vector.<String>();
         this._loadingLoaders = new Vector.<BaseLoader>();
      }
      
      public static function get Instance() : UIModuleLoader
      {
         if(_instance == null)
         {
            _instance = new UIModuleLoader();
         }
         return _instance;
      }
      
      public function addUIModlue(_arg_1:String) : void
      {
         if(this._queue.indexOf(_arg_1) != -1)
         {
            return;
         }
         this._queue.push(_arg_1);
         if(!this.isLoading)
         {
            this.loadNextModule();
         }
      }
      
      public function addUIModuleImp(_arg_1:String) : void
      {
         var _local_2:int = int(this._queue.indexOf(_arg_1));
         if(_local_2 != -1)
         {
            this._queue.splice(_local_2,1);
         }
         this._queue.unshift(_arg_1);
         if(!this.isLoading)
         {
            this.loadNextModule();
         }
      }
      
      public function get isLoading() : Boolean
      {
         return this._loadingLoaders.length > 0;
      }
      
      private function __onLoadError(_arg_1:LoaderEvent) : void
      {
         _arg_1.loader.removeEventListener("loadError",this.__onLoadError);
         _arg_1.loader.removeEventListener("progress",this.__onResourceProgress);
         _arg_1.loader.removeEventListener("complete",this.__onResourceComplete);
         dispatchEvent(new UIModuleEvent("uiModuleError",_arg_1.loader));
      }
      
      private function __onResourceComplete(_arg_1:LoaderEvent) : void
      {
         _arg_1.loader.removeEventListener("loadError",this.__onLoadError);
         _arg_1.loader.removeEventListener("progress",this.__onResourceProgress);
         _arg_1.loader.removeEventListener("complete",this.__onResourceComplete);
         this.removeLastLoader(_arg_1.loader);
         dispatchEvent(new UIModuleEvent("uiModuleComplete",_arg_1.loader));
         this.loadNextModule();
      }
      
      private function removeLastLoader(_arg_1:BaseLoader) : void
      {
         if(this._loadingLoaders.indexOf(_arg_1) != -1)
         {
            this._loadingLoaders.splice(this._loadingLoaders.indexOf(_arg_1),1);
         }
         if(this._queue.indexOf(_arg_1.loadProgressMessage) != -1)
         {
            this._queue.splice(this._queue.indexOf(_arg_1.loadProgressMessage),1);
         }
      }
      
      private function __onResourceProgress(_arg_1:LoaderEvent) : void
      {
         dispatchEvent(new UIModuleEvent("uiMoudleProgress",_arg_1.loader));
      }
      
      private function loadNextModule() : void
      {
         if(this._queue.length <= 0)
         {
            dispatchEvent(new LoaderResourceEvent("loadxmlComplete"));
            return;
         }
         var _local_1:String = this._queue[0];
         if(!this.isLoadingModule(_local_1))
         {
            this.loadModuleUI(_local_1);
         }
      }
      
      private function isLoadingModule(_arg_1:String) : Boolean
      {
         var _local_2:int = 0;
         _local_2 = 0;
         while(_local_2 < this._loadingLoaders.length)
         {
            if(this._loadingLoaders[_local_2].loadProgressMessage == _arg_1)
            {
               return true;
            }
            _local_2++;
         }
         return false;
      }
      
      private function loadModuleUI(_arg_1:String) : void
      {
         LoadResourceManager.Instance.startLoad(this.getUIModuleLoader(_arg_1),true);
      }
      
      public function getUIModuleLoader(_arg_1:String) : BaseLoader
      {
         ComponentFactory.Instance.analysisXML(_arg_1);
         var _local_2:String = GlobalData.resFlashSite + ComponentSetting.getUISourcePath(_arg_1);
         var _local_3:BaseLoader = LoadResourceManager.Instance.createLoader(_local_2,4);
         _local_3.loadProgressMessage = _arg_1;
         _local_3.addEventListener("loadError",this.__onLoadError);
         _local_3.addEventListener("progress",this.__onResourceProgress);
         _local_3.addEventListener("complete",this.__onResourceComplete);
         if(this._loadingLoaders.indexOf(_local_3) == -1)
         {
            this._loadingLoaders.push(_local_3);
         }
         if(this._queue.indexOf(_arg_1) == -1)
         {
            this._queue.push(_arg_1);
         }
         return _local_3;
      }
   }
}

