package cloudBuyLottery.data
{
   public class HappyBuyBuyBuyInfo
   {
      
      public var ActivityType:int;
      
      public var Quality:int;
      
      public var TemplateID:int;
      
      public var ValidDate:int;
      
      public var Count:int = 1;
      
      public var IsBind:Boolean;
      
      public var StrengthLevel:int;
      
      public var AttackCompose:int;
      
      public var DefendCompose:int;
      
      public var AgilityCompose:int;
      
      public var LuckCompose:int;
      
      public function HappyBuyBuyBuyInfo(_arg_1:int = 0, _arg_2:int = 0)
      {
         super();
         this.Quality = _arg_1;
         this.TemplateID = _arg_2;
      }
   }
}

