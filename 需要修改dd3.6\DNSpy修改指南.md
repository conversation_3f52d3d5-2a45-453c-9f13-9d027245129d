# DNSpy 修改指南

## 🎯 修改顺序（重要！）

**必须按照以下顺序修改，否则会出现类型冲突错误：**

### 1. 先修改 GameProperties.cs
### 2. 再修改 forcesbattle.cs
### 3. 最后一起保存

---

## 📝 具体修改内容

### **文件1: GameProperties.cs**
**路径：** `Road.Service.dll` → `Bussiness` → `GameProperties`

**找到第862-863行：**
```csharp
// 原来的代码：
[ConfigProperty("RelicUpgradeItem", "RelicUpgradeItem", 50)]
public static readonly int RelicUpgradeItem;
```

**修改为：**
```csharp
// 新的代码：
[ConfigProperty("RelicUpgradeItem", "RelicUpgradeItem", "386298,50")]
public static readonly string RelicUpgradeItem;
```

**关键修改点：**
- `int` → `string`
- `50` → `"386298,50"`

---

### **文件2: forcesbattle.cs**
**路径：** `Road.Service.dll` → `Game.Server.Packets.Client` → `forcesbattle`

**找到 SendUpgradeRelic 方法（约第95-139行）：**

**在方法开头添加配置解析代码：**
```csharp
public void SendUpgradeRelic(GamePlayer Player, GSPacketIn packet)
{
    int _curDetailID = packet.ReadInt();
    int templateID = packet.ReadInt();
    int num = packet.ReadInt();

    // 解析圣物升级配置
    string[] relicUpgradeConfig = GameProperties.RelicUpgradeItem.Split(',');
    int configTemplateID = int.Parse(relicUpgradeConfig[0]);      // 386298
    int relicUpgradeExpValue = int.Parse(relicUpgradeConfig[1]);  // 50

    bool flag = num <= 0;
    if (flag)
    {
        Player.SendMessage("数量异常");
    }
    else
    {
        // 验证使用的是正确的圣物升级石
        bool flag1 = templateID != configTemplateID;
        if (flag1)
        {
            Player.SendMessage("请使用正确的圣物升级石！");
        }
        else
        {
            ItemInfo itemByTemplateID = Player.PropBag.GetItemByTemplateID(templateID);
            bool flag2 = itemByTemplateID == null || itemByTemplateID.Count < num;
            if (flag2)
            {
                Player.SendMessage("圣物升级石数量不足！");
            }
            else
            {
                bool flag3 = !Player.PropBag.RemoveCountFromStack(itemByTemplateID, num);
                if (flag3)
                {
                    Player.SendMessage("扣除道具失败，请稍后重试！");
                }
                else
                {
                    Sys_User_RelicItemTemplate sys_User_RelicItemTemplate = Player.PlayerCharacter.RelicItem.FirstOrDefault((Sys_User_RelicItemTemplate i) => i.itemID == _curDetailID);
                    bool flag4 = sys_User_RelicItemTemplate == null;
                    if (flag4)
                    {
                        Player.SendMessage("未找到对应的圣物信息！");
                    }
                    else
                    {
                        sys_User_RelicItemTemplate.curExp += num * relicUpgradeExpValue;
                        int relicLevel = RelicItemMgr.GetRelicLevel(sys_User_RelicItemTemplate.itemID, sys_User_RelicItemTemplate.curExp, sys_User_RelicItemTemplate.level);
                        bool flag5 = relicLevel != sys_User_RelicItemTemplate.level;
                        if (flag5)
                        {
                            Sys_User_RelicItemTemplate sys_User_RelicItemTemplate2 = sys_User_RelicItemTemplate;
                            int level = sys_User_RelicItemTemplate2.level;
                            sys_User_RelicItemTemplate2.level = level + 1;
                            sys_User_RelicItemTemplate.curExp = 0;
                        }
                        this.SendUserRelicItem(Player, 3);
                        Player.EquipBag.UpdatePlayerProperties();
                        Player.SendMessage("升级成功！");
                    }
                }
            }
        }
    }
}
```

**关键修改点：**
1. 添加了配置解析代码
2. 添加了物品ID验证
3. 使用 `relicUpgradeExpValue` 变量而不是硬编码的50

---

## ⚠️ 重要注意事项

1. **必须先修改 GameProperties.cs，再修改 forcesbattle.cs**
2. **如果出现类型错误，请重启 DNSpy**
3. **确保以管理员身份运行 DNSpy**
4. **修改前备份原文件**

---

## 🎯 修改验证

修改完成后，检查：
1. GameProperties.RelicUpgradeItem 是 string 类型
2. forcesbattle.cs 能正常编译
3. 保存时没有错误提示

---

## 📋 其他可选修改

如果需要完整的圣物功能，还可以修改：
- PropUseHandler.cs（圣物强化石使用）
- OpenUpArkHandler.cs（容器开启处理）

但核心的升级功能只需要修改上述两个文件即可。
