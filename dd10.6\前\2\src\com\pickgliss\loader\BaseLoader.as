package com.pickgliss.loader
{
   import ddt.GlobalData;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.HTTPStatusEvent;
   import flash.events.IOErrorEvent;
   import flash.events.ProgressEvent;
   import flash.external.ExternalInterface;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLVariables;
   import flash.system.ApplicationDomain;
   import flash.utils.ByteArray;
   import flash.utils.getTimer;
   
   [Event(name="complete",type="com.pickgliss.loader.LoaderEvent")]
   [Event(name="loadError",type="com.pickgliss.loader.LoaderEvent")]
   [Event(name="progress",type="com.pickgliss.loader.LoaderEvent")]
   public class BaseLoader extends EventDispatcher
   {
      
      public static const BITMAP_LOADER:int = 0;
      
      public static const BYTE_LOADER:int = 3;
      
      public static const DISPLAY_LOADER:int = 1;
      
      public static const TEXT_LOADER:int = 2;
      
      public static const MODULE_LOADER:int = 4;
      
      public static const COMPRESS_TEXT_LOADER:int = 5;
      
      public static const REQUEST_LOADER:int = 6;
      
      public static const COMPRESS_REQUEST_LOADER:int = 7;
      
      public static const TRY_LOAD_TIMES:int = 3;
      
      public static const MORNUI_DATA_LOADER:int = 8;
      
      public static const CODE_MODULE_LOADER:int = 9;
      
      public static const BONES_LOADER:int = 10;
      
      public static const ZIP_LOADER:int = 11;
      
      public var loadCompleteMessage:String;
      
      public var loadErrorMessage:String;
      
      public var loadProgressMessage:String;
      
      protected var _args:URLVariables;
      
      protected var _id:int;
      
      protected var _isComplete:Boolean;
      
      protected var _isSuccess:Boolean;
      
      protected var _loader:URLLoader;
      
      protected var _progress:Number = 0;
      
      protected var _url:String;
      
      protected var _isLoading:Boolean;
      
      protected var _requestMethod:String;
      
      protected var _currentLoadPath:String;
      
      private var _currentTryTime:int = 0;
      
      protected var _starTime:int;
      
      public var analyzer:DataAnalyzer;
      
      public var domain:ApplicationDomain;
      
      public function BaseLoader(_arg_1:int, _arg_2:String, _arg_3:URLVariables = null, _arg_4:String = "GET")
      {
         super();
         this.checkUrl(_arg_2);
         this._args = _arg_3;
         this._id = _arg_1;
         this._loader = new URLLoader();
         this._requestMethod = _arg_4;
      }
      
      private function checkUrl(_arg_1:String) : void
      {
         var _local_2:String = GlobalData.resourceSite;
         var _local_3:* = _arg_1;
         if(_local_2 != "" && _arg_1.indexOf(_local_2) != -1)
         {
            _local_3 = _arg_1.toLocaleLowerCase();
         }
         this._url = _local_3;
      }
      
      public function get args() : URLVariables
      {
         return this._args;
      }
      
      public function get content() : *
      {
         return this._loader.data;
      }
      
      public function getFilePathFromExternal() : void
      {
         ExternalInterface.call("ExternalLoadStart",this._id,this._url);
      }
      
      public function get id() : int
      {
         return this._id;
      }
      
      public function get isComplete() : Boolean
      {
         return this._isComplete;
      }
      
      public function set isComplete(_arg_1:Boolean) : void
      {
         this._isComplete = _arg_1;
      }
      
      public function get isSuccess() : Boolean
      {
         return this._isSuccess;
      }
      
      public function loadFromExternal(_arg_1:String) : void
      {
         this.startLoad(_arg_1);
      }
      
      public function loadFromBytes(_arg_1:ByteArray) : void
      {
         this._starTime = getTimer();
      }
      
      public function loadFromWeb() : void
      {
         this.startLoad(this._url);
      }
      
      public function get progress() : Number
      {
         return this._progress;
      }
      
      public function get type() : int
      {
         return 3;
      }
      
      public function get url() : String
      {
         return this._url;
      }
      
      public function set url(_arg_1:String) : void
      {
         this._url = _arg_1;
      }
      
      public function get isLoading() : Boolean
      {
         return this._isLoading;
      }
      
      public function set isLoading(_arg_1:Boolean) : void
      {
         this._isLoading = _arg_1;
      }
      
      protected function __onDataLoadComplete(_arg_1:Event) : void
      {
         this.removeEvent();
         this._loader.close();
         this.fireCompleteEvent();
      }
      
      protected function fireCompleteEvent() : void
      {
         this._progress = 1;
         dispatchEvent(new LoaderEvent("progress",this));
         this._isSuccess = true;
         this._isComplete = true;
         this._isLoading = false;
         this.domain = null;
         dispatchEvent(new LoaderEvent("complete",this));
      }
      
      protected function __onIOError(_arg_1:IOErrorEvent) : void
      {
         LoadInterfaceManager.traceMsg("微端加载资源错误：" + _arg_1.text + " " + this._currentLoadPath);
         this.onLoadError();
      }
      
      protected function __onProgress(_arg_1:ProgressEvent) : void
      {
         this._progress = _arg_1.bytesLoaded / _arg_1.bytesTotal;
         if(Boolean(this.loadProgressMessage))
         {
            this.loadProgressMessage = this.loadProgressMessage.replace("{progress}",Math.round(this._progress * 100));
         }
         dispatchEvent(new LoaderEvent("progress",this));
      }
      
      protected function __onStatus(_arg_1:HTTPStatusEvent) : void
      {
         if(_arg_1.status > 399)
         {
         }
      }
      
      protected function addEvent() : void
      {
         this._loader.addEventListener("complete",this.__onDataLoadComplete);
         this._loader.addEventListener("progress",this.__onProgress);
         this._loader.addEventListener("ioError",this.__onIOError);
      }
      
      protected function getLoadDataFormat() : String
      {
         return "binary";
      }
      
      protected function onLoadError() : void
      {
         this.removeEvent();
         if(this._currentTryTime < 3)
         {
            ++this._currentTryTime;
            this._isLoading = false;
            if(this.logInstance != null)
            {
               this.logInstance.log.debug("路径_" + this._currentLoadPath + "#_加载异常次数_" + this._currentTryTime);
            }
            this.startLoad(this._currentLoadPath);
         }
         else
         {
            this._loader.close();
            this._isComplete = true;
            this._isLoading = false;
            this._isSuccess = false;
            dispatchEvent(new LoaderEvent("loadError",this));
            dispatchEvent(new LoaderEvent("complete",this));
         }
      }
      
      protected function fireErrorEvent() : void
      {
         dispatchEvent(new LoaderEvent("loadError",this));
      }
      
      protected function removeEvent() : void
      {
         this._loader.removeEventListener("complete",this.__onDataLoadComplete);
         this._loader.removeEventListener("progress",this.__onProgress);
         this._loader.removeEventListener("ioError",this.__onIOError);
      }
      
      protected function startLoad(_arg_1:String) : void
      {
         if(this._isLoading)
         {
            return;
         }
         this.addEvent();
         this._currentLoadPath = _arg_1.toLocaleLowerCase();
         this._loader.dataFormat = this.getLoadDataFormat();
         var _local_2:URLRequest = new URLRequest(this._currentLoadPath);
         _local_2.method = this._requestMethod;
         _local_2.data = this._args;
         this._isLoading = true;
         this._loader.load(_local_2);
         this._starTime = getTimer();
      }
      
      public function unload() : void
      {
         try
         {
            this._loader.close();
         }
         catch(error:Error)
         {
            trace(error.message);
         }
      }
      
      protected function get logInstance() : Object
      {
         return null;
      }
   }
}

