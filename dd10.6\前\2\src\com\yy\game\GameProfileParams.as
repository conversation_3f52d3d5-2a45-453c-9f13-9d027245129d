package com.yy.game
{
   import flash.net.URLVariables;
   
   public class GameProfileParams
   {
      
      private var _fightPower:Number;
      
      private var _sex:String;
      
      private var _job:String;
      
      private var _partner:String;
      
      private var _equip:String;
      
      private var _iversion:String;
      
      private var _updateVersion:String;
      
      private var _runResource:String;
      
      private var _referrer:String;
      
      public function GameProfileParams(_arg_1:Object = null)
      {
         var _local_2:Object = null;
         var _local_3:String = null;
         var _local_4:String = null;
         super();
         if(<PERSON><PERSON>an(_arg_1))
         {
            _local_2 = {
               "equip":"equip",
               "fight_cap":"fightPower",
               "ive":"iversion",
               "job":"job",
               "partner":"partner",
               "ref":"referrer",
               "rso":"runResource",
               "sex":"sex",
               "uve":"updateVersion"
            };
            for(_local_3 in _arg_1)
            {
               _local_4 = _local_2[_local_3];
               this[_local_4] = _arg_1[_local_3];
            }
         }
      }
      
      public function get fightPower() : Number
      {
         return this._fightPower;
      }
      
      public function set fightPower(_arg_1:Number) : void
      {
         this._fightPower = _arg_1;
      }
      
      public function get sex() : String
      {
         return this._sex;
      }
      
      public function set sex(_arg_1:String) : void
      {
         if(_arg_1 != Gender.MALE && _arg_1 != Gender.FEMALE)
         {
            throw new ArgumentError("性别必须是Gender类的常量值");
         }
         this._sex = _arg_1;
      }
      
      public function get job() : String
      {
         return this._job;
      }
      
      public function set job(_arg_1:String) : void
      {
         if(_arg_1 == null || _arg_1.length <= 0)
         {
            throw new ArgumentError("job参数不能是null或空字符串");
         }
         this._job = _arg_1;
      }
      
      public function get partner() : String
      {
         return this._partner;
      }
      
      public function set partner(_arg_1:String) : void
      {
         if(_arg_1 == null || _arg_1.length <= 0)
         {
            throw new ArgumentError("partner参数不能是null或空字符串");
         }
         this._partner = _arg_1;
      }
      
      public function get equip() : String
      {
         return this._equip;
      }
      
      public function set equip(_arg_1:String) : void
      {
         if(_arg_1 == null || _arg_1.length <= 0)
         {
            throw new ArgumentError("equip参数不能是null或空字符串");
         }
         this._equip = _arg_1;
      }
      
      public function get iversion() : String
      {
         return this._iversion;
      }
      
      public function set iversion(_arg_1:String) : void
      {
         if(_arg_1 == null || _arg_1.length <= 0)
         {
            throw new ArgumentError("iversion参数不能是null或空字符串");
         }
         this._iversion = _arg_1;
      }
      
      public function get updateVersion() : String
      {
         if(this._updateVersion == null)
         {
            return this._iversion;
         }
         return this._updateVersion;
      }
      
      public function set updateVersion(_arg_1:String) : void
      {
         if(_arg_1 == null || _arg_1.length <= 0)
         {
            throw new ArgumentError("updateVersion参数不能是null或空字符串");
         }
         this._updateVersion = _arg_1;
      }
      
      public function get runResource() : String
      {
         return this._runResource;
      }
      
      public function set runResource(_arg_1:String) : void
      {
         if(_arg_1 == null || _arg_1.length <= 0)
         {
            throw new ArgumentError("runResource参数不能是null或空字符串");
         }
         this._runResource = _arg_1;
      }
      
      public function get referrer() : String
      {
         return this._referrer;
      }
      
      public function set referrer(_arg_1:String) : void
      {
         if(_arg_1 == null || _arg_1.length <= 0)
         {
            throw new ArgumentError("referrer参数不能是null或空字符串");
         }
         this._referrer = _arg_1;
      }
      
      internal function setVariable(_arg_1:URLVariables) : void
      {
         if(Boolean(this._iversion))
         {
            _arg_1.ive = this._iversion;
         }
         if(Boolean(this._updateVersion))
         {
            _arg_1.uve = this._updateVersion;
         }
         if(Boolean(this._runResource))
         {
            _arg_1.rso = this._runResource;
         }
         if(Boolean(this._referrer))
         {
            _arg_1.ref = this._referrer;
         }
      }
      
      private function getVal(_arg_1:String, _arg_2:Object) : String
      {
         if(_arg_2.hasOwnProperty(_arg_1))
         {
            return _arg_2[_arg_1];
         }
         return null;
      }
   }
}

