package cloudBuyLottery.model
{
   import cloudBuyLottery.view.WinningLogItemInfo;
   
   public class CloudBuyLotteryModel
   {
      
      private var _isOpen:Boolean;
      
      private var _luckTime:Date;
      
      public var myGiftData:Vector.<WinningLogItemInfo>;
      
      public var packsLen:int = 15;
      
      private var _moneyNum:int = 200;
      
      private var _templateId:int;
      
      private var _templatedIdCount:int;
      
      private var _validDate:int;
      
      private var _property:Array;
      
      private var _count:int;
      
      private var _buyGoodsIDArray:Array;
      
      private var _buyGoodsCountArray:Array;
      
      private var _buyMoney:int;
      
      private var _maxNum:int;
      
      private var _currentNum:int;
      
      private var _totalSeconds:int;
      
      private var _luckCount:int;
      
      private var _remainTimes:int;
      
      private var _luckDrawId:int;
      
      private var _isGame:Boolean;
      
      private var _isGetReward:Boolean;
      
      public function CloudBuyLotteryModel()
      {
         super();
      }
      
      public function get isOpen() : Boolean
      {
         return this._isOpen;
      }
      
      public function set isOpen(_arg_1:<PERSON><PERSON><PERSON>) : void
      {
         this._isOpen = _arg_1;
      }
      
      public function get luckTime() : Date
      {
         return this._luckTime;
      }
      
      public function set luckTime(_arg_1:Date) : void
      {
         this._luckTime = _arg_1;
      }
      
      public function get moneyNum() : int
      {
         return this._moneyNum;
      }
      
      public function set moneyNum(_arg_1:int) : void
      {
         this._moneyNum = _arg_1;
      }
      
      public function get templateId() : int
      {
         return this._templateId;
      }
      
      public function set templateId(_arg_1:int) : void
      {
         this._templateId = _arg_1;
      }
      
      public function get validDate() : int
      {
         return this._validDate;
      }
      
      public function set validDate(_arg_1:int) : void
      {
         this._validDate = _arg_1;
      }
      
      public function get count() : int
      {
         return this._count;
      }
      
      public function set count(_arg_1:int) : void
      {
         this._count = _arg_1;
      }
      
      public function get buyGoodsIDArray() : Array
      {
         return this._buyGoodsIDArray;
      }
      
      public function set buyGoodsIDArray(_arg_1:Array) : void
      {
         this._buyGoodsIDArray = _arg_1;
      }
      
      public function get buyGoodsCountArray() : Array
      {
         return this._buyGoodsCountArray;
      }
      
      public function set buyGoodsCountArray(_arg_1:Array) : void
      {
         this._buyGoodsCountArray = _arg_1;
      }
      
      public function get buyMoney() : int
      {
         return this._buyMoney;
      }
      
      public function set buyMoney(_arg_1:int) : void
      {
         this._buyMoney = _arg_1;
      }
      
      public function get maxNum() : int
      {
         return this._maxNum;
      }
      
      public function set maxNum(_arg_1:int) : void
      {
         this._maxNum = _arg_1;
      }
      
      public function get currentNum() : int
      {
         return this._currentNum;
      }
      
      public function set currentNum(_arg_1:int) : void
      {
         this._currentNum = _arg_1;
      }
      
      public function get totalSeconds() : int
      {
         return this._totalSeconds;
      }
      
      public function set totalSeconds(_arg_1:int) : void
      {
         this._totalSeconds = _arg_1;
      }
      
      public function get luckCount() : int
      {
         return this._luckCount;
      }
      
      public function set luckCount(_arg_1:int) : void
      {
         this._luckCount = _arg_1;
      }
      
      public function get remainTimes() : int
      {
         return this._remainTimes;
      }
      
      public function set remainTimes(_arg_1:int) : void
      {
         this._remainTimes = _arg_1;
      }
      
      public function get property() : Array
      {
         return this._property;
      }
      
      public function set property(_arg_1:Array) : void
      {
         this._property = _arg_1;
      }
      
      public function get luckDrawId() : int
      {
         return this._luckDrawId;
      }
      
      public function set luckDrawId(_arg_1:int) : void
      {
         this._luckDrawId = _arg_1;
      }
      
      public function get isGame() : Boolean
      {
         return this._isGame;
      }
      
      public function set isGame(_arg_1:Boolean) : void
      {
         this._isGame = _arg_1;
      }
      
      public function get isGetReward() : Boolean
      {
         return this._isGetReward;
      }
      
      public function set isGetReward(_arg_1:Boolean) : void
      {
         this._isGetReward = _arg_1;
      }
      
      public function get templatedIdCount() : int
      {
         return this._templatedIdCount;
      }
      
      public function set templatedIdCount(_arg_1:int) : void
      {
         this._templatedIdCount = _arg_1;
      }
   }
}

