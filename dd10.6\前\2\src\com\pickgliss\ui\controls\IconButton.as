package com.pickgliss.ui.controls
{
   import com.pickgliss.geom.InnerRectangle;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.DisplayUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.DisplayObject;
   
   public class IconButton extends TextButton
   {
      
      public static const P_icon:String = "icon";
      
      public static const P_iconInnerRect:String = "iconInnerRect";
      
      protected var _icon:DisplayObject;
      
      protected var _iconInnerRect:InnerRectangle;
      
      protected var _iconInnerRectString:String;
      
      protected var _iconStyle:String;
      
      public function IconButton()
      {
         super();
      }
      
      override public function dispose() : void
      {
         if(Boolean(this._icon))
         {
            ObjectUtils.disposeObject(this._icon);
         }
         this._icon = null;
         super.dispose();
      }
      
      public function set icon(_arg_1:DisplayObject) : void
      {
         if(this._icon == _arg_1)
         {
            return;
         }
         ObjectUtils.disposeObject(this._icon);
         this._icon = _arg_1;
         onPropertiesChanged("icon");
      }
      
      public function set iconInnerRectString(_arg_1:String) : void
      {
         if(this._iconInnerRectString == _arg_1)
         {
            return;
         }
         this._iconInnerRectString = _arg_1;
         this._iconInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._iconInnerRectString));
         onPropertiesChanged("iconInnerRect");
      }
      
      public function set iconStyle(_arg_1:String) : void
      {
         if(this._iconStyle == _arg_1)
         {
            return;
         }
         this._iconStyle = _arg_1;
         this.icon = ComponentFactory.Instance.creat(this._iconStyle);
      }
      
      override protected function addChildren() : void
      {
         super.addChildren();
         if(Boolean(this._icon))
         {
            addChild(this._icon);
         }
      }
      
      override protected function onProppertiesUpdate() : void
      {
         super.onProppertiesUpdate();
         if(Boolean(_changedPropeties["width"]) || Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["iconInnerRect"]) || Boolean(_changedPropeties["icon"]))
         {
            this.updateIconPos();
         }
      }
      
      protected function updateIconPos() : void
      {
         if(Boolean(this._icon) && Boolean(this._iconInnerRect))
         {
            DisplayUtils.layoutDisplayWithInnerRect(this._icon,this._iconInnerRect,_width,_height);
         }
      }
      
      override public function setFrame(_arg_1:int) : void
      {
         super.setFrame(_arg_1);
         DisplayUtils.setFrame(this._icon,_arg_1);
      }
   }
}

