package anotherDimension.controller
{
   import anotherDimension.model.AnotherDimensionInfo;
   import anotherDimension.model.AnotherDimensionMsgInfo;
   import anotherDimension.model.AnotherDimensionResourceInfo;
   import ddt.CoreManager;
   import ddt.data.player.PlayerInfo;
   import ddt.events.CrazyTankSocketEvent;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.utils.HelperUIModuleLoad;
   import flash.events.Event;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   
   public class AnotherDimensionManager extends CoreManager
   {
      
      private static var _instance:AnotherDimensionManager;
      
      public static const SHOWMAINVIEW:String = "showMainView";
      
      public static const UPDATEVIEW:String = "updateView";
      
      public static const UPDATE_RESOURCEDATA:String = "updateResourceData";
      
      public static const ADDMSG:String = "addMsg";
      
      public var resourceList:Array;
      
      public var haveResourceList:Array;
      
      public var gameOver:Boolean;
      
      public var isOpen:Boolean;
      
      public var anotherDimensionInfo:AnotherDimensionInfo;
      
      public var msgArr:Array;
      
      public var showBuyCountFram:Boolean = true;
      
      private var refreshOnly:int;
      
      public function AnotherDimensionManager()
      {
         super();
      }
      
      public static function get Instance() : AnotherDimensionManager
      {
         if(_instance == null)
         {
            _instance = new AnotherDimensionManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener("anotherDimension",this.pkgHandler);
      }
      
      override protected function start() : void
      {
         new HelperUIModuleLoad().loadUIModule(["anotherDimension"],this.onLoaded);
      }
      
      override protected function onLoaded() : void
      {
         dispatchEvent(new Event("showMainView"));
      }
      
      private function pkgHandler(_arg_1:CrazyTankSocketEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_3.readInt();
         switch(_local_2)
         {
            case 3:
               this.openMainView(_local_3);
               return;
            case 4:
               this.updateResource(_local_3);
               return;
            case 1:
               this.openOrClose(_local_3);
               return;
            case 2:
               this.updateInfo(_local_3);
               return;
            case 7:
               this.receiveMsg(_local_3);
         }
      }
      
      private function receiveMsg(_arg_1:PackageIn) : void
      {
         var _local_2:int = 0;
         var _local_8:int = 0;
         var _local_5:int = 0;
         var _local_6:int = 0;
         var _local_7:* = null;
         var _local_4:* = null;
         if(this.msgArr == null)
         {
            this.msgArr = [];
         }
         var _local_3:Boolean = _arg_1.readBoolean();
         if(_local_3)
         {
            _local_2 = _arg_1.readInt();
            _local_8 = 0;
            while(_local_8 < _local_2)
            {
               _local_7 = new AnotherDimensionMsgInfo();
               _local_7.userID = _arg_1.readInt();
               _local_7.ownUserID = _arg_1.readInt();
               _local_7.ownName = _arg_1.readUTF();
               _local_7.resPos = _arg_1.readInt();
               _local_7.restatus = _arg_1.readInt();
               if(this.msgArr.length < 30)
               {
                  this.msgArr.push(_local_7);
               }
               _local_8++;
            }
         }
         else
         {
            _local_5 = _arg_1.readInt();
            _local_6 = 0;
            while(_local_6 < _local_5)
            {
               _local_4 = new AnotherDimensionMsgInfo();
               _local_4.userID = _arg_1.readInt();
               _local_4.ownUserID = _arg_1.readInt();
               _local_4.ownName = _arg_1.readUTF();
               _local_4.resPos = _arg_1.readInt();
               _local_4.restatus = _arg_1.readInt();
               if(this.msgArr.length < 30)
               {
                  this.msgArr.unshift(_local_4);
               }
               else
               {
                  this.msgArr.unshift(_local_4);
                  this.msgArr.pop();
               }
               _local_6++;
            }
         }
         dispatchEvent(new Event("addMsg"));
      }
      
      private function openMainView(_arg_1:PackageIn) : void
      {
         var _local_10:int = 0;
         var _local_3:int = 0;
         var _local_6:int = 0;
         var _local_4:* = null;
         var _local_9:* = null;
         var _local_5:* = null;
         this.resourceList = [];
         this.haveResourceList = [];
         this.gameOver = false;
         var _local_2:int = _arg_1.readInt();
         _local_10 = 0;
         while(_local_10 < _local_2)
         {
            _local_4 = new AnotherDimensionResourceInfo();
            _local_4.isHave = false;
            _local_4.resourcePos = _arg_1.readInt();
            _local_4.monsterId = _arg_1.readInt();
            if(_local_4.monsterId == 0)
            {
               _local_9 = new PlayerInfo();
               _local_9.ID = _arg_1.readInt();
               _local_9.NickName = _arg_1.readUTF();
               _local_3 = _arg_1.readInt();
               _local_9.Sex = _local_3 == 0 ? false : true;
               _local_9.Hide = _arg_1.readInt();
               _local_9.Style = _arg_1.readUTF();
               _local_9.Colors = _arg_1.readUTF();
               _local_9.Skin = _arg_1.readUTF();
               _local_9.Grade = _arg_1.readInt();
               _local_4.resourcePlayerInfo = _local_9;
            }
            else
            {
               _arg_1.readInt();
               _arg_1.readUTF();
               _arg_1.readInt();
               _arg_1.readInt();
               _arg_1.readUTF();
               _arg_1.readUTF();
               _arg_1.readUTF();
               _arg_1.readInt();
            }
            _arg_1.readInt();
            _local_4.resourceLevel = _arg_1.readInt();
            _local_4.resourceState = _arg_1.readInt();
            _local_4.haveResourceTime = _arg_1.readDate();
            _local_4.lastMaxMunites = _arg_1.readInt();
            _local_4.itemId = _arg_1.readInt();
            _local_4.itemCountPerHour = _arg_1.readInt();
            this.resourceList.push(_local_4);
            _local_10++;
         }
         var _local_8:int = _arg_1.readInt();
         var _local_7:int = 3;
         _local_6 = 0;
         while(_local_6 < _local_8)
         {
            _local_5 = new AnotherDimensionResourceInfo();
            _local_7++;
            _local_5.isHave = true;
            _local_5.resourcePos = _local_7;
            _local_5.resourcePlayerInfo = PlayerManager.Instance.Self;
            _local_5.resourceLevel = _arg_1.readInt();
            _local_5.haveResourceTime = _arg_1.readDate();
            _local_5.haveResourceLast = _arg_1.readInt();
            _local_5.lastMaxMunites = _local_5.haveResourceLast;
            _local_5.itemId = _arg_1.readInt();
            _local_5.itemCountPerHour = _arg_1.readInt();
            this.haveResourceList.push(_local_5);
            _local_6++;
         }
         show();
      }
      
      private function updateInfo(_arg_1:PackageIn) : void
      {
         var _local_2:Boolean = false;
         this.anotherDimensionInfo = new AnotherDimensionInfo();
         this.anotherDimensionInfo.occupyCount = _arg_1.readInt();
         this.anotherDimensionInfo.totalOccupyCount = _arg_1.readInt();
         this.anotherDimensionInfo.lootCount = _arg_1.readInt();
         this.anotherDimensionInfo.totalLootCount = _arg_1.readInt();
         this.anotherDimensionInfo.refreshCount = _arg_1.readInt();
         if(this.anotherDimensionInfo.refreshCount != this.refreshOnly)
         {
            _local_2 = true;
         }
         this.anotherDimensionInfo.timeControlLv = _arg_1.readInt();
         this.anotherDimensionInfo.timeControlExp = _arg_1.readInt();
         this.anotherDimensionInfo.spaceControlLv = _arg_1.readInt();
         this.anotherDimensionInfo.spaceControlExp = _arg_1.readInt();
         this.anotherDimensionInfo.looterControlLv = _arg_1.readInt();
         this.anotherDimensionInfo.looterControlExp = _arg_1.readInt();
         this.refreshOnly = this.anotherDimensionInfo.refreshCount;
         if(!_local_2)
         {
            dispatchEvent(new Event("updateView"));
         }
      }
      
      private function openOrClose(_arg_1:PackageIn) : void
      {
         this.isOpen = _arg_1.readBoolean();
      }
      
      private function updateResource(_arg_1:PackageIn) : void
      {
         var _local_10:int = 0;
         var _local_3:int = 0;
         var _local_6:int = 0;
         var _local_4:* = null;
         var _local_9:* = null;
         var _local_5:* = null;
         this.resourceList = [];
         this.haveResourceList = [];
         var _local_2:int = _arg_1.readInt();
         _local_10 = 0;
         while(_local_10 < _local_2)
         {
            _local_4 = new AnotherDimensionResourceInfo();
            _local_4.isHave = false;
            _local_4.resourcePos = _arg_1.readInt();
            _local_4.monsterId = _arg_1.readInt();
            if(_local_4.monsterId == 0)
            {
               _local_9 = new PlayerInfo();
               _local_9.ID = _arg_1.readInt();
               _local_9.NickName = _arg_1.readUTF();
               _local_3 = _arg_1.readInt();
               _local_9.Sex = _local_3 == 0 ? false : true;
               _local_9.Hide = _arg_1.readInt();
               _local_9.Style = _arg_1.readUTF();
               _local_9.Colors = _arg_1.readUTF();
               _local_9.Skin = _arg_1.readUTF();
               _local_9.Grade = _arg_1.readInt();
               _local_4.resourcePlayerInfo = _local_9;
            }
            else
            {
               _arg_1.readInt();
               _arg_1.readUTF();
               _arg_1.readInt();
               _arg_1.readInt();
               _arg_1.readUTF();
               _arg_1.readUTF();
               _arg_1.readUTF();
               _arg_1.readInt();
            }
            _arg_1.readInt();
            _local_4.resourceLevel = _arg_1.readInt();
            _local_4.resourceState = _arg_1.readInt();
            _local_4.haveResourceTime = _arg_1.readDate();
            _local_4.lastMaxMunites = _arg_1.readInt();
            _local_4.itemId = _arg_1.readInt();
            _local_4.itemCountPerHour = _arg_1.readInt();
            this.resourceList.push(_local_4);
            _local_10++;
         }
         var _local_8:int = _arg_1.readInt();
         var _local_7:int = 3;
         _local_6 = 0;
         while(_local_6 < _local_8)
         {
            _local_5 = new AnotherDimensionResourceInfo();
            _local_7++;
            _local_5.isHave = true;
            _local_5.resourcePos = _local_7;
            _local_5.resourcePlayerInfo = PlayerManager.Instance.Self;
            _local_5.resourceLevel = _arg_1.readInt();
            _local_5.haveResourceTime = _arg_1.readDate();
            _local_5.haveResourceLast = _arg_1.readInt();
            _local_5.lastMaxMunites = _local_5.haveResourceLast;
            _local_5.itemId = _arg_1.readInt();
            _local_5.itemCountPerHour = _arg_1.readInt();
            this.haveResourceList.push(_local_5);
            _local_6++;
         }
         dispatchEvent(new Event("updateResourceData"));
      }
      
      public function checkShowIcon() : void
      {
         if(PlayerManager.Instance.Self.Grade >= 30)
         {
            HallIconManager.instance.updateSwitchHandler("buried",false);
         }
         else
         {
            HallIconManager.instance.executeCacheRightIconLevelLimit("buried",false,30);
         }
      }
   }
}

