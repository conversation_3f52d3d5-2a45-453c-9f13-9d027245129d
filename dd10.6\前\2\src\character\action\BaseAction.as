package character.action
{
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import mx.events.PropertyChangeEvent;
   
   public class BaseAction implements IAction, IEventDispatcher
   {
      
      public static const BASE:int = -1;
      
      public static const SIMPLE_ACTION:int = 0;
      
      public static const COMPLEX_ACTION:int = 1;
      
      public static const MOVIE_ACTION:int = 2;
      
      protected var _asset:DisplayObject;
      
      protected var _nextAction:String;
      
      protected var _name:String;
      
      protected var _priority:uint;
      
      protected var _len:int;
      
      protected var _endStop:Boolean;
      
      protected var _sound:String = "";
      
      protected var _type:int;
      
      private var _bindingEventDispatcher:EventDispatcher = new EventDispatcher(IEventDispatcher(this));
      
      public function BaseAction(_arg_1:String = "", _arg_2:String = "", _arg_3:uint = 0, _arg_4:<PERSON><PERSON><PERSON> = false)
      {
         super();
         this._type = BASE;
         this._name = _arg_1;
         this._nextAction = _arg_2;
         this._priority = _arg_3;
         this._endStop = _arg_4;
      }
      
      public function get len() : int
      {
         return this._len;
      }
      
      public function get isEnd() : Boolean
      {
         return true;
      }
      
      private function set _3373707name(_arg_1:String) : void
      {
         this._name = _arg_1;
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      private function set _1794985207nextAction(_arg_1:String) : void
      {
         this._nextAction = _arg_1;
      }
      
      public function get nextAction() : String
      {
         return this._nextAction;
      }
      
      private function set _1165461084priority(_arg_1:uint) : void
      {
         this._priority = _arg_1;
      }
      
      public function get priority() : uint
      {
         return this._priority;
      }
      
      public function get asset() : DisplayObject
      {
         return this._asset;
      }
      
      public function reset() : void
      {
      }
      
      public function dispose() : void
      {
         this._asset = null;
      }
      
      public function toXml() : XML
      {
         var _local_1:XML = <action/>;
         _local_1.@type = this._type;
         _local_1.@name = this._name;
         _local_1.@next = this._nextAction;
         _local_1.@priority = this._priority;
         _local_1.@endStop = this._endStop;
         _local_1.@sound = this._sound;
         return _local_1;
      }
      
      public function get endStop() : Boolean
      {
         return this._endStop;
      }
      
      private function set _1607262339endStop(_arg_1:Boolean) : void
      {
         this._endStop = _arg_1;
      }
      
      public function get sound() : String
      {
         return this._sound;
      }
      
      private function set _109627663sound(_arg_1:String) : void
      {
         this._sound = _arg_1;
      }
      
      [Bindable(event="propertyChange")]
      public function set nextAction(_arg_1:String) : void
      {
         var _local_2:Object = this.nextAction;
         if(_local_2 !== _arg_1)
         {
            this._1794985207nextAction = _arg_1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"nextAction",_local_2,_arg_1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function set sound(_arg_1:String) : void
      {
         var _local_2:Object = this.sound;
         if(_local_2 !== _arg_1)
         {
            this._109627663sound = _arg_1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"sound",_local_2,_arg_1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function set priority(_arg_1:uint) : void
      {
         var _local_2:Object = this.priority;
         if(_local_2 !== _arg_1)
         {
            this._1165461084priority = _arg_1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"priority",_local_2,_arg_1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function set endStop(_arg_1:Boolean) : void
      {
         var _local_2:Object = this.endStop;
         if(_local_2 !== _arg_1)
         {
            this._1607262339endStop = _arg_1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"endStop",_local_2,_arg_1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function set name(_arg_1:String) : void
      {
         var _local_2:Object = this.name;
         if(_local_2 !== _arg_1)
         {
            this._3373707name = _arg_1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"name",_local_2,_arg_1));
            }
         }
      }
      
      public function addEventListener(_arg_1:String, _arg_2:Function, _arg_3:Boolean = false, _arg_4:int = 0, _arg_5:Boolean = false) : void
      {
         this._bindingEventDispatcher.addEventListener(_arg_1,_arg_2,_arg_3,_arg_4,_arg_5);
      }
      
      public function dispatchEvent(_arg_1:Event) : Boolean
      {
         return this._bindingEventDispatcher.dispatchEvent(_arg_1);
      }
      
      public function hasEventListener(_arg_1:String) : Boolean
      {
         return this._bindingEventDispatcher.hasEventListener(_arg_1);
      }
      
      public function removeEventListener(_arg_1:String, _arg_2:Function, _arg_3:Boolean = false) : void
      {
         this._bindingEventDispatcher.removeEventListener(_arg_1,_arg_2,_arg_3);
      }
      
      public function willTrigger(_arg_1:String) : Boolean
      {
         return this._bindingEventDispatcher.willTrigger(_arg_1);
      }
   }
}

