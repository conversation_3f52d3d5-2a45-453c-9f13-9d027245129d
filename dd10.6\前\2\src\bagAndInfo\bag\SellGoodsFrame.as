package bagAndInfo.bag
{
   import bagAndInfo.cell.BaseCell;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.QualityType;
   import ddt.manager.LanguageMgr;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.PositionUtils;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class SellGoodsFrame extends Frame
   {
      
      public static const OK:String = "ok";
      
      public static const CANCEL:String = "cancel";
      
      private var _itemInfo:InventoryItemInfo;
      
      private var _confirm:TextButton;
      
      private var _cancel:TextButton;
      
      private var _cell:BaseCell;
      
      private var _nameTxt:FilterFrameText;
      
      private var _descript:FilterFrameText;
      
      private var _price:FilterFrameText;
      
      public function SellGoodsFrame()
      {
         super();
         this.initView();
         addEventListener("response",this.__responseHandler);
         this._confirm.addEventListener("click",this.__confirmhandler);
         this._cancel.addEventListener("click",this.__cancelHandler);
      }
      
      protected function __confirmhandler(_arg_1:MouseEvent) : void
      {
         this.ok();
      }
      
      protected function __cancelHandler(_arg_1:MouseEvent) : void
      {
         this.cancel();
      }
      
      protected function __responseHandler(_arg_1:FrameEvent) : void
      {
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               this.ok();
               return;
            case 0:
            case 1:
            case 4:
               this.cancel();
         }
      }
      
      private function ok() : void
      {
         SoundManager.instance.play("008");
         SocketManager.Instance.out.reclaimGoods(this._itemInfo.BagType,this._itemInfo.Place,this._itemInfo.Count);
         dispatchEvent(new Event("ok"));
         this.dispose();
      }
      
      private function cancel() : void
      {
         SoundManager.instance.play("008");
         dispatchEvent(new Event("cancel"));
         this.dispose();
      }
      
      public function set itemInfo(_arg_1:InventoryItemInfo) : void
      {
         this._itemInfo = _arg_1;
         this._cell.info = _arg_1;
         if(_arg_1.Name.length > 22)
         {
            this._nameTxt.text = _arg_1.Name.substr(0,22) + "...";
         }
         else
         {
            this._nameTxt.text = _arg_1.Name;
         }
         if(_arg_1.CategoryID == 18)
         {
            this._nameTxt.textColor = QualityType.QUALITY_COLOR[_arg_1.Quality + 1];
         }
         else
         {
            this._nameTxt.textColor = QualityType.QUALITY_COLOR[_arg_1.Quality];
         }
         var _local_3:String = _arg_1.ReclaimType == 1 ? LanguageMgr.GetTranslation("shop.ShopIIShoppingCarItem.gold") : (_arg_1.ReclaimType == 2 ? LanguageMgr.GetTranslation("tank.gameover.takecard.gifttoken") : "");
         var _local_2:String = "                                                       ";
         this._descript.htmlText = LanguageMgr.GetTranslation("bagAndInfo.sellFrame.explainTxt",_arg_1.Count) + "       " + _local_2.substr(0,this._price.text.length * 2 + 2) + _local_3;
         this._price.text = _arg_1.Count * _arg_1.ReclaimValue + "";
      }
      
      private function initView() : void
      {
         titleText = LanguageMgr.GetTranslation("AlertDialog.Info");
         this._confirm = ComponentFactory.Instance.creatComponentByStylename("sellGoodsFrame.ok");
         this._confirm.text = LanguageMgr.GetTranslation("ok");
         this._cancel = ComponentFactory.Instance.creatComponentByStylename("sellGoodsFrame.cancel");
         this._cancel.text = LanguageMgr.GetTranslation("cancel");
         this._cell = new BaseCell(ClassUtils.CreatInstance("asset.core.ItemCellBG"));
         PositionUtils.setPos(this._cell,"sellFrame.cellPos");
         this._nameTxt = ComponentFactory.Instance.creatComponentByStylename("sellGoodsFrame.name");
         PositionUtils.setPos(this._nameTxt,"sellFrame.namePos");
         this._descript = ComponentFactory.Instance.creatComponentByStylename("sellGoodsFrame.explain");
         this._price = ComponentFactory.Instance.creatComponentByStylename("sellGoodsFrame.price");
         addToContent(this._confirm);
         addToContent(this._cancel);
         addToContent(this._cell);
         addToContent(this._nameTxt);
         addToContent(this._descript);
         addToContent(this._price);
      }
      
      override public function dispose() : void
      {
         removeEventListener("response",this.__responseHandler);
         if(Boolean(this._confirm))
         {
            this._confirm.removeEventListener("click",this.__confirmhandler);
         }
         if(Boolean(this._cancel))
         {
            this._cancel.removeEventListener("click",this.__cancelHandler);
         }
         super.dispose();
         this._itemInfo = null;
         if(Boolean(this._confirm))
         {
            ObjectUtils.disposeObject(this._confirm);
         }
         this._confirm = null;
         if(Boolean(this._cancel))
         {
            ObjectUtils.disposeObject(this._cancel);
         }
         this._cancel = null;
         if(Boolean(this._cell))
         {
            ObjectUtils.disposeObject(this._cell);
         }
         this._cell = null;
         if(Boolean(this._nameTxt))
         {
            ObjectUtils.disposeObject(this._nameTxt);
         }
         this._nameTxt = null;
         if(Boolean(this._descript))
         {
            ObjectUtils.disposeObject(this._descript);
         }
         this._descript = null;
         if(Boolean(this._price))
         {
            ObjectUtils.disposeObject(this._price);
         }
         this._price = null;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

