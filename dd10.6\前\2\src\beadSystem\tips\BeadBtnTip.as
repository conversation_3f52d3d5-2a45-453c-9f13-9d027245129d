package beadSystem.tips
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.image.ScaleBitmapImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.ui.tip.BaseTip;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.LanguageMgr;
   import ddt.manager.ServerConfigManager;
   
   public class BeadBtnTip extends BaseTip
   {
      
      private var _bg:ScaleBitmapImage;
      
      private var _nameTxt:FilterFrameText;
      
      private var _discTxt:FilterFrameText;
      
      private var _beadTipData:Object;
      
      private var _nameList:Array;
      
      private var _priceList:Array;
      
      public function BeadBtnTip()
      {
         super();
         this.initView();
         this.initData();
      }
      
      private function initView() : void
      {
         this._bg = ComponentFactory.Instance.creatComponentByStylename("beadSystem.getBead.requestBtn.tip.bg");
         this._nameTxt = ComponentFactory.Instance.creatComponentByStylename("beadSystem.getBead.requestBtn.tip.name");
         this._discTxt = ComponentFactory.Instance.creatComponentByStylename("beadSystem.getBead.requestBtn.tip.disc");
         addChild(this._bg);
         addChild(this._nameTxt);
         addChild(this._discTxt);
      }
      
      private function initData() : void
      {
         var _local_1:String = LanguageMgr.GetTranslation("ddt.beadSystem.requestBeadNames");
         this._nameList = _local_1.split(",");
         this._priceList = ServerConfigManager.instance.getRequestBeadPrice();
      }
      
      override public function get tipData() : Object
      {
         return this._beadTipData;
      }
      
      override public function set tipData(_arg_1:Object) : void
      {
         var _local_2:int = 0;
         var _local_3:* = null;
         this._beadTipData = _arg_1;
         var _local_4:int = int(_arg_1);
         switch(_local_4)
         {
            case 0:
               _local_3 = this._nameList[0];
               _local_2 = int(this._priceList[0]);
               break;
            case 1:
               _local_3 = this._nameList[1];
               _local_2 = int(this._priceList[1]);
               break;
            case 2:
               _local_3 = this._nameList[2];
               _local_2 = int(this._priceList[2]);
               break;
            case 3:
               _local_3 = this._nameList[3];
               _local_2 = int(this._priceList[3]);
               break;
            default:
               _local_3 = "";
               _local_2 = 0;
         }
         this._nameTxt.text = _local_3;
         this._discTxt.text = LanguageMgr.GetTranslation("ddt.beadSystem.getBead.requestBtn.tip.disc") + _local_2.toString();
         this.updateSize();
      }
      
      private function updateSize() : void
      {
         this._bg.width = Math.max(this._discTxt.x + this._discTxt.width,this._nameTxt.x + this._nameTxt.width) + 15;
         this._bg.height = this._discTxt.y + this._discTxt.height + 10;
      }
      
      override public function dispose() : void
      {
         this._beadTipData = null;
         if(Boolean(this._bg))
         {
            ObjectUtils.disposeObject(this._bg);
         }
         this._bg = null;
         if(Boolean(this._nameTxt))
         {
            ObjectUtils.disposeObject(this._nameTxt);
         }
         this._nameTxt = null;
         if(Boolean(this._discTxt))
         {
            ObjectUtils.disposeObject(this._discTxt);
         }
         this._discTxt = null;
         super.dispose();
      }
   }
}

