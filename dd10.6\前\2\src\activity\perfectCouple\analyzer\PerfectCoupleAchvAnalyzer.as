package activity.perfectCouple.analyzer
{
   import activity.perfectCouple.data.PerfectCoupleAchvTemp;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import flash.utils.Dictionary;
   
   public class PerfectCoupleAchvAnalyzer extends DataAnalyzer
   {
      
      private var _data:Dictionary;
      
      public function PerfectCoupleAchvAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         this._data = new Dictionary();
         var _local_2:XML = new XML(_arg_1);
         if(_local_2.@value == "true")
         {
            _local_3 = _local_2..Item;
            _local_5 = 0;
            while(_local_5 < _local_3.length())
            {
               _local_4 = new PerfectCoupleAchvTemp();
               ObjectUtils.copyPorpertiesByXML(_local_4,_local_3[_local_5]);
               this._data[_local_4.ID] = _local_4;
               _local_5++;
            }
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
         }
         onAnalyzeComplete();
         this._data = null;
      }
      
      public function get data() : Dictionary
      {
         return this._data;
      }
   }
}

