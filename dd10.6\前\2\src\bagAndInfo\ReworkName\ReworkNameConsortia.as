package bagAndInfo.ReworkName
{
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.analyze.ReworkNameAnalyzer;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.utils.FilterWordManager;
   import flash.events.Event;
   import road7th.utils.StringHelper;
   
   public class ReworkNameConsortia extends ReworkNameFrame
   {
      
      public function ReworkNameConsortia()
      {
         super();
         _path = "ConsortiaNameCheck.ashx";
         _nicknameDetail = LanguageMgr.GetTranslation("tank.view.ConsortiaReworkNameView.consortiaNameAlert");
      }
      
      override protected function configUi() : void
      {
         super.configUi();
         titleText = LanguageMgr.GetTranslation("tank.view.ReworkNameView.consortiaReworkName");
         _tittleField.text = LanguageMgr.GetTranslation("tank.view.ReworkNameView.consortiaInputName");
         _resultField.text = LanguageMgr.GetTranslation("tank.view.ConsortiaReworkNameView.consortiaNameAlert");
         if(Boolean(_nicknameInput))
         {
            ObjectUtils.disposeObject(_nicknameInput);
            _nicknameInput = null;
         }
         _nicknameInput = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.reworkname.ConsortiaInput");
         addToContent(_nicknameInput);
      }
      
      override protected function __onInputChange(_arg_1:Event) : void
      {
         super.__onInputChange(_arg_1);
         StringHelper.checkTextFieldLength(_nicknameInput,12);
      }
      
      override protected function nameInputCheck() : Boolean
      {
         var _local_1:* = null;
         if(_nicknameInput.text != "")
         {
            if(FilterWordManager.isGotForbiddenWords(_nicknameInput.text,"name"))
            {
               _local_1 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("tank.RenameFrame.Consortia.FailWord"),LanguageMgr.GetTranslation("ok"),"",false,false,false,2);
               _local_1.addEventListener("response",__onAlertResponse);
               return false;
            }
            if(FilterWordManager.IsNullorEmpty(_nicknameInput.text))
            {
               _local_1 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("tank.RenameFrame.Consortia.space"),LanguageMgr.GetTranslation("ok"),"",false,false,false,2);
               _local_1.addEventListener("response",__onAlertResponse);
               return false;
            }
            if(FilterWordManager.containUnableChar(_nicknameInput.text))
            {
               _local_1 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("tank.RenameFrame.Consortia.string"),LanguageMgr.GetTranslation("ok"),"",false,false,false,2);
               _local_1.addEventListener("response",__onAlertResponse);
               return false;
            }
            return true;
         }
         _local_1 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("tank.RenameFrame.Consortia.input"),LanguageMgr.GetTranslation("ok"),"",false,false,false,2);
         _local_1.addEventListener("response",__onAlertResponse);
         return false;
      }
      
      override protected function setCheckTxt(_arg_1:String) : void
      {
         if(_arg_1 == LanguageMgr.GetTranslation("tank.view.ConsortiaReworkNameView.consortiaNameAlert4"))
         {
            state = "aviable";
            _isCanRework = true;
         }
         else
         {
            state = "unaviable";
         }
         _resultField.text = _arg_1;
      }
      
      override protected function submitCheckCallBack(_arg_1:ReworkNameAnalyzer) : void
      {
         var _local_3:* = null;
         complete = true;
         var _local_2:XML = _arg_1.result;
         this.setCheckTxt(_local_2.@message);
         if(this.nameInputCheck() && _isCanRework)
         {
            _local_3 = _nicknameInput.text;
            SocketManager.Instance.out.sendUseConsortiaReworkName(PlayerManager.Instance.Self.ConsortiaID,_bagType,_place,_local_3);
            reworkNameComplete();
         }
      }
   }
}

