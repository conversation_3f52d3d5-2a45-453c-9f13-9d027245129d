package bombKing
{
   import bagAndInfo.info.PlayerInfoViewControl;
   import bombKing.data.BKingStatueInfo;
   import bombKing.event.BombKingEvent;
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.CoreManager;
   import ddt.data.player.PlayerInfo;
   import ddt.events.PkgEvent;
   import ddt.manager.ChatManager;
   import ddt.manager.PathManager;
   import ddt.manager.SharedManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.RequestVairableCreater;
   import flash.events.MouseEvent;
   import flash.net.URLVariables;
   import flash.utils.Dictionary;
   import hall.IHallStateView;
   import road7th.comm.PackageIn;
   import starling.newHall.NewHallPlatform;
   import starling.scene.hall.HallPlayerView;
   import starling.scene.hall.statue.SceneKingStatue;
   import tofflist.analyze.TofflistListTwoAnalyzer;
   import tofflist.data.TofflistListData;
   
   public class BombKingManager extends CoreManager
   {
      
      private static var _instance:BombKingManager;
      
      public var Recording:Boolean;
      
      public var ShowFlag:Boolean;
      
      private var _playerView:HallPlayerView;
      
      private var _defaultFlag:Boolean = true;
      
      private var _btnEnter:BaseButton;
      
      private var defaultStyleArr:Array = ["1860|head360,2201|default,3428|hair128,4429|eff129,5827|cloth327,6402|face102,70102|brick3,13201|default,15001|default,16000|default,","1730|head330,2101|default,3337|hair137,4351|eff151,5549|cloth249,6198|face98,702414|boomerang6,13101|default,15001|default,16008|chatBall8,17005|offhand5","1101|default,2101|default,3331|hair131,4151|eff51,5190|cloth90,6178|face78,70094|electricbar6,13101|default,15001|default,16000|default,"];
      
      private var _type:int;
      
      private var _items:Dictionary;
      
      private var _colorList:Array = ["","",""];
      
      private var _data:TofflistListData;
      
      public function BombKingManager()
      {
         super();
      }
      
      public static function get instance() : BombKingManager
      {
         if(!_instance)
         {
            _instance = new BombKingManager();
         }
         return _instance;
      }
      
      public function addToHallStateView(_arg_1:IHallStateView) : void
      {
         if(this._btnEnter == null)
         {
            this._btnEnter = ComponentFactory.Instance.creat("hall.bombKingButton");
         }
         _arg_1.leftTopGbox.addChild(this._btnEnter);
         _arg_1.arrangeLeftGrid();
         this._btnEnter.addEventListener("click",this.onEnterClick);
      }
      
      public function removeFromeHallStateView(_arg_1:IHallStateView) : void
      {
         if(Boolean(this._btnEnter) && Boolean(this._btnEnter.parent))
         {
            _arg_1.leftTopGbox.removeChild(this._btnEnter);
         }
         this._btnEnter.removeEventListener("click",this.onEnterClick);
         ObjectUtils.disposeObject(this._btnEnter);
         this._btnEnter = null;
      }
      
      protected function onEnterClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         show();
      }
      
      private function loadTop3Data(_arg_1:Function) : void
      {
         var _local_2:* = null;
         _local_2 = new TofflistListTwoAnalyzer(_arg_1);
         this._loadXml("CelebByDayFightPowerList.xml",_local_2,5);
      }
      
      private function __personalResult(_arg_1:TofflistListTwoAnalyzer) : void
      {
         this._data = _arg_1.data;
         var _local_2:int = (this._data.list[this._type] as PlayerInfo).ID;
         PlayerInfoViewControl.viewByID(_local_2,-1,true,false);
         PlayerInfoViewControl.isOpenFromBag = false;
      }
      
      private function _loadXml(_arg_1:String, _arg_2:DataAnalyzer, _arg_3:int, _arg_4:String = "") : void
      {
         var _local_5:URLVariables = RequestVairableCreater.creatWidthKey(true);
         var _local_6:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath(_arg_1),_arg_3,_local_5);
         _local_6.loadErrorMessage = _arg_4;
         _local_6.analyzer = _arg_2;
         LoadResourceManager.Instance.startLoad(_local_6);
      }
      
      public function onPlayerClicked(_arg_1:int) : void
      {
         this._type = _arg_1;
         this.loadTop3Data(this.__personalResult);
      }
      
      public function setup(_arg_1:HallPlayerView) : void
      {
         var view:* = undefined;
         view = undefined;
         view = _arg_1;
         var initSetup:* = function(_arg_1:TofflistListTwoAnalyzer):void
         {
            _data = _arg_1.data;
            _playerView = view;
            initDefaultStatue();
            updateStatus();
         };
         this.initEvent();
         this._items = new Dictionary();
         SocketManager.Instance.out.requestBKingShowTip();
         this.loadTop3Data(initSetup);
      }
      
      private function updateStatus() : void
      {
         var _local_6:int = 0;
         var _local_2:Boolean = false;
         var _local_5:* = null;
         var _local_4:* = null;
         var _local_1:* = null;
         var _local_3:* = null;
         if(!this._playerView)
         {
            return;
         }
         this.clearStatue();
         this._defaultFlag = true;
         _local_6 = 0;
         while(_local_6 < 3)
         {
            _local_2 = this._data.list.length > _local_6;
            if(_local_2)
            {
               this._defaultFlag = false;
               _local_5 = new BKingStatueInfo();
               _local_4 = this._data.list[_local_6];
               _local_5.name = _local_4.NickName;
               _local_5.vipType = _local_4.typeVIP;
               _local_5.vipLevel = _local_4.VIPLevel;
               _local_1 = _local_4.Style;
               _local_5.style = _local_1;
               _local_5.color = _local_4.Colors;
               _local_5.sex = _local_4.Sex;
               _local_5.isAttest = _local_4.isAttest;
               _local_5.rank = _local_6;
               _local_3 = new SceneKingStatue(_local_6);
               _local_3.info = _local_5;
               this.addToHall(_local_3,_local_6);
               this._items[_local_6] = _local_3;
            }
            _local_6++;
         }
         if(this._defaultFlag)
         {
            this.initDefaultStatue();
         }
      }
      
      private function initDefaultStatue() : void
      {
         var _local_3:int = 0;
         var _local_2:* = null;
         var _local_1:* = null;
         _local_3 = 0;
         while(_local_3 < 3)
         {
            _local_2 = new BKingStatueInfo();
            _local_2.style = this.defaultStyleArr[_local_3];
            _local_2.color = ",,,,,,,,,,";
            _local_2.sex = false;
            _local_2.isAttest = false;
            _local_1 = new SceneKingStatue(_local_3);
            _local_1.info = _local_2;
            this.addToHall(_local_1,_local_3);
            this._items[_local_3] = _local_1;
            _local_3++;
         }
      }
      
      public function checkStaue() : void
      {
         var _local_2:* = undefined;
         var _local_3:* = undefined;
         var _local_1:* = null;
         if(this._playerView == null)
         {
            return;
         }
         for(_local_2 in this._items)
         {
            _local_1 = this._items[_local_2];
            if(SharedManager.Instance.isUseNewHall)
            {
               if(this._playerView.newStaticLayer == null)
               {
                  return;
               }
               _local_1.x = 0;
               _local_1.y = -135;
               _local_3 = 0.8;
               _local_1.scaleY = _local_3;
               _local_1.scaleX = _local_3;
               switch(_local_2)
               {
                  case 0:
                     _local_1.x = 10;
                     (this._playerView.newStaticLayer.getBuildNpcBtnByName("first") as NewHallPlatform).addPeople(_local_1);
                     break;
                  case 1:
                     (this._playerView.newStaticLayer.getBuildNpcBtnByName("second") as NewHallPlatform).addPeople(_local_1);
                     break;
                  case 2:
                     (this._playerView.newStaticLayer.getBuildNpcBtnByName("third") as NewHallPlatform).addPeople(_local_1);
               }
            }
            else
            {
               if(this._playerView.staticLayer == null)
               {
                  return;
               }
               _local_3 = 1;
               _local_1.scaleY = _local_3;
               _local_1.scaleX = _local_3;
               switch(_local_2)
               {
                  case 0:
                     _local_1.x = -65;
                     _local_1.y = -302;
                     this._playerView.staticLayer.getBuildNpcBtnByName("bluePlatform").addChild(_local_1);
                     break;
                  case 1:
                     _local_1.x = -60;
                     _local_1.y = -298;
                     this._playerView.staticLayer.getBuildNpcBtnByName("goldPlatform").addChild(_local_1);
                     break;
                  case 2:
                     _local_1.x = -58;
                     _local_1.y = -283;
                     this._playerView.staticLayer.getBuildNpcBtnByName("redPlatform").addChild(_local_1);
               }
            }
         }
      }
      
      private function initEvent() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(263,8),this.__showText);
      }
      
      protected function __showText(_arg_1:PkgEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:String = _local_3.readUTF();
         ChatManager.Instance.sysChatAmaranth(_local_2);
      }
      
      protected function __updateStatue(_arg_1:PkgEvent) : void
      {
         var _local_7:int = 0;
         var _local_3:Boolean = false;
         var _local_6:* = null;
         var _local_2:* = null;
         var _local_4:* = null;
         if(!this._playerView)
         {
            return;
         }
         var _local_5:PackageIn = _arg_1.pkg;
         this.clearStatue();
         this._defaultFlag = true;
         _local_7 = 0;
         while(_local_7 < 3)
         {
            _local_3 = _local_5.readBoolean();
            if(_local_3)
            {
               this._defaultFlag = false;
               _local_6 = new BKingStatueInfo();
               _local_6.name = _local_5.readUTF();
               _local_6.vipType = _local_5.readInt();
               _local_6.vipLevel = _local_5.readInt();
               _local_2 = _local_5.readUTF();
               _local_6.style = _local_2;
               _local_6.color = _local_5.readUTF();
               _local_6.sex = _local_5.readBoolean();
               _local_6.isAttest = _local_5.readBoolean();
               _local_6.rank = _local_7;
               _local_4 = new SceneKingStatue(_local_7);
               _local_4.info = _local_6;
               this.addToHall(_local_4,_local_7);
               this._items[_local_7] = _local_4;
            }
            _local_7++;
         }
         if(this._defaultFlag)
         {
            this.initDefaultStatue();
         }
      }
      
      public function cutSuitStr(_arg_1:String) : String
      {
         var _local_2:Array = _arg_1.split(",");
         _local_2[7] = "";
         return _local_2.join(",");
      }
      
      private function clearStatue() : void
      {
         var _local_3:int = 0;
         var _local_2:* = null;
      }
      
      private function addToHall(_arg_1:SceneKingStatue, _arg_2:int) : void
      {
         var _local_3:* = undefined;
         if(this._playerView == null)
         {
            return;
         }
         if(SharedManager.Instance.isUseNewHall)
         {
            if(this._playerView.newStaticLayer == null)
            {
               return;
            }
            _arg_1.x = 0;
            _arg_1.y = -135;
            _local_3 = 0.8;
            _arg_1.scaleY = _local_3;
            _arg_1.scaleX = _local_3;
            switch(_arg_2)
            {
               case 0:
                  _arg_1.x = 10;
                  (this._playerView.newStaticLayer.getBuildNpcBtnByName("first") as NewHallPlatform).addPeople(_arg_1);
                  break;
               case 1:
                  (this._playerView.newStaticLayer.getBuildNpcBtnByName("second") as NewHallPlatform).addPeople(_arg_1);
                  break;
               case 2:
                  (this._playerView.newStaticLayer.getBuildNpcBtnByName("third") as NewHallPlatform).addPeople(_arg_1);
            }
         }
         else
         {
            if(this._playerView.staticLayer == null)
            {
               return;
            }
            switch(_arg_2)
            {
               case 0:
                  _arg_1.x = -65;
                  _arg_1.y = -302;
                  this._playerView.staticLayer.getBuildNpcBtnByName("bluePlatform").addChild(_arg_1);
                  return;
               case 1:
                  _arg_1.x = -60;
                  _arg_1.y = -298;
                  this._playerView.staticLayer.getBuildNpcBtnByName("goldPlatform").addChild(_arg_1);
                  return;
               case 2:
                  _arg_1.x = -58;
                  _arg_1.y = -283;
                  this._playerView.staticLayer.getBuildNpcBtnByName("redPlatform").addChild(_arg_1);
            }
         }
      }
      
      override protected function start() : void
      {
         dispatchEvent(new BombKingEvent("bombkingOpenView"));
      }
      
      public function get defaultFlag() : Boolean
      {
         return this._defaultFlag;
      }
   }
}

