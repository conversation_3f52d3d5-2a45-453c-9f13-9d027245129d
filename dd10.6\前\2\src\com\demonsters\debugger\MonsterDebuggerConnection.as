package com.demonsters.debugger
{
   internal class MonsterDebuggerConnection
   {
      
      private static var connector:IMonsterDebuggerConnection;
      
      public function MonsterDebuggerConnection()
      {
         super();
      }
      
      internal static function initialize() : void
      {
         connector = new MonsterDebuggerConnectionDefault();
      }
      
      internal static function set address(_arg_1:String) : void
      {
         connector.address = _arg_1;
      }
      
      internal static function get connected() : Boolean
      {
         return connector.connected;
      }
      
      internal static function processQueue() : void
      {
         connector.processQueue();
      }
      
      internal static function send(_arg_1:String, _arg_2:Object, _arg_3:Boolean = false) : void
      {
         connector.send(_arg_1,_arg_2,_arg_3);
      }
      
      internal static function connect() : void
      {
         connector.connect();
      }
   }
}

