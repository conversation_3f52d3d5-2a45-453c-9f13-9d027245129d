package cardNewSystem.data
{
   import ddt.manager.LanguageMgr;
   
   public class CardSuitTempInfo
   {
      
      public var SuitTemplateId:int;
      
      public var SuitName:String;
      
      public var NeedCardTempIds:String;
      
      public var SkillDesc:String;
      
      public var Hp:int;
      
      public var Attack:int;
      
      public var Defence:int;
      
      public var Agility:int;
      
      public var Lucky:int;
      
      public var MagicAttack:int;
      
      public var MagicDefence:int;
      
      public var Crit:int;
      
      public var SunderArmor:int;
      
      public var ViolenceInjury:int;
      
      public var Speed:int;
      
      public var TricKill:int;
      
      public var Damage:int;
      
      public var Armor:int;
      
      public var AddPercents:String;
      
      public var isAlive:Boolean;
      
      public var canAlive:Boolean;
      
      public var hasNum:int;
      
      public var profile:int = 3;
      
      public function CardSuitTempInfo()
      {
         super();
      }
      
      public function getProArr() : Array
      {
         var _local_1:int = 0;
         var _local_3:Array = [];
         var _local_5:Array = LanguageMgr.GetTranslation("tank.newCard.ProName").split(",");
         var _local_2:Array = this.AddPercents.split("|");
         var _local_4:int = int(_local_2[this.profile - 1]);
         if(this.Hp > 0)
         {
            _local_1 = int(int(this.Hp + this.Hp * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[0],
               "count":_local_1
            });
         }
         if(this.Attack > 0)
         {
            _local_1 = int(int(this.Attack + this.Attack * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[1],
               "count":_local_1
            });
         }
         if(this.Defence > 0)
         {
            _local_1 = int(int(this.Defence + this.Defence * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[2],
               "count":_local_1
            });
         }
         if(this.Agility > 0)
         {
            _local_1 = int(int(this.Agility + this.Agility * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[3],
               "count":_local_1
            });
         }
         if(this.Lucky > 0)
         {
            _local_1 = int(int(this.Lucky + this.Lucky * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[4],
               "count":_local_1
            });
         }
         if(this.MagicAttack > 0)
         {
            _local_1 = int(int(this.MagicAttack + this.MagicAttack * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[5],
               "count":_local_1
            });
         }
         if(this.MagicDefence > 0)
         {
            _local_1 = int(int(this.MagicDefence + this.MagicDefence * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[6],
               "count":_local_1
            });
         }
         if(this.Crit > 0)
         {
            _local_1 = int(int(this.Crit + this.Crit * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[7],
               "count":_local_1
            });
         }
         if(this.SunderArmor > 0)
         {
            _local_1 = int(int(this.SunderArmor + this.SunderArmor * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[8],
               "count":_local_1
            });
         }
         if(this.ViolenceInjury > 0)
         {
            _local_1 = int(int(this.ViolenceInjury + this.ViolenceInjury * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[9],
               "count":_local_1
            });
         }
         if(this.Speed > 0)
         {
            _local_1 = int(int(this.Speed + this.Speed * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[10],
               "count":_local_1
            });
         }
         if(this.TricKill > 0)
         {
            _local_1 = int(int(this.TricKill + this.TricKill * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[11],
               "count":_local_1
            });
         }
         if(this.Damage > 0)
         {
            _local_1 = int(int(this.Damage + this.Damage * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[12],
               "count":_local_1
            });
         }
         if(this.Armor > 0)
         {
            _local_1 = int(int(this.Armor + this.Armor * _local_4 / 1000));
            _local_3.push({
               "proName":_local_5[13],
               "count":_local_1
            });
         }
         return _local_3;
      }
   }
}

