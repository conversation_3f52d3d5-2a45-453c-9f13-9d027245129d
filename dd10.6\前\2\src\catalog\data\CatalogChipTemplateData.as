package catalog.data
{
   public class CatalogChipTemplateData
   {
      
      public var ItemID:int;
      
      public var FocusId:int;
      
      public var Place:int;
      
      public var Character:int;
      
      public var Attribute1:int;
      
      public var Attribute2:int;
      
      public var Attribute3:int;
      
      public var Attribute4:int;
      
      public var Attribute5:int;
      
      public var Attribute6:int;
      
      public var Attribute7:int;
      
      public var Attribute8:int;
      
      public var Attribute9:int;
      
      public var Attribute10:int;
      
      public var Attribute11:int;
      
      public var Attribute12:int;
      
      public var Attribute13:int;
      
      public var Attribute14:int;
      
      public function CatalogChipTemplateData()
      {
         super();
      }
   }
}

