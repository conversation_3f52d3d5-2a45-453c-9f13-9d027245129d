package accumulativeLogin
{
   import accumulativeLogin.view.AccumulativeLoginView;
   import com.pickgliss.events.UIModuleEvent;
   import com.pickgliss.loader.UIModuleLoader;
   import ddt.data.player.SelfInfo;
   import ddt.events.CrazyTankSocketEvent;
   import ddt.events.PkgEvent;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.view.UIModuleSmallLoading;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import flash.utils.Dictionary;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   
   public class AccumulativeManager extends EventDispatcher
   {
      
      private static var _instance:AccumulativeManager;
      
      public static const ACCUMULATIVE_AWARD_REFRESH:String = "accumulativeLoginAwardRefresh";
      
      public var dataDic:Dictionary;
      
      public function AccumulativeManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get instance() : AccumulativeManager
      {
         if(_instance == null)
         {
            _instance = new AccumulativeManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(238),this.__awardHandler);
      }
      
      protected function __awardHandler(_arg_1:CrazyTankSocketEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         var _local_3:SelfInfo = PlayerManager.Instance.Self;
         _local_3.accumulativeLoginDays = _local_2.readInt();
         _local_3.accumulativeAwardDays = _local_2.readInt();
         dispatchEvent(new Event("accumulativeLoginAwardRefresh"));
      }
      
      public function addAct() : void
      {
         if(PlayerManager.Instance.Self.Grade >= 10)
         {
            HallIconManager.instance.updateSwitchHandler("accumulativeLogin",false);
         }
         else
         {
            HallIconManager.instance.executeCacheRightIconLevelLimit("accumulativeLogin",false,10);
         }
      }
      
      public function removeAct() : void
      {
         HallIconManager.instance.updateSwitchHandler("accumulativeLogin",false);
         HallIconManager.instance.executeCacheRightIconLevelLimit("accumulativeLogin",false);
      }
      
      public function loadTempleteDataComplete(_arg_1:AccumulativeLoginAnalyer) : void
      {
         this.dataDic = _arg_1.accumulativeloginDataDic;
      }
      
      public function showFrame() : void
      {
         SoundManager.instance.play("008");
         UIModuleSmallLoading.Instance.progress = 0;
         UIModuleSmallLoading.Instance.show();
         UIModuleLoader.Instance.addEventListener("uiModuleComplete",this.loadCompleteHandler);
         UIModuleLoader.Instance.addEventListener("uiMoudleProgress",this.onUimoduleLoadProgress);
         UIModuleLoader.Instance.addUIModuleImp("wonderfulactivity");
      }
      
      private function onUimoduleLoadProgress(_arg_1:UIModuleEvent) : void
      {
         if(_arg_1.module == "wonderfulactivity")
         {
            UIModuleSmallLoading.Instance.progress = _arg_1.loader.progress * 100;
         }
      }
      
      private function loadCompleteHandler(_arg_1:UIModuleEvent) : void
      {
         var _local_2:* = null;
         if(_arg_1.module == "wonderfulactivity")
         {
            UIModuleSmallLoading.Instance.hide();
            UIModuleLoader.Instance.removeEventListener("uiModuleComplete",this.loadCompleteHandler);
            UIModuleLoader.Instance.removeEventListener("uiMoudleProgress",this.onUimoduleLoadProgress);
            _local_2 = new AccumulativeLoginView();
            _local_2.init();
            _local_2.x = -227;
            HallIconManager.instance.showCommonFrame(_local_2,"wonderfulActivityManager.btnTxt15");
         }
      }
   }
}

