package activity.newyear.analyzer
{
   import activity.newyear.data.NewYearRankRewardInfo;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   
   public class NewYearRankRewardAnalyzer extends DataAnalyzer
   {
      
      private var _data:Array;
      
      public function NewYearRankRewardAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         var _local_2:XML = new XML(_arg_1);
         this._data = [];
         if(_local_2.@value == "true")
         {
            _local_3 = _local_2..Item;
            _local_5 = 0;
            while(_local_5 < _local_3.length())
            {
               _local_4 = new NewYearRankRewardInfo();
               ObjectUtils.copyPorpertiesByXML(_local_4,_local_3[_local_5]);
               this._data.push(_local_4);
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
            onAnalyzeComplete();
         }
         this._data = null;
      }
      
      public function get data() : Array
      {
         return this._data;
      }
   }
}

