package bagAndInfo.bag
{
   import bagAndInfo.cell.BagCell;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.BaseButton;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.manager.PlayerManager;
   import ddt.manager.SoundManager;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import playerDress.components.DressUtils;
   
   public class CellMenu extends Sprite
   {
      
      private static var _instance:CellMenu;
      
      public static const ADDPRICE:String = "addprice";
      
      public static const MOVE:String = "move";
      
      public static const OPEN:String = "open";
      
      public static const USE:String = "use";
      
      public static const OPEN_BATCH:String = "open_batch";
      
      public static const COLOR_CHANGE:String = "color_change";
      
      public static const SELL:String = "delete";
      
      public static const RELIEVE:String = "relieve";
      
      private var _bg:Bitmap;
      
      private var _bg2:Bitmap;
      
      private var _cell:BagCell;
      
      private var _addpriceitem:BaseButton;
      
      private var _moveitem:BaseButton;
      
      private var _openitem:BaseButton;
      
      private var _useitem:BaseButton;
      
      private var _openBatchItem:BaseButton;
      
      private var _colorChangeItem:BaseButton;
      
      private var _sellItem:BaseButton;
      
      private var _relieveBtm:BaseButton;
      
      private var _list:Sprite;
      
      public function CellMenu(_arg_1:SingletonFoce)
      {
         super();
         this.init();
      }
      
      public static function get instance() : CellMenu
      {
         if(_instance == null)
         {
            _instance = new CellMenu(new SingletonFoce());
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._bg = ComponentFactory.Instance.creatBitmap("bagAndInfo.cellMenu.CellMenuBGAsset");
         this._bg2 = ComponentFactory.Instance.creatBitmap("bagAndInfo.cellMenu.CellMenuBG2Asset");
         addChild(this._bg);
         addChild(this._bg2);
         this._list = new Sprite();
         this._list.x = 5;
         this._list.y = 5;
         addChild(this._list);
         graphics.beginFill(0,0);
         graphics.drawRect(-3000,-3000,6000,6000);
         graphics.endFill();
         addEventListener("click",this.__mouseClick);
         this._addpriceitem = ComponentFactory.Instance.creatComponentByStylename("addPriceBtn");
         this._moveitem = ComponentFactory.Instance.creatComponentByStylename("moveGoodsBtn");
         this._openitem = ComponentFactory.Instance.creatComponentByStylename("openGoodsBtn");
         this._useitem = ComponentFactory.Instance.creatComponentByStylename("useGoodsBtn");
         this._openBatchItem = ComponentFactory.Instance.creatComponentByStylename("openBatchGoodsBtn");
         this._colorChangeItem = ComponentFactory.Instance.creatComponentByStylename("colorChangeBtn");
         this._sellItem = ComponentFactory.Instance.creatComponentByStylename("sellBtn");
         this._moveitem.y = 27;
         this._relieveBtm = ComponentFactory.Instance.creatComponentByStylename("relieveBtn");
         this._addpriceitem.addEventListener("click",this.__addpriceClick);
         this._moveitem.addEventListener("click",this.__moveClick);
         this._openitem.addEventListener("click",this.__openClick);
         this._useitem.addEventListener("click",this.__useClick);
         this._openBatchItem.addEventListener("click",this.__openBatchClick);
         this._colorChangeItem.addEventListener("click",this.__colorChangeClick);
         this._sellItem.addEventListener("click",this.__sellClick);
         this._relieveBtm.addEventListener("click",this.__relieveClick);
      }
      
      private function __relieveClick(_arg_1:MouseEvent) : void
      {
         _arg_1.stopImmediatePropagation();
         SoundManager.instance.play("008");
         dispatchEvent(new Event("relieve"));
         this.hide();
      }
      
      public function set cell(_arg_1:BagCell) : void
      {
         this._cell = _arg_1;
      }
      
      public function get cell() : BagCell
      {
         return this._cell;
      }
      
      private function __mouseClick(_arg_1:MouseEvent) : void
      {
         this.hide();
         SoundManager.instance.play("008");
      }
      
      private function __addpriceClick(_arg_1:MouseEvent) : void
      {
         _arg_1.stopImmediatePropagation();
         SoundManager.instance.play("008");
         dispatchEvent(new Event("addprice"));
         this.hide();
      }
      
      private function __moveClick(_arg_1:MouseEvent) : void
      {
         _arg_1.stopImmediatePropagation();
         SoundManager.instance.play("008");
         dispatchEvent(new Event("move"));
         this.hide();
      }
      
      private function __openClick(_arg_1:MouseEvent) : void
      {
         _arg_1.stopImmediatePropagation();
         SoundManager.instance.play("008");
         dispatchEvent(new Event("open"));
         this.hide();
      }
      
      private function __useClick(_arg_1:MouseEvent) : void
      {
         _arg_1.stopImmediatePropagation();
         SoundManager.instance.play("008");
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
         dispatchEvent(new Event("use"));
         this.hide();
      }
      
      private function __openBatchClick(_arg_1:MouseEvent) : void
      {
         _arg_1.stopImmediatePropagation();
         SoundManager.instance.play("008");
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
         dispatchEvent(new Event("open_batch"));
         this.hide();
      }
      
      private function __colorChangeClick(_arg_1:MouseEvent) : void
      {
         _arg_1.stopImmediatePropagation();
         SoundManager.instance.play("008");
         dispatchEvent(new Event("color_change"));
         this.hide();
      }
      
      private function __sellClick(_arg_1:MouseEvent) : void
      {
         _arg_1.stopImmediatePropagation();
         SoundManager.instance.play("008");
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
         dispatchEvent(new Event("delete"));
         this.hide();
      }
      
      public function show(_arg_1:BagCell, _arg_2:int, _arg_3:int) : void
      {
         this._cell = _arg_1;
         if(this._cell == null)
         {
            return;
         }
         var _local_4:ItemTemplateInfo = this._cell.info;
         if(_local_4 == null)
         {
            return;
         }
         this._bg.visible = true;
         this._bg2.visible = false;
         this._openitem.x = 0;
         this._openitem.y = 0;
         this._moveitem.x = 0;
         this._moveitem.y = 27;
         if(DressUtils.isDress(_local_4 as InventoryItemInfo))
         {
            if(InventoryItemInfo(_local_4).getRemainDate() <= 0)
            {
               this._list.addChild(this._addpriceitem);
            }
            else
            {
               this._list.addChild(this._colorChangeItem);
            }
            this._list.addChild(this._sellItem);
            this._colorChangeItem.y = -1;
            this._sellItem.y = 28;
         }
         else if(_local_4.CategoryID == 73)
         {
            if(_arg_1.itemInfo.ItemID != PlayerManager.Instance.curcentId)
            {
               this._list.addChild(this._useitem);
               this._useitem.x = -2;
               this._useitem.y = -2;
            }
            else if(_arg_1.itemInfo.getRemainDate() > 0)
            {
               this._list.addChild(this._relieveBtm);
               this._relieveBtm.x = -2;
               this._relieveBtm.y = -2;
            }
            else
            {
               this._list.addChild(this._useitem);
               this._useitem.x = -2;
               this._useitem.y = -2;
            }
            this._sellItem.x = -2;
            this._sellItem.y = 27;
            this._list.addChild(this._sellItem);
         }
         else
         {
            if(InventoryItemInfo(_local_4).getRemainDate() <= 0)
            {
               this._list.addChild(this._addpriceitem);
            }
            else if(_local_4.CategoryID == 43)
            {
               this._list.addChild(this._openitem);
               this._openitem.x = -2;
               this._openitem.y = -2;
               this._moveitem.x = -2;
               this._moveitem.y = 27;
            }
            else if(EquipType.isCardSoule(_local_4) || EquipType.isPackage(_local_4) || EquipType.isGetPackage(_local_4) || EquipType.isFireworks(_local_4) || (EquipType.isNewRedPackage(_local_4.TemplateID) || _local_4.TemplateID == 11906))
            {
               this._list.addChild(this._openitem);
               if(EquipType.isCanBatchHandler(_local_4 as InventoryItemInfo) || this.isCanBatch(_local_4.TemplateID) || (EquipType.isFireworks(_local_4) || _local_4.TemplateID == 11906))
               {
                  this._list.addChild(this._openBatchItem);
                  this._bg.visible = false;
                  this._bg2.visible = true;
                  this._openitem.x = -2;
                  this._openitem.y = -2;
                  this._openBatchItem.x = -2;
                  this._openBatchItem.y = 27;
                  this._moveitem.x = -2;
                  this._moveitem.y = 56;
               }
            }
            else if(EquipType.isBatchOnlyDouble(_local_4))
            {
               this._list.addChild(this._useitem);
               if(EquipType.isCanBatchHandler(_local_4 as InventoryItemInfo))
               {
                  this._list.addChild(this._openBatchItem);
                  this._bg.visible = false;
                  this._bg2.visible = true;
                  this._openitem.x = -2;
                  this._openitem.y = -2;
                  this._openBatchItem.x = -2;
                  this._openBatchItem.y = 27;
                  this._moveitem.x = -2;
                  this._moveitem.y = 56;
               }
            }
            else if(EquipType.isPetEgg(_local_4.CategoryID) || _local_4.CategoryID == 68)
            {
               this._list.addChild(this._openitem);
            }
            else if(EquipType.canBeUsed(_local_4))
            {
               this._list.addChild(this._useitem);
               if(EquipType.isOpenBatch(_local_4) && EquipType.isCanBatchHandler(_local_4 as InventoryItemInfo))
               {
                  this._list.addChild(this._openBatchItem);
                  this._bg.visible = false;
                  this._bg2.visible = true;
                  this._useitem.x = -2;
                  this._useitem.y = -2;
                  this._openBatchItem.x = -2;
                  this._openBatchItem.y = 27;
                  this._moveitem.x = -2;
                  this._moveitem.y = 56;
               }
            }
            this._list.addChild(this._moveitem);
         }
         LayerManager.Instance.addToLayer(this,2);
         this.x = _arg_2;
         this.y = _arg_3;
      }
      
      private function isCanBatch(_arg_1:int) : Boolean
      {
         var _local_2:* = 121999;
         var _local_3:* = 129999;
         if(_arg_1 > _local_2 && _arg_1 <= _local_3)
         {
            return true;
         }
         return false;
      }
      
      public function hide() : void
      {
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
         while(this._list.numChildren > 0)
         {
            this._list.removeChildAt(0);
         }
         this._cell = null;
      }
      
      public function get showed() : Boolean
      {
         return stage != null;
      }
   }
}

class SingletonFoce
{
   
   public function SingletonFoce()
   {
      super();
   }
}
