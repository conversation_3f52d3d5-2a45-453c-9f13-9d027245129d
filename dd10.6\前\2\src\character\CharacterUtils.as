package character
{
   import flash.geom.Point;
   
   public class CharacterUtils
   {
      
      public function CharacterUtils()
      {
         super();
      }
      
      public static function creatFrames(_arg_1:String) : Vector.<int>
      {
         var _local_5:String = null;
         var _local_6:int = 0;
         var _local_7:int = 0;
         var _local_8:int = 0;
         var _local_4:int = 0;
         var _local_2:Vector.<int> = new Vector.<int>();
         var _local_3:Array = _arg_1.split(",");
         while(_local_4 < _local_3.length)
         {
            _local_5 = _local_3[_local_4];
            if(_local_5.indexOf("-") > -1)
            {
               _local_6 = int(_local_5.split("-")[0]);
               _local_7 = int(_local_5.split("-")[1]);
               _local_8 = _local_6;
               while(_local_8 <= _local_7)
               {
                  _local_2.push(_local_8);
                  _local_8++;
               }
            }
            else
            {
               _local_2.push(int(_local_5));
            }
            _local_4++;
         }
         return _local_2;
      }
      
      public static function creatPoints(_arg_1:String) : Vector.<Point>
      {
         var _local_5:String = null;
         var _local_6:Point = null;
         var _local_4:int = 0;
         var _local_2:Vector.<Point> = new Vector.<Point>();
         var _local_3:Array = _arg_1.split("|");
         while(_local_4 < _local_3.length)
         {
            _local_5 = _local_3[_local_4];
            _local_6 = new Point(Number(_local_5.split(",")[0]),Number(_local_5.split(",")[1]));
            _local_2.push(_local_6);
            _local_4++;
         }
         return _local_2;
      }
   }
}

