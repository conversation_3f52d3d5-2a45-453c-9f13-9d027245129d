package com.pickgliss.ui.controls
{
   import com.pickgliss.geom.InnerRectangle;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.DisplayUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.text.TextField;
   
   public class TextButton extends BaseButton
   {
      
      public static const P_backOuterRect:String = "backOuterRect";
      
      public static const P_text:String = "text";
      
      public static const P_textField:String = "textField";
      
      protected var _backgoundInnerRect:InnerRectangle = new InnerRectangle(0,0,0,0,-1);
      
      protected var _backgoundInnerRectString:String;
      
      protected var _text:String = "";
      
      protected var _textField:TextField;
      
      protected var _textStyle:String;
      
      public function TextButton()
      {
         super();
      }
      
      public function set backgoundInnerRectString(_arg_1:String) : void
      {
         if(this._backgoundInnerRectString == _arg_1)
         {
            return;
         }
         this._backgoundInnerRectString = _arg_1;
         this._backgoundInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._backgoundInnerRectString));
         onPropertiesChanged("backOuterRect");
      }
      
      override public function dispose() : void
      {
         if(Boolean(this._textField))
         {
            ObjectUtils.disposeObject(this._textField);
         }
         this._textField = null;
         super.dispose();
      }
      
      public function get text() : String
      {
         return this._text;
      }
      
      public function set text(_arg_1:String) : void
      {
         if(this._text == _arg_1)
         {
            return;
         }
         this._text = _arg_1;
         onPropertiesChanged("text");
      }
      
      public function get textField() : TextField
      {
         return this._textField;
      }
      
      public function set textField(_arg_1:TextField) : void
      {
         if(this._textField == _arg_1)
         {
            return;
         }
         ObjectUtils.disposeObject(this._textField);
         this._textField = _arg_1;
         onPropertiesChanged("textField");
      }
      
      public function set textStyle(_arg_1:String) : void
      {
         if(this._textStyle == _arg_1)
         {
            return;
         }
         this._textStyle = _arg_1;
         this.textField = ComponentFactory.Instance.creat(this._textStyle);
      }
      
      override protected function addChildren() : void
      {
         super.addChildren();
         if(Boolean(this._textField))
         {
            addChild(this._textField);
         }
      }
      
      override protected function onProppertiesUpdate() : void
      {
         var _local_1:* = undefined;
         var _local_2:* = undefined;
         _local_1 = null;
         super.onProppertiesUpdate();
         if(this._textField == null)
         {
            return;
         }
         this._textField.text = this._text;
         if(_autoSizeAble)
         {
            _local_1 = this._backgoundInnerRect.getInnerRect(this._textField.textWidth,this._textField.textHeight);
            _local_2 = _local_1.width;
            _back.width = _local_2;
            _width = _local_2;
            _local_2 = _local_1.height;
            _back.height = _local_2;
            _height = _local_2;
            this._textField.x = this._backgoundInnerRect.para1;
            this._textField.y = this._backgoundInnerRect.para3;
         }
         else
         {
            _back.width = _width;
            _back.height = _height;
            this._textField.x = this._backgoundInnerRect.para1;
            this._textField.y = this._backgoundInnerRect.para3;
         }
      }
      
      override public function setFrame(_arg_1:int) : void
      {
         super.setFrame(_arg_1);
         DisplayUtils.setFrame(this._textField,_arg_1);
      }
   }
}

