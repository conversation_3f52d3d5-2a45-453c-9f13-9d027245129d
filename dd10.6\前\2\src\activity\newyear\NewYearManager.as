package activity.newyear
{
   import activity.newyear.analyzer.NewYearRankRewardAnalyzer;
   import activity.newyear.analyzer.NewYearScoreRewardAnalyzer;
   import activity.newyear.data.NewYearEvent;
   import activity.newyear.data.NewYearModel;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ClassUtils;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.LanguageMgr;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.utils.AssetModuleLoader;
   import flash.display.Sprite;
   import flash.events.EventDispatcher;
   import hallIcon.HallIconManager;
   import road7th.utils.DateUtils;
   
   public class NewYearManager extends EventDispatcher
   {
      
      private static var _instance:NewYearManager;
      
      private var _model:NewYearModel;
      
      public function NewYearManager()
      {
         super();
         this._model = new NewYearModel();
      }
      
      public static function get instance() : NewYearManager
      {
         if(_instance == null)
         {
            _instance = new NewYearManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(885,1),this.__onInit);
         SocketManager.Instance.addEventListener(PkgEvent.format(885,20),this.__onUpdatePlayerInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(885,6),this.__onUpdateRank);
         SocketManager.Instance.addEventListener(PkgEvent.format(885,10),this.__onUpdatePlayerRecord);
      }
      
      private function __onInit(_arg_1:PkgEvent) : void
      {
         var _local_3:* = null;
         var _local_2:* = null;
         this._model.activiteState = _arg_1.pkg.readByte();
         if(this._model.isOpen)
         {
            this._model.buyLimitPrice = ServerConfigManager.instance.newYearLimitBuyPrice;
            _local_3 = ServerConfigManager.instance.newYearBeginDate;
            this._model.endTime = DateUtils.getDateByStr(ServerConfigManager.instance.newYearEndDate).time + 86400000;
            _local_2 = ServerConfigManager.instance.newYearEndDate;
            this._model.date = LanguageMgr.GetTranslation("tank.newyear.activityDate",DateUtils.dateFormatString(_local_3),DateUtils.dateFormatString(_local_2));
         }
         HallIconManager.instance.updateSwitchHandler("ballgame",this._model.isOpen);
         dispatchEvent(new NewYearEvent("activitestatechange"));
      }
      
      private function __onUpdatePlayerInfo(_arg_1:PkgEvent) : void
      {
         this._model.limitCount = _arg_1.pkg.readShort();
         this._model.selfScore = _arg_1.pkg.readInt();
         this._model.selfTotalScore = _arg_1.pkg.readInt();
         this._model.progressRewards = _arg_1.pkg.readInt();
         this._model.rate = _arg_1.pkg.readInt();
         this._model.buyFightCount = _arg_1.pkg.readInt();
         dispatchEvent(new NewYearEvent("updateinfo"));
      }
      
      private function __onUpdateRank(_arg_1:PkgEvent) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_3:int = _arg_1.pkg.readShort();
         this._model.selfRank = _arg_1.pkg.readInt();
         this._model.playerRankList = [];
         var _local_2:int = _arg_1.pkg.readShort();
         _local_5 = 0;
         while(_local_5 < _local_2)
         {
            _local_4 = {};
            _local_4.id = _arg_1.pkg.readInt();
            _local_4.name = _arg_1.pkg.readUTF();
            _local_4.areaName = _arg_1.pkg.readUTF();
            _local_4.score = _arg_1.pkg.readInt();
            _local_4.rank = _arg_1.pkg.readInt();
            this._model.playerRankList.push(_local_4);
            _local_5++;
         }
         dispatchEvent(new NewYearEvent("updaterank"));
      }
      
      private function __onUpdatePlayerRecord(_arg_1:PkgEvent) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:Array = [];
         var _local_3:int = _arg_1.pkg.readByte();
         _local_5 = 0;
         while(_local_5 < _local_3)
         {
            _local_4 = {};
            _local_4.name = _arg_1.pkg.readUTF();
            _local_4.score = _arg_1.pkg.readInt();
            _local_2.push(_local_4);
            _local_5++;
         }
         this._model.fightContend = _local_2;
         dispatchEvent(new NewYearEvent("updateContent"));
      }
      
      public function show() : void
      {
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createNewYearRankRewardLoader());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createNewYearScoreRewardLoader());
         AssetModuleLoader.addModelLoader("newyear",5);
         AssetModuleLoader.startCodeLoader(this.onLoadComplete);
      }
      
      private function onLoadComplete() : void
      {
         var _local_1:Sprite = ClassUtils.CreatInstance("activity.newyear.view.NewYearMainView");
         LayerManager.Instance.addToLayer(_local_1,3,true,1);
         SocketManager.Instance.out.sendNewYearOpenView();
         SocketManager.Instance.out.sendNewYearRankList();
         SocketManager.Instance.out.sendNewYearPlayerRecord();
      }
      
      public function analyerRankReward(_arg_1:NewYearRankRewardAnalyzer) : void
      {
         this._model.rankReward = _arg_1.data;
      }
      
      public function analyerScoreReward(_arg_1:NewYearScoreRewardAnalyzer) : void
      {
         this._model.scoreReward = _arg_1.data;
      }
      
      public function get model() : NewYearModel
      {
         return this._model;
      }
   }
}

