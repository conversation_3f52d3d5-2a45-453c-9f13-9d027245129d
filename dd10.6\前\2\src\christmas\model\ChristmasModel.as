package christmas.model
{
   import christmas.ChristmasCoreManager;
   import flash.events.EventDispatcher;
   
   public class ChristmasModel extends EventDispatcher
   {
      
      public var isOpen:Boolean;
      
      public var isEnter:Boolean;
      
      public var beginTime:Date;
      
      public var endTime:Date;
      
      public var gameBeginTime:Date;
      
      public var gameEndTime:Date;
      
      public var exp:int = 0;
      
      public var totalExp:int = 10;
      
      public var awardState:int;
      
      public var packsNumber:int;
      
      public var packsLen:int;
      
      public var myGiftData:Array;
      
      public var isSelect:Boolean;
      
      public var lastPacks:int;
      
      public var money:int;
      
      public var snowPackNumber:int;
      
      public var maxSnowMenNumber:int = 2000;
      
      public var bossResureTime:Date;
      
      public var bossRemainOpenTime:int;
      
      public var bossActiveId:int;
      
      public var buffCount:int;
      
      public var remainPlayCount:int;
      
      public var bossStartTime:Date;
      
      public var bossEndTime:Date;
      
      public var bossState:int;
      
      public var myDamage:int;
      
      public var myRank:int;
      
      public var allDamage:Number;
      
      public var bossRankArr:Array = [];
      
      public var isAlert1:Boolean = false;
      
      public var isAlert2:Boolean = false;
      
      public var count:int;
      
      public var todayCount:int;
      
      public var rewardStr:String;
      
      public var rewardRemainStr:String;
      
      public var bossMaxHp:Number;
      
      public var bossCurHp:Number;
      
      public function ChristmasModel()
      {
         super();
      }
      
      public function get activityTime() : String
      {
         var _local_2:* = null;
         var _local_1:* = null;
         var _local_3:String = "";
         this.beginTime = ChristmasCoreManager.instance.model.beginTime;
         this.endTime = ChristmasCoreManager.instance.model.endTime;
         if(Boolean(this.beginTime) && Boolean(this.endTime))
         {
            _local_2 = this.beginTime.minutes > 9 ? this.beginTime.minutes + "" : "0" + this.beginTime.minutes;
            _local_1 = this.endTime.minutes > 9 ? this.endTime.minutes + "" : "0" + this.endTime.minutes;
            _local_3 = this.beginTime.fullYear + "." + (this.beginTime.month + 1) + "." + this.beginTime.date + " - " + this.endTime.fullYear + "." + (this.endTime.month + 1) + "." + this.endTime.date;
         }
         return _local_3;
      }
   }
}

