package com.hurlant.util
{
   import flash.utils.ByteArray;
   
   public class Hex
   {
      
      public function Hex()
      {
         super();
      }
      
      public static function dump(_arg_1:ByteArray) : String
      {
         var _local_5:int = 0;
         var _local_4:uint = 0;
         var _local_2:String = "";
         var _local_3:String = "";
         while(_local_4 < _arg_1.length)
         {
            if(_local_4 % 16 == 0)
            {
               _local_2 += ("00000000" + _local_4.toString(16)).substr(-8,8) + " ";
            }
            if(_local_4 % 8 == 0)
            {
               _local_2 += " ";
            }
            _local_5 = int(_arg_1[_local_4]);
            _local_2 += ("0" + _local_5.toString(16)).substr(-2,2) + " ";
            _local_3 += _local_5 < 32 || _local_5 > 126 ? "." : String.fromCharCode(_local_5);
            if((_local_4 + 1) % 16 == 0 || _local_4 == _arg_1.length - 1)
            {
               _local_2 += " |" + _local_3 + "|\n";
               _local_3 = "";
            }
            _local_4++;
         }
         return _local_2;
      }
      
      public static function fromString(_arg_1:String, _arg_2:Boolean = false) : String
      {
         var _local_3:ByteArray = new ByteArray();
         _local_3.writeUTFBytes(_arg_1);
         return fromArray(_local_3,_arg_2);
      }
      
      public static function toString(_arg_1:String) : String
      {
         var _local_2:ByteArray = toArray(_arg_1);
         return _local_2.readUTFBytes(_local_2.length);
      }
      
      public static function undump(_arg_1:String) : ByteArray
      {
         var _local_4:Array = null;
         var _local_2:ByteArray = null;
         _local_2 = new ByteArray();
         var _local_3:Array = _arg_1.split(/(\n|\r)+/);
         while(_local_3.length > 0 && _local_3[0].length > 0)
         {
            _local_4 = _local_3.shift().substr(10).split("  |")[0].split(/\s+/);
            while(_local_4.length > 0 && _local_4[0].length > 0)
            {
               _local_2.writeByte(parseInt(_local_4.shift(),16));
            }
         }
         _local_2.position = 0;
         return _local_2;
      }
      
      public static function toArray(_arg_1:String) : ByteArray
      {
         var _local_3:uint = 0;
         _arg_1 = _arg_1.replace(/\s|:/gm,"");
         var _local_2:ByteArray = new ByteArray();
         if(Boolean(_arg_1.length) && 1 == 1)
         {
            _arg_1 = "0" + _arg_1;
         }
         while(_local_3 < _arg_1.length)
         {
            _local_2[_local_3 / 2] = parseInt(_arg_1.substr(_local_3,2),16);
            _local_3 += 2;
         }
         return _local_2;
      }
      
      public static function fromArray(_arg_1:ByteArray, _arg_2:Boolean = false) : String
      {
         var _local_4:uint = 0;
         var _local_3:String = "";
         while(_local_4 < _arg_1.length)
         {
            _local_3 += ("0" + _arg_1[_local_4].toString(16)).substr(-2,2);
            if(_arg_2)
            {
               if(_local_4 < _arg_1.length - 1)
               {
                  _local_3 += ":";
               }
            }
            _local_4++;
         }
         return _local_3;
      }
   }
}

