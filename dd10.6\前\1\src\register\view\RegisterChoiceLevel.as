package register.view
{
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import register.RegisterSoundManager;
   
   public class RegisterChoiceLevel extends Sprite implements Disposeable
   {
      
      public var registerSelectLevel:Boolean;
      
      private var _mc:MovieClip;
      
      private var _bg:DisplayObject;
      
      public function RegisterChoiceLevel()
      {
         super();
         init();
      }
      
      private function init() : void
      {
         _bg = ClassUtils.CreatInstance("asset.loading.RegisterBgAsset") as DisplayObject;
         addChild(_bg);
         _mc = ClassUtils.CreatInstance("asset.choicelevel.MainView") as MovieClip;
         _mc.x = 188;
         _mc.y = 47;
         addChild(_mc);
         _mc["newPlayerBtn"].buttonMode = _mc["oldPlayerBtn"].buttonMode = true;
         _mc["newPlayerBtn"].addEventListener("click",__onClickNewPlayer);
         _mc["oldPlayerBtn"].addEventListener("click",__onClickOldPlayer);
      }
      
      private function __onClickOldPlayer(param1:MouseEvent) : void
      {
         RegisterSoundManager.instance.playButton();
         registerSelectLevel = true;
         dispatchEvent(new Event("select"));
      }
      
      private function __onClickNewPlayer(param1:MouseEvent) : void
      {
         RegisterSoundManager.instance.playButton();
         registerSelectLevel = false;
         dispatchEvent(new Event("select"));
      }
      
      public function dispose() : void
      {
         _mc["newPlayerBtn"].removeEventListener("click",__onClickNewPlayer);
         _mc["oldPlayerBtn"].removeEventListener("click",__onClickOldPlayer);
         ObjectUtils.disposeObject(_mc);
         _mc = null;
         ObjectUtils.disposeObject(_bg);
         _bg = null;
      }
   }
}

