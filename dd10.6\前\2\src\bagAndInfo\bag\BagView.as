package bagAndInfo.bag
{
   import bagAndInfo.BagAndInfoManager;
   import bagAndInfo.ReworkName.ReworkNameBattleTeam;
   import bagAndInfo.ReworkName.ReworkNameConsortia;
   import bagAndInfo.ReworkName.ReworkNameFrame;
   import bagAndInfo.cell.BagCell;
   import bagAndInfo.changeSex.ChangeSexAlertFrame;
   import baglocked.BaglockedManager;
   import beadSystem.beadSystemManager;
   import beadSystem.controls.BeadBagList;
   import beadSystem.controls.BeadCell;
   import beadSystem.controls.BeadFeedButton;
   import beadSystem.controls.BeadLockButton;
   import beadSystem.data.BeadEvent;
   import beadSystem.model.BeadModel;
   import calendar.CalendarManager;
   import com.pickgliss.events.ComponentEvent;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.OutMainListPanel;
   import com.pickgliss.ui.controls.SelectedButton;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.image.MovieImage;
   import com.pickgliss.ui.image.MutipleImage;
   import com.pickgliss.ui.image.ScaleBitmapImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.ui.vo.AlertInfo;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.view.selfConsortia.ConsortionBankBagView;
   import ddt.bagStore.BagStore;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.data.player.SelfInfo;
   import ddt.events.BagEvent;
   import ddt.events.CEvent;
   import ddt.events.CellEvent;
   import ddt.events.CrazyTankSocketEvent;
   import ddt.events.PkgEvent;
   import ddt.events.PlayerPropertyEvent;
   import ddt.manager.BeadTemplateManager;
   import ddt.manager.ChangeColorManager;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.LeavePageManager;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.RandomSuitCardManager;
   import ddt.manager.RouletteManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SharedManager;
   import ddt.manager.ShopManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.TimeManager;
   import ddt.utils.ConfirmAlertData;
   import ddt.utils.HelperBuyAlert;
   import ddt.utils.PositionUtils;
   import ddt.view.chat.ChatBugleInputFrame;
   import ddt.view.goods.AddPricePanel;
   import elf.ElfManager;
   import flash.display.Bitmap;
   import flash.display.MovieClip;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.external.ExternalInterface;
   import flash.filters.GlowFilter;
   import flash.geom.Point;
   import flash.media.SoundTransform;
   import flash.ui.Mouse;
   import flash.utils.getQualifiedClassName;
   import horse.HorseManager;
   import itemActivityGift.ItemActivityGiftManager;
   import newRedPackage.NewRedPackageManager;
   import petsBag.PetsBagManager;
   import petsSystem.PetsManager;
   import playerDress.PlayerDressManager;
   import playerDress.components.DressModel;
   import playerDress.components.DressUtils;
   import playerDress.event.PlayerDressEvent;
   import quest.TaskManager;
   import quest.TrusteeshipManager;
   import road7th.comm.PackageIn;
   import road7th.data.DictionaryEvent;
   import road7th.utils.DateUtils;
   import texpSystem.controller.TexpManager;
   import uigeneral.rewardSelect.RewardSelectBox;
   import vipCoupons.VIPCouponsManager;
   import wonderfulActivity.WonderfulActivityManager;
   
   [Event(name="sellstart")]
   [Event(name="sellstop")]
   public class BagView extends Sprite implements Disposeable
   {
      
      public static const FIRST_GET_CARD:String = "firstGetCard";
      
      public static const TABCHANGE:String = "tabChange";
      
      public static const SHOWBEAD:String = "showBeadFrame";
      
      public static const EQUIP:int = 0;
      
      public static const PROP:int = 1;
      
      public static const CARD:int = 2;
      
      public static const CONSORTION:int = 3;
      
      public static const PET:int = 5;
      
      public static const BEAD:int = 21;
      
      public static const DRESS:int = 8;
      
      public static const MAGICHOUSE:int = 51;
      
      public static const MARK:int = 100;
      
      public static const EMBLEM:int = 101;
      
      private static const UseColorShellLevel:int = 10;
      
      public static var isShowCardBag:Boolean = false;
      
      private static var _sortEquipBagData:ConfirmAlertData = new ConfirmAlertData();
      
      private static var _sortPetEquipBagData:ConfirmAlertData = new ConfirmAlertData();
      
      private const STATE_SELL:uint = 1;
      
      private const STATE_BEADFEED:uint = 1;
      
      protected var _equipBagPage:int = 1;
      
      private var _index:int = 0;
      
      private var bead_state:uint = 0;
      
      protected var _bgShape:Shape;
      
      protected var _bgShapeII:MovieImage;
      
      private var state:uint = 0;
      
      private var _info:SelfInfo;
      
      protected var _equiplist:BagEquipListView;
      
      protected var _proplist:BagListView;
      
      protected var _petlist:PetBagListView;
      
      protected var _beadList:BeadBagList;
      
      protected var _beadList2:BeadBagList;
      
      protected var _beadList3:BeadBagList;
      
      protected var _dressbagView:Sprite;
      
      protected var _currIndex:int = 1;
      
      protected var _sellBtn:SellGoodsBtn;
      
      protected var _continueBtn:ContinueGoodsBtn;
      
      protected var _lists:Array;
      
      protected var _currentList:BagListView;
      
      protected var _currentBeadList:BeadBagList;
      
      protected var _breakBtn:BreakGoodsBtn;
      
      protected var _keySortBtn:TextButton;
      
      private var _chatBugleInputFrame:ChatBugleInputFrame;
      
      protected var _bagType:int;
      
      private var _self:SelfInfo = PlayerManager.Instance.Self;
      
      private var _beadFeedBtn:BeadFeedButton;
      
      private var _beadLockBtn:BeadLockButton;
      
      private var _beadOneKeyBtn:SimpleBitmapButton;
      
      protected var _goldText:FilterFrameText;
      
      protected var _moneyText:FilterFrameText;
      
      protected var _giftText:FilterFrameText;
      
      protected var _orderTxt:FilterFrameText;
      
      protected var _goldButton:RichesButton;
      
      protected var _giftButton:RichesButton;
      
      protected var _moneyButton:RichesButton;
      
      protected var _bg:MutipleImage;
      
      protected var _bg1:MovieImage;
      
      protected var _tabBtn1:Sprite;
      
      protected var _tabBtn2:Sprite;
      
      protected var _tabBtn3:Sprite;
      
      protected var _tabBtn4:Sprite;
      
      protected var _cardEnbleFlase:Bitmap;
      
      protected var _goodsNumInfoBg:Bitmap;
      
      protected var _goodsNumInfoText:FilterFrameText;
      
      protected var _goodsNumTotalText:FilterFrameText;
      
      protected var _moneyBg:ScaleBitmapImage;
      
      protected var _moneyBg1:ScaleBitmapImage;
      
      protected var _moneyBg2:ScaleBitmapImage;
      
      protected var _moneyBg3:ScaleBitmapImage;
      
      protected var _PointCouponBitmap:Bitmap;
      
      protected var _LiJinBitmap:Bitmap;
      
      protected var _MoneyBitmap:Bitmap;
      
      protected var _orderTxtBitmap:Bitmap;
      
      private var _reworknameView:ReworkNameFrame;
      
      private var _consortaiReworkName:ReworkNameConsortia;
      
      private var _battleTeamReworkName:ReworkNameBattleTeam;
      
      private var _baseAlerFrame:BaseAlerFrame;
      
      protected var _isScreenFood:Boolean = false;
      
      private var _bagList:OutMainListPanel;
      
      private var _pageTxt:FilterFrameText;
      
      private var _pgdn:BaseButton;
      
      private var _pgup:BaseButton;
      
      private var _pageTxtBg:Bitmap;
      
      private var _beadSortBtn:TextButton;
      
      protected var _equipEnbleFlase:Bitmap;
      
      protected var _propEnbleFlase:Bitmap;
      
      protected var _beadEnbleFlase:Bitmap;
      
      private var _disEnabledFilters:Array;
      
      private var _oneKeyFeedMC:MovieClip;
      
      protected var _buttonContainer:Sprite;
      
      protected var _bagArrangeSprite:BagArrangeTipSprite;
      
      protected var _equipSelectedBtn:SelectedButton;
      
      protected var _propSelectedBtn:SelectedButton;
      
      protected var _beadSelectedBtn:SelectedButton;
      
      protected var _dressSelectedBtn:SelectedButton;
      
      public var bagLockBtn:SimpleBitmapButton;
      
      private var _allExp:int;
      
      private var _feedVec:Vector.<Vector.<BeadCell>>;
      
      private var _bindVec:Vector.<Boolean>;
      
      private var _feedID:int = 0;
      
      private var _id:int;
      
      private var clickSign:int = 0;
      
      private var temInfo:InventoryItemInfo;
      
      private var _currentCell:BagCell;
      
      private var _getFriendPackFrame:GetFriendPackFrame;
      
      private var _tmpCell:BagCell;
      
      private var getNewCardMovie:MovieClip;
      
      private var _soundControl:SoundTransform;
      
      public var _orderBtn:RichesButton;
      
      private var _currentUseItemTemp:BagCell;
      
      public function BagView()
      {
         super();
         this._buttonContainer = ComponentFactory.Instance.creatCustomObject("bagAndInfo.bagView.buttonContainer");
         this.init();
         this.initEvent();
      }
      
      public function get bagType() : int
      {
         return this._bagType;
      }
      
      protected function init() : void
      {
         this.initBackGround();
         this.initBagList();
         this.initMoneyTexts();
         this.initButtons();
         this.initTabButtons();
         this.initGoodsNumInfo();
         this.set_breakBtn_enable();
         this.set_text_location();
         this.set_btn_location();
         this.setBagType(0);
      }
      
      protected function initBackGround() : void
      {
         this._bg = ComponentFactory.Instance.creatComponentByStylename("bagBGAsset4");
         addChild(this._bg);
         this._equipSelectedBtn = ComponentFactory.Instance.creatComponentByStylename("bagView.equipTabBtn");
         this._equipSelectedBtn.mouseEnabled = false;
         this._equipSelectedBtn.mouseChildren = false;
         this._equipSelectedBtn.selected = true;
         addChild(this._equipSelectedBtn);
         this._propSelectedBtn = ComponentFactory.Instance.creatComponentByStylename("bagView.propTabBtn");
         this._propSelectedBtn.mouseEnabled = false;
         this._propSelectedBtn.mouseChildren = false;
         addChild(this._propSelectedBtn);
         this._beadSelectedBtn = ComponentFactory.Instance.creatComponentByStylename("bagView.beadTabBtn");
         this._beadSelectedBtn.mouseEnabled = false;
         this._beadSelectedBtn.mouseChildren = false;
         this._beadSelectedBtn.visible = false;
         addChild(this._beadSelectedBtn);
         this._dressSelectedBtn = ComponentFactory.Instance.creatComponentByStylename("bagView.dressTabBtn");
         this._dressSelectedBtn.mouseEnabled = false;
         this._dressSelectedBtn.mouseChildren = false;
         addChild(this._dressSelectedBtn);
         this._bg1 = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.view.bgIII");
         this._buttonContainer.addChild(this._bg1);
         this._moneyBg = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.moneyViewBG");
         this._buttonContainer.addChild(this._moneyBg);
         this._moneyBg1 = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.moneyViewBGI");
         this._buttonContainer.addChild(this._moneyBg1);
         this._moneyBg2 = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.moneyViewBGII");
         this._buttonContainer.addChild(this._moneyBg2);
         this._moneyBg3 = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.moneyViewBGIII");
         this._buttonContainer.addChild(this._moneyBg3);
         this._bgShape = new Shape();
         this._bgShape.graphics.beginFill(15262671,1);
         this._bgShape.graphics.drawRoundRect(0,0,327,328,2,2);
         this._bgShape.graphics.endFill();
         this._bgShape.x = 11;
         this._bgShape.y = 50;
         this._bgShapeII = ComponentFactory.Instance.creatComponentByStylename("asset.ddtcardCell.BG");
         addChild(this._bgShapeII);
         this._bgShapeII.visible = false;
         this.bagLockBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.lockBtn");
         addChild(this.bagLockBtn);
      }
      
      protected function initBagList() : void
      {
         this._equiplist = new BagEquipListView(0,31,79,7,this._equipBagPage);
         this._proplist = new BagProListView(1,0,48);
         this._petlist = new PetBagListView(1);
         PositionUtils.setPos(this._petlist,"bagAndInfo.bagView.petBag.pos18");
         this._beadList = new BeadBagList(21,32,80);
         this._beadList2 = new BeadBagList(21,81,129);
         this._beadList3 = new BeadBagList(21,130,178);
         var _local_1:* = 14;
         this._beadList3.x = _local_1;
         this._beadList2.x = _local_1;
         this._beadList.x = _local_1;
         this._proplist.x = _local_1;
         this._equiplist.x = _local_1;
         _local_1 = 48;
         this._beadList3.y = _local_1;
         this._beadList2.y = _local_1;
         this._beadList.y = _local_1;
         this._proplist.y = _local_1;
         this._equiplist.y = _local_1;
         _local_1 = 330;
         this._petlist.width = _local_1;
         this._beadList3.width = _local_1;
         this._beadList2.width = _local_1;
         this._beadList.width = _local_1;
         this._proplist.width = _local_1;
         this._equiplist.width = _local_1;
         _local_1 = 320;
         this._petlist.height = _local_1;
         this._beadList3.height = _local_1;
         this._beadList2.height = _local_1;
         this._beadList.height = _local_1;
         this._proplist.height = _local_1;
         this._equiplist.height = _local_1;
         this._proplist.visible = false;
         this._petlist.visible = false;
         this._beadList.visible = false;
         this._beadList2.visible = false;
         this._beadList3.visible = false;
         this._lists = [this._equiplist,this._proplist,this._petlist,this._beadList,this._beadList2,this._beadList3];
         this._currentList = this._equiplist;
         addChild(this._equiplist);
         addChild(this._proplist);
         addChild(this._petlist);
         addChild(this._beadList);
         addChild(this._beadList2);
         addChild(this._beadList3);
      }
      
      protected function initMoneyTexts() : void
      {
         this._moneyText = ComponentFactory.Instance.creatComponentByStylename("BagMoneyInfoText");
         this._goldText = ComponentFactory.Instance.creatComponentByStylename("BagGoldInfoText");
         this._giftText = ComponentFactory.Instance.creatComponentByStylename("BagGiftInfoText");
         this._giftText.x = 147;
         this._giftText.y = 385;
         this._orderTxt = ComponentFactory.Instance.creatComponentByStylename("BagGiftInfoText");
         this._orderTxt.x = 146;
         this._orderTxt.y = 418;
         this._buttonContainer.addChild(this._goldText);
         this._buttonContainer.addChild(this._moneyText);
         this._buttonContainer.addChild(this._giftText);
         this._buttonContainer.addChild(this._orderTxt);
         this._goldButton = ComponentFactory.Instance.creatCustomObject("bagAndInfo.bag.GoldButton");
         this._goldButton.tipData = LanguageMgr.GetTranslation("tank.view.bagII.GoldDirections");
         this._orderBtn = ComponentFactory.Instance.creatCustomObject("bagAndInfo.bag.OrderBtn");
         this._buttonContainer.addChild(this._goldButton);
         this._buttonContainer.addChild(this._orderBtn);
         var _local_2:int = ServerConfigManager.instance.getBindBidLimit(PlayerManager.Instance.Self.Grade,PlayerManager.Instance.Self.VIPLevel);
         var _local_1:int = int(ServerConfigManager.instance.VIPExtraBindMoneyUpper[PlayerManager.Instance.Self.VIPLevel - 1]);
         this._giftButton = ComponentFactory.Instance.creatCustomObject("bagAndInfo.bag.GiftButton");
         this._giftButton.tipData = LanguageMgr.GetTranslation("tank.view.bagII.GiftDirections",_local_2.toString());
         if(PlayerManager.Instance.Self.IsVIP)
         {
            this._orderBtn.tipData = LanguageMgr.GetTranslation("tank.view.bagII.MedalDirections",ServerConfigManager.instance.getBindBidLimit(PlayerManager.Instance.Self.Grade,PlayerManager.Instance.Self.VIPLevel));
         }
         else
         {
            this._orderBtn.tipData = LanguageMgr.GetTranslation("tank.view.bagII.MedalDirections",ServerConfigManager.instance.getBindBidLimit(PlayerManager.Instance.Self.Grade));
         }
         this._buttonContainer.addChild(this._giftButton);
         this._moneyButton = ComponentFactory.Instance.creatCustomObject("bagAndInfo.bag.MoneyButton");
         this._moneyButton.tipData = LanguageMgr.GetTranslation("tank.view.bagII.MoneyDirections");
         this._buttonContainer.addChild(this._moneyButton);
      }
      
      protected function initButtons() : void
      {
         this._sellBtn = ComponentFactory.Instance.creatComponentByStylename("bagSellButton1");
         this._sellBtn.text = LanguageMgr.GetTranslation("tank.view.bagII.BagIIView.bagSell");
         this._buttonContainer.addChild(this._sellBtn);
         this._continueBtn = ComponentFactory.Instance.creatComponentByStylename("bagContinueButton1");
         this._continueBtn.text = LanguageMgr.GetTranslation("tank.view.bagII.BagIIView.bagContinue");
         this._buttonContainer.addChild(this._continueBtn);
         this._breakBtn = ComponentFactory.Instance.creatComponentByStylename("bagBreakButton1");
         this._breakBtn.text = LanguageMgr.GetTranslation("tank.view.bagII.BagIIView.bagBreak");
         this._buttonContainer.addChild(this._breakBtn);
         this._keySortBtn = ComponentFactory.Instance.creatComponentByStylename("bagKeySetButton1");
         this._keySortBtn.text = LanguageMgr.GetTranslation("tank.view.bagII.BagIIView.bagSortTxt");
         this._buttonContainer.addChild(this._keySortBtn);
         this._keySortBtn.enable = this._isSkillCanUse();
         this._PointCouponBitmap = ComponentFactory.Instance.creatBitmap("bagAndInfo.info.PointCoupon");
         this._buttonContainer.addChild(this._PointCouponBitmap);
         this._LiJinBitmap = ComponentFactory.Instance.creatBitmap("bagAndInfo.info.ddtMoney1");
         this._buttonContainer.addChild(this._LiJinBitmap);
         this._MoneyBitmap = ComponentFactory.Instance.creatBitmap("bagAndInfo.info.money");
         this._buttonContainer.addChild(this._MoneyBitmap);
         this._orderTxtBitmap = ComponentFactory.Instance.creat("bagAndInfo.order");
         this._orderTxtBitmap.x = 120;
         this._orderTxtBitmap.y = 415;
         this._buttonContainer.addChild(this._orderTxtBitmap);
      }
      
      public function set sortBagEnable(_arg_1:Boolean) : void
      {
         this._keySortBtn.enable = _arg_1;
      }
      
      public function set breakBtnEnable(_arg_1:Boolean) : void
      {
         this._breakBtn.enable = _arg_1;
      }
      
      public function set cardbtnVible(_arg_1:Boolean) : void
      {
         this._cardEnbleFlase.visible = _arg_1;
      }
      
      public function set cardbtnFilter(_arg_1:Array) : void
      {
         this._cardEnbleFlase.filters = _arg_1;
      }
      
      public function set sortBagFilter(_arg_1:Array) : void
      {
         this._keySortBtn.filters = _arg_1;
      }
      
      public function set breakBtnFilter(_arg_1:Array) : void
      {
         this._breakBtn.filters = _arg_1;
      }
      
      public function set tableEnable(_arg_1:Boolean) : void
      {
         this._tabBtn3.mouseEnabled = _arg_1;
      }
      
      public function switchButtomVisible(_arg_1:Boolean) : void
      {
         this._bg1.visible = _arg_1;
         this._sellBtn.visible = _arg_1;
         this._breakBtn.visible = _arg_1;
         this._continueBtn.visible = _arg_1;
         this._keySortBtn.visible = _arg_1;
         this._goldText.visible = _arg_1;
         this._giftButton.visible = _arg_1;
         this._giftText.visible = _arg_1;
         this._moneyButton.visible = _arg_1;
         this._orderTxt.visible = _arg_1;
         this._orderBtn.visible = _arg_1;
         this._moneyBg3.visible = _arg_1;
         if(Boolean(this._pgup))
         {
            this._pgup.visible = !_arg_1;
         }
         if(Boolean(this._pgdn))
         {
            this._pgdn.visible = !_arg_1;
         }
         if(Boolean(this._pageTxt))
         {
            this._pageTxt.visible = !_arg_1;
         }
         if(Boolean(this._pageTxtBg))
         {
            this._pageTxtBg.visible = !_arg_1;
         }
         if(Boolean(this._beadFeedBtn))
         {
            this._beadFeedBtn.visible = !_arg_1;
         }
         if(Boolean(this._beadLockBtn))
         {
            this._beadLockBtn.visible = !_arg_1;
         }
         if(Boolean(this._beadOneKeyBtn))
         {
            this._beadOneKeyBtn.visible = !_arg_1;
         }
         if(Boolean(this._beadSortBtn))
         {
            this._beadSortBtn.visible = !_arg_1;
         }
         this.enableBeadFunctionBtns(!_arg_1);
         if(Boolean(this._moneyBg1))
         {
            this._moneyBg1.visible = _arg_1;
         }
         if(Boolean(this._moneyBg2))
         {
            this._moneyBg2.visible = _arg_1;
         }
         if(Boolean(this._LiJinBitmap))
         {
            this._LiJinBitmap.visible = _arg_1;
         }
         if(Boolean(this._MoneyBitmap))
         {
            this._MoneyBitmap.visible = _arg_1;
         }
         if(Boolean(this._orderTxtBitmap))
         {
            this._orderTxtBitmap.visible = _arg_1;
         }
      }
      
      public function enableBeadFunctionBtns(_arg_1:Boolean) : void
      {
         if(Boolean(this._beadFeedBtn))
         {
            this._beadFeedBtn.enable = _arg_1;
         }
         if(Boolean(this._beadLockBtn))
         {
            this._beadLockBtn.enable = _arg_1;
         }
         if(Boolean(this._beadOneKeyBtn))
         {
            this._beadOneKeyBtn.enable = _arg_1;
         }
         if(Boolean(this._beadSortBtn))
         {
            this._beadSortBtn.enable = _arg_1;
         }
      }
      
      public function initBeadButton() : void
      {
         this._pgup = ComponentFactory.Instance.creatComponentByStylename("beadSystem.prePageBtn");
         addChild(this._pgup);
         this._pgdn = ComponentFactory.Instance.creatComponentByStylename("beadSystem.nextPageBtn");
         addChild(this._pgdn);
         this._pageTxtBg = ComponentFactory.Instance.creatBitmap("beadSystem.pageTxt.bg");
         addChild(this._pageTxtBg);
         this._pageTxt = ComponentFactory.Instance.creatComponentByStylename("beadSystem.pageTxt");
         addChild(this._pageTxt);
         this._pageTxt.text = "1/3";
         this._pgup.addEventListener("click",this.__pgupHandler);
         this._pgdn.addEventListener("click",this.__pgdnHandler);
         this._beadSortBtn = ComponentFactory.Instance.creatComponentByStylename("beadSystem.sortBtn");
         this._beadSortBtn.text = LanguageMgr.GetTranslation("ddt.beadSystem.sortBtnTxt");
         this._beadSortBtn.addEventListener("click",this.__sortBagClick,false,0,true);
         addChild(this._beadSortBtn);
         this._beadFeedBtn = ComponentFactory.Instance.creatComponentByStylename("beadSystem.feedbtn1");
         this._beadFeedBtn.width = 106;
         this._beadFeedBtn.tipStyle = "ddtstore.StoreEmbedBG.MultipleLineTip";
         this._beadFeedBtn.tipData = LanguageMgr.GetTranslation("ddt.bagandinfo.beadTip");
         addChild(this._beadFeedBtn);
         this._beadLockBtn = ComponentFactory.Instance.creatComponentByStylename("beadSystem.lockbtn1");
         this._beadLockBtn.tipStyle = "ddtstore.StoreEmbedBG.MultipleLineTip";
         this._beadLockBtn.tipData = LanguageMgr.GetTranslation("ddt.bagandinfo.beadLockTip");
         addChild(this._beadLockBtn);
         this._beadOneKeyBtn = ComponentFactory.Instance.creatComponentByStylename("beadSystem.feedAllBtn");
         this._beadOneKeyBtn.tipStyle = "ddtstore.StoreEmbedBG.MultipleLineTip";
         this._beadOneKeyBtn.tipData = LanguageMgr.GetTranslation("ddt.bagandinfo.beadOneKeyTip");
         addChild(this._beadOneKeyBtn);
         this._beadOneKeyBtn.addEventListener("click",this.__oneKeyFeedClick);
      }
      
      public function adjustBeadBagPage(_arg_1:Boolean) : void
      {
         var _local_3:InventoryItemInfo = null;
         var _local_2:int = 0;
         var _local_4:* = 2147483647;
         for each(_local_3 in this._info.BeadBag.items)
         {
            if(_local_3.Place < _local_4 && _local_3.Place > 31 && (!_arg_1 || !_local_3.IsBinds))
            {
               _local_4 = _local_3.Place;
            }
         }
         _local_2 = int(int(int((_local_4 - 32) / 49 + 1)));
         if(_local_2 <= 0 || _local_2 > 3)
         {
            _local_2 = 1;
         }
         if(Boolean(this._pageTxt))
         {
            this._pageTxt.text = _local_2 + "/3";
         }
         this._beadList.visible = _local_2 == 1;
         this._beadList2.visible = _local_2 == 2;
         this._beadList3.visible = _local_2 == 3;
         this._currentBeadList = [this._beadList,this._beadList2,this._beadList3][_local_2 - 1];
      }
      
      public function __oneKeyFeedClick(_arg_1:MouseEvent) : void
      {
         var _local_7:int = 0;
         var _local_6:Boolean = false;
         var _local_5:int = 0;
         var _local_2:Boolean = false;
         var _local_4:int = 0;
         var _local_3:BeadCell = null;
         SoundManager.instance.play("008");
         if(BeadModel.beadCanUpgrade)
         {
            if(PlayerManager.Instance.Self.embedUpLevelCell.itemInfo.Hole1 == 21)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.beadSystem.mostHightLevel"));
               return;
            }
            _local_7 = 0;
            _local_6 = false;
            this._feedID = 0;
            if(!this._feedVec)
            {
               this._feedVec = new Vector.<Vector.<BeadCell>>(8);
               this._bindVec = new Vector.<Boolean>(8);
            }
            _local_5 = 0;
            while(_local_5 < 8)
            {
               if(!this._feedVec[_local_5])
               {
                  this._feedVec[_local_5] = new Vector.<BeadCell>();
                  this._bindVec[_local_5] = false;
               }
               else
               {
                  this._feedVec[_local_5].length = 0;
               }
               _local_5++;
            }
            for each(_local_3 in this._currentBeadList.BeadCells)
            {
               if(Boolean(_local_3.info) && !_local_3.itemInfo.IsUsed)
               {
                  if(_local_3.itemInfo.Hole1 < 13 && BeadTemplateManager.Instance.GetBeadInfobyID(_local_3.itemInfo.TemplateID).Type3 <= 0)
                  {
                     this._feedVec[0].push(_local_3);
                     _local_7 += _local_3.itemInfo.Hole2;
                     _local_3.locked = true;
                     if(!this._bindVec[0] && _local_3.itemInfo.IsBinds)
                     {
                        this._bindVec[0] = true;
                     }
                  }
                  else if(_local_3.itemInfo.Hole1 == 13)
                  {
                     this._feedVec[1].push(_local_3);
                  }
                  else if(_local_3.itemInfo.Hole1 == 14)
                  {
                     this._feedVec[2].push(_local_3);
                  }
                  else if(_local_3.itemInfo.Hole1 == 15)
                  {
                     this._feedVec[3].push(_local_3);
                  }
                  else if(_local_3.itemInfo.Hole1 == 16 || BeadTemplateManager.Instance.GetBeadInfobyID(_local_3.itemInfo.TemplateID).Type3 > 0)
                  {
                     this._feedVec[4].push(_local_3);
                  }
                  else if(_local_3.itemInfo.Hole1 == 17)
                  {
                     this._feedVec[5].push(_local_3);
                  }
                  else if(_local_3.itemInfo.Hole1 == 18)
                  {
                     this._feedVec[6].push(_local_3);
                  }
                  else if(_local_3.itemInfo.Hole1 == 19)
                  {
                     this._feedVec[7].push(_local_3);
                  }
                  else if(_local_3.itemInfo.Hole1 == 20)
                  {
                     this._feedVec[7].push(_local_3);
                  }
                  else if(_local_3.itemInfo.Hole1 == 21)
                  {
                     this._feedVec[7].push(_local_3);
                  }
               }
            }
            if(_local_7 == 0)
            {
               _local_2 = true;
               _local_4 = 1;
               while(_local_4 < 8)
               {
                  if(this._feedVec[_local_4].length > 0)
                  {
                     _local_2 = false;
                     break;
                  }
                  _local_4++;
               }
               if(_local_2)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.beadSystem.noBeadToFeed"));
               }
               else
               {
                  this._feedID = _local_4;
                  this.checkBoxPrompts(this._feedID);
               }
               return;
            }
            this._allExp = _local_7;
            this.boxPrompts(this._bindVec[0]);
         }
         else
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.beadSystem.tipNoFeedBead"));
         }
      }
      
      private function checkNextBox() : void
      {
         ++this._feedID;
         if(this._feedID < 5)
         {
            this.checkBoxPrompts(this._feedID);
         }
      }
      
      private function checkBoxPrompts(_arg_1:int) : void
      {
         var _local_3:int = 0;
         this._allExp = 0;
         this._id = _arg_1;
         var _local_2:int = int(this._feedVec[_arg_1].length);
         if(_local_2 > 0)
         {
            _local_3 = 0;
            while(_local_3 < _local_2)
            {
               this._allExp += this._feedVec[_arg_1][_local_3].itemInfo.Hole2;
               this._feedVec[_arg_1][_local_3].locked = true;
               if(!this._bindVec[_arg_1] && this._feedVec[_arg_1][_local_3].itemInfo.IsBinds)
               {
                  this._bindVec[_arg_1] = true;
               }
               _local_3++;
            }
            beadSystemManager.Instance.addEventListener("createComplete",this.__onCreateComplete);
            beadSystemManager.Instance.showFrame("infoframe");
         }
         else
         {
            ++this._feedID;
            if(this._feedID < 5)
            {
               this.checkBoxPrompts(this._feedID);
            }
            else
            {
               this._feedID = 0;
            }
         }
      }
      
      private function __onCreateComplete(_arg_1:CEvent) : void
      {
         var _local_3:* = undefined;
         var _local_4:* = undefined;
         var _local_2:* = null;
         beadSystemManager.Instance.removeEventListener("createComplete",this.__onCreateComplete);
         if(_arg_1.data.type == "infoframe")
         {
            _local_2 = _arg_1.data.spr;
            _local_3 = _local_2;
            _local_4 = _local_3;
            _local_4["setBeadName"](this._feedVec[this._id][0].tipData["beadName"]);
            LayerManager.Instance.addToLayer(_local_2,1,true,1);
            _local_2["textInput"].setFocus();
            _local_2["isBind"] = this._bindVec[this._id];
            _local_2.addEventListener("response",this.__onConfigResponse);
         }
      }
      
      private function boxPrompts(_arg_1:Boolean) : void
      {
         var _local_4:* = null;
         var _local_3:* = null;
         var _local_2:* = null;
         if(_arg_1 && !BeadModel.isBeadCellIsBind)
         {
            _local_4 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("ddt.beadSystem.useBindBead"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,true,false,2);
            _local_4.addEventListener("response",this.__onBindRespones);
         }
         else
         {
            _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("ddt.beadSystem.FeedBeadConfirm"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,true,false,2);
            _local_2 = ComponentFactory.Instance.creatComponentByStylename("beadSystem.feedBeadShowExpTextOneFeed");
            _local_2.htmlText = LanguageMgr.GetTranslation("ddt.beadSystem.feedBeadGetExp",this._allExp);
            _local_3.addChild(_local_2);
            _local_3.addEventListener("response",this.__onFeedResponse);
         }
      }
      
      protected function __onConfigResponse(_arg_1:FrameEvent) : void
      {
         var _local_2:int = 0;
         var _local_3:int = 0;
         var _local_4:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         SoundManager.instance.playButtonSound();
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               if(_local_4["textInput"].text == "YES" || _local_4["textInput"].text == "yes")
               {
                  this.boxPrompts(_local_4["isBind"]);
                  _local_4.removeEventListener("response",this.__onConfigResponse);
                  ObjectUtils.disposeObject(_local_4);
               }
               else
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.beadSystem.feedBeadPromptInfo"));
               }
               return;
            default:
               _local_2 = int(this._feedVec[this._feedID].length);
               _local_3 = 0;
               while(_local_3 < _local_2)
               {
                  this._feedVec[this._feedID][_local_3].locked = false;
                  _local_3++;
               }
               _local_4.removeEventListener("response",this.__onConfigResponse);
               ObjectUtils.disposeObject(_local_4);
               return;
         }
      }
      
      protected function __onBindRespones(_arg_1:FrameEvent) : void
      {
         var _local_4:int = 0;
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_2:* = null;
         SoundManager.instance.playButtonSound();
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
            case 4:
               _local_4 = int(this._feedVec[this._feedID].length);
               _local_5 = 0;
               while(_local_5 < _local_4)
               {
                  this._feedVec[this._feedID][_local_5].locked = false;
                  _local_5++;
               }
               break;
            case 2:
            case 3:
               _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("ddt.beadSystem.FeedBeadConfirm"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,true,false,2);
               _local_2 = ComponentFactory.Instance.creatComponentByStylename("beadSystem.feedBeadShowExpTextOneFeed");
               _local_2.htmlText = LanguageMgr.GetTranslation("ddt.beadSystem.feedBeadGetExp",this._allExp);
               _local_3.addChild(_local_2);
               _local_3.addEventListener("response",this.__onFeedResponse);
         }
         _arg_1.currentTarget.removeEventListener("response",this.__onBindRespones);
         ObjectUtils.disposeObject(_arg_1.currentTarget);
      }
      
      protected function __onFeedResponse(_arg_1:FrameEvent) : void
      {
         var _local_2:int = 0;
         var _local_3:int = 0;
         var _local_4:* = undefined;
         SoundManager.instance.play("008");
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
            case 4:
               _local_2 = int(this._feedVec[this._feedID].length);
               _local_3 = 0;
               while(_local_3 < _local_2)
               {
                  this._feedVec[this._feedID][_local_3].locked = false;
                  _local_3++;
               }
               break;
            case 2:
            case 3:
               if(this._feedVec[this._feedID].length > 0)
               {
                  if(!this._oneKeyFeedMC)
                  {
                     this._oneKeyFeedMC = ClassUtils.CreatInstance("beadSystem.oneKeyFeed.MC");
                     this._oneKeyFeedMC.gotoAndPlay(1);
                     _local_4 = 0.9;
                     this._oneKeyFeedMC.scaleY = _local_4;
                     this._oneKeyFeedMC.scaleX = _local_4;
                     this._oneKeyFeedMC.x = 707;
                     this._oneKeyFeedMC.y = 295;
                     this._oneKeyFeedMC.addEventListener("oneKeyComplete",this.__disposeOneKeyMC);
                     LayerManager.Instance.addToLayer(this._oneKeyFeedMC,0,false,1,true);
                  }
               }
               else
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.beadSystem.tipNoBead"));
               }
         }
         _arg_1.currentTarget.removeEventListener("response",this.__onFeedResponse);
         ObjectUtils.disposeObject(_arg_1.currentTarget);
      }
      
      private function __disposeOneKeyMC(_arg_1:Event) : void
      {
         var _local_5:int = 0;
         var _local_2:BeadCell = null;
         var _local_4:int = 0;
         var _local_3:Array = [];
         for each(_local_2 in this._feedVec[this._feedID])
         {
            if(Boolean(_local_2.info) && !_local_2.itemInfo.IsUsed)
            {
               _local_3.push(_local_2.beadPlace);
            }
         }
         SocketManager.Instance.out.sendBeadUpgrade(_local_3);
         _local_4 = int(this._feedVec[this._feedID].length);
         _local_5 = 0;
         while(_local_5 < _local_4)
         {
            this._feedVec[this._feedID][_local_5].locked = false;
            _local_5++;
         }
         this._oneKeyFeedMC.removeEventListener("oneKeyComplete",this.__disposeOneKeyMC);
         this._oneKeyFeedMC.stop();
         ObjectUtils.disposeObject(this._oneKeyFeedMC);
         this._oneKeyFeedMC = null;
         if(this._allExp + BeadModel.upgradeCellInfo.Hole2 >= ServerConfigManager.instance.getBeadUpgradeExp()[BeadModel.upgradeCellInfo.Hole1 + 1])
         {
            beadSystemManager.Instance.dispatchEvent(new BeadEvent("playUpgradeMC"));
         }
         this.checkNextBox();
      }
      
      protected function initTabButtons() : void
      {
         this._tabBtn1 = new Sprite();
         this._tabBtn1.graphics.beginFill(255,1);
         this._tabBtn1.graphics.drawRoundRect(348,39,51,131,15,15);
         this._tabBtn1.graphics.endFill();
         this._tabBtn1.alpha = 0;
         this._tabBtn1.buttonMode = true;
         addChild(this._tabBtn1);
         this._tabBtn2 = new Sprite();
         this._tabBtn2.graphics.beginFill(255,1);
         this._tabBtn2.graphics.drawRoundRect(349,183,51,131,15,15);
         this._tabBtn2.graphics.endFill();
         this._tabBtn2.alpha = 0;
         this._tabBtn2.buttonMode = true;
         addChild(this._tabBtn2);
         this._tabBtn3 = new Sprite();
         this._tabBtn3.graphics.beginFill(255,1);
         this._tabBtn3.graphics.drawRoundRect(349,327,51,131,15,15);
         this._tabBtn3.graphics.endFill();
         this._tabBtn3.alpha = 0;
         this._tabBtn3.buttonMode = true;
         this._tabBtn3.visible = false;
         addChild(this._tabBtn3);
         this._tabBtn4 = new Sprite();
         this._tabBtn4.graphics.beginFill(255,1);
         this._tabBtn4.graphics.drawRoundRect(349,327,51,131,15,15);
         this._tabBtn4.graphics.endFill();
         this._tabBtn4.alpha = 0;
         this._tabBtn4.buttonMode = true;
         addChild(this._tabBtn4);
         this._cardEnbleFlase = ComponentFactory.Instance.creatBitmap("asset.cardbtn.enblefalse");
         this._cardEnbleFlase.visible = false;
         addChild(this._buttonContainer);
      }
      
      private function initGoodsNumInfo() : void
      {
         this._goodsNumInfoText = ComponentFactory.Instance.creatComponentByStylename("bagGoodsInfoNumText");
         this._goodsNumTotalText = ComponentFactory.Instance.creatComponentByStylename("bagGoodsInfoNumTotalText");
         this._goodsNumTotalText.text = "/ 49";
      }
      
      private function updateView() : void
      {
         this.updateMoney();
         this.updateBagList();
      }
      
      protected function updateBagList() : void
      {
         if(Boolean(this._info))
         {
            this._equiplist.currentBagType = this._bagType;
            this._equiplist.setData(this._info.Bag);
            if(this._isScreenFood)
            {
               this._petlist.setData(this._info.PropBag);
            }
            else
            {
               this._proplist.setData(this._info.PropBag);
            }
            if(this._bagType != 5)
            {
               if(Boolean(this._beadList))
               {
                  this._beadList.setData(this._info.BeadBag);
               }
               if(Boolean(this._beadList2))
               {
                  this._beadList2.setData(this._info.BeadBag);
               }
               if(Boolean(this._beadList3))
               {
                  this._beadList3.setData(this._info.BeadBag);
               }
            }
         }
         else
         {
            this._equiplist.setData(null);
            this._proplist.setData(null);
            this._petlist.setData(null);
         }
      }
      
      private function __showBead(_arg_1:BagEvent) : void
      {
         MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.beadSystem.beadToBeadBag"));
      }
      
      protected function initEvent() : void
      {
         if(Boolean(this._sellBtn))
         {
            this._sellBtn.addEventListener("click",this.__sellClick);
         }
         if(Boolean(this._breakBtn))
         {
            this._breakBtn.addEventListener("click",this.__breakClick);
         }
         if(Boolean(this._equiplist))
         {
            this._equiplist.addEventListener("itemclick",this.__cellClick);
         }
         if(Boolean(this._equiplist))
         {
            this._equiplist.addEventListener("doubleclick",this.__cellDoubleClick);
         }
         if(Boolean(this._equiplist))
         {
            this._equiplist.addEventListener("change",this.__listChange);
         }
         if(Boolean(this._proplist))
         {
            this._proplist.doubleClickEnabled = true;
         }
         if(Boolean(this._proplist))
         {
            this._proplist.addEventListener("itemclick",this.__cellClick);
         }
         if(Boolean(this._proplist))
         {
            this._proplist.addEventListener("doubleclick",this.__cellDoubleClick);
         }
         if(Boolean(this._petlist))
         {
            this._petlist.addEventListener("itemclick",this.__cellClick);
            this._petlist.addEventListener("doubleclick",this.__cellDoubleClick);
         }
         if(Boolean(this._beadList))
         {
            this._beadList.addEventListener("itemclick",this.__cellClick);
            this._beadList2.addEventListener("itemclick",this.__cellClick);
            this._beadList3.addEventListener("itemclick",this.__cellClick);
         }
         if(Boolean(this._tabBtn1))
         {
            this._tabBtn1.addEventListener("click",this.__itemtabBtnClick);
         }
         if(Boolean(this._tabBtn2))
         {
            this._tabBtn2.addEventListener("click",this.__itemtabBtnClick);
         }
         if(Boolean(this._tabBtn3))
         {
            this._tabBtn3.addEventListener("click",this.__itemtabBtnClick);
         }
         if(Boolean(this._tabBtn4))
         {
            this._tabBtn4.addEventListener("click",this.__itemtabBtnClick);
         }
         CellMenu.instance.addEventListener("addprice",this.__cellAddPrice);
         CellMenu.instance.addEventListener("move",this.__cellMove);
         CellMenu.instance.addEventListener("open",this.__cellOpen);
         CellMenu.instance.addEventListener("use",this.__cellUse);
         CellMenu.instance.addEventListener("open_batch",this.__cellOpenBatch);
         CellMenu.instance.addEventListener("color_change",this.__cellColorChange);
         CellMenu.instance.addEventListener("delete",this.__cellSell);
         if(Boolean(this._keySortBtn))
         {
            this._keySortBtn.addEventListener("click",this.__sortBagClick);
         }
         if(Boolean(this._keySortBtn))
         {
            this._keySortBtn.addEventListener("rollOver",this.__bagArrangeOver);
         }
         if(Boolean(this._keySortBtn))
         {
            this._keySortBtn.addEventListener("rollOut",this.__bagArrangeOut);
         }
         SocketManager.Instance.addEventListener(PkgEvent.format(205),this.__useColorShell);
         beadSystemManager.Instance.addEventListener("autoOpenBead",this.__onAutoOpenBeadChanged);
         if(Boolean(this.bagLockBtn))
         {
            this.bagLockBtn.addEventListener("click",this.bagLockHandler,false,0,true);
         }
         this.adjustEvent();
      }
      
      protected function bagLockHandler(_arg_1:MouseEvent) : void
      {
         this.__openSettingLock(null);
      }
      
      protected function __bagArrangeOut(_arg_1:MouseEvent) : void
      {
         if(this._bagType == 21)
         {
            return;
         }
         if(this._bagArrangeSprite && this._bagArrangeSprite.parent && !this.containPoint(_arg_1.localX,_arg_1.localY))
         {
            removeChild(this._bagArrangeSprite);
         }
      }
      
      private function containPoint(_arg_1:int, _arg_2:int) : Boolean
      {
         if(_arg_1 > 0 && _arg_1 < this._bagArrangeSprite.width && _arg_2 <= 3 && _arg_2 > -this._bagArrangeSprite.height)
         {
            return true;
         }
         return false;
      }
      
      private function __onAutoOpenBeadChanged(_arg_1:BeadEvent) : void
      {
         if(!this._beadOneKeyBtn || !this._beadLockBtn || !this._beadFeedBtn)
         {
            return;
         }
         if(_arg_1.CellId == 0)
         {
            this._beadOneKeyBtn.enable = true;
            this._beadLockBtn.enable = true;
            this._beadFeedBtn.enable = true;
         }
         else if(_arg_1.CellId == 1)
         {
            this._beadOneKeyBtn.enable = false;
            this._beadLockBtn.enable = false;
            this._beadFeedBtn.enable = false;
         }
      }
      
      private function isInBag(_arg_1:InventoryItemInfo, _arg_2:BeadBagList) : Boolean
      {
         if(_arg_1.Place >= _arg_2._startIndex && _arg_1.Place <= _arg_2._stopIndex)
         {
            return true;
         }
         return false;
      }
      
      protected function __onBeadBagChanged(_arg_1:DictionaryEvent) : void
      {
         var _local_4:int = 0;
         var _local_6:int = 0;
         if(this._bagType != 21)
         {
            return;
         }
         var _local_2:Array = [this._beadList,this._beadList2,this._beadList3];
         var _local_5:int = 1;
         var _local_3:InventoryItemInfo = InventoryItemInfo(_arg_1.data);
         if(_local_3.Place < 32)
         {
            return;
         }
         if(Boolean(this._info.BeadBag.getItemAt(_local_3.Place)))
         {
            _local_4 = 1;
            _local_6 = 0;
            while(_local_6 < _local_2.length)
            {
               if(this.isInBag(_local_3,_local_2[_local_6]))
               {
                  _local_4 = _local_6 + 1;
                  break;
               }
               _local_6++;
            }
            _local_5 = _local_4 > _local_5 ? _local_4 : _local_5;
         }
         if(_local_5 > 3 || _local_5 < 1)
         {
            _local_5 = 1;
         }
         if(this._currIndex == _local_5)
         {
            return;
         }
         this._currIndex = _local_5;
         this._beadList.visible = _local_5 == 1;
         this._beadList2.visible = _local_5 == 2;
         this._beadList3.visible = _local_5 == 3;
         this._pageTxt.text = _local_5 + "/3";
         this._currentBeadList = _local_2[_local_5 - 1];
      }
      
      private function __pgupHandler(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(this._currIndex == 1)
         {
            this._currIndex = 3;
            this._beadList.visible = false;
            this._beadList2.visible = false;
            this._beadList3.visible = true;
            this._pageTxt.text = "3/3";
            this._currentBeadList = this._beadList3;
         }
         else if(this._currIndex == 2)
         {
            this._currIndex = 1;
            this._beadList.visible = true;
            this._beadList2.visible = false;
            this._beadList3.visible = false;
            this._pageTxt.text = "1/3";
            this._currentBeadList = this._beadList;
         }
         else if(this._currIndex == 3)
         {
            this._currIndex = 2;
            this._beadList.visible = false;
            this._beadList2.visible = true;
            this._beadList3.visible = false;
            this._pageTxt.text = "2/3";
            this._currentBeadList = this._beadList2;
         }
      }
      
      private function setCurrPage(_arg_1:int) : void
      {
      }
      
      public function __pgdnHandler(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(this._currIndex == 1)
         {
            this._currIndex = 2;
            this._beadList.visible = false;
            this._beadList2.visible = true;
            this._beadList3.visible = false;
            this._pageTxt.text = "2/3";
            this._currentBeadList = this._beadList2;
         }
         else if(this._currIndex == 2)
         {
            this._currIndex = 3;
            this._beadList.visible = false;
            this._beadList2.visible = false;
            this._beadList3.visible = true;
            this._pageTxt.text = "3/3";
            this._currentBeadList = this._beadList3;
         }
         else if(this._currIndex == 3)
         {
            this._currIndex = 1;
            this._beadList.visible = true;
            this._beadList2.visible = false;
            this._beadList3.visible = false;
            this._pageTxt.text = "1/3";
            this._currentBeadList = this._beadList;
         }
      }
      
      protected function adjustEvent() : void
      {
      }
      
      protected function __useColorShell(_arg_1:PkgEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:Boolean = _local_3.readBoolean();
         if(_local_2)
         {
            SoundManager.instance.play("063");
         }
      }
      
      protected function removeEvents() : void
      {
         if(Boolean(this._sellBtn))
         {
            this._sellBtn.removeEventListener("click",this.__sellClick);
         }
         if(Boolean(this._breakBtn))
         {
            this._breakBtn.removeEventListener("click",this.__breakClick);
         }
         if(Boolean(this._equiplist))
         {
            this._equiplist.removeEventListener("itemclick",this.__cellClick);
         }
         if(Boolean(this._equiplist))
         {
            this._equiplist.removeEventListener("change",this.__listChange);
         }
         if(Boolean(this._equiplist))
         {
            this._equiplist.removeEventListener("doubleclick",this.__cellDoubleClick);
         }
         if(Boolean(this._proplist))
         {
            this._proplist.removeEventListener("itemclick",this.__cellClick);
         }
         if(Boolean(this._proplist))
         {
            this._proplist.removeEventListener("doubleclick",this.__cellDoubleClick);
         }
         if(Boolean(this._proplist))
         {
            this._proplist.removeEventListener("change",this.__listChange);
         }
         if(Boolean(this._petlist))
         {
            this._petlist.removeEventListener("doubleclick",this.__cellDoubleClick);
         }
         if(Boolean(this._petlist))
         {
            this._petlist.removeEventListener("itemclick",this.__cellClick);
         }
         if(Boolean(this._beadList))
         {
            this._beadList.removeEventListener("itemclick",this.__cellClick);
         }
         if(Boolean(this._beadList2))
         {
            this._beadList.removeEventListener("itemclick",this.__cellClick);
         }
         if(Boolean(this._beadList3))
         {
            this._beadList.removeEventListener("itemclick",this.__cellClick);
         }
         if(Boolean(this._dressbagView))
         {
            this._dressbagView.removeEventListener("itemclick",this.__cellClick);
         }
         if(Boolean(this._dressbagView))
         {
            this._dressbagView.removeEventListener("doubleclick",this.__cellDoubleClick);
         }
         if(Boolean(this._beadList))
         {
            this._beadList.removeEventListener("change",this.__listChange);
         }
         if(Boolean(this._tabBtn1))
         {
            this._tabBtn1.removeEventListener("click",this.__itemtabBtnClick);
         }
         if(Boolean(this._tabBtn2))
         {
            this._tabBtn2.removeEventListener("click",this.__itemtabBtnClick);
         }
         if(Boolean(this._tabBtn3))
         {
            this._tabBtn3.removeEventListener("click",this.__itemtabBtnClick);
         }
         if(Boolean(this._tabBtn4))
         {
            this._tabBtn4.removeEventListener("click",this.__itemtabBtnClick);
         }
         if(Boolean(this._pgup))
         {
            this._pgup.removeEventListener("click",this.__pgupHandler);
         }
         if(Boolean(this._pgdn))
         {
            this._pgdn.removeEventListener("click",this.__pgdnHandler);
         }
         if(Boolean(this._beadSortBtn))
         {
            this._beadSortBtn.removeEventListener("click",this.__sortBagClick);
         }
         CellMenu.instance.removeEventListener("addprice",this.__cellAddPrice);
         CellMenu.instance.removeEventListener("move",this.__cellMove);
         CellMenu.instance.removeEventListener("open",this.__cellOpen);
         CellMenu.instance.removeEventListener("use",this.__cellUse);
         CellMenu.instance.removeEventListener("open_batch",this.__cellOpenBatch);
         CellMenu.instance.removeEventListener("color_change",this.__cellColorChange);
         CellMenu.instance.removeEventListener("delete",this.__cellSell);
         if(Boolean(this._keySortBtn))
         {
            this._keySortBtn.removeEventListener("click",this.__sortBagClick);
            this._keySortBtn.removeEventListener("rollOver",this.__bagArrangeOver);
            this._keySortBtn.removeEventListener("rollOut",this.__bagArrangeOut);
         }
         PlayerManager.Instance.Self.removeEventListener("showBead",this.__showBead);
         if(Boolean(this._info))
         {
            this._info.removeEventListener("propertychange",this.__propertyChange);
            this._info.getBag(0).removeEventListener("update",this.__onBagUpdateEQUIPBAG);
            this._info.getBag(1).removeEventListener("update",this.__onBagUpdatePROPBAG);
            this._info.BeadBag.items.removeEventListener("add",this.__onBeadBagChanged);
         }
         SocketManager.Instance.removeEventListener(PkgEvent.format(205),this.__useColorShell);
         beadSystemManager.Instance.removeEventListener("autoOpenBead",this.__onAutoOpenBeadChanged);
         if(Boolean(this.bagLockBtn))
         {
            this.bagLockBtn.removeEventListener("click",this.bagLockHandler);
         }
         PlayerDressManager.instance.removeEventListener("bagViewComplete",this.showDressBagView);
      }
      
      protected function __itemtabBtnClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         switch(_arg_1.currentTarget)
         {
            case this._tabBtn1:
               if(this._bagType == 0)
               {
                  return;
               }
               this.setBagType(0);
               this.refreshSelectedButton(1);
               return;
               break;
            case this._tabBtn2:
               if(this._bagType == 1 || this._bagType == 5)
               {
                  return;
               }
               this.setBagType(this._isScreenFood ? 5 : 1);
               this.refreshSelectedButton(2);
               return;
               break;
            case this._tabBtn3:
               if(PlayerManager.Instance.Self.Grade < 16)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.giftSystem.openBeadBtn.text",16));
                  return;
               }
               if(this._bagType == 21)
               {
                  return;
               }
               this.setBagType(21);
               this.refreshSelectedButton(3);
               return;
               break;
            case this._tabBtn4:
               this.setBagType(8);
               this.refreshSelectedButton(4);
               return;
            default:
               return;
         }
      }
      
      public function enableOrdisableSB(_arg_1:Boolean) : void
      {
         if(Boolean(this._equipSelectedBtn))
         {
            this._equipSelectedBtn.enable = _arg_1;
         }
         if(Boolean(this._propSelectedBtn))
         {
            this._propSelectedBtn.enable = _arg_1;
         }
         if(Boolean(this._dressSelectedBtn))
         {
            this._dressSelectedBtn.enable = _arg_1;
         }
         if(Boolean(this._tabBtn1))
         {
            this._tabBtn1.visible = _arg_1;
         }
         if(Boolean(this._tabBtn2))
         {
            this._tabBtn2.visible = _arg_1;
         }
         if(Boolean(this._tabBtn4))
         {
            this._tabBtn4.visible = _arg_1;
         }
      }
      
      public function showDressSelectedBtnOnly(_arg_1:Boolean) : void
      {
         if(Boolean(this._equipSelectedBtn))
         {
            this._equipSelectedBtn.enable = !_arg_1;
         }
         if(Boolean(this._propSelectedBtn))
         {
            this._propSelectedBtn.enable = !_arg_1;
         }
         if(Boolean(this._dressSelectedBtn))
         {
            this._dressSelectedBtn.enable = _arg_1;
         }
         if(Boolean(this._tabBtn1))
         {
            this._tabBtn1.visible = !_arg_1;
         }
         if(Boolean(this._tabBtn2))
         {
            this._tabBtn2.visible = !_arg_1;
         }
         if(Boolean(this._tabBtn4))
         {
            this._tabBtn4.visible = _arg_1;
         }
      }
      
      public function enableDressSelectedBtn(_arg_1:Boolean) : void
      {
         if(Boolean(this._dressSelectedBtn))
         {
            this._dressSelectedBtn.enable = _arg_1;
         }
         if(Boolean(this._tabBtn4))
         {
            this._tabBtn4.visible = _arg_1;
         }
      }
      
      public function showOrHideSB(_arg_1:Boolean) : void
      {
         if(Boolean(this._equipSelectedBtn))
         {
            this._equipSelectedBtn.visible = _arg_1;
         }
         if(Boolean(this._propSelectedBtn))
         {
            this._propSelectedBtn.visible = _arg_1;
         }
         if(Boolean(this._dressSelectedBtn))
         {
            this._dressSelectedBtn.visible = _arg_1;
         }
      }
      
      protected function refreshSelectedButton(_arg_1:int) : void
      {
         if(Boolean(this._equipSelectedBtn))
         {
            this._equipSelectedBtn.selected = _arg_1 == 1;
         }
         if(Boolean(this._propSelectedBtn))
         {
            this._propSelectedBtn.selected = _arg_1 == 2;
         }
         if(Boolean(this._beadSelectedBtn))
         {
            this._beadSelectedBtn.selected = _arg_1 == 3;
         }
         if(Boolean(this._dressSelectedBtn))
         {
            this._dressSelectedBtn.selected = _arg_1 == 4;
         }
      }
      
      public function setBagType(_arg_1:int) : void
      {
         var _local_2:* = null;
         if(_arg_1 != 21)
         {
            this._currIndex = 1;
            if(Boolean(this._beadList))
            {
               this._currentBeadList = this._beadList;
            }
            if(Boolean(this._pageTxt))
            {
               this._pageTxt.text = "1/3";
            }
         }
         if(Boolean(this._equipEnbleFlase))
         {
            this.btnReable();
         }
         this._bagType = _arg_1;
         if(_arg_1 == 5)
         {
            this.refreshSelectedButton(2);
         }
         if(_arg_1 == 0)
         {
            this.refreshSelectedButton(1);
         }
         else if(_arg_1 == 1)
         {
            this.refreshSelectedButton(2);
         }
         else if(_arg_1 == 2)
         {
            _arg_1 = 0;
            this.refreshSelectedButton(1);
         }
         else if(_arg_1 == 21)
         {
            this.refreshSelectedButton(3);
            this.switchButtomVisible(false);
         }
         else if(_arg_1 == 8)
         {
            PlayerDressManager.instance.addEventListener("bagViewComplete",this.showDressBagView);
            PlayerDressManager.instance.showView(1);
            this.refreshSelectedButton(4);
         }
         dispatchEvent(new Event("tabChange"));
         this._buttonContainer.visible = this._bagType != 8;
         this._bgShape.visible = this._bagType == 0 || this._bagType == 1 || this._bagType == 5;
         this._equiplist.visible = this._bagType == 0;
         this._proplist.visible = this._bagType == 1;
         if(Boolean(this._dressbagView))
         {
            this._dressbagView.visible = this._bagType == 8;
         }
         if(Boolean(this._petlist))
         {
            this._petlist.visible = this._bagType == 5;
         }
         if(Boolean(this._beadList))
         {
            this._beadList.visible = this._bagType == 21;
            if(this._beadList.visible)
            {
               this._beadList2.visible = this._bagType == 21;
               this._beadList3.visible = this._bagType == 21;
               this._currentBeadList = this._beadList;
               this._beadList2.visible = false;
               this._beadList3.visible = false;
            }
            else
            {
               this._beadList2.visible = this._bagType == 21;
               this._beadList3.visible = this._bagType == 21;
               this._currentBeadList = null;
            }
         }
         if(Boolean(this._bagList))
         {
            this._bagList.visible = this._bagType == 2;
         }
         if(Boolean(this._bgShapeII))
         {
            this._bgShapeII.visible = this._bagType == 2;
         }
         this.set_breakBtn_enable();
         var _local_3:* = this._bagType != 2 && this._bagType != 100;
         this._continueBtn.enable = _local_3;
         this._sellBtn.enable = _local_3;
         if(this._bagType == 0 || this._bagType == 1 || this._bagType == 5)
         {
            this._sellBtn.filters = ComponentFactory.Instance.creatFilters("lightFilter");
            this._continueBtn.filters = ComponentFactory.Instance.creatFilters("lightFilter");
         }
         if(this._bagType == 2)
         {
            this._sellBtn.filters = ComponentFactory.Instance.creatFilters("grayFilter");
            this._continueBtn.filters = ComponentFactory.Instance.creatFilters("grayFilter");
            this.btnUnable();
            this.showOrHideSB(false);
            if(Boolean(this.bagLockBtn))
            {
               this.bagLockBtn.x = 265;
               this.bagLockBtn.y = -59;
            }
         }
         else
         {
            this.showOrHideSB(true);
            _local_2 = getQualifiedClassName(this);
            if(Boolean(this.bagLockBtn) && _local_2 != "email.view::EmailBagView")
            {
               this.bagLockBtn.x = 298;
               this.bagLockBtn.y = -64;
            }
         }
         if(this._bagType == 0)
         {
            this._moneyText.x = 47;
            this._moneyText.y = 384;
            this._moneyBg.x = 18;
            this._moneyBg.y = 381;
            this._goldButton.x = 19;
            this._goldButton.y = 385;
            this._PointCouponBitmap.x = 19;
            this._PointCouponBitmap.y = 382;
            this._giftText.x = 147;
            this._giftText.y = 385;
            this._LiJinBitmap.x = 119;
            this._LiJinBitmap.y = 382;
            this._giftButton.x = 121;
            this._giftButton.y = 386;
            this._moneyBg1.x = 119;
            this._moneyBg1.y = 381;
            this._orderTxt.x = 146;
            this._orderTxt.y = 418;
         }
         if(this._bagType == 21)
         {
            this._moneyText.x = 270;
            this._moneyText.y = 397;
            this._moneyBg.x = 241;
            this._moneyBg.y = 394;
            this._goldButton.x = 241;
            this._goldButton.y = 399;
            this._PointCouponBitmap.x = 240;
            this._PointCouponBitmap.y = 395;
            this._giftText.x = 269;
            this._giftText.y = 426;
            this._LiJinBitmap.x = 240;
            this._LiJinBitmap.y = 423;
            this._giftButton.x = 242;
            this._giftButton.y = 426;
            this._moneyBg1.x = 241;
            this._moneyBg1.y = 422;
            this._giftButton.visible = true;
            this._giftText.visible = true;
            this._LiJinBitmap.visible = true;
            this._moneyBg1.visible = true;
            this.adjustBeadBagPage(false);
         }
         else
         {
            this._moneyText.x = 47;
            this._moneyText.y = 384;
            this._moneyBg.x = 18;
            this._moneyBg.y = 381;
            this._goldButton.x = 19;
            this._goldButton.y = 385;
            this._PointCouponBitmap.x = 19;
            this._PointCouponBitmap.y = 382;
            this._giftText.x = 147;
            this._giftText.y = 385;
            this._LiJinBitmap.x = 119;
            this._LiJinBitmap.y = 382;
            this._giftButton.x = 121;
            this._giftButton.y = 386;
            this._moneyBg1.x = 119;
            this._moneyBg1.y = 381;
         }
      }
      
      protected function showDressBagView(_arg_1:PlayerDressEvent) : void
      {
         var _local_2:* = undefined;
         var _local_3:* = undefined;
         if(!this._dressbagView)
         {
            this._dressbagView = _arg_1.info;
            addChild(this._dressbagView);
            this._dressbagView.addEventListener("itemclick",this.__cellClick);
            this._dressbagView.addEventListener("doubleclick",this.__cellDoubleClick);
         }
         else
         {
            this._dressbagView.visible = true;
            _local_2 = this._dressbagView;
            _local_3 = _local_2;
            _local_3["updateBagList"]();
         }
      }
      
      private function btnUnable() : void
      {
         if(Boolean(this._tabBtn1))
         {
            this._tabBtn1.buttonMode = false;
         }
         if(Boolean(this._tabBtn1))
         {
            this._tabBtn1.removeEventListener("click",this.__itemtabBtnClick);
         }
         if(Boolean(this._tabBtn2))
         {
            this._tabBtn2.buttonMode = false;
         }
         if(Boolean(this._tabBtn2))
         {
            this._tabBtn2.removeEventListener("click",this.__itemtabBtnClick);
         }
         if(Boolean(this._tabBtn3))
         {
            this._tabBtn3.buttonMode = false;
         }
         if(Boolean(this._tabBtn3))
         {
            this._tabBtn3.removeEventListener("click",this.__itemtabBtnClick);
         }
         if(Boolean(this._tabBtn4))
         {
            this._tabBtn4.buttonMode = false;
         }
         if(Boolean(this._tabBtn4))
         {
            this._tabBtn4.removeEventListener("click",this.__itemtabBtnClick);
         }
         this._disEnabledFilters = [ComponentFactory.Instance.model.getSet("bagAndInfo.reworkname.ButtonDisenable")];
         this._equipEnbleFlase = ComponentFactory.Instance.creatBitmap("asset.equipbtn.enblefalse");
         this._equipEnbleFlase.visible = false;
         addChild(this._equipEnbleFlase);
         this._equipEnbleFlase.filters = this._disEnabledFilters;
         this._propEnbleFlase = ComponentFactory.Instance.creatBitmap("asset.propbtn.enblefalse");
         this._propEnbleFlase.visible = false;
         addChild(this._propEnbleFlase);
         this._propEnbleFlase.filters = this._disEnabledFilters;
         this._beadEnbleFlase = ComponentFactory.Instance.creatBitmap("asset.cardbtn.enblefalse");
         this._beadEnbleFlase.visible = false;
         addChild(this._beadEnbleFlase);
         this._beadEnbleFlase.filters = this._disEnabledFilters;
         PositionUtils.setPos(this._equipEnbleFlase,"equipEnbleFlasePos");
         PositionUtils.setPos(this._propEnbleFlase,"propEnbleFlasePos");
         PositionUtils.setPos(this._beadEnbleFlase,"beadEnbleFlasePos");
      }
      
      private function btnReable() : void
      {
         if(Boolean(this._tabBtn1))
         {
            this._tabBtn1.buttonMode = true;
         }
         if(Boolean(this._tabBtn1))
         {
            this._tabBtn1.addEventListener("click",this.__itemtabBtnClick);
         }
         if(Boolean(this._tabBtn2))
         {
            this._tabBtn2.buttonMode = true;
         }
         if(Boolean(this._tabBtn2))
         {
            this._tabBtn2.addEventListener("click",this.__itemtabBtnClick);
         }
         if(Boolean(this._tabBtn3))
         {
            this._tabBtn3.buttonMode = true;
         }
         if(Boolean(this._tabBtn3))
         {
            this._tabBtn3.addEventListener("click",this.__itemtabBtnClick);
         }
         if(Boolean(this._tabBtn4))
         {
            this._tabBtn4.buttonMode = true;
         }
         if(Boolean(this._tabBtn4))
         {
            this._tabBtn4.addEventListener("click",this.__itemtabBtnClick);
         }
         this._disEnabledFilters = null;
         ObjectUtils.disposeObject(this._equipEnbleFlase);
         this._equipEnbleFlase = null;
         ObjectUtils.disposeObject(this._propEnbleFlase);
         this._propEnbleFlase = null;
         ObjectUtils.disposeObject(this._beadEnbleFlase);
         this._beadEnbleFlase = null;
      }
      
      public function isNeedCard(_arg_1:Boolean) : void
      {
      }
      
      protected function set_breakBtn_enable() : void
      {
      }
      
      protected function set_text_location() : void
      {
      }
      
      protected function set_btn_location() : void
      {
      }
      
      private function __onBagUpdateEQUIPBAG(_arg_1:BagEvent) : void
      {
         if(!(this._dressbagView && this._dressbagView.visible == true))
         {
            this.setBagCountShow(0);
         }
      }
      
      protected function __onBagUpdatePROPBAG(_arg_1:BagEvent) : void
      {
         if(this.bagType != 21 && !this._isScreenFood && this.bagType != 2)
         {
            this.setBagCountShow(1);
         }
      }
      
      private function __openSettingLock(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         BaglockedManager.Instance.onShow();
         SharedManager.Instance.setBagLocked = true;
         SharedManager.Instance.save();
      }
      
      protected function __bagArrangeOver(_arg_1:MouseEvent) : void
      {
         if(this._bagType == 21 || this._bagType == 0 && this._isScreenFood)
         {
            return;
         }
         if(!this._bagArrangeSprite)
         {
            this._bagArrangeSprite = ComponentFactory.Instance.creatCustomObject("bagArrangeTipSprite");
         }
         this._bagArrangeSprite.y = this._keySortBtn.y + this._buttonContainer.y - 24;
         addChild(this._bagArrangeSprite);
      }
      
      protected function __sortBagClick(_arg_1:MouseEvent) : void
      {
         var alert:BaseAlerFrame = null;
         var msg:String = null;
         var onClickSortPetEquipBag:* = undefined;
         var onClickSortEquipBag:* = undefined;
         var evt:* = _arg_1;
         SoundManager.instance.play("008");
         if(this._bagType != 21)
         {
            if(this._isScreenFood && this._bagType == 0)
            {
               onClickSortPetEquipBag = function():void
               {
                  PlayerManager.Instance.Self.getBag(_bagType).foldPetEquips(_bagType,PlayerManager.Instance.Self.getBag(_bagType),0,96);
               };
               if(PlayerManager.Instance.Self.bagLocked)
               {
                  BaglockedManager.Instance.show();
                  return;
               }
               if(_sortPetEquipBagData.notShowAlertAgain)
               {
                  PlayerManager.Instance.Self.getBag(this._bagType).foldPetEquips(this._bagType,PlayerManager.Instance.Self.getBag(this._bagType),0,96);
                  return;
               }
               msg = LanguageMgr.GetTranslation("petBag.sortTips");
               HelperBuyAlert.getInstance().alert(msg,_sortPetEquipBagData,"SimpleAlertWithNotShowAgain",null,onClickSortPetEquipBag,null,0);
               return;
            }
            if(Boolean(this._bagArrangeSprite) && this._bagType == 1)
            {
               PlayerManager.Instance.Self.PropBag.sortBag(this._bagType,PlayerManager.Instance.Self.getBag(this._bagType),0,95,this._bagArrangeSprite.arrangeAdd);
               return;
            }
            if(this._bagArrangeSprite && this._bagArrangeSprite.arrangeAdd && this.bagType == 0 && !_sortEquipBagData.notShowAlertAgain)
            {
               onClickSortEquipBag = function():void
               {
                  PlayerManager.Instance.Self.PropBag.sortBag(_bagType,PlayerManager.Instance.Self.getBag(_bagType),0,96,_bagArrangeSprite.arrangeAdd);
               };
               if(PlayerManager.Instance.Self.bagLocked)
               {
                  BaglockedManager.Instance.show();
                  return;
               }
               msg = LanguageMgr.GetTranslation("bag.bagView.equipSortTips");
               HelperBuyAlert.getInstance().alert(msg,_sortEquipBagData,"SimpleAlertWithNotShowAgain",null,onClickSortEquipBag,null,0);
            }
            else if(Boolean(this._bagArrangeSprite))
            {
               PlayerManager.Instance.Self.PropBag.sortBag(this._bagType,PlayerManager.Instance.Self.getBag(this._bagType),0,96,this._bagArrangeSprite.arrangeAdd);
            }
         }
         else
         {
            PlayerManager.Instance.Self.PropBag.sortBag(this._bagType,PlayerManager.Instance.Self.getBag(this._bagType),32,178,true);
         }
      }
      
      private function __frameEvent(_arg_1:FrameEvent) : void
      {
         var _local_2:BaseAlerFrame = _arg_1.target as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__frameEvent);
         _local_2.dispose();
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               if(this._bagType != 21)
               {
                  PlayerManager.Instance.Self.PropBag.sortBag(this._bagType,PlayerManager.Instance.Self.getBag(this._bagType),0,96,true);
               }
               else
               {
                  PlayerManager.Instance.Self.PropBag.sortBag(this._bagType,PlayerManager.Instance.Self.getBag(this._bagType),32,178,true);
               }
               return;
            case 0:
            case 1:
            case 4:
               if(this._bagType != 21)
               {
                  PlayerManager.Instance.Self.PropBag.sortBag(this._bagType,PlayerManager.Instance.Self.getBag(this._bagType),0,96,false);
                  break;
               }
         }
      }
      
      private function __propertyChange(_arg_1:PlayerPropertyEvent) : void
      {
         if(Boolean(_arg_1.changedProperties["BandMoney"]) || Boolean(_arg_1.changedProperties["Money"]) || Boolean(_arg_1.changedProperties["Gold"]) || Boolean(_arg_1.changedProperties["Money"]))
         {
            this.updateMoney();
         }
      }
      
      private function updateMoney() : void
      {
         var _local_1:* = undefined;
         if(Boolean(this._info))
         {
            this._goldText.text = String(this._info.Gold);
            this._moneyText.text = String(this._info.Money);
            this._giftText.text = String(this._info.BandMoney);
            this._orderTxt.text = String(this._info.DDTMoney);
         }
         else
         {
            _local_1 = "";
            this._giftText.text = _local_1;
            this._moneyText.text = _local_1;
            this._goldText.text = _local_1;
         }
      }
      
      protected function __listChange(_arg_1:Event) : void
      {
         if(!(this._dressbagView && this._dressbagView.visible == true))
         {
            this.setBagType(0);
         }
      }
      
      private function __feedClick(_arg_1:MouseEvent) : void
      {
         if(!(this.bead_state & 1))
         {
            this.bead_state |= 1;
            SoundManager.instance.play("008");
            this._beadFeedBtn.dragStart(_arg_1.stageX,_arg_1.stageY);
            this._beadFeedBtn.addEventListener("stopfeed",this.__stopFeed);
            dispatchEvent(new Event("sellstart"));
            stage.addEventListener("click",this.__onStageClick_FeedBtn);
            _arg_1.stopImmediatePropagation();
         }
         else
         {
            this.bead_state = ~1 & this.bead_state;
            this._beadFeedBtn.stopDrag();
         }
      }
      
      private function __stopFeed(_arg_1:Event) : void
      {
         this.bead_state = ~1 & this.bead_state;
         this._beadFeedBtn.removeEventListener("stopsell",this.__stopSell);
         dispatchEvent(new Event("stopfeed"));
         if(Boolean(stage))
         {
            stage.removeEventListener("click",this.__onStageClick_FeedBtn);
         }
      }
      
      private function __onStageClick_FeedBtn(_arg_1:Event) : void
      {
         this.bead_state = ~1 & this.bead_state;
         dispatchEvent(new Event("stopfeed"));
         if(Boolean(stage))
         {
            stage.removeEventListener("click",this.__onStageClick_FeedBtn);
         }
      }
      
      private function __sellClick(_arg_1:MouseEvent) : void
      {
         if(!(this.state & 1))
         {
            this.state |= 1;
            SoundManager.instance.play("008");
            this._sellBtn.dragStart(_arg_1.stageX,_arg_1.stageY);
            this._sellBtn.addEventListener("stopsell",this.__stopSell);
            dispatchEvent(new Event("sellstart"));
            stage.addEventListener("click",this.__onStageClick_SellBtn);
            _arg_1.stopImmediatePropagation();
         }
         else
         {
            this.state = ~1 & this.state;
            this._sellBtn.stopDrag();
         }
      }
      
      private function __stopSell(_arg_1:Event) : void
      {
         this.state = ~1 & this.state;
         this._sellBtn.removeEventListener("stopsell",this.__stopSell);
         dispatchEvent(new Event("sellstop"));
         if(Boolean(stage))
         {
            stage.removeEventListener("click",this.__onStageClick_SellBtn);
         }
      }
      
      private function __onStageClick_SellBtn(_arg_1:Event) : void
      {
         this.state = ~1 & this.state;
         dispatchEvent(new Event("sellstop"));
         if(Boolean(stage))
         {
            stage.removeEventListener("click",this.__onStageClick_SellBtn);
         }
      }
      
      private function __breakClick(_arg_1:MouseEvent) : void
      {
         if(this._breakBtn.enable)
         {
            SoundManager.instance.play("008");
            if(PlayerManager.Instance.Self.bagLocked)
            {
               BaglockedManager.Instance.show();
            }
            else
            {
               this._breakBtn.dragStart(_arg_1.stageX,_arg_1.stageY);
            }
         }
      }
      
      public function resetMouse() : void
      {
         this.state = ~1 & this.state;
         LayerManager.Instance.clearnStageDynamic();
         Mouse.show();
         if(Boolean(this._breakBtn))
         {
            this._breakBtn.stopDrag();
         }
      }
      
      private function isOnlyGivingGoods(_arg_1:InventoryItemInfo) : Boolean
      {
         return _arg_1.IsBinds == false && EquipType.isPackage(_arg_1) && _arg_1.Property2 == "10";
      }
      
      protected function __cellClick(_arg_1:CellEvent) : void
      {
         var _local_2:* = undefined;
         var _local_4:* = null;
         var _local_3:* = null;
         if(Boolean(this._sellBtn) && !this._sellBtn.isActive)
         {
            _arg_1.stopImmediatePropagation();
            if(_arg_1.data is BagCell)
            {
               _local_2 = _arg_1.data as BagCell;
            }
            else
            {
               _local_2 = _arg_1.data as BeadCell;
            }
            if(_local_2)
            {
               _local_4 = _local_2.itemInfo as InventoryItemInfo;
            }
            if(_local_4 == null)
            {
               return;
            }
            if(!_local_2.locked)
            {
               SoundManager.instance.play("008");
               if(!this.isOnlyGivingGoods(_local_4) && (_local_4.getRemainDate() <= 0 && !EquipType.isProp(_local_4) || EquipType.isPackage(_local_4) || _local_4.getRemainDate() <= 0 && _local_4.TemplateID == 10200 || _local_4.TemplateID == 11955 || _local_4.TemplateID == 11963 || EquipType.canBeUsed(_local_4) || DressUtils.isDress(_local_4) || EquipType.isGetPackage(_local_4) || EquipType.isFireworks(_local_4) || EquipType.isNewRedPackage(_local_4.TemplateID) || _local_4.CategoryID == 68 || _local_4.CategoryID == 43 || _local_4.CategoryID == 73))
               {
                  _local_3 = _local_2.parent.localToGlobal(new Point(_local_2.x,_local_2.y));
                  CellMenu.instance.show(_local_2,_local_3.x + 20,_local_3.y + 20);
               }
               else
               {
                  _local_2.dragStart();
               }
            }
         }
      }
      
      public function set cellDoubleClickEnable(_arg_1:Boolean) : void
      {
         if(_arg_1)
         {
            this._equiplist.addEventListener("doubleclick",this.__cellDoubleClick);
            this._proplist.addEventListener("doubleclick",this.__cellDoubleClick);
         }
         else
         {
            this._equiplist.removeEventListener("doubleclick",this.__cellDoubleClick);
            this._proplist.removeEventListener("doubleclick",this.__cellDoubleClick);
         }
      }
      
      protected function __cellDoubleClick(_arg_1:CellEvent) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         _arg_1.stopImmediatePropagation();
         var _local_4:BagCell = _arg_1.data as BagCell;
         var _local_7:InventoryItemInfo = _local_4.info as InventoryItemInfo;
         if(Boolean(BagAndInfoManager.Instance.getBagAndGiftFrame()) && BagAndInfoManager.Instance.getBagAndGiftFrame().infoFrame.currentType == 3)
         {
            if(_local_7.CategoryID == TexpManager.Instance.texpType)
            {
               SocketManager.Instance.out.sendClearStoreBag();
               SocketManager.Instance.out.sendMoveGoods(_local_7.BagType,_local_7.Place,12,0,_local_7.Count,true);
               return;
            }
            if(TexpManager.Instance.texpType == 53)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("texpSystem.view.TextCell.magicTexpTips"));
               return;
            }
            if(TexpManager.Instance.texpType == 20)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("texpSystem.view.TextCell.texpTips"));
               return;
            }
            if(TexpManager.Instance.texpType == 78)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("texpSystem.view.TextCell.nsTexpTips"));
               return;
            }
            if(TexpManager.Instance.texpType == 88)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("texpSystem.view.TextCell.lsTexpTips"));
               return;
            }
         }
         var _local_6:ItemTemplateInfo = ItemManager.Instance.getTemplateById(_local_7.TemplateID);
         var _local_2:int = PlayerManager.Instance.Self.Sex ? 1 : 2;
         if(_local_7.getRemainDate() <= 0)
         {
            return;
         }
         if(_local_6.NeedSex != _local_2 && _local_6.NeedSex != 0)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.data.player.SelfInfo.object"));
            return;
         }
         if(!_local_4.locked)
         {
            if(int(_local_4.info.Property1) != 1100 && (_local_4.info.BindType == 1 || _local_4.info.BindType == 2 || _local_4.info.BindType == 3) && _local_4.itemInfo.IsBinds == false)
            {
               _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("tank.view.bagII.BagIIView.BindsInfo"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),true,true,true,2);
               _local_3.addEventListener("response",this.__onResponse);
               this.temInfo = _local_7;
            }
            else
            {
               SoundManager.instance.play("008");
               if(_local_7.BagType == 1)
               {
                  if(_arg_1.target is PetBagListView && _local_7.CategoryID == 34)
                  {
                     PetsManager.instance.show(0,1);
                  }
                  else
                  {
                     CellMenu.instance.cell = _local_4;
                     this.__cellUse(_arg_1);
                  }
               }
               else if(PlayerManager.Instance.Self.canEquip(_local_7))
               {
                  if(EquipType.isArmShell(_local_7))
                  {
                     return;
                  }
                  _local_5 = PlayerManager.Instance.getDressEquipPlace(_local_7);
                  SocketManager.Instance.out.sendMoveGoods(0,_local_7.Place,0,_local_5,_local_7.Count);
               }
            }
         }
      }
      
      private function __onResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.target as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__onResponse);
         _local_2.dispose();
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            this.sendDefy();
         }
      }
      
      private function sendDefy() : void
      {
         var _local_1:int = 0;
         SoundManager.instance.play("008");
         if(PlayerManager.Instance.Self.canEquip(this.temInfo))
         {
            _local_1 = PlayerManager.Instance.getDressEquipPlace(this.temInfo);
            SocketManager.Instance.out.sendMoveGoods(0,this.temInfo.Place,0,_local_1,this.temInfo.Count);
         }
      }
      
      private function __cellAddPrice(_arg_1:Event) : void
      {
         var _local_2:BagCell = CellMenu.instance.cell;
         if(Boolean(_local_2))
         {
            if(ShopManager.Instance.canAddPrice(_local_2.itemInfo.TemplateID))
            {
               if(PlayerManager.Instance.Self.bagLocked)
               {
                  BaglockedManager.Instance.show();
                  return;
               }
               AddPricePanel.Instance.setInfo(_local_2.itemInfo,false);
               AddPricePanel.Instance.show();
            }
            else
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.cantAddPrice"));
            }
         }
      }
      
      protected function __cellMove(_arg_1:Event) : void
      {
         var _local_2:BagCell = CellMenu.instance.cell;
         if(Boolean(_local_2))
         {
            _local_2.dragStart();
         }
      }
      
      protected function __cellOpenBatch(_arg_1:Event) : void
      {
         var _local_2:* = null;
         var _local_3:BagCell = CellMenu.instance.cell as BagCell;
         if(_local_3 != null && _local_3.itemInfo != null)
         {
            if(PlayerManager.Instance.Self.bagLocked)
            {
               BaglockedManager.Instance.show();
               return;
            }
            _local_2 = ComponentFactory.Instance.creatComponentByStylename("bag.OpenBatchView");
            _local_2.item = _local_3.itemInfo;
            LayerManager.Instance.addToLayer(_local_2,3,true,1);
         }
      }
      
      protected function __cellOpen(_arg_1:Event) : void
      {
         var _local_10:Number = NaN;
         var _local_5:int = 0;
         var _local_9:int = 0;
         var _local_8:int = 0;
         var _local_6:* = null;
         var _local_3:* = null;
         var _local_2:* = null;
         var _local_7:* = null;
         var reward_select:RewardSelectBox = null;
         var _local_4:BagCell = CellMenu.instance.cell as BagCell;
         this._currentCell = _local_4;
         if(_local_4 != null && _local_4.itemInfo != null)
         {
            if(PlayerManager.Instance.Self.bagLocked)
            {
               BaglockedManager.Instance.show();
               return;
            }
            BagAndInfoManager.Instance.isUpgradePack = _local_4.itemInfo.Property1 == "6" && _local_4.itemInfo.Property2 == "9" && _local_4.itemInfo.Property3 == "0";
            _local_10 = PlayerManager.Instance.Self.Sex ? 1 : 2;
            if(_local_4.info.NeedSex != 0 && _local_10 != _local_4.info.NeedSex)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.sexErr"));
               return;
            }
            if(PlayerManager.Instance.Self.Grade >= _local_4.info.NeedLevel)
            {
               if(_local_4.info.TemplateID == 112109)
               {
                  if(PlayerManager.Instance.Self.IsVIP)
                  {
                     RouletteManager.instance.useVipBox(_local_4);
                  }
                  else
                  {
                     _arg_1.stopImmediatePropagation();
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.vip.vipIcon.notVip"));
                  }
               }
               else if(_local_4.info.TemplateID == 112019)
               {
                  RouletteManager.instance.useRouletteBox(_local_4);
               }
               else if(_local_4.info.TemplateID == 190000)
               {
                  _arg_1.stopImmediatePropagation();
                  RouletteManager.instance.useSurpriseRoulette(_local_4);
               }
               else if(EquipType.isCaddy(_local_4.info))
               {
                  _arg_1.stopImmediatePropagation();
                  RouletteManager.instance.useCaddy(_local_4);
               }
               else if(_local_4.info.TemplateID == 112222 || _local_4.info.TemplateID == 112223 || _local_4.info.TemplateID == 112224)
               {
                  _arg_1.stopImmediatePropagation();
                  RouletteManager.instance.useBless(_local_4);
               }
               else if(EquipType.isBeadNeedOpen(_local_4.info))
               {
                  _arg_1.stopImmediatePropagation();
                  RouletteManager.instance.useBead(_local_4.info.TemplateID);
               }
               else if(35 == _local_4.info.CategoryID)
               {
                  SocketManager.Instance.out.sendAddPet(_local_4.itemInfo.Place,_local_4.itemInfo.BagType);
               }
               else if(_local_4.info.TemplateID == 112255)
               {
                  _arg_1.stopImmediatePropagation();
                  RouletteManager.instance.useCelebrationBox();
               }
               else if(_local_4.info.TemplateID == 112262 && !_local_4.itemInfo.IsBinds)
               {
                  _arg_1.stopImmediatePropagation();
                  WonderfulActivityManager.Instance.useBattleCompanion(_local_4.itemInfo);
               }
               else if(EquipType.isOfferPackage(_local_4.info))
               {
                  _arg_1.stopImmediatePropagation();
                  RouletteManager.instance.useOfferPack(_local_4);
               }
               else if(EquipType.isTimeBox(_local_4.info))
               {
                  _local_6 = DateUtils.getDateByStr(InventoryItemInfo(_local_4.info).BeginDate);
                  _local_5 = int(int(int(int(_local_4.info.Property3) * 60 - (TimeManager.Instance.Now().getTime() - _local_6.getTime()) / 1000)));
                  if(_local_5 <= 0)
                  {
                     SocketManager.Instance.out.sendItemOpenUp(_local_4.itemInfo.BagType,_local_4["place"]);
                  }
                  else
                  {
                     _local_9 = int(int(int(_local_5 / 3600)));
                     _local_8 = int(int(int(_local_5 % 3600 / 60)));
                     _local_8 = _local_8 > 0 ? _local_8 : 1;
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.userGuild.boxTip",_local_9,_local_8));
                  }
               }
               else if(_local_4.info.CategoryID == 18)
               {
                  _arg_1.stopImmediatePropagation();
                  SocketManager.Instance.out.sendOpenCardBox(_local_4["place"],1,_local_4.bagType);
               }
               else if(_local_4.info.CategoryID == 66)
               {
                  _arg_1.stopImmediatePropagation();
                  SocketManager.Instance.out.sendOpenSpecialCardBox(_local_4["place"],1,_local_4.bagType);
               }
               else if(_local_4.info.TemplateID == 112108 || _local_4.info.TemplateID == 112150 || _local_4.info.TemplateID == 1120538 || _local_4.info.TemplateID == 1120539)
               {
                  SocketManager.Instance.out.sendOpenRandomBox(_local_4["place"],1);
               }
               else if(_local_4.info.TemplateID == 11961 || _local_4.info.TemplateID == 11965 || _local_4.info.TemplateID == 11967)
               {
                  SocketManager.Instance.out.sendOpenNationWord(_local_4.bagType,_local_4.place,1);
               }
               else if(EquipType.isSpecilPackage(_local_4.info))
               {
                  if(PlayerManager.Instance.Self.DDTMoney >= int(_local_4.info.Property3))
                  {
                     _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("tank.view.bagII.BagIIView.AskGiftBag",_local_4.info.Property3,_local_4.info.Name),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,false,false,2);
                     _local_3.addEventListener("response",this.__GiftBagframeClose);
                  }
                  else
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.BagIIView.AskGiftBagII",_local_4.info.Property3));
                  }
               }
               else if(_local_4.info.Property2 == "8")
               {
                  _local_2 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("bagView.consumePack.openTxt",_local_4.info.Property3),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),true,true,true,2,null,"SimpleAlert",60,false);
                  _local_2.addEventListener("response",this.onConsumePackResponse);
               }
               else if(_local_4.info.Property2 == "11")
               {
                  _local_7 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("bagView.consumePack.openTxt",_local_4.info.Property3),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),true,true,true,2,null,"SimpleAlert",60,false,0);
                  _local_7.addEventListener("response",this.onConsumePackResponse);
               }
               else if(_local_4.itemInfo.CategoryID == 222222222)
               {
                  ItemActivityGiftManager.instance.showFrame(1,_local_4.itemInfo);
               }
               else if(_local_4.info.TemplateID == 1120412 || _local_4.info.TemplateID == 1120413 || _local_4.info.TemplateID == 1120414 || _local_4.info.TemplateID == 1120433 || _local_4.info.TemplateID == 1120434 || _local_4.info.TemplateID == 12545 || _local_4.info.TemplateID == 101000042 || _local_4.info.TemplateID == 101000043 || _local_4.info.TemplateID == 101000044 || _local_4.info.TemplateID == 101000045 || _local_4.info.TemplateID == 101000046 || _local_4.info.TemplateID == 101000047 || _local_4.info.TemplateID == 101000048 || _local_4.info.TemplateID == 101000049 || _local_4.info.TemplateID == 101000050 || _local_4.info.TemplateID == 101000051)
               {
                  SocketManager.Instance.out.sendChangeSex(_local_4.bagType,_local_4.place);
               }
               else if(_local_4.info.CategoryID == 72)
               {
                  SocketManager.Instance.out.sendItemOpenUp(_local_4.itemInfo.BagType,_local_4.itemInfo.Place);
               }
               else if(EquipType.isGetPackage(_local_4.info))
               {
                  this.showGetFriendPackFrame(_local_4.info,_local_4.itemInfo.BagType,_local_4.itemInfo.Place);
               }
               else if(EquipType.isFireworks(_local_4.info))
               {
                  SocketManager.Instance.out.sendUseCard(_local_4.itemInfo.BagType,_local_4.itemInfo.Place,[_local_4.info.TemplateID],_local_4.info.PayType);
               }
               else if(_local_4.info.CategoryID == 68)
               {
                  SocketManager.Instance.out.sendOpenAmuletBox(_local_4.itemInfo.BagType,_local_4.itemInfo.Place);
               }
               else if(_local_4.info.CategoryID == 11 && _local_4.info.Property1 == "66")
               {
                  reward_select = new RewardSelectBox(_local_4.itemInfo);
                  LayerManager.Instance.addToLayer(reward_select,3,true,1);
               }
               else if(_local_4.info.CategoryID == 43)
               {
                  SocketManager.Instance.out.sendRoomBordenItemUp(_local_4.place);
               }
               else if(EquipType.isNewRedPackage(_local_4.info.TemplateID))
               {
                  NewRedPackageManager.ins.showRed(_local_4.info.TemplateID,int(_local_4.info.Property2),int(_local_4.info.Property1));
               }
               else
               {
                  SocketManager.Instance.out.sendItemOpenUp(_local_4.itemInfo.BagType,_local_4.itemInfo.Place);
               }
            }
            else if(_local_4.info.CategoryID == 18)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.cardSystem.bagView.openCardBox.level"));
            }
            else
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.BagIIView.level"));
            }
         }
      }
      
      private function showGetFriendPackFrame(_arg_1:ItemTemplateInfo, _arg_2:int, _arg_3:int) : void
      {
         var _local_4:ItemTemplateInfo = ItemManager.Instance.getTemplateById(int(_arg_1.Property8));
         if(Boolean(this._getFriendPackFrame))
         {
            this._getFriendPackFrame.dispose();
            this._getFriendPackFrame = null;
         }
         this._getFriendPackFrame = ComponentFactory.Instance.creatComponentByStylename("bag.getFriendPackFrame");
         this._getFriendPackFrame.updateView(_local_4,_arg_2,_arg_3);
         this._getFriendPackFrame.show();
      }
      
      protected function onConsumePackResponse(_arg_1:FrameEvent) : void
      {
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         _local_2.removeEventListener("response",this.onConsumePackResponse);
         if(_arg_1.responseCode == 0 || _arg_1.responseCode == 4 || _arg_1.responseCode == 1)
         {
            _local_2.dispose();
            return;
         }
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            if(_local_2.isBand)
            {
               if(PlayerManager.Instance.Self.BandMoney < int(this._currentCell.info.Property3))
               {
                  this.initAlertFarme();
               }
               else
               {
                  SocketManager.Instance.out.sendItemOpenUp(this._currentCell.itemInfo.BagType,this._currentCell.itemInfo.Place,1,_local_2.isBand);
               }
            }
            else if(PlayerManager.Instance.Self.Money < int(this._currentCell.info.Property3))
            {
               LeavePageManager.showFillFrame();
            }
            else
            {
               SocketManager.Instance.out.sendItemOpenUp(this._currentCell.itemInfo.BagType,this._currentCell.itemInfo.Place,1,_local_2.isBand);
            }
         }
         _local_2.dispose();
      }
      
      private function initAlertFarme() : void
      {
         var _local_1:* = null;
         _local_1 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("buried.alertInfo.noBindMoney"),"",LanguageMgr.GetTranslation("cancel"),true,false,false,2);
         _local_1.addEventListener("response",this.onResponseHander);
      }
      
      protected function onResponseHander(_arg_1:FrameEvent) : void
      {
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            if(PlayerManager.Instance.Self.Money < int(this._currentCell.info.Property3))
            {
               LeavePageManager.showFillFrame();
            }
            else
            {
               SocketManager.Instance.out.sendItemOpenUp(this._currentCell.itemInfo.BagType,this._currentCell.itemInfo.Place,1,false);
            }
         }
         _arg_1.currentTarget.dispose();
      }
      
      private function __GiftBagframeClose(_arg_1:FrameEvent) : void
      {
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               if(Boolean(this._currentCell) && Boolean(this._currentCell.itemInfo))
               {
                  SocketManager.Instance.out.sendItemOpenUp(this._currentCell.itemInfo.BagType,this._currentCell["place"]);
                  break;
               }
         }
         _arg_1.currentTarget.removeEventListener("response",this.__GiftBagframeClose);
         ObjectUtils.disposeObject(_arg_1.currentTarget);
      }
      
      private function __cellUse(evt:Event) : void
      {
         var alert:* = null;
         var thisQualifiedClassName:* = null;
         var msg:* = null;
         var alertCheckTips:* = null;
         evt.stopImmediatePropagation();
         var reward_select:RewardSelectBox = null;
         var cell:BagCell = CellMenu.instance.cell as BagCell;
         if(cell.info.CategoryID == 73)
         {
            return;
         }
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         if(!cell || cell.info == null)
         {
            return;
         }
         if(cell.info.TemplateID == 11560 || cell.info.TemplateID == 11561 || cell.info.TemplateID == 11562)
         {
            if(this is ConsortionBankBagView)
            {
               BagStore.instance.isFromConsortionBankFrame = true;
            }
            else if(this.isHomeBankBagView())
            {
               BagStore.instance.isFromHomeBankFrame = true;
            }
            else
            {
               BagStore.instance.isFromBagFrame = true;
            }
            BagStore.instance.openStore("forge_store",1);
            return;
         }
         if(cell.info.TemplateID == 11961 || cell.info.TemplateID == 11965 || cell.info.TemplateID == 11967)
         {
            SocketManager.Instance.out.sendOpenNationWord(cell.bagType,cell.place,1);
            return;
         }
         if(cell.info.TemplateID == 11994)
         {
            this.startReworkName(cell.bagType,cell.place);
            return;
         }
         if(cell.info.CategoryID == 11 && cell.info.Property1 == "5" && cell.info.Property2 != "0")
         {
            this.showChatBugleInputFrame(cell.info.TemplateID);
            return;
         }
         if(cell.info.CategoryID == 23)
         {
            if(PlayerManager.Instance.Self.Grade < 13)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("texpSystem.view.TexpCell.noGrade",13));
               return;
            }
            if(TexpManager.Instance.getLv(TexpManager.Instance.getExp(int(cell.info.Property1))) >= 100)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("texpSystem.view.TexpCell.lvToplimit"));
               return;
            }
            if(TaskManager.instance.texpQuests.length > 0)
            {
               this._tmpCell = cell;
               alert = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("texpSystem.view.TexpView.refreshTaskTip"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),true,true,true,2);
               alert.addEventListener("response",this.__texpResponse);
               return;
            }
            SocketManager.Instance.out.sendTexp(-1,cell.info.TemplateID,1,cell.place);
            return;
         }
         if(cell.info.TemplateID == 11993)
         {
            if(PlayerManager.Instance.Self.ConsortiaID == 0)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.ConsortiaReworkNameView.consortiaNameAlert1"));
               return;
            }
            if(PlayerManager.Instance.Self.NickName != PlayerManager.Instance.Self.consortiaInfo.ChairmanName)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.ConsortiaReworkNameView.consortiaNameAlert2"));
               return;
            }
            this.startupConsortiaReworkName(cell.bagType,cell.place);
            return;
         }
         if(cell.info.TemplateID == 12604)
         {
            if(PlayerManager.Instance.Self.teamID == 0)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.battleTeamRenameAlert"));
               return;
            }
            if(PlayerManager.Instance.Self.teamDuty != 1)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.battleTeamRenameAlert1"));
               return;
            }
            this.startupBattleTeamReworkName(cell.bagType,cell.place);
            return;
         }
         if(cell.info.TemplateID == 11569)
         {
            this.startupChangeSex(cell.bagType,cell.place);
            return;
         }
         if(cell.info.CategoryID == 11 && int(cell.info.Property1) == 37)
         {
            if(!PlayerManager.Instance.Self.Bag.getItemAt(6))
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.bagAndInfo.ColorShell.NoWeapon"));
               return;
            }
            if(PlayerManager.Instance.Self.Bag.getItemAt(6).StrengthenLevel >= 10)
            {
               SocketManager.Instance.out.sendUseChangeColorShell(cell.bagType,cell.place);
               return;
            }
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("bagAndInfo.bag.UnableUseColorShell"));
         }
         else if(cell.info.CategoryID == 18)
         {
            SocketManager.Instance.out.sendOpenCardBox(cell.place,1,cell.bagType);
         }
         else if(cell.info.CategoryID == 66)
         {
            SocketManager.Instance.out.sendOpenSpecialCardBox(cell.place,1,cell.bagType);
         }
         if(cell.info.TemplateID == 12511)
         {
            CalendarManager.getInstance().open(1);
         }
         else if(cell.info.TemplateID == 11999)
         {
            ChangeColorManager.instance.changeColorModel.place = cell.place;
            ChangeColorManager.instance.changeColorModel.getColorEditableThings();
            ChangeColorManager.instance.show();
         }
         else if(cell.info.TemplateID != 34101)
         {
            if(cell.info.CategoryID == 11 && int(cell.info.Property1) == 24)
            {
               if(TrusteeshipManager.instance.spiritValue >= TrusteeshipManager.instance.maxSpiritValue)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.trusteeship.maxSpiritCannotUseTxt"));
                  return;
               }
               SocketManager.Instance.out.sendTrusteeshipUseSpiritItem(cell.place,cell.bagType);
            }
            else if(cell.info.CategoryID == 11 && int(cell.info.Property1) == 39)
            {
               SocketManager.Instance.out.sendUseItemKingBless(cell.place,cell.bagType);
            }
            else if(cell.info.CategoryID == 11 && int(cell.info.Property1) == 39 && int(cell.info.Property2) == 10)
            {
               SocketManager.Instance.out.sendUseItemDeed(cell.place,cell.bagType);
            }
            else if(cell.info.CategoryID == 11 && int(cell.info.Property1) == 101)
            {
               if(this is ConsortionBankBagView)
               {
                  BagStore.instance.isFromConsortionBankFrame = true;
               }
               else if(this.isHomeBankBagView())
               {
                  BagStore.instance.isFromHomeBankFrame = true;
               }
               else
               {
                  BagStore.instance.isFromBagFrame = true;
               }
               if(PlayerManager.Instance.Self.Grade < 5)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",5));
                  return;
               }
               BagStore.instance.openStore("forge_store",0);
            }
            else if(EquipType.isStrengthStone(cell.info))
            {
               if(this is ConsortionBankBagView)
               {
                  BagStore.instance.isFromConsortionBankFrame = true;
               }
               else if(this.isHomeBankBagView())
               {
                  BagStore.instance.isFromHomeBankFrame = true;
               }
               else
               {
                  BagStore.instance.isFromBagFrame = true;
               }
               BagStore.instance.openStore("bag_store");
            }
            else if(cell.info.CategoryID == 11 && int(cell.info.Property1) == 45)
            {
               if(PlayerManager.Instance.Self.Grade < 25)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.godTemple.openGodTempleBtn.text",(25).toString()));
                  return;
               }
               thisQualifiedClassName = getQualifiedClassName(this);
               if(this is ConsortionBankBagView)
               {
                  BagStore.instance.isFromConsortionBankFrame = true;
               }
               else if(this.isHomeBankBagView())
               {
                  BagStore.instance.isFromHomeBankFrame = true;
               }
               else
               {
                  BagStore.instance.isFromBagFrame = true;
               }
               BagStore.instance.openStore("bag_store",1);
            }
            else if(cell.info.CategoryID == 11 && int(cell.info.Property1) == 82)
            {
               if((cell.info as InventoryItemInfo).ValidDate == 0)
               {
                  if(this._self.horsePicCherishDic.hasKey(cell.info.Property2) && this._self.horsePicCherishDic[cell.info.Property2].isValid && Boolean(this._self.horsePicCherishDic[cell.info.Property2].isUsed))
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("horse.pic.alreadyActive"));
                     return;
                  }
                  HorseManager.instance.skipHorsePicCherishId = int(cell.info.Property2);
                  HorseManager.instance.isSkipFromBagView = true;
                  HorseManager.instance.show();
               }
               else
               {
                  SocketManager.Instance.out.sendActiveHorsePicCherish((cell.info as InventoryItemInfo).Place);
               }
            }
            else if(cell.info.CategoryID == 62 && cell.info.Property1 == "1")
            {
               SocketManager.Instance.out.sendUsePetTemporaryCard((cell.info as InventoryItemInfo).BagType,(cell.info as InventoryItemInfo).Place);
            }
            else if(cell.info.CategoryID == 11 && (int(cell.info.Property1) == 27 || int(cell.info.Property1) == 29 || int(cell.info.Property1) == 107))
            {
               if(this._self.Grade < 19)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.pets.openTxt",19));
                  return;
               }
               PetsBagManager.instance().show();
            }
            else if(!(cell.info.CategoryID == 11 && int(cell.info.Property1) == 47))
            {
               if(cell.info.CategoryID == 62 && int(cell.info.Property1) == 0)
               {
                  if(this._self.Grade < 25)
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.pets.openTxt",25));
                     return;
                  }
                  PetsBagManager.instance().show();
               }
               else if(24 == cell.info.CategoryID)
               {
                  SocketManager.Instance.out.sendNewTitleCard(cell.itemInfo.Place,cell.itemInfo.BagType);
               }
               else if(EquipType.isComposeStone(cell.info))
               {
                  if(this is ConsortionBankBagView)
                  {
                     BagStore.instance.isFromConsortionBankFrame = true;
                  }
                  else if(this.isHomeBankBagView())
                  {
                     BagStore.instance.isFromHomeBankFrame = true;
                  }
                  else
                  {
                     BagStore.instance.isFromBagFrame = true;
                  }
                  BagStore.instance.openStore("bag_store",2);
               }
               else if(cell.info.TemplateID == 100100)
               {
                  if(PlayerManager.Instance.Self.Grade < 30)
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("gemstone.limitLevel.tipTxt",30));
                     return;
                  }
                  if(this is ConsortionBankBagView)
                  {
                     BagStore.instance.isFromConsortionBankFrame = true;
                  }
                  else if(this.isHomeBankBagView())
                  {
                     BagStore.instance.isFromHomeBankFrame = true;
                  }
                  else
                  {
                     BagStore.instance.isFromBagFrame = true;
                  }
                  BagStore.instance.openStore("forge_store",3);
               }
               else if(cell.info.TemplateID == 11164 || cell.info.TemplateID == 11165)
               {
                  HorseManager.instance.show();
               }
               else if(cell.info.TemplateID == 12568)
               {
                  VIPCouponsManager.instance.openShow(cell.bagType,cell.place);
               }
               else if(cell.info.TemplateID == 12569)
               {
                  VIPCouponsManager.instance.useVipCoupons(cell.bagType,cell.place);
               }
               else if(cell.info.CategoryID == 11 && int(cell.info.Property1) == 104)
               {
                  if(PlayerManager.Instance.Self.Grade < 40)
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.godTemple.openGodTempleBtn.text",40));
                     return;
                  }
                  BagStore.instance.openStore("forge_store",4);
               }
               else if(cell.info.TemplateID == 11179)
               {
                  SocketManager.Instance.out.sendItemOpenUp(cell.itemInfo.BagType,cell["place"]);
               }
               else if(cell.info.TemplateID == 12536 || cell.info.TemplateID == 12537)
               {
                  if(PlayerManager.Instance.Self.IsMarried)
                  {
                     SocketManager.Instance.out.sendUseLoveFeelingly(cell.itemInfo.BagType,cell["place"]);
                  }
                  else
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddtBagAndInfo.hasNoMerry.info"));
                  }
               }
               else if(cell.info.TemplateID == 11966)
               {
                  RandomSuitCardManager.getInstance().useCard(cell.itemInfo.Place);
               }
               else if(cell.info.TemplateID == 12545)
               {
                  SocketManager.Instance.out.sendChangeSex(cell.bagType,cell.place);
               }
               else if(cell.info.CategoryID == 68)
               {
                  SocketManager.Instance.out.sendOpenAmuletBox(cell.itemInfo.BagType,cell.itemInfo.Place);
               }
               else if((cell.info.CategoryID == 20 || cell.info.CategoryID == 53) && int(cell.info.Property1) == 0)
               {
                  BagAndInfoManager.Instance.showBagAndInfo(2);
               }
               else if(cell.info.CategoryID == 11 && int(cell.info.Property1) == 108)
               {
                  BagAndInfoManager.Instance.showBagAndInfo(5);
               }
               else if(cell.info.CategoryID == 11 && int(cell.info.Property1) == 106)
               {
                  BagAndInfoManager.Instance.showBagAndInfo(9);
               }
               else if(cell.info.CategoryID == 11 && (int(cell.info.Property1) == 120 || int(cell.info.Property1) == 21))
               {
                  SocketManager.Instance.out.sendUseCard(cell.itemInfo.BagType,cell.itemInfo.Place,[cell.info.TemplateID],cell.info.PayType);
               }
               else if(cell.info.CategoryID == 11 && int(cell.info.Property1) == 22)
               {
                  if(PlayerManager.Instance.Self.Grade < cell.info.NeedLevel)
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.consortia.club.CreatConsortiaFrame.yourGrade",cell.info.NeedLevel));
                     return;
                  }
                  if(int(cell.info.Property3) > 0 && PlayerManager.Instance.Self.VIPLevel < int(cell.info.Property3))
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("panicBuying.vipLvlLimit",cell.info.Property3));
                     return;
                  }
                  msg = LanguageMgr.GetTranslation("DecExpBottle.gourdHelpTipView.useTips",cell.info.Name,cell.info.Property2);
                  if(int(cell.info.Property2) == 0)
                  {
                     msg = LanguageMgr.GetTranslation("DecExpBottle.gourdHelpTipView.AlluseTips",cell.info.Name);
                  }
                  this._currentUseItemTemp = cell;
                  alertCheckTips = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),msg,LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,true,false,2);
                  alertCheckTips.addEventListener("response",this.__onDecGourdExBottleHandler);
               }
               else if(cell.info.CategoryID == 85)
               {
                  ElfManager.instance.sendElfActivate(cell.bagType,cell.place);
               }
               else if(cell.info.CategoryID == 86)
               {
                  ElfManager.instance.show(1,0);
               }
               else if(cell.info.CategoryID == 11 && cell.info.Property1 == "66")
               {
                  reward_select = new RewardSelectBox(cell.itemInfo);
                  LayerManager.Instance.addToLayer(reward_select,3,true,1);
               }
               else if(cell.info.CategoryID == 91)
               {
                  ElfManager.instance.sendGetEllfEquip(cell.bagType,cell.place);
               }
               else if(cell.info.CategoryID == 11 && (int(cell.info.Property1) == 100 || int(cell.info.Property1) == 1100 || int(cell.info.Property1) == 115 || int(cell.info.Property1) == 1200))
               {
                  this.useProp(cell.itemInfo);
               }
               else if((cell.info.CategoryID == 11 || cell.info.CategoryID == 72) && int(cell.info.Property1) == 6)
               {
                  this.__cellOpen(evt);
               }
               else
               {
                  this.useCard(cell.itemInfo);
               }
            }
         }
      }
      
      private function isHomeBankBagView() : Boolean
      {
         var _local_1:String = getQualifiedClassName(this);
         if(_local_1 == "homeBank.view::HomeBankBagView")
         {
            return true;
         }
         return false;
      }
      
      protected function __cellColorChange(_arg_1:Event) : void
      {
         var _local_2:BagCell = CellMenu.instance.cell;
         if(Boolean(_local_2))
         {
            if(PlayerManager.Instance.Self.bagLocked)
            {
               BaglockedManager.Instance.show();
               return;
            }
            if(_local_2.itemInfo.CategoryID == 13 || _local_2.itemInfo.CategoryID == 15)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.changeColor.suitAndWingCannotChange"));
               return;
            }
            if(this.checkDress(_local_2))
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("playerDress.canNotChangeColor"));
               return;
            }
            ChangeColorManager.instance.changeColorModel.place = -1;
            ChangeColorManager.instance.addOneThing(_local_2);
            ChangeColorManager.instance.show();
         }
      }
      
      private function __alertChangeColor(_arg_1:FrameEvent) : void
      {
         _arg_1.currentTarget.removeEventListener("response",this.__alertChangeColor);
         SoundManager.instance.play("008");
         if(_arg_1.responseCode == 3 || _arg_1.responseCode == 2)
         {
            if(PlayerManager.Instance.Self.Money < ShopManager.Instance.getGiftShopItemByTemplateID(11999).getItemPrice(1).bothMoneyValue)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("shop.view.giftLack"));
               return;
            }
         }
      }
      
      protected function __cellSell(_arg_1:Event) : void
      {
         var _local_2:BagCell = CellMenu.instance.cell;
         if(_local_2.info.CategoryID == 73)
         {
            return;
         }
         if(this.checkDress(_local_2))
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("playerDress.canNotSell"));
            return;
         }
         if(Boolean(_local_2))
         {
            _local_2.sellItem(_local_2.itemInfo);
         }
      }
      
      private function checkDress(_arg_1:BagCell) : Boolean
      {
         var _local_7:int = 0;
         var _local_3:Array = null;
         var _local_2:DressModel = null;
         var _local_4:InventoryItemInfo = null;
         var _local_6:* = null;
         if(!DressUtils.isDress(_arg_1.itemInfo))
         {
            return false;
         }
         var _local_5:Array = PlayerDressManager.instance.modelArr;
         for each(_local_3 in _local_5)
         {
            _local_7 = 0;
            while(_local_7 <= _local_3.length - 1)
            {
               _local_6 = _local_3[_local_7];
               if(_local_6.itemId == _arg_1.itemInfo.ItemID)
               {
                  return true;
               }
               _local_7++;
            }
         }
         _local_2 = PlayerDressManager.instance.currentModel;
         if(Boolean(_local_2))
         {
            for each(_local_4 in _local_2.model.Bag.items)
            {
               if(Boolean(_local_4) && _local_4.ItemID == _arg_1.itemInfo.ItemID)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      private function __texpResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.playButtonSound();
         var _local_2:BaseAlerFrame = _arg_1.target as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__texpResponse);
         _local_2.dispose();
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            if(PlayerManager.Instance.Self.Money < 10)
            {
               LeavePageManager.showFillFrame();
               this._tmpCell = null;
               return;
            }
            SocketManager.Instance.out.sendTexp(-1,this._tmpCell.info.TemplateID,1,this._tmpCell.place);
            this._tmpCell = null;
         }
      }
      
      private function useCard(_arg_1:InventoryItemInfo) : void
      {
         if(_arg_1.TemplateID == 11995 || _arg_1.TemplateID == 11998 || _arg_1.TemplateID == 11997 || _arg_1.TemplateID == 11996 || _arg_1.TemplateID.toString().substring(0,3) == "119" || _arg_1.TemplateID == 11992 || _arg_1.TemplateID == 20150 || _arg_1.TemplateID == 201145 || _arg_1.TemplateID == 12535 || _arg_1.TemplateID == 1120435 || _arg_1.TemplateID == 12746)
         {
            if(this._self.Grade < 3 && (_arg_1.TemplateID == 11992 || _arg_1.CategoryID == 11 && _arg_1.Property1 == "25"))
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",3));
               return;
            }
            if(_arg_1.TemplateID != 11916 && _arg_1.TemplateID != 11957 && _arg_1.TemplateID != 11968)
            {
               SocketManager.Instance.out.sendUseCard(_arg_1.BagType,_arg_1.Place,[_arg_1.TemplateID],_arg_1.PayType);
            }
         }
      }
      
      private function useProp(_arg_1:InventoryItemInfo) : void
      {
         if(!_arg_1)
         {
            return;
         }
         SocketManager.Instance.out.sendUseProp(_arg_1.BagType,_arg_1.Place,[_arg_1.TemplateID],_arg_1.PayType);
      }
      
      private function createBreakWin(_arg_1:BagCell) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BreakGoodsView = ComponentFactory.Instance.creatComponentByStylename("breakGoodsView");
      }
      
      public function setCellInfo(_arg_1:int, _arg_2:InventoryItemInfo) : void
      {
         this._currentList.setCellInfo(_arg_1,_arg_2);
      }
      
      public function dispose() : void
      {
         if(Boolean(this._oneKeyFeedMC))
         {
            this._oneKeyFeedMC.removeEventListener("oneKeyComplete",this.__disposeOneKeyMC);
            this._oneKeyFeedMC.stop();
            ObjectUtils.disposeObject(this._oneKeyFeedMC);
         }
         this.removeEvents();
         this.resetMouse();
         this._info = null;
         this._lists = null;
         this._tmpCell = null;
         this._self.getBag(0).removeEventListener("update",this.__onBagUpdateEQUIPBAG);
         this._self.getBag(1).removeEventListener("update",this.__onBagUpdatePROPBAG);
         if(Boolean(this._pgup))
         {
            ObjectUtils.disposeObject(this._pgup);
         }
         this._pgup = null;
         if(Boolean(this._pgdn))
         {
            ObjectUtils.disposeObject(this._pgdn);
         }
         this._pgdn = null;
         if(Boolean(this._pageTxt))
         {
            ObjectUtils.disposeObject(this._pageTxt);
         }
         this._pageTxt = null;
         if(Boolean(this._pageTxtBg))
         {
            ObjectUtils.disposeObject(this._pageTxtBg);
         }
         this._pageTxtBg = null;
         if(Boolean(this._beadSortBtn))
         {
            ObjectUtils.disposeObject(this._beadSortBtn);
         }
         this._beadSortBtn = null;
         if(Boolean(this._sellBtn))
         {
            this._sellBtn.removeEventListener("click",this.__sellClick);
         }
         if(Boolean(this._sellBtn))
         {
            this._sellBtn.removeEventListener("stopsell",this.__stopSell);
         }
         if(Boolean(this._breakBtn))
         {
            this._breakBtn.removeEventListener("click",this.__breakClick);
         }
         if(Boolean(this._goodsNumInfoBg))
         {
            ObjectUtils.disposeObject(this._goodsNumInfoBg);
         }
         this._goodsNumInfoBg = null;
         if(Boolean(this._goodsNumInfoText))
         {
            ObjectUtils.disposeObject(this._goodsNumInfoText);
         }
         this._goodsNumInfoText = null;
         if(Boolean(this._goodsNumTotalText))
         {
            ObjectUtils.disposeObject(this._goodsNumTotalText);
         }
         this._goodsNumTotalText = null;
         if(Boolean(this._tabBtn1))
         {
            ObjectUtils.disposeObject(this._tabBtn1);
         }
         this._tabBtn1 = null;
         if(Boolean(this._tabBtn2))
         {
            ObjectUtils.disposeObject(this._tabBtn2);
         }
         this._tabBtn2 = null;
         if(Boolean(this._tabBtn3))
         {
            ObjectUtils.disposeObject(this._tabBtn3);
         }
         this._tabBtn3 = null;
         if(Boolean(this._tabBtn4))
         {
            ObjectUtils.disposeObject(this._tabBtn4);
         }
         this._tabBtn4 = null;
         if(Boolean(this._bg))
         {
            ObjectUtils.disposeObject(this._bg);
         }
         this._bg = null;
         if(Boolean(this._bg1))
         {
            ObjectUtils.disposeObject(this._bg1);
         }
         this._bg1 = null;
         if(Boolean(this._goldText))
         {
            ObjectUtils.disposeObject(this._goldText);
         }
         this._goldText = null;
         if(Boolean(this._moneyText))
         {
            ObjectUtils.disposeObject(this._moneyText);
         }
         this._moneyText = null;
         ObjectUtils.disposeObject(this._giftText);
         this._giftText = null;
         ObjectUtils.disposeObject(this._orderTxt);
         this._orderTxt = null;
         ObjectUtils.disposeObject(this._keySortBtn);
         this._keySortBtn = null;
         ObjectUtils.disposeObject(this._breakBtn);
         this._breakBtn = null;
         this._currentList = null;
         ObjectUtils.disposeObject(this._sellBtn);
         this._sellBtn = null;
         ObjectUtils.disposeObject(this._proplist);
         this._proplist = null;
         ObjectUtils.disposeObject(this._petlist);
         this._petlist = null;
         ObjectUtils.disposeObject(this._equiplist);
         this._equiplist = null;
         ObjectUtils.disposeObject(this._beadList);
         this._beadList = null;
         ObjectUtils.disposeObject(this._beadList2);
         this._beadList2 = null;
         ObjectUtils.disposeObject(this._beadList3);
         this._beadList3 = null;
         ObjectUtils.disposeObject(this._bgShape);
         this._bgShape = null;
         ObjectUtils.disposeObject(this._goldButton);
         this._goldButton = null;
         ObjectUtils.disposeObject(this._giftButton);
         this._giftButton = null;
         ObjectUtils.disposeObject(this._moneyButton);
         this._moneyButton = null;
         ObjectUtils.disposeObject(this._orderBtn);
         this._orderBtn = null;
         ObjectUtils.disposeObject(this._continueBtn);
         this._continueBtn = null;
         ObjectUtils.disposeObject(this._chatBugleInputFrame);
         this._chatBugleInputFrame = null;
         ObjectUtils.disposeObject(this._bgShapeII);
         this._bgShapeII = null;
         ObjectUtils.disposeObject(this._bagList);
         this._bagList = null;
         ObjectUtils.disposeObject(this._PointCouponBitmap);
         this._PointCouponBitmap = null;
         ObjectUtils.disposeObject(this._LiJinBitmap);
         this._LiJinBitmap = null;
         ObjectUtils.disposeObject(this._MoneyBitmap);
         this._MoneyBitmap = null;
         ObjectUtils.disposeObject(this._orderTxtBitmap);
         this._orderTxtBitmap = null;
         ObjectUtils.disposeObject(this._currentBeadList);
         this._currentBeadList = null;
         ObjectUtils.disposeObject(this.bagLockBtn);
         this.bagLockBtn = null;
         ObjectUtils.disposeObject(this._equipSelectedBtn);
         this._equipSelectedBtn = null;
         ObjectUtils.disposeObject(this._propSelectedBtn);
         this._propSelectedBtn = null;
         ObjectUtils.disposeObject(this._beadSelectedBtn);
         this._beadSelectedBtn = null;
         ObjectUtils.disposeObject(this._dressSelectedBtn);
         this._dressSelectedBtn = null;
         ObjectUtils.disposeObject(this._cardEnbleFlase);
         this._cardEnbleFlase = null;
         ObjectUtils.disposeObject(this._moneyBg);
         this._moneyBg = null;
         ObjectUtils.disposeObject(this._moneyBg1);
         this._moneyBg1 = null;
         ObjectUtils.disposeObject(this._moneyBg2);
         this._moneyBg2 = null;
         ObjectUtils.disposeObject(this._moneyBg3);
         this._moneyBg3 = null;
         ObjectUtils.disposeObject(this._buttonContainer);
         this._buttonContainer = null;
         ObjectUtils.disposeObject(this._bagArrangeSprite);
         this._bagArrangeSprite = null;
         ObjectUtils.disposeObject(this._dressbagView);
         this._dressbagView = null;
         PlayerDressManager.instance.disposeView(1);
         if(Boolean(this._oneKeyFeedMC))
         {
            ObjectUtils.disposeObject(this._oneKeyFeedMC);
         }
         this._oneKeyFeedMC = null;
         if(Boolean(this._reworknameView))
         {
            this.shutdownReworkName();
         }
         if(Boolean(this._consortaiReworkName))
         {
            this.shutdownConsortiaReworkName();
         }
         if(Boolean(this._battleTeamReworkName))
         {
            this.shutdownBattleTeamReworkName();
         }
         if(CellMenu.instance.showed)
         {
            CellMenu.instance.hide();
         }
         AddPricePanel.Instance.close();
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
      
      public function setBagCountShow(_arg_1:int) : void
      {
         var _local_3:int = 0;
         var _local_2:uint = 0;
         var _local_4:* = null;
         switch(_arg_1)
         {
            case 0:
               _local_3 = PlayerManager.Instance.Self.getBag(_arg_1).itemBgNumber(this._equiplist._startIndex,this._equiplist._stopIndex);
               if(_local_3 >= 49)
               {
                  _local_2 = 16711680;
                  _local_4 = new GlowFilter(16777215,0.5,3,3,10);
               }
               else
               {
                  _local_2 = 1310468;
                  _local_4 = new GlowFilter(876032,0.5,3,3,10);
               }
               break;
            case 1:
               _local_3 = PlayerManager.Instance.Self.getBag(_arg_1).itemBgNumber(0,48);
               if(_local_3 >= 48 + 1)
               {
                  _local_2 = 16711680;
                  _local_4 = new GlowFilter(16777215,0.5,3,3,10);
               }
               else
               {
                  _local_2 = 1310468;
                  _local_4 = new GlowFilter(876032,0.5,3,3,10);
               }
         }
         this._goodsNumInfoText.textColor = _local_2;
         this._goodsNumInfoText.text = _local_3.toString();
         this.setBagType(_arg_1);
      }
      
      public function get info() : SelfInfo
      {
         return this._info;
      }
      
      public function set info(_arg_1:SelfInfo) : void
      {
         if(Boolean(this._info))
         {
            this._info.removeEventListener("propertychange",this.__propertyChange);
            this._info.getBag(0).removeEventListener("update",this.__onBagUpdateEQUIPBAG);
            this._info.getBag(1).removeEventListener("update",this.__onBagUpdatePROPBAG);
            this._info.BeadBag.items.removeEventListener("add",this.__onBeadBagChanged);
            PlayerManager.Instance.Self.removeEventListener("showBead",this.__showBead);
         }
         this._info = _arg_1;
         if(Boolean(this._info))
         {
            this._info.addEventListener("propertychange",this.__propertyChange);
            this._info.getBag(0).addEventListener("update",this.__onBagUpdateEQUIPBAG);
            this._info.getBag(1).addEventListener("update",this.__onBagUpdatePROPBAG);
            this._info.BeadBag.items.addEventListener("add",this.__onBeadBagChanged);
            PlayerManager.Instance.Self.addEventListener("showBead",this.__showBead);
         }
         this.updateView();
      }
      
      private function startReworkName(_arg_1:int, _arg_2:int) : void
      {
         this._reworknameView = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.ReworkName.ReworkNameFrame");
         LayerManager.Instance.addToLayer(this._reworknameView,3,true,1);
         this._reworknameView.initialize(_arg_1,_arg_2);
         this._reworknameView.addEventListener("complete",this.__onRenameComplete);
      }
      
      private function shutdownReworkName() : void
      {
         this._reworknameView.removeEventListener("complete",this.__onRenameComplete);
         ObjectUtils.disposeObject(this._reworknameView);
         this._reworknameView = null;
      }
      
      private function __onRenameComplete(_arg_1:Event) : void
      {
         this.shutdownReworkName();
      }
      
      private function startupConsortiaReworkName(_arg_1:int, _arg_2:int) : void
      {
         this._consortaiReworkName = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.ReworkName.ReworkNameConsortia");
         LayerManager.Instance.addToLayer(this._consortaiReworkName,3,true,1);
         this._consortaiReworkName.initialize(_arg_1,_arg_2);
         this._consortaiReworkName.addEventListener("complete",this.__onConsortiaRenameComplete);
      }
      
      private function startupBattleTeamReworkName(_arg_1:int, _arg_2:int) : void
      {
         this._battleTeamReworkName = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.ReworkName.ReworkNameBattleTeam");
         LayerManager.Instance.addToLayer(this._battleTeamReworkName,3,true,1);
         this._battleTeamReworkName.initialize(_arg_1,_arg_2);
      }
      
      private function shutdownConsortiaReworkName() : void
      {
         this._consortaiReworkName.removeEventListener("complete",this.__onConsortiaRenameComplete);
         ObjectUtils.disposeObject(this._consortaiReworkName);
         this._consortaiReworkName = null;
      }
      
      private function showChatBugleInputFrame(_arg_1:int) : void
      {
         if(this._chatBugleInputFrame == null)
         {
            this._chatBugleInputFrame = ComponentFactory.Instance.creat("chat.BugleInputFrame");
         }
         this._chatBugleInputFrame.templateID = _arg_1;
         LayerManager.Instance.addToLayer(this._chatBugleInputFrame,3,true,2);
      }
      
      private function shutdownBattleTeamReworkName() : void
      {
         this._battleTeamReworkName.removeEventListener("complete",this.__onBattleTeamRenameComplete);
         ObjectUtils.disposeObject(this._battleTeamReworkName);
         this._battleTeamReworkName = null;
      }
      
      private function __onBattleTeamRenameComplete(_arg_1:Event) : void
      {
         this.shutdownBattleTeamReworkName();
      }
      
      private function __onConsortiaRenameComplete(_arg_1:Event) : void
      {
         this.shutdownConsortiaReworkName();
      }
      
      public function hide() : void
      {
         if(Boolean(this._reworknameView))
         {
            this.shutdownReworkName();
         }
         if(Boolean(this._consortaiReworkName))
         {
            this.shutdownConsortiaReworkName();
         }
         if(Boolean(this._battleTeamReworkName))
         {
            this.shutdownBattleTeamReworkName();
         }
      }
      
      private function judgeAndPlayCardMovie() : void
      {
      }
      
      private function __showOver(_arg_1:Event) : void
      {
         this.getNewCardMovie.removeEventListener("complete",this.__showOver);
         this._soundControl.volume = 0;
         this.getNewCardMovie.soundTransform = this._soundControl;
         this._soundControl = null;
         ObjectUtils.disposeObject(this.getNewCardMovie);
         this.getNewCardMovie = null;
      }
      
      protected function _isSkillCanUse() : Boolean
      {
         var _local_1:Boolean = false;
         if(PlayerManager.Instance.Self.IsWeakGuildFinish(5) && PlayerManager.Instance.Self.IsWeakGuildFinish(2) && PlayerManager.Instance.Self.IsWeakGuildFinish(12) && PlayerManager.Instance.Self.IsWeakGuildFinish(51) && PlayerManager.Instance.Self.IsWeakGuildFinish(55))
         {
            _local_1 = true;
         }
         return _local_1;
      }
      
      private function startupChangeSex(_arg_1:int, _arg_2:int) : void
      {
         var _local_3:ChangeSexAlertFrame = ComponentFactory.Instance.creat("bagAndInfo.bag.changeSexAlert");
         _local_3.bagType = _arg_1;
         _local_3.place = _arg_2;
         _local_3.info = this.getAlertInfo("tank.view.bagII.changeSexAlert",true);
         _local_3.addEventListener("propertiesChanged",this.__onAlertSizeChanged);
         _local_3.addEventListener("response",this.__onAlertResponse);
         LayerManager.Instance.addToLayer(_local_3,3,_local_3.info.frameCenter,1);
         StageReferance.stage.focus = _local_3;
      }
      
      private function getAlertInfo(_arg_1:String, _arg_2:Boolean = false) : AlertInfo
      {
         var _local_3:AlertInfo = new AlertInfo();
         _local_3.autoDispose = true;
         _local_3.showSubmit = true;
         _local_3.showCancel = _arg_2;
         _local_3.enterEnable = true;
         _local_3.escEnable = true;
         _local_3.moveEnable = false;
         _local_3.title = LanguageMgr.GetTranslation("AlertDialog.Info");
         _local_3.data = LanguageMgr.GetTranslation(_arg_1);
         return _local_3;
      }
      
      private function __onAlertSizeChanged(_arg_1:ComponentEvent) : void
      {
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         if(_local_2.info.frameCenter)
         {
            _local_2.x = (StageReferance.stageWidth - _local_2.width) / 2;
            _local_2.y = (StageReferance.stageHeight - _local_2.height) / 2;
         }
      }
      
      private function __onAlertResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:ChangeSexAlertFrame = ChangeSexAlertFrame(_arg_1.currentTarget);
         _local_2.removeEventListener("propertiesChanged",this.__onAlertSizeChanged);
         _local_2.removeEventListener("response",this.__onAlertResponse);
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               SocketManager.Instance.out.sendChangeSex(_local_2.bagType,_local_2.place);
         }
         _local_2.dispose();
         _local_2 = null;
      }
      
      private function __changeSexHandler(_arg_1:CrazyTankSocketEvent) : void
      {
         var _local_2:* = null;
         SocketManager.Instance.socket.close();
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         if(_local_3)
         {
            _local_2 = ComponentFactory.Instance.creat("sellGoodsAlert");
            _local_2.info = this.getAlertInfo("tank.view.bagII.changeSexAlert.success",false);
            _local_2.addEventListener("propertiesChanged",this.__onAlertSizeChanged);
            _local_2.addEventListener("response",this.__onSuccessAlertResponse);
            LayerManager.Instance.addToLayer(_local_2,3,_local_2.info.frameCenter,1);
            StageReferance.stage.focus = _local_2;
         }
         else
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.changeSexAlert.failed"));
         }
      }
      
      private function __onSuccessAlertResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         ExternalInterface.call("WindowReturn");
      }
      
      public function set isScreenFood(_arg_1:Boolean) : void
      {
         this._isScreenFood = _arg_1;
      }
      
      public function get beadFeedBtn() : BeadFeedButton
      {
         return this._beadFeedBtn;
      }
      
      public function deleteButtonForPet() : void
      {
         if(Boolean(this.bagLockBtn))
         {
            this.bagLockBtn.dispose();
            this.bagLockBtn = null;
         }
         if(Boolean(this._dressSelectedBtn))
         {
            this._dressSelectedBtn.dispose();
            this._dressSelectedBtn = null;
         }
      }
      
      private function __onDecGourdExBottleHandler(_arg_1:FrameEvent) : void
      {
         var _local_3:* = null;
         SoundManager.instance.playButtonSound();
         var _local_2:BaseAlerFrame = _arg_1.target as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__texpResponse);
         _local_2.dispose();
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            _local_3 = this._currentUseItemTemp;
            SocketManager.Instance.out.sendUseCard(_local_3.itemInfo.BagType,_local_3.itemInfo.Place,[_local_3.info.TemplateID],_local_3.info.PayType);
         }
         this._currentUseItemTemp = null;
      }
   }
}

