package com.hurlant.crypto.prng
{
   import com.hurlant.crypto.symmetric.IStreamCipher;
   import com.hurlant.util.Memory;
   import flash.utils.ByteArray;
   
   public class ARC4 implements IPRNG, IStreamCipher
   {
      
      private const psize:uint = 256;
      
      private var S:ByteArray;
      
      private var i:int = 0;
      
      private var j:int = 0;
      
      public function ARC4(_arg_1:ByteArray = null)
      {
         super();
         this.S = new ByteArray();
         if(Boolean(_arg_1))
         {
            this.init(_arg_1);
         }
      }
      
      public function decrypt(_arg_1:ByteArray) : void
      {
         this.encrypt(_arg_1);
      }
      
      public function init(_arg_1:ByteArray) : void
      {
         var _local_2:int = 0;
         var _local_3:int = 0;
         var _local_4:int = 0;
         _local_2 = 0;
         while(_local_2 < 256)
         {
            this.S[_local_2] = _local_2;
            _local_2++;
         }
         _local_3 = 0;
         _local_2 = 0;
         while(_local_2 < 256)
         {
            _local_3 = _local_3 + this.S[_local_2] + _arg_1[_local_2 % _arg_1.length] & 0xFF;
            _local_4 = int(this.S[_local_2]);
            this.S[_local_2] = this.S[_local_3];
            this.S[_local_3] = _local_4;
            _local_2++;
         }
         this.i = 0;
         this.j = 0;
      }
      
      public function dispose() : void
      {
         var _local_1:uint = 0;
         _local_1 = 0;
         if(this.S != null)
         {
            _local_1 = 0;
            while(_local_1 < this.S.length)
            {
               this.S[_local_1] = Math.random() * 256;
               _local_1++;
            }
            this.S.length = 0;
            this.S = null;
         }
         this.i = 0;
         this.j = 0;
         Memory.gc();
      }
      
      public function encrypt(_arg_1:ByteArray) : void
      {
         var _local_2:uint = 0;
         var _local_3:* = undefined;
         _local_2 = 0;
         while(_local_2 < _arg_1.length)
         {
            _local_3 = _local_2++;
            _arg_1[_local_3] ^= this.next();
         }
      }
      
      public function next() : uint
      {
         var _local_1:int = 0;
         this.i = this.i + 1 & 0xFF;
         this.j = this.j + this.S[this.i] & 0xFF;
         _local_1 = int(this.S[this.i]);
         this.S[this.i] = this.S[this.j];
         this.S[this.j] = _local_1;
         return this.S[_local_1 + this.S[this.i] & 0xFF];
      }
      
      public function getBlockSize() : uint
      {
         return 1;
      }
      
      public function getPoolSize() : uint
      {
         return this.psize;
      }
      
      public function toString() : String
      {
         return "rc4";
      }
   }
}

