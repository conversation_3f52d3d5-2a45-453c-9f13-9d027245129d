package bagAndInfo.ddtKingGrade
{
   import com.pickgliss.ui.ComponentFactory;
   import ddt.CoreManager;
   import ddt.loader.LoaderCreate;
   import ddt.utils.HelperDataModuleLoad;
   import ddt.utils.HelperUIModuleLoad;
   import road7th.data.DictionaryData;
   
   public class DDTKingGradeManager extends CoreManager
   {
      
      private static var _instance:DDTKingGradeManager;
      
      private var _data:DictionaryData;
      
      public var isOpen:Boolean;
      
      public function DDTKingGradeManager()
      {
         super();
      }
      
      public static function get Instance() : DDTKingGradeManager
      {
         if(_instance == null)
         {
            _instance = new DDTKingGradeManager();
         }
         return _instance;
      }
      
      override protected function start() : void
      {
         new HelperDataModuleLoad().loadDataModule([LoaderCreate.Instance.createDDTKingGradeTemplate()],function():void
         {
            new HelperUIModuleLoad().loadUIModule(["ddtkinggrade"],onComplete);
         });
      }
      
      private function onComplete() : void
      {
         var _local_1:* = undefined;
         if(this.isOpen == false)
         {
            this.isOpen = true;
            _local_1 = ComponentFactory.Instance.creatComponentByStylename("ddtKingGrade.mainView");
            _local_1.show();
         }
      }
      
      public function analyzer(_arg_1:DDTKingGradeAnalyzer) : void
      {
         this._data = _arg_1.data;
      }
      
      public function get data() : DictionaryData
      {
         return this._data;
      }
      
      public function getInfoByCost(_arg_1:int) : DDTKingGradeInfo
      {
         var _local_3:DDTKingGradeInfo = null;
         var _local_2:* = null;
         for each(_local_3 in this._data)
         {
            if(_arg_1 >= _local_3.Cost)
            {
               _local_2 = _local_3;
            }
         }
         return _local_2;
      }
   }
}

