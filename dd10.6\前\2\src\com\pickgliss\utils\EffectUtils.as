package com.pickgliss.utils
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.filters.GradientGlowFilter;
   import flash.geom.ColorTransform;
   
   public class EffectUtils
   {
      
      public function EffectUtils()
      {
         super();
      }
      
      public static function imageGlower(_arg_1:DisplayObject, _arg_2:Number, _arg_3:Number, _arg_4:Number, _arg_5:String) : void
      {
         var _local_18:<PERSON><PERSON><PERSON> = false;
         var _local_10:* = null;
         var _local_17:* = 0;
         var _local_7:* = 45;
         if(_arg_5 == "yellow")
         {
            _local_10 = [16754176,16754176,16754176,16754176];
         }
         if(_arg_5 == "gold")
         {
            _local_10 = [6684672,16737792,16755200,16777164];
         }
         if(_arg_5 == "blue")
         {
            _local_10 = [13209,13209,39423,10079487];
         }
         if(_arg_5 == "green")
         {
            _local_10 = [26112,3381504,10092288,16777164];
         }
         if(_arg_5 == "ocean")
         {
            _local_10 = [13107,3368550,10079436,13434879];
         }
         if(_arg_5 == "aqua")
         {
            _local_10 = [13107,26214,52428,65535];
         }
         if(_arg_5 == "ice")
         {
            _local_10 = [13158,3368601,6724044,10079487];
         }
         if(_arg_5 == "spark")
         {
            _local_10 = [102,26265,3394815,13434879];
         }
         if(_arg_5 == "silver")
         {
            _local_10 = [3355443,6710886,12303291,16777215];
         }
         if(_arg_5 == "neon")
         {
            _local_10 = [3355596,6697932,10066431,13421823];
         }
         var _local_8:Array = [0,1,1,1];
         var _local_13:Array = [0,63,126,255];
         var _local_11:* = _arg_3;
         var _local_14:* = _arg_3;
         var _local_12:* = _arg_2;
         var _local_15:* = _arg_4;
         var _local_6:String = "outer";
         var _local_16:GradientGlowFilter = new GradientGlowFilter(_local_17,_local_7,_local_10,_local_8,_local_13,_local_11,_local_14,_local_12,_local_15,_local_6,_local_18);
         var _local_9:Array = [];
         _local_9.push(_local_16);
         _arg_1.filters = _local_9;
      }
      
      public static function imageShiner(_arg_1:DisplayObject, _arg_2:Number) : void
      {
         var _local_3:ColorTransform = new ColorTransform();
         var _local_4:* = _arg_2;
         _local_3.redOffset = _local_4;
         _local_3.redMultiplier = _local_4 / 100 + 1;
         _local_3.greenOffset = _local_4;
         _local_3.greenMultiplier = _local_4 / 100 + 1;
         _local_3.blueOffset = _local_4;
         _local_3.blueMultiplier = _local_4 / 100 + 1;
         _arg_1.transform.colorTransform = _local_3;
      }
      
      public static function creatMcToBitmap(_arg_1:DisplayObject, _arg_2:uint) : Bitmap
      {
         var _local_3:BitmapData = new BitmapData(_arg_1.width,_arg_1.height,true,_arg_2);
         _local_3.draw(_arg_1);
         return new Bitmap(_local_3);
      }
      
      public static function toRadian(_arg_1:Number) : Number
      {
         return 3.14159265358979 / 180 * _arg_1;
      }
   }
}

