package bagAndInfo.info
{
   import armShell.ArmShellManager;
   import bagAndInfo.BagAndInfoManager;
   import bagAndInfo.PlayerProValueAddManager;
   import bagAndInfo.amulet.EquipAmuletManager;
   import bagAndInfo.bag.ring.RingSystemView;
   import bagAndInfo.bag.trailelite.TrailEliteView;
   import bagAndInfo.cell.CellFactory;
   import bagAndInfo.cell.PersonalInfoCell;
   import bagAndInfo.energyData.EnergyData;
   import baglocked.BaglockedManager;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.ShowTipManager;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.SelectedButtonGroup;
   import com.pickgliss.ui.controls.SelectedCheckButton;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.controls.container.VBox;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.image.Image;
   import com.pickgliss.ui.image.MovieImage;
   import com.pickgliss.ui.image.MutipleImage;
   import com.pickgliss.ui.image.Scale9CornerImage;
   import com.pickgliss.ui.image.ScaleFrameImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.ui.text.GradientText;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.DisplayUtils;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.bagStore.BagStore;
   import ddt.data.EquipType;
   import ddt.data.Experience;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.data.player.PlayerInfo;
   import ddt.data.player.SelfInfo;
   import ddt.events.BagEvent;
   import ddt.events.CellEvent;
   import ddt.events.PlayerPropertyEvent;
   import ddt.events.ShortcutBuyEvent;
   import ddt.manager.EffortManager;
   import ddt.manager.FineSuitManager;
   import ddt.manager.IMManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PathManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.StateManager;
   import ddt.utils.AssetModuleLoader;
   import ddt.utils.PositionUtils;
   import ddt.utils.StaticFormula;
   import ddt.view.character.CharactoryFactory;
   import ddt.view.character.RoomCharacter;
   import ddt.view.common.LevelIcon;
   import ddt.view.common.MarriedIcon;
   import ddt.view.common.VipLevelIcon;
   import ddthonor.DDTHonorManager;
   import explorerManual.ExplorerManualManager;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.utils.getDefinitionByName;
   import forcesbattle.ForcesRelicManager;
   import gemstone.GemstoneManager;
   import gemstone.info.GemstListInfo;
   import guardCore.GuardCoreIcon;
   import guardCore.GuardCoreManager;
   import hall.event.NewHallEvent;
   import magicStone.data.MagicStoneInfo;
   import powerUp.PowerUpMovieManager;
   import quest.TaskManager;
   import road7th.data.DictionaryData;
   import road7th.data.DictionaryEvent;
   import room.RoomManager;
   import shop.manager.ShopBuyManager;
   import socialContact.SocialContactManage;
   import soulMark.SoulMarkManager;
   import team.TeamManager;
   import texpSystem.view.TexpInfoTipArea;
   import trainer.view.NewHandContainer;
   import uigeneral.honor.HonorIcon;
   import vip.VipController;
   
   public class PlayerInfoView extends Sprite implements Disposeable
   {
      
      private var _info:PlayerInfo;
      
      private var _showSelfOperation:Boolean;
      
      private var _cellPos:Array;
      
      private var _energyData:EnergyData;
      
      private var _honorNameTxt:FilterFrameText;
      
      private var _playerInfoEffortHonorView:PlayerInfoEffortHonorView;
      
      private var _nickNameTxt:FilterFrameText;
      
      private var _consortiaTxt:FilterFrameText;
      
      private var _dutyField:FilterFrameText;
      
      private var _storeBtn:SimpleBitmapButton;
      
      private var _reputeField:FilterFrameText;
      
      private var _gesteField:FilterFrameText;
      
      private var _iconContainer:VBox;
      
      private var _levelIcon:LevelIcon;
      
      private var _marriedIcon:MarriedIcon;
      
      private var _bagDefinitionGroup:SelectedButtonGroup;
      
      private var _bagDefinitionBtnI:SelectedCheckButton;
      
      private var _bagDefinitionBtnII:SelectedCheckButton;
      
      private var _battle:FilterFrameText;
      
      private var _hiddenControlsBg:Bitmap;
      
      private var _hideHatBtn:SelectedCheckButton;
      
      private var _hideGlassBtn:SelectedCheckButton;
      
      private var _hideSuitBtn:SelectedCheckButton;
      
      private var _hideWingBtn:SelectedCheckButton;
      
      private var _achvEnable:Boolean = true;
      
      private var _addFriendBtn:TextButton;
      
      private var _buyAvatar:TextButton;
      
      private var _attackTxt:FilterFrameText;
      
      private var _agilityTxt:FilterFrameText;
      
      private var _defenceTxt:FilterFrameText;
      
      private var _luckTxt:FilterFrameText;
      
      private var _magicAttackTxt:FilterFrameText;
      
      private var _magicDefenceTxt:FilterFrameText;
      
      private var _attackTxt1:FilterFrameText;
      
      private var _agilityTxt1:FilterFrameText;
      
      private var _defenceTxt1:FilterFrameText;
      
      private var _luckTxt1:FilterFrameText;
      
      private var _attackButton:GlowPropButton;
      
      private var _agilityButton:GlowPropButton;
      
      private var _defenceButton:GlowPropButton;
      
      private var _luckButton:GlowPropButton;
      
      private var _magicAttackButton:GlowPropButton;
      
      private var _magicDefenceButton:GlowPropButton;
      
      private var _damageTxt:FilterFrameText;
      
      private var _damageButton:PropButton;
      
      private var _armorTxt:FilterFrameText;
      
      private var _armorButton:PropButton;
      
      private var _HPText:FilterFrameText;
      
      private var _hpButton:PropButton;
      
      private var _vitality:FilterFrameText;
      
      private var _vitalityBuntton:PropButton;
      
      private var _textLevelPrpgress:FilterFrameText;
      
      private var _progressLevel:LevelProgress;
      
      private var _cellContent:Sprite;
      
      private var _character:RoomCharacter;
      
      private var _cells:Vector.<PersonalInfoCell>;
      
      private var _dragDropArea:PersonalInfoDragInArea;
      
      private var _offerLabel:Bitmap;
      
      private var _offerSourcePosition:Point;
      
      private var _vipName:GradientText;
      
      private var _showEquip:Sprite;
      
      private var _showCard:Sprite;
      
      private var _cardEquipView:Sprite;
      
      private var _bg:MutipleImage;
      
      private var _bg1:MovieImage;
      
      private var _bg11:MovieImage;
      
      private var _textBg:Scale9CornerImage;
      
      private var _textBg1:Scale9CornerImage;
      
      private var _textBg2:Scale9CornerImage;
      
      private var _textBg3:Scale9CornerImage;
      
      private var _textBg4:Scale9CornerImage;
      
      private var _textBg5:Scale9CornerImage;
      
      private var _textBg6:Scale9CornerImage;
      
      private var _bg2:MovieImage;
      
      private var _gongxunbg:MovieImage;
      
      private var _characterSprite:TexpInfoTipArea;
      
      private var _isVisible:Boolean = true;
      
      private var _openNecklacePtetrochemicalView:SimpleBitmapButton;
      
      private var _vipIcon:VipLevelIcon;
      
      private var _fineSuitIcon:ScaleFrameImage;
      
      private var _attestBtn:ScaleFrameImage;
      
      private var _guardCore:GuardCoreIcon;
      
      private var _explorerIcon:ScaleFrameImage;
      
      private var _ringSystemBtn:SimpleBitmapButton;
      
      private var _ringSystemView:RingSystemView;
      
      private var _trailEliteView:TrailEliteView;
      
      protected var _armShellBitmapBtn:BaseButton;
      
      private var _amuletBtn:SimpleBitmapButton;
      
      private var _teamIcon:ScaleFrameImage;
      
      private var _honorIcon:HonorIcon;
      
      private var _switchShowII:Boolean = true;
      
      private var _isTextTips:Boolean;
      
      private var _ddtEmblemBtn:Image;
      
      private var _ddtHonorBtn:SimpleBitmapButton;
      
      private var _renjuBtn:SimpleBitmapButton;
      
      private var _backBtn:SimpleBitmapButton;
      
      private var _SywQhBtn:SimpleBitmapButton;
      
      private var _forcesRelicBtn:SimpleBitmapButton;
      
      public function PlayerInfoView()
      {
         super();
         this.initView();
         this.initProperties();
         this.initPos();
         this.creatCells();
         this.initEvents();
         this.cardGuide1();
         this.checkGuardCoreGuide();
         var a:int = 21;
         while(a <= 25)
         {
            (this._cells[a] as PersonalInfoCell).visible = false;
            a++;
         }
      }
      
      private function cardGuide1() : void
      {
         var _local_1:* = null;
         if(!PlayerManager.Instance.Self.isNewOnceFinish(125))
         {
            if(PlayerManager.Instance.Self.Grade == 14 && TaskManager.instance.isAchieved(TaskManager.instance.getQuestByID(25)))
            {
               _local_1 = PlayerManager.Instance.Self.cardBagDic;
               if(_local_1.length > 0)
               {
                  this.cardGuide2(null);
               }
               else
               {
                  NewHandContainer.Instance.showArrow(141,0,new Point(-150,0),"asset.trainer.txtCardGuide1","guide.card.txtPos1",this);
                  PlayerManager.Instance.Self.cardBagDic.addEventListener("add",this.cardGuide2);
               }
            }
         }
      }
      
      private function cardGuide2(_arg_1:DictionaryEvent) : void
      {
         PlayerManager.Instance.Self.cardBagDic.removeEventListener("add",this.cardGuide2);
         NewHandContainer.Instance.showArrow(141,180,new Point(294,157),"","",LayerManager.Instance.getLayerByType(2),0,true);
      }
      
      public function checkGuardCoreGuide() : void
      {
         if(this._showSelfOperation && this._info && this._info.ID == PlayerManager.Instance.Self.ID && PlayerManager.Instance.Self.Grade >= 50 && !PlayerManager.Instance.Self.isNewOnceFinish(140))
         {
            NewHandContainer.Instance.showArrow(150,180,new Point(308,249),"","",this,0,true);
         }
      }
      
      public function checkGuide() : void
      {
         var _local_1:int = 0;
         if(this._showSelfOperation && this._info && this._info.isSelf)
         {
            _local_1 = PlayerManager.Instance.Self.Grade;
            if(_local_1 >= 50 && !PlayerManager.Instance.Self.isNewOnceFinish(140))
            {
               NewHandContainer.Instance.showArrow(150,180,new Point(308,249),"","",this,0,true);
            }
            else if(_local_1 >= 70 && !PlayerManager.Instance.Self.isNewOnceFinish(160))
            {
               NewHandContainer.Instance.showArrow(151,180,new Point(310,169),"","",this,0,true);
            }
            else if(_local_1 >= 32 && !PlayerManager.Instance.Self.isNewOnceFinish(161))
            {
               NewHandContainer.Instance.showArrow(152,180,new Point(156,169),"","",this,0,true);
            }
         }
      }
      
      private function initView() : void
      {
         this._bg = ComponentFactory.Instance.creatComponentByStylename("bagBGAsset2");
         addChild(this._bg);
         this._bg1 = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.view.bg");
         addChild(this._bg1);
         this._bg11 = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.view.bgs");
         addChild(this._bg11);
         this._bg11.visible = false;
         this._bg2 = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.view.ddtbg");
         this._bg2.visible = this._showSelfOperation;
         addChild(this._bg2);
         this._dragDropArea = new PersonalInfoDragInArea();
         addChild(this._dragDropArea);
         this._textBg = ComponentFactory.Instance.creatComponentByStylename("ddtbagAndInfoTextView");
         addChild(this._textBg);
         this._textBg1 = ComponentFactory.Instance.creatComponentByStylename("ddtbagAndInfoTextViewI");
         addChild(this._textBg1);
         this._textBg2 = ComponentFactory.Instance.creatComponentByStylename("ddtbagAndInfoTextViewII");
         addChild(this._textBg2);
         this._textBg3 = ComponentFactory.Instance.creatComponentByStylename("ddtbagAndInfoTextViewIII");
         addChild(this._textBg3);
         this._textBg4 = ComponentFactory.Instance.creatComponentByStylename("ddtbagAndInfoTextViewIV");
         addChild(this._textBg4);
         this._textBg5 = ComponentFactory.Instance.creatComponentByStylename("ddtbagAndInfoTextViewV");
         addChild(this._textBg5);
         this._textBg6 = ComponentFactory.Instance.creatComponentByStylename("ddtbagAndInfoTextViewVI");
         addChild(this._textBg6);
         this._gongxunbg = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.view.gongxunBg");
         addChild(this._gongxunbg);
         this._honorNameTxt = ComponentFactory.Instance.creatComponentByStylename("personInfoViewNameText");
         if(PathManager.solveAchieveEnable())
         {
            addChild(this._honorNameTxt);
         }
         this._honorNameTxt.setTextFormat(this._honorNameTxt.getTextFormat());
         this._nickNameTxt = ComponentFactory.Instance.creatComponentByStylename("personInfoViewNickNameText");
         this._attestBtn = ComponentFactory.Instance.creatComponentByStylename("hall.playerInfo.attest");
         addChild(this._attestBtn);
         this._attestBtn.visible = false;
         this._consortiaTxt = ComponentFactory.Instance.creatComponentByStylename("personInfoViewConsortiaText");
         addChild(this._consortiaTxt);
         this._bagDefinitionBtnI = ComponentFactory.Instance.creat("bag.DefinitionBtnI");
         addChild(this._bagDefinitionBtnI);
         this._bagDefinitionBtnII = ComponentFactory.Instance.creat("bag.DefinitionBtnII");
         addChild(this._bagDefinitionBtnII);
         this._bagDefinitionGroup = new SelectedButtonGroup();
         this._bagDefinitionGroup.addSelectItem(this._bagDefinitionBtnI);
         this._bagDefinitionGroup.addSelectItem(this._bagDefinitionBtnII);
         this._bagDefinitionBtnI.visible = false;
         this._bagDefinitionBtnII.visible = false;
         this._attackTxt = ComponentFactory.Instance.creatComponentByStylename("personInfoViewAttackText");
         addChild(this._attackTxt);
         this._attackButton = ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.AttackButton");
         this._attackButton.property = LanguageMgr.GetTranslation("tank.view.personalinfoII.attact");
         this._attackButton.detail = LanguageMgr.GetTranslation("tank.view.personalinfoII.attactDetail");
         this._attackButton.propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.propertySourceTxtNew",0,0,0,0,0,0,0,0);
         ShowTipManager.Instance.addTip(this._attackButton);
         addChild(this._attackButton);
         this._agilityTxt = ComponentFactory.Instance.creatComponentByStylename("personInfoViewAgilityText");
         addChild(this._agilityTxt);
         this._agilityButton = ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.AgilityButton");
         this._agilityButton.property = LanguageMgr.GetTranslation("tank.view.personalinfoII.agility");
         this._agilityButton.detail = LanguageMgr.GetTranslation("tank.view.personalinfoII.agilityDetail");
         this._agilityButton.propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.propertySourceTxtNew",0,0,0,0,0,0,0,0,0);
         ShowTipManager.Instance.addTip(this._agilityButton);
         addChild(this._agilityButton);
         this._defenceTxt = ComponentFactory.Instance.creatComponentByStylename("personInfoViewDefenceText");
         addChild(this._defenceTxt);
         this._defenceButton = ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.DefenceButton");
         this._defenceButton.property = LanguageMgr.GetTranslation("tank.view.personalinfoII.defense");
         this._defenceButton.detail = LanguageMgr.GetTranslation("tank.view.personalinfoII.defenseDetail");
         this._defenceButton.propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.propertySourceTxtNew",0,0,0,0,0,0,0,0);
         ShowTipManager.Instance.addTip(this._defenceButton);
         addChild(this._defenceButton);
         this._luckTxt = ComponentFactory.Instance.creatComponentByStylename("personInfoViewLuckText");
         addChild(this._luckTxt);
         this._luckButton = ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.LuckButton");
         this._luckButton.property = LanguageMgr.GetTranslation("tank.view.personalinfoII.luck");
         this._luckButton.detail = LanguageMgr.GetTranslation("tank.view.personalinfoII.luckDetail");
         this._luckButton.propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.propertySourceTxtNew",0,0,0,0,0,0,0,0);
         ShowTipManager.Instance.addTip(this._luckButton);
         addChild(this._luckButton);
         this._magicAttackTxt = ComponentFactory.Instance.creatComponentByStylename("personInfoViewMagicAttackText");
         addChild(this._magicAttackTxt);
         this._magicAttackButton = ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.MagicAttackButton");
         this._magicAttackButton.property = LanguageMgr.GetTranslation("tank.view.personalinfoII.magicAttack");
         this._magicAttackButton.detail = LanguageMgr.GetTranslation("tank.view.personalinfoII.magicAttackDetail");
         this._magicAttackButton.propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.magicPropertySourceTxtNew",0);
         ShowTipManager.Instance.addTip(this._magicAttackButton);
         addChild(this._magicAttackButton);
         this._magicDefenceTxt = ComponentFactory.Instance.creatComponentByStylename("personInfoViewMagicDefenceText");
         addChild(this._magicDefenceTxt);
         this._magicDefenceButton = ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.MagicDefenceButton");
         this._magicDefenceButton.property = LanguageMgr.GetTranslation("tank.view.personalinfoII.magicDefence");
         this._magicDefenceButton.detail = LanguageMgr.GetTranslation("tank.view.personalinfoII.magicDefenceDetail");
         this._magicDefenceButton.propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.magicPropertySourceTxtNew",0);
         ShowTipManager.Instance.addTip(this._magicDefenceButton);
         addChild(this._magicDefenceButton);
         this._damageTxt = ComponentFactory.Instance.creatComponentByStylename("personInfoViewDamageText");
         addChild(this._damageTxt);
         this._damageButton = ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.DamageButton");
         this._damageButton.property = LanguageMgr.GetTranslation("tank.view.personalinfoII.damage");
         this._damageButton.detail = LanguageMgr.GetTranslation("tank.view.personalinfoII.damageDetail");
         (this._damageButton as GlowPropButton).propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.damagePropertySourceTxtNew",0,0,0);
         ShowTipManager.Instance.addTip(this._damageButton);
         addChild(this._damageButton);
         this._armorTxt = ComponentFactory.Instance.creatComponentByStylename("personInfoViewArmorText");
         addChild(this._armorTxt);
         this._armorButton = ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.ArmorButton");
         this._armorButton.property = LanguageMgr.GetTranslation("tank.view.personalinfoII.recovery");
         this._armorButton.detail = LanguageMgr.GetTranslation("tank.view.personalinfoII.recoveryDetail");
         (this._armorButton as GlowPropButton).propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.recoveryPropertySourceTxtNew",0,0,0);
         ShowTipManager.Instance.addTip(this._armorButton);
         addChild(this._armorButton);
         this._HPText = ComponentFactory.Instance.creatComponentByStylename("personInfoViewHPText");
         addChild(this._HPText);
         this._hpButton = ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.HPButton");
         this._hpButton.property = LanguageMgr.GetTranslation("tank.view.personalinfoII.hp");
         this._hpButton.detail = LanguageMgr.GetTranslation("tank.view.personalinfoII.hpDetail");
         (this._hpButton as GlowPropButton).propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.hpPropertySourceTxtNew",0,0,0,0,0,0,0,0,0,0);
         ShowTipManager.Instance.addTip(this._hpButton);
         addChild(this._hpButton);
         this._vitality = ComponentFactory.Instance.creatComponentByStylename("personInfoViewVitalityText");
         addChild(this._vitality);
         this._vitalityBuntton = ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.VitalityButton");
         this._vitalityBuntton.property = LanguageMgr.GetTranslation("tank.view.personalinfoII.energy");
         this._vitalityBuntton.detail = LanguageMgr.GetTranslation("tank.view.personalinfoII.energyDetail");
         ShowTipManager.Instance.addTip(this._vitalityBuntton);
         addChild(this._vitalityBuntton);
         this._storeBtn = ComponentFactory.Instance.creatComponentByStylename("personInfoViewStoreButton");
         this._storeBtn.tipData = LanguageMgr.GetTranslation("tank.view.shortcutforge.tip");
         addChild(this._storeBtn);
         this._storeBtn.visible = true;
         this._addFriendBtn = ComponentFactory.Instance.creatComponentByStylename("addFriendBtn1");
         PositionUtils.setPos(this._addFriendBtn,"bagAndInfo.FritendBtn.Pos");
         this._addFriendBtn.text = LanguageMgr.GetTranslation("tank.view.im.addFriendBtn");
         addChild(this._addFriendBtn);
         this._reputeField = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.info.ReputeField");
         addChild(this._reputeField);
         this._gesteField = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.info.GesteField");
         addChild(this._gesteField);
         this._offerLabel = ComponentFactory.Instance.creatBitmap("bagAndInfo.info.OfferLabel");
         addChild(this._offerLabel);
         this._offerLabel.visible = false;
         this._offerSourcePosition = new Point(this._offerLabel.x,this._offerLabel.y);
         this._dutyField = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.info.DutyField");
         addChild(this._dutyField);
         this._playerInfoEffortHonorView = new PlayerInfoEffortHonorView();
         if(PathManager.solveAchieveEnable())
         {
            addChild(this._playerInfoEffortHonorView);
         }
         this._showEquip = new Sprite();
         addChild(this._showEquip);
         this._iconContainer = ComponentFactory.Instance.creatComponentByStylename("asset.bagAndInfo.iconContainer");
         this._showEquip.addChild(this._iconContainer);
         this._showCard = new Sprite();
         addChild(this._showCard);
         this._showCard.visible = false;
         this._battle = ComponentFactory.Instance.creatComponentByStylename("personInfoViewBattleText");
         this._showEquip.addChild(this._battle);
         this._progressLevel = ComponentFactory.Instance.creatComponentByStylename("LevelProgress");
         this._showEquip.addChild(this._progressLevel);
         this._progressLevel.tipStyle = "ddt.view.tips.OneLineTip";
         this._progressLevel.tipDirctions = "3,7,6";
         this._progressLevel.tipGapV = 4;
         this._hideGlassBtn = ComponentFactory.Instance.creatComponentByStylename("personanHideHatCheckBox");
         this._showEquip.addChild(this._hideGlassBtn);
         this._hideHatBtn = ComponentFactory.Instance.creatComponentByStylename("personanHideGlassCheckBox");
         this._showEquip.addChild(this._hideHatBtn);
         this._hideSuitBtn = ComponentFactory.Instance.creatComponentByStylename("personanHideSuitCheckBox");
         this._showEquip.addChild(this._hideSuitBtn);
         this._hideWingBtn = ComponentFactory.Instance.creatComponentByStylename("personanHideWingCheckBox");
         this._showEquip.addChild(this._hideWingBtn);
         this._buyAvatar = ComponentFactory.Instance.creatComponentByStylename("addFriendBtn2");
         this._buyAvatar.text = LanguageMgr.GetTranslation("ddt.bagandinfo.buyOtherCloth");
         this._buyAvatar.x = 181;
         this._buyAvatar.y = 74;
         this._showEquip.addChild(this._buyAvatar);
         this._cellContent = new Sprite();
         this._showEquip.addChild(this._cellContent);
         this._attackTxt1 = ComponentFactory.Instance.creatComponentByStylename("personInfoViewAttackText");
         addChild(this._attackTxt1);
         PositionUtils.setPos(this._attackTxt1,"personInfoViewAttackTextPos");
         this._attackTxt1.visible = false;
         this._agilityTxt1 = ComponentFactory.Instance.creatComponentByStylename("personInfoViewAgilityText");
         addChild(this._agilityTxt1);
         PositionUtils.setPos(this._agilityTxt1,"personInfoViewAgilityPos");
         this._agilityTxt1.visible = false;
         this._defenceTxt1 = ComponentFactory.Instance.creatComponentByStylename("personInfoViewDefenceText");
         addChild(this._defenceTxt1);
         PositionUtils.setPos(this._defenceTxt1,"personInfoViewDefencePos");
         this._defenceTxt1.visible = false;
         this._luckTxt1 = ComponentFactory.Instance.creatComponentByStylename("personInfoViewLuckText");
         addChild(this._luckTxt1);
         PositionUtils.setPos(this._luckTxt1,"personInfoViewLuckPos");
         this._luckTxt1.visible = false;
         this._openNecklacePtetrochemicalView = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.NecklacePtetrochemicalView.OpenBtn");
         addChild(this._openNecklacePtetrochemicalView);
         this._ringSystemBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.RingSystemView.OpenBtn");
         addChild(this._ringSystemBtn);
         this._amuletBtn = ComponentFactory.Instance.creatComponentByStylename("equipAmulet.enterFrameBtn");
         addChild(this._amuletBtn);
         this._ddtEmblemBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.ddtemblem.icon");
         addChild(this._ddtEmblemBtn);
         this._forcesRelicBtn = ComponentFactory.Instance.creatComponentByStylename("forces.relic.enterBtn");
         addChild(this._forcesRelicBtn);
         this._fineSuitIcon = ComponentFactory.Instance.creatComponentByStylename("fineSuit.tipsSimple.Icon");
         this._showEquip.addChild(this._fineSuitIcon);
         this._explorerIcon = ComponentFactory.Instance.creatComponentByStylename("explorerManual.playerManualEnter.levIcon");
         this._showEquip.addChild(this._explorerIcon);
         this._explorerIcon.buttonMode = true;
         this._teamIcon = ComponentFactory.Instance.creatComponentByStylename("ddtcorei.playerinfoView.teamIcon");
         this._teamIcon.visible = false;
         this._showEquip.addChild(this._teamIcon);
         this._honorIcon = new HonorIcon();
         this._honorIcon.tipStyle = "uigeneral.honor.HonorTip";
         this._honorIcon.visible = false;
         this._honorIcon.x = 124;
         this._honorIcon.y = 154;
         this._honorIcon.tipDirctions = "2,0,0";
         this._showEquip.addChild(this._honorIcon);
         this._renjuBtn = ComponentFactory.Instance.creatComponentByStylename("personInfoViewRenJuBtnAsset");
         this._renjuBtn.tipData = "圣遗物背包";
         this._showEquip.addChild(this._renjuBtn);
         this._renjuBtn.visible = false;
         this._backBtn = ComponentFactory.Instance.creatComponentByStylename("personInfoViewEquipmentBtnAsset");
         this._storeBtn.visible = true;
         this._backBtn.tipData = "装备背包";
         this._showEquip.addChild(this._backBtn);
         this._backBtn.visible = false;
         this._SywQhBtn = ComponentFactory.Instance.creatComponentByStylename("personInfoViewSywQh");
         this._SywQhBtn.tipData = "圣遗物强化";
         this._showEquip.addChild(this._SywQhBtn);
         this._SywQhBtn.visible = false;
      }
      
      protected function addArmShellBtn() : void
      {
         if(this._showSelfOperation && this._armShellBitmapBtn == null)
         {
            this._armShellBitmapBtn = ComponentFactory.Instance.creatComponentByStylename("core.isArmShellBitmapBtn");
            this._armShellBitmapBtn.addEventListener("click",this.__armShellBitmapBtnClickHandler);
            addChild(this._armShellBitmapBtn);
         }
      }
      
      protected function removeArmShellBtn() : void
      {
         if(Boolean(this._armShellBitmapBtn))
         {
            this._armShellBitmapBtn.removeEventListener("click",this.__armShellBitmapBtnClickHandler);
            ObjectUtils.disposeObject(this._armShellBitmapBtn);
            this._armShellBitmapBtn = null;
         }
      }
      
      protected function __armShellBitmapBtnClickHandler(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.playButtonSound();
         ArmShellManager.instance.showArmShellFrame();
      }
      
      private function removeFromStageHandler(_arg_1:Event) : void
      {
         BagStore.instance.reduceTipPanelNumber();
      }
      
      private function __shortCutBuyHandler(_arg_1:ShortcutBuyEvent) : void
      {
         _arg_1.stopImmediatePropagation();
         dispatchEvent(new ShortcutBuyEvent(_arg_1.ItemID,_arg_1.ItemNum));
      }
      
      public function switchShow(_arg_1:Boolean) : void
      {
         this._isTextTips = _arg_1;
         this._showEquip.visible = !_arg_1;
         this._showCard.visible = _arg_1;
         this._bg.visible = !_arg_1;
         this._bg2.visible = this._showSelfOperation;
         this._nickNameTxt.visible = !_arg_1;
         this._consortiaTxt.visible = !_arg_1;
         this._dutyField.visible = !_arg_1;
         this._reputeField.visible = !_arg_1;
         this._damageTxt.visible = !_arg_1;
         this._damageButton.visible = !_arg_1;
         this._armorTxt.visible = !_arg_1;
         this._armorButton.visible = !_arg_1;
         this._HPText.visible = !_arg_1;
         this._hpButton.visible = !_arg_1;
         this._vitality.visible = !_arg_1;
         this._vitalityBuntton.visible = !_arg_1;
         if(this._vipName != null)
         {
            this._vipName.visible = !_arg_1;
            this._isVisible = !_arg_1;
         }
         this._textBg1.visible = !_arg_1;
         this._textBg2.visible = !_arg_1;
         this._textBg3.visible = !_arg_1;
         this._textBg4.visible = !_arg_1;
         this._textBg5.visible = !_arg_1;
         this._textBg6.visible = !_arg_1;
         this._attackTxt.visible = !_arg_1;
         this._attackButton.visible = !_arg_1;
         this._agilityTxt.visible = !_arg_1;
         this._agilityButton.visible = !_arg_1;
         this._defenceTxt.visible = !_arg_1;
         this._defenceButton.visible = !_arg_1;
         this._luckTxt.visible = !_arg_1;
         this._luckButton.visible = !_arg_1;
         this._magicAttackTxt.visible = !_arg_1;
         this._magicAttackButton.visible = !_arg_1;
         this._magicDefenceTxt.visible = !_arg_1;
         this._magicDefenceButton.visible = !_arg_1;
         this._attackTxt1.visible = _arg_1;
         this._agilityTxt1.visible = _arg_1;
         this._defenceTxt1.visible = _arg_1;
         this._luckTxt1.visible = _arg_1;
         this.__onUpdatePlayerProperty(null);
         this._openNecklacePtetrochemicalView.visible = this._showSelfOperation && this._showEquip.visible;
         if(Boolean(this._armShellBitmapBtn))
         {
            this._armShellBitmapBtn.visible = !_arg_1;
         }
      }
      
      public function switchShowII(_arg_1:Boolean) : void
      {
         this._switchShowII = !_arg_1;
         this.switchShow(_arg_1);
         if(Boolean(this._cardEquipView))
         {
            this._cardEquipView["clickEnable"] = this._showSelfOperation;
         }
         this._addFriendBtn.visible = !_arg_1;
         if(this._info.ID == PlayerManager.Instance.Self.ID)
         {
            this._addFriendBtn.visible = false;
         }
      }
      
      private function initProperties() : void
      {
         this._storeBtn.transparentEnable = true;
         this._hideHatBtn.text = LanguageMgr.GetTranslation("shop.ShopIITryDressView.hideHat");
         this._hideGlassBtn.text = LanguageMgr.GetTranslation("tank.view.changeColor.ChangeColorLeftView.glass");
         this._hideSuitBtn.text = LanguageMgr.GetTranslation("tank.view.changeColor.ChangeColorLeftView.suit");
         this._hideWingBtn.text = LanguageMgr.GetTranslation("tank.view.changeColor.ChangeColorLeftView.wing");
      }
      
      private function initPos() : void
      {
         this._cellPos = [ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos1"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos2"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos3"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos4"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos5"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos6"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos7"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos8"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos9"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos10"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos11"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos12"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos13"),ComponentFactory.Instance
         .creatCustomObject("bagAndInfo.info.equip.pos14"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos15"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos16"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos17"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos18"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos19"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos20"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos21"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos22"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos23"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos24"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos25"),ComponentFactory.Instance.creatCustomObject("bagAndInfo.info.equip.pos26")];
      }
      
      private function initEvents() : void
      {
         this._storeBtn.addEventListener("click",this.__storeBtnClickHandler);
         this._addFriendBtn.addEventListener("click",this.__addFriendClickHandler);
         this._buyAvatar.addEventListener("click",this.__buyAvatarClickHandler);
         this._hideGlassBtn.addEventListener("click",this.__hideGlassClickHandler);
         this._hideHatBtn.addEventListener("click",this.__hideHatClickHandler);
         this._hideSuitBtn.addEventListener("click",this.__hideSuitClickHandler);
         this._hideWingBtn.addEventListener("click",this.__hideWingClickHandler);
         this._bagDefinitionGroup.addEventListener("change",this._definitionGroupChange);
         this._openNecklacePtetrochemicalView.addEventListener("click",this.__openNecklacePtetrochemicalView);
         this._ringSystemBtn.addEventListener("click",this.__openRingSystemView);
         this._amuletBtn.addEventListener("click",this.__onClickAmulet);
         this._explorerIcon.addEventListener("click",this.__openExplorerHandler);
         this._forcesRelicBtn.addEventListener("click",this.__onClickForcesRelic);
         PlayerManager.Instance.addEventListener("updatePlayerState",this.__onUpdatePlayerProperty);
         if(Boolean(this._teamIcon))
         {
            this._teamIcon.addEventListener("click",this.__onClickTeam);
         }
         this._renjuBtn.addEventListener(MouseEvent.CLICK,this.__showRenju);
         this._backBtn.addEventListener(MouseEvent.CLICK,this.__hideRenju);
         this._SywQhBtn.addEventListener(MouseEvent.CLICK,this._showSywTs);
      }
      
      private function __onClickTeam(_arg_1:MouseEvent) : void
      {
         if(this._info && this._info.isSelf && this._showSelfOperation)
         {
            SoundManager.instance.playButtonSound();
            if(PlayerManager.Instance.Self.Grade < 26)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",26));
               return;
            }
            BagAndInfoManager.Instance.hideBagAndInfo();
            TeamManager.instance.showTeamFrame();
         }
      }
      
      private function __onClickForcesRelic(_arg_1:MouseEvent) : void
      {
         if(StateManager.currentStateType == "fighting" || StateManager.currentStateType == "fighting3d")
         {
            return;
         }
         if(this._info is SelfInfo && this._showSelfOperation)
         {
            SoundManager.instance.playButtonSound();
            ForcesRelicManager.instance.loadAllResForHall();
            return;
         }
      }
      
      private function __openExplorerHandler(_arg_1:MouseEvent) : void
      {
         if(this._info is SelfInfo)
         {
            SoundManager.instance.playButtonSound();
            ExplorerManualManager.instance.show();
            BagAndInfoManager.Instance.hideBagAndInfo();
            if(!PlayerManager.Instance.Self.isNewOnceFinish(161))
            {
               SocketManager.Instance.out.syncWeakStep(161);
            }
         }
      }
      
      protected function __openRingSystemView(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.playButtonSound();
         var _local_2:InventoryItemInfo = PlayerManager.Instance.Self.Bag.items[16];
         if(!_local_2)
         {
            return;
         }
         if(_local_2.TemplateID == 9900)
         {
            SocialContactManage.ins.loadRes(2);
         }
         else
         {
            SocialContactManage.ins.loadRes();
         }
      }
      
      private function _openTrailEliteView(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.playButtonSound();
         if(BagAndInfoManager.Instance.trialEliteModel.isOpen)
         {
            this._trailEliteView = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.TrailEliteView");
            LayerManager.Instance.addToLayer(this._trailEliteView,3,true,1);
            this._trailEliteView.addEventListener("response",this.__onTrailEliteSystemClose);
         }
      }
      
      protected function __onRingSystemClose(_arg_1:FrameEvent) : void
      {
         if(_arg_1.responseCode == 0 || _arg_1.responseCode == 1 || _arg_1.responseCode == 4)
         {
            SoundManager.instance.playButtonSound();
            this._ringSystemView.removeEventListener("response",this.__onRingSystemClose);
            ObjectUtils.disposeObject(this._ringSystemView);
            this._ringSystemView = null;
         }
      }
      
      protected function __onTrailEliteSystemClose(_arg_1:FrameEvent) : void
      {
         if(_arg_1.responseCode == 0 || _arg_1.responseCode == 1 || _arg_1.responseCode == 4)
         {
            SoundManager.instance.playButtonSound();
            this._trailEliteView.removeEventListener("response",this.__onTrailEliteSystemClose);
            ObjectUtils.disposeObject(this._trailEliteView);
            this._trailEliteView = null;
         }
      }
      
      protected function __openNecklacePtetrochemicalView(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.playButtonSound();
         AssetModuleLoader.addModelLoader("necklace",5);
         AssetModuleLoader.startCodeLoader(this.showFrame);
      }
      
      private function showFrame() : void
      {
         var _local_1:Sprite = ClassUtils.CreatInstance("bagAndInfo.necklace.view.NecklaceMain");
         LayerManager.Instance.addToLayer(_local_1,3,true,1);
         _local_1.x = 248;
         _local_1.y = 34;
         SocketManager.Instance.out.necklaceStrength(1);
      }
      
      private function __onClickAmulet(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.playButtonSound();
         if(PlayerManager.Instance.Self.Bag.items[18] == null)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.equipAmulet.notEquipAmulet"));
            return;
         }
         EquipAmuletManager.Instance.show();
      }
      
      private function formatMarking(_arg_1:int) : String
      {
         return _arg_1 * 0.1 + "%";
      }
      
      private function get baseHpAddValue() : int
      {
         var _local_1:int = this._info.Grade;
         return Experience.getBasicHP(_local_1);
      }
      
      private function equipAddProValue(_arg_1:String) : int
      {
         if(this._info == null)
         {
            return 0;
         }
         var _local_2:int = PlayerProValueAddManager.equipAddProValue(_arg_1,this._info);
         _local_2 += this.ringAddProValue(_arg_1);
         return _local_2 + this.soulMarkAddProValue(_arg_1);
      }
      
      private function soulMarkAddProValue(_arg_1:String) : int
      {
         var _local_9:int = 0;
         var _local_6:int = 0;
         var _local_5:int = 0;
         var _local_7:int = 0;
         var _local_3:* = null;
         var _local_2:* = null;
         var _local_8:* = null;
         var _local_4:* = null;
         if(this._info == null)
         {
            return 0;
         }
         _local_9 = 0;
         while(_local_9 < SoulMarkManager.EQUIPARR.length)
         {
            _local_6 = int(SoulMarkManager.EQUIPARR[_local_9]);
            _local_3 = this._info.Bag.getItemAt(_local_6);
            if(_local_3 && this._info.isSelf && this._info.soulMarkDic && Boolean(this._info.soulMarkDic[_local_6]))
            {
               _local_2 = this._info.soulMarkDic[_local_6];
               _local_8 = _local_2.proStr.split("|");
               _local_5 = 0;
               while(_local_5 < _local_8.length)
               {
                  _local_4 = (_local_8[_local_5] as String).split(",");
                  if(SoulMarkManager.PROTYPENAME[parseInt(_local_4[0]) - 1] == _arg_1)
                  {
                     _local_7 += parseInt(_local_4[1]);
                  }
                  _local_5++;
               }
            }
            _local_9++;
         }
         return _local_7;
      }
      
      private function ringAddProValue(_arg_1:String) : int
      {
         var _local_4:int = 0;
         var _local_2:* = null;
         if(this._info == null)
         {
            return 0;
         }
         var _local_3:InventoryItemInfo = this._info.Bag.getItemAt(16);
         if(Boolean(_local_3) && EquipType.isWeddingRing(_local_3))
         {
            _local_2 = BagAndInfoManager.Instance.getRingData(this._info.RingExp);
            if(_local_2 && _local_3.hasOwnProperty(_arg_1) && Boolean(_local_2.hasOwnProperty(_arg_1)))
            {
               _local_4 = _local_3[_arg_1] * _local_2[_arg_1] * 0.01;
            }
         }
         return _local_4;
      }
      
      private function get equipAddHPValue() : int
      {
         if(this._info == null)
         {
            return 0;
         }
         return PlayerProValueAddManager.equipAddHPValue(this._info);
      }
      
      private function manualAddProValue(_arg_1:String) : int
      {
         if(this._info == null)
         {
            return 0;
         }
         if(_arg_1 == "Attack")
         {
            _arg_1 = "pro_Attack";
         }
         else if(_arg_1 == "Defence")
         {
            _arg_1 = "pro_Defense";
         }
         else if(_arg_1 == "Agility")
         {
            _arg_1 = "pro_Agile";
         }
         else if(_arg_1 == "Luck")
         {
            _arg_1 = "pro_Lucky";
         }
         return PlayerProValueAddManager.manualAddProValue(_arg_1,this._info);
      }
      
      private function marKingAddProValue(_arg_1:String) : String
      {
         if(this._info == null)
         {
            return "0%";
         }
         var _local_2:DictionaryData = this._info.getPropertyAdditionByType("marKing");
         if(_local_2.hasKey(_arg_1))
         {
            return _local_2[_arg_1] / 10 + "%";
         }
         return "0%";
      }
      
      protected function __onUpdatePlayerProperty(_arg_1:Event) : void
      {
         var _local_8:int = 0;
         var _local_5:String = null;
         var _local_14:DictionaryData = null;
         var _local_6:* = null;
         var _local_7:* = null;
         if(this._info.propertyAddition == null)
         {
            return;
         }
         var _local_3:Vector.<GlowPropButton> = Vector.<GlowPropButton>([this._attackButton,this._defenceButton,this._agilityButton,this._luckButton]);
         var _local_10:Array = ["Attack","Defence","Agility","Luck"];
         var _local_15:String = LanguageMgr.GetTranslation("tank.data.EquipType.suit");
         var _local_4:String = LanguageMgr.GetTranslation("tank.data.EquipType.gem");
         for each(_local_5 in _local_10)
         {
            _local_6 = this._info.getPropertyAdditionByType(_local_5);
            if(_local_6)
            {
               _local_7 = LanguageMgr.GetTranslation("tank.view.personalinfoII.propertySourceTxtNew",_local_6["Equip"],_local_6["Card"],_local_6["Pet"],_local_6["Totem"],_local_6["gem"],_local_6["Bead"],_local_6["Avatar"],_local_6["MagicStone"],_local_6["Temple"],_local_6["mark"],_local_6["Suit"],this.manualAddProValue(_local_10[_local_8]),_local_6["Texp"],this.formatMarking(_local_6["marKing"]),_local_6["FineSuit"],_local_6["titleAdd"],_local_6["dig"],_local_6["petseal"],_local_6["ElfEquip"],_local_6["SywInfo"],_local_6["forcesRelic"],_local_6["LianTi"]);
               _local_3[_local_8].propertySource = _local_7;
            }
            if(_local_8 >= 4)
            {
               break;
            }
            _local_8++;
         }
         _local_14 = this._info.getPropertyAdditionByType("MagicAttack");
         if(Boolean(_local_14))
         {
            GlowPropButton(this._magicAttackButton).propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.magicPropertySourceTxtNew",_local_14["Equip"],_local_14["Horse"],_local_14["HorsePicCherish"],_local_14["Enchant"],_local_14["Suit"],_local_14["Texp"],_local_14["Card"],_local_14["mark"],_local_14["magicHouse"],this.manualAddProValue("pro_MagicAttack"),_local_14["FineSuit"],this.formatMarking(_local_14["marKing"]),_local_14["Temple"],_local_14["MagicStone"],_local_14["petseal"],_local_14["SywInfo"],_local_14["forcesRelic"],_local_14["LianTi"]);
         }
         var _local_12:DictionaryData = this._info.getPropertyAdditionByType("MagicDefence");
         if(Boolean(_local_12))
         {
            GlowPropButton(this._magicDefenceButton).propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.magicPropertySourceTxtNew",_local_12["Equip"],_local_12["Horse"],_local_12["HorsePicCherish"],_local_12["Enchant"],_local_12["Suit"],_local_12["Texp"],_local_12["Card"],_local_12["mark"],_local_12["magicHouse"],this.manualAddProValue("pro_MagicResistance"),_local_12["FineSuit"],this.formatMarking(_local_12["marKing"]),_local_12["Temple"],_local_12["MagicStone"],_local_12["petseal"],_local_12["SywInfo"],_local_12["forcesRelic"],_local_12["LianTi"]);
         }
         var _local_2:DictionaryData = this._info.getPropertyAdditionByType("HP");
         if(Boolean(_local_2))
         {
            GlowPropButton(this._hpButton).propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.hpPropertySourceTxtNew",this.baseHpAddValue,this.equipAddHPValue,this.manualAddProValue("pro_HP"),_local_2["mark"],_local_2["Texp"],_local_2["Pet"],_local_2["Totem"],_local_2["Bead"],_local_2["Avatar"],_local_2["Horse"],_local_2["HorsePicCherish"],_local_2["Suit"],_local_2["Temple"],this._info.cardAchievementHp,this._info.horseAmuletHp,_local_2["gem"],_local_2["FineSuit"],_local_2["petseal"],_local_2["honor"],_local_2["necklaceCast"],_local_2["ElfEquip"],_local_2["forcesRelic"]);
         }
         var _local_11:DictionaryData = this._info.getPropertyAdditionByType("Armor");
         if(Boolean(_local_11))
         {
            GlowPropButton(this._armorButton).propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.recoveryPropertySourceTxtNew",_local_11["Equip"],this.manualAddProValue("pro_Armor"),_local_11["mark"],StaticFormula.getCardRecoveryAddition(this._info),_local_11["Totem"],_local_11["Bead"],_local_11["Avatar"],_local_11["Horse"],_local_11["HorsePicCherish"],_local_11["FineSuit"],_local_11["Temple"],_local_11["Texp"],_local_11["Pet"],_local_11["Suit"],_local_11["petseal"],_local_11["forcesRelic"]);
         }
         var _local_9:DictionaryData = this._info.getPropertyAdditionByType("Damage");
         if(Boolean(_local_9))
         {
            GlowPropButton(this._damageButton).propertySource = LanguageMgr.GetTranslation("tank.view.personalinfoII.damagePropertySourceTxtNew",_local_9["Equip"],this.manualAddProValue("pro_Damage"),_local_9["mark"],StaticFormula.getCardDamageAddition(this._info),_local_9["Totem"],_local_9["Bead"],_local_9["Avatar"],_local_9["Horse"],_local_9["HorsePicCherish"],_local_9["Suit"],_local_9["Texp"],_local_9["petseal"],_local_9["ElfEquip"],_local_9["forcesRelic"]);
         }
         if(Boolean(PlayerManager.Instance.Self.Bag.items[12]))
         {
            if(!this._openNecklacePtetrochemicalView.parent)
            {
               addChild(this._openNecklacePtetrochemicalView);
            }
         }
         else if(Boolean(this._openNecklacePtetrochemicalView.parent))
         {
            this._openNecklacePtetrochemicalView.parent.removeChild(this._openNecklacePtetrochemicalView);
         }
         this.showHideSmallBtn();
      }
      
      private function showHideSmallBtn() : void
      {
         var _local_2:* = null;
         this._ringSystemBtn.visible = this._showSelfOperation && this._showEquip.visible ? Boolean(PlayerManager.Instance.Self.Bag.items[16]) : false;
         var _local_1:Boolean = this._showSelfOperation && this._showEquip.visible ? Boolean(PlayerManager.Instance.Self.Bag.items[18]) : false;
         if(_local_1)
         {
            _local_2 = PlayerManager.Instance.Self.Bag.items[18] as InventoryItemInfo;
            if(_local_2.CategoryID != 19)
            {
               this._amuletBtn.visible = true;
            }
            else
            {
               this._amuletBtn.visible = false;
            }
         }
         else
         {
            this._amuletBtn.visible = false;
         }
      }
      
      private function removeEvent() : void
      {
         this._storeBtn.removeEventListener("click",this.__storeBtnClickHandler);
         this._addFriendBtn.removeEventListener("click",this.__addFriendClickHandler);
         this._buyAvatar.removeEventListener("click",this.__buyAvatarClickHandler);
         this._hideGlassBtn.removeEventListener("click",this.__hideGlassClickHandler);
         this._hideHatBtn.removeEventListener("click",this.__hideHatClickHandler);
         this._hideSuitBtn.removeEventListener("click",this.__hideSuitClickHandler);
         this._hideWingBtn.removeEventListener("click",this.__hideWingClickHandler);
         this._openNecklacePtetrochemicalView.removeEventListener("click",this.__openNecklacePtetrochemicalView);
         this._ringSystemBtn.removeEventListener("click",this.__openRingSystemView);
         this._amuletBtn.removeEventListener("click",this.__onClickAmulet);
         this._forcesRelicBtn.removeEventListener("click",this.__onClickForcesRelic);
         if(Boolean(this._teamIcon))
         {
            this._teamIcon.removeEventListener("click",this.__onClickTeam);
         }
         this._explorerIcon.removeEventListener("click",this.__openExplorerHandler);
         if(this._info is PlayerInfo)
         {
            this._info.Bag.removeEventListener("update",this.__updateCells);
            this._info.removeEventListener("propertychange",this.__changeHandler);
            if(this._info is SelfInfo)
            {
               (this._info as SelfInfo).magicStoneBag.removeEventListener("update",this.__equipMagicStone);
            }
         }
         PlayerManager.Instance.removeEventListener("VIPStateChange",this.__upVip);
         this._bagDefinitionGroup.removeEventListener("change",this._definitionGroupChange);
         PlayerManager.Instance.removeEventListener("updatePlayerState",this.__onUpdatePlayerProperty);
         this._renjuBtn.removeEventListener(MouseEvent.CLICK,this.__showRenju);
         this._backBtn.removeEventListener(MouseEvent.CLICK,this.__hideRenju);
         this._SywQhBtn.removeEventListener(MouseEvent.CLICK,this._showSywTs);
      }
      
      private function _showSywTs(evt:MouseEvent) : void
      {
         SoundManager.instance.playButtonSound();
         AssetModuleLoader.addModelLoader("sywStrength",5);
         AssetModuleLoader.startCodeLoader(this.sywShowFrame);
      }
      
      private function sywShowFrame() : void
      {
         var _local_1:Sprite = ClassUtils.CreatInstance("bagAndInfo.sywStrength.view.sywMain");
         LayerManager.Instance.addToLayer(_local_1,3,true,1);
         _local_1.x = 0;
         _local_1.width = 1000;
         _local_1.y = 34;
      }
      
      private function _removeSywTs(evt:MouseEvent) : void
      {
      }
      
      private function __showRenju(evt:MouseEvent) : void
      {
         var b:int = 0;
         this._bg1.visible = false;
         this._bg11.visible = true;
         var a:int = 21;
         while(a <= 25)
         {
            (this._cells[a] as PersonalInfoCell).visible = true;
            a++;
         }
         while(b <= 20)
         {
            (this._cells[b] as PersonalInfoCell).visible = false;
            b++;
         }
         this._renjuBtn.visible = true;
         this._backBtn.visible = true;
         this._SywQhBtn.visible = true;
         if(Boolean(this._openNecklacePtetrochemicalView.parent))
         {
            this._openNecklacePtetrochemicalView.parent.removeChild(this._openNecklacePtetrochemicalView);
         }
         if(Boolean(this._ringSystemBtn.parent))
         {
            this._ringSystemBtn.parent.removeChild(this._ringSystemBtn);
         }
         if(Boolean(this._amuletBtn.parent))
         {
            this._amuletBtn.parent.removeChild(this._amuletBtn);
         }
         this.removeArmShellBtn();
      }
      
      private function __hideRenju(evt:MouseEvent) : void
      {
         var b:int = 0;
         this._bg1.visible = true;
         this._bg11.visible = false;
         this._SywQhBtn.visible = false;
         var a:int = 21;
         while(a <= 25)
         {
            (this._cells[a] as PersonalInfoCell).visible = false;
            a++;
         }
         while(b <= 20)
         {
            (this._cells[b] as PersonalInfoCell).visible = true;
            b++;
         }
         this._renjuBtn.visible = true;
         this._backBtn.visible = true;
         if(Boolean(PlayerManager.Instance.Self.Bag.items[12]))
         {
            if(!this._openNecklacePtetrochemicalView.parent)
            {
               addChild(this._openNecklacePtetrochemicalView);
            }
         }
         if(!this._ringSystemBtn.parent && Boolean(PlayerManager.Instance.Self.Bag.items[16]))
         {
            addChild(this._ringSystemBtn);
         }
         if(!this._amuletBtn.parent && Boolean(PlayerManager.Instance.Self.Bag.items[18]))
         {
            addChild(this._amuletBtn);
         }
         this.addArmShellBtn();
      }
      
      private function __storeBtnClickHandler(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(PlayerManager.Instance.Self.Grade < 5)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",5));
            return;
         }
         BagStore.instance.isFromBagFrame = true;
         BagStore.instance.openStore();
      }
      
      private function __addFriendClickHandler(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         IMManager.Instance.addFriend(this._info.NickName);
      }
      
      private function __buyAvatarClickHandler(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         ShopBuyManager.Instance.buyAvatar(this._info);
      }
      
      private function __hideGlassClickHandler(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         SocketManager.Instance.out.sendHideLayer(2,this._hideGlassBtn.selected);
      }
      
      private function __hideHatClickHandler(_arg_1:Event) : void
      {
         SoundManager.instance.play("008");
         SocketManager.Instance.out.sendHideLayer(1,this._hideHatBtn.selected);
      }
      
      private function __hideSuitClickHandler(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         SocketManager.Instance.out.sendHideLayer(13,this._hideSuitBtn.selected);
      }
      
      private function __hideWingClickHandler(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         SocketManager.Instance.out.sendHideLayer(15,this._hideWingBtn.selected);
      }
      
      private function creatCells() : void
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         this._cells = new Vector.<PersonalInfoCell>();
         _local_2 = 0;
         while(_local_2 < 26)
         {
            _local_1 = CellFactory.instance.createPersonalInfoCell(_local_2) as PersonalInfoCell;
            switch(_local_2)
            {
               case 0:
               case 1:
               case 2:
               case 3:
               case 4:
               case 5:
               case 11:
               case 13:
                  break;
               default:
                  _local_1.addEventListener("itemclick",this.__cellClickHandler);
                  _local_1.addEventListener("doubleclick",this.__cellDoubleClickHandler);
                  break;
            }
            _local_1.x = this._cellPos[_local_2].x;
            _local_1.y = this._cellPos[_local_2].y;
            this._cellContent.addChild(_local_1);
            this._cells.push(_local_1);
            _local_2++;
         }
      }
      
      private function clearCells() : void
      {
         var _local_1:int = 0;
         _local_1 = 0;
         while(_local_1 < this._cells.length)
         {
            if(Boolean(this._cells[_local_1]))
            {
               if(this._cells[_local_1].hasEventListener("itemclick"))
               {
                  this._cells[_local_1].removeEventListener("itemclick",this.__cellClickHandler);
               }
               if(this._cells[_local_1].hasEventListener("doubleclick"))
               {
                  this._cells[_local_1].removeEventListener("doubleclick",this.__cellDoubleClickHandler);
               }
               if(Boolean(this._cells[_local_1].parent))
               {
                  this._cells[_local_1].parent.removeChild(this._cells[_local_1] as PersonalInfoCell);
               }
               this._cells[_local_1].dispose();
               this._cells[_local_1] = null;
            }
            _local_1++;
         }
      }
      
      public function set info(_arg_1:*) : void
      {
         PlayerInfoViewControl.currentPlayer = _arg_1;
         if(this._info == _arg_1)
         {
            return;
         }
         if(PlayerInfoViewControl._isBattle)
         {
            this._info = _arg_1;
            this.updateView(PlayerInfoViewControl._isBattle);
            return;
         }
         if(Boolean(this._info))
         {
            this._info.removeEventListener("propertychange",this.__changeHandler);
            PlayerManager.Instance.removeEventListener("VIPStateChange",this.__upVip);
            PlayerManager.Instance.removeEventListener("equip",this.__onBeadBagUpdate);
            this._info.Bag.removeEventListener("update",this.__updateCells);
            if(this._info is SelfInfo)
            {
               (this._info as SelfInfo).magicStoneBag.removeEventListener("update",this.__equipMagicStone);
            }
            this._info = null;
         }
         this._info = _arg_1;
         if(Boolean(this._info))
         {
            this._info.addEventListener("propertychange",this.__changeHandler);
            PlayerManager.Instance.addEventListener("VIPStateChange",this.__upVip);
            this._info.Bag.addEventListener("update",this.__updateCells);
            if(this._info is SelfInfo)
            {
               (this._info as SelfInfo).magicStoneBag.addEventListener("update",this.__equipMagicStone);
            }
            if(this._info.Grade >= 24)
            {
            }
            this._ddtEmblemBtn.tipData = this._info;
            this._ddtEmblemBtn.visible = true;
            if(Boolean(this._cardEquipView))
            {
               this._cardEquipView["playerInfo"] = this._info;
            }
            this._forcesRelicBtn.tipData = LanguageMgr.GetTranslation("tank.forceRelic.txt33");
            this._forcesRelicBtn.visible = this._info.isSelf && this._showSelfOperation;
         }
         else
         {
            this._ddtEmblemBtn.visible = false;
            this._forcesRelicBtn.visible = false;
         }
         this.updateView();
      }
      
      protected function __onBeadBagUpdate(_arg_1:Event) : void
      {
         this.updatePersonInfo();
      }
      
      private function __changeHandler(_arg_1:PlayerPropertyEvent) : void
      {
         this.updatePersonInfo();
         this.updateHide();
         this.updateIcons();
         this.setTexpViewProTxt();
         if(Boolean(this._info) && Boolean(this._characterSprite))
         {
            this._characterSprite.info = this._info;
         }
      }
      
      private function __upVip(_arg_1:Event) : void
      {
         this.__changeHandler(null);
      }
      
      private function __updateCells(_arg_1:BagEvent) : void
      {
         var _local_2:int = 0;
         var _local_3:String = null;
         for(_local_3 in _arg_1.changedSlots)
         {
            _local_2 = int(_local_3);
            if(_local_2 <= 30 && _local_2 != 20)
            {
               this._cells[_local_2].info = this._info.Bag.getItemAt(_local_2);
            }
            if(Boolean(GemstoneManager.Instance.getByPlayerInfoList(_local_2,this._info.ID)))
            {
               if(Boolean(this._cells[_local_2].info))
               {
                  (this._cells[_local_2].info as InventoryItemInfo).gemstoneList = GemstoneManager.Instance.getByPlayerInfoList(_local_2,this._info.ID);
               }
            }
         }
         this.updateCells();
      }
      
      private function __equipMagicStone(_arg_1:BagEvent) : void
      {
         var _local_2:int = 0;
         var _local_3:String = null;
         for(_local_3 in _arg_1.changedSlots)
         {
            _local_2 = int(_local_3);
            if(_local_2 <= 30)
            {
               this.updateCells();
               return;
            }
         }
      }
      
      private function __cellClickHandler(_arg_1:CellEvent) : void
      {
         var _local_2:* = null;
         if(this._showSelfOperation)
         {
            _local_2 = _arg_1.data as PersonalInfoCell;
            _local_2.dragStart();
         }
      }
      
      private function __cellDoubleClickHandler(_arg_1:CellEvent) : void
      {
         var _local_2:* = null;
         var _local_3:* = null;
         if(Boolean(this._info) && this._info.ID != PlayerManager.Instance.Self.ID)
         {
            return;
         }
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         if(this._showSelfOperation)
         {
            _local_2 = _arg_1.data as PersonalInfoCell;
            if(_local_2 && Boolean(_local_2.info))
            {
               _local_3 = _local_2.info as InventoryItemInfo;
               SocketManager.Instance.out.sendMoveGoods(0,_local_3.Place,0,-1,_local_3.Count);
            }
         }
      }
      
      private function updateView(_arg_1:Boolean = false) : void
      {
         if(_arg_1)
         {
            this.updatePersonInfo();
            this.updateCharacter();
            return;
         }
         this.updateCharacter();
         this.updateCells();
         this.updatePersonInfo();
         this.updateHide();
         this.updateIcons();
         this.updateShowOperation();
         this.addArmShellBtn();
      }
      
      private function updateHide() : void
      {
         if(Boolean(this._info))
         {
            this._hideGlassBtn.selected = this._info.getGlassHide();
            this._hideHatBtn.selected = this._info.getHatHide();
            this._hideSuitBtn.selected = this._info.getSuitesHide();
            this._hideWingBtn.selected = this._info.wingHide;
         }
      }
      
      private function updateCharacter() : void
      {
         if(Boolean(this._info))
         {
            if(Boolean(this._character))
            {
               this._character.dispose();
               this._character = null;
            }
            this._character = CharactoryFactory.createCharacter(this._info,"room") as RoomCharacter;
            this._character.showGun = false;
            this._character.show(false,-1);
            this._character.x = 267;
            this._character.y = 108;
            this._showEquip.addChildAt(this._character,0);
            if(!this._characterSprite)
            {
               this._characterSprite = new TexpInfoTipArea();
               this._characterSprite.x = this._character.x;
               this._characterSprite.y = this._character.y;
               this._characterSprite.scaleX = -1;
               this._showEquip.addChildAt(this._characterSprite,0);
            }
            this._characterSprite.info = this._info;
         }
         else
         {
            this._character.dispose();
            this._character = null;
            ObjectUtils.disposeObject(this._characterSprite);
            this._characterSprite = null;
         }
      }
      
      private function updateCells() : void
      {
         var _local_3:PersonalInfoCell = null;
         var _local_2:* = null;
         var _local_1:* = null;
         var _local_4:* = null;
         for each(_local_3 in this._cells)
         {
            if(!this._info)
            {
               break;
            }
            _local_2 = this._info.Bag.getItemAt(_local_3.place);
            _local_3.info = _local_2;
            if(_local_2)
            {
               _local_2.gemstoneList = GemstoneManager.Instance.getByPlayerInfoList(_local_3.place,this._info.ID);
               if(this._info == PlayerManager.Instance.Self)
               {
                  _local_1 = PlayerManager.Instance.Self.magicStoneBag.getItemAt(_local_3.place);
                  if(!_local_1)
                  {
                     _local_2.magicStoneAttr = null;
                  }
                  else
                  {
                     _local_4 = new MagicStoneInfo();
                     _local_4.templateId = _local_1.TemplateID;
                     _local_4.level = _local_1.StrengthenLevel;
                     _local_4.attack = _local_1.AttackCompose;
                     _local_4.defence = _local_1.DefendCompose;
                     _local_4.agility = _local_1.AgilityCompose;
                     _local_4.luck = _local_1.LuckCompose;
                     _local_4.magicAttack = _local_1.MagicAttack;
                     _local_4.magicDefence = _local_1.MagicDefence;
                     _local_2.magicStoneAttr = _local_4;
                     _local_2.RingExp = this._info.RingExp;
                  }
               }
            }
         }
         if(Boolean(PlayerManager.Instance.Self.Bag.items[12]))
         {
            if(!this._openNecklacePtetrochemicalView.parent)
            {
               addChild(this._openNecklacePtetrochemicalView);
            }
         }
         else if(Boolean(this._openNecklacePtetrochemicalView.parent))
         {
            this._openNecklacePtetrochemicalView.parent.removeChild(this._openNecklacePtetrochemicalView);
         }
         this.showHideSmallBtn();
      }
      
      private function getList(_arg_1:int) : Vector.<GemstListInfo>
      {
         var _local_2:int = 0;
         _local_2 = 0;
         while(_local_2 < 5)
         {
            if(Boolean(PlayerManager.Instance.gemstoneInfoList[_local_2]))
            {
               if(_arg_1 == PlayerManager.Instance.gemstoneInfoList[_local_2].equipPlace)
               {
                  return PlayerManager.Instance.gemstoneInfoList[_local_2].list;
               }
            }
            _local_2++;
         }
         return null;
      }
      
      public function allowLvIconClick() : void
      {
         if(Boolean(this._levelIcon) && this._info.isSelf)
         {
            this._levelIcon.allowClick();
         }
      }
      
      private function updateIcons() : void
      {
         var _local_2:int = 0;
         var _local_3:* = undefined;
         var _local_4:int = 0;
         var _local_1:int = 0;
         var _local_6:* = undefined;
         var _local_5:* = null;
         if(Boolean(this._info))
         {
            if(this._levelIcon == null)
            {
               this._levelIcon = ComponentFactory.Instance.creatCustomObject("asset.bagAndInfo.levelIcon");
               if(this._info.IsVIP)
               {
                  this._levelIcon.x += 1;
               }
            }
            this._levelIcon.setSize(0);
            _local_2 = 1;
            if(StateManager.currentStateType == "fighting" || StateManager.currentStateType == "fighting3d" || StateManager.currentStateType == "trainer1" || StateManager.currentStateType == "trainer2" || StateManager.currentStateType == "fightLabGameView")
            {
               _local_3 = getDefinitionByName("gameCommon.GameControl");
               if(_local_3)
               {
                  _local_2 = _local_3.Instance.Current.findLivingByPlayerID(this._info.ID,this._info.ZoneID) == null ? -1 : int(_local_3.Instance.Current.findLivingByPlayerID(this._info.ID,this._info.ZoneID).team);
               }
            }
            this._levelIcon.setInfo(this._info.Grade,this._info.ddtKingGrade,this._info.Repute,this._info.WinCount,this._info.TotalCount,this._info.FightPower_Show,this._info.Offer,true,false,_local_2);
            this._showEquip.addChild(this._levelIcon);
            if(this._info.SpouseID > 0)
            {
               if(this._marriedIcon == null)
               {
                  this._marriedIcon = ComponentFactory.Instance.creatCustomObject("asset.bagAndInfo.MarriedIcon");
               }
               this._marriedIcon.tipData = {
                  "nickName":this._info.SpouseName,
                  "gender":this._info.Sex
               };
               this._iconContainer.addChild(this._marriedIcon);
            }
            else if(Boolean(this._marriedIcon))
            {
               this._marriedIcon.dispose();
               this._marriedIcon = null;
            }
            if(this._info.Grade >= GuardCoreManager.instance.guardCoreMinLevel)
            {
               if(this._guardCore == null)
               {
                  this._guardCore = ComponentFactory.Instance.creatComponentByStylename("core.guardCoreIcon");
                  this._guardCore.setup(this._info,this._showSelfOperation);
                  this._iconContainer.addChild(this._guardCore);
               }
               else
               {
                  if(this._iconContainer.contains(this._guardCore))
                  {
                     this._iconContainer.removeChild(this._guardCore);
                  }
                  this._iconContainer.addChild(this._guardCore);
               }
            }
            if(Boolean(this._fineSuitIcon))
            {
               this._fineSuitIcon.tipData = this._info.fineSuitExp;
               _local_5 = FineSuitManager.Instance.getSuitVoByExp(this._info.fineSuitExp);
               _local_4 = int(int(_local_5.level / 14));
               this._fineSuitIcon.setFrame(Math.min(_local_4 + 1,5));
            }
            if(Boolean(this._explorerIcon) && PlayerManager.Instance.Self.Grade >= 32)
            {
               this._explorerIcon.visible = true;
               this._explorerIcon.tipData = this._info.manualProInfo;
               _local_1 = this._info.manualProInfo.manual_Level;
               this._explorerIcon.setFrame(Math.min(int((_local_1 - 1) / 5) + 1,4));
            }
            else
            {
               this._explorerIcon.visible = false;
            }
            if(this._info.Grade >= 26)
            {
               if(this._info.teamID == 0 && this._info.teamScore == 0)
               {
                  this._teamIcon.setFrame(1);
               }
               else
               {
                  this._teamIcon.setFrame(this._info.teamDivision + 2);
               }
               this._teamIcon.visible = true;
            }
            else
            {
               this._teamIcon.visible = false;
            }
            this._teamIcon.tipData = this._info;
            _local_6 = this._info.isSelf && this._showSelfOperation;
            this._teamIcon.buttonMode = _local_6;
            this._explorerIcon.buttonMode = _local_6;
            if(this._info.Grade >= 20)
            {
               this._honorIcon.visible = true;
               this._honorIcon.info = this._info;
               this._honorIcon.tipData = this._info;
            }
         }
         else
         {
            this._honorIcon.visible = false;
            this._honorIcon.tipData = null;
            if(Boolean(this._levelIcon))
            {
               this._levelIcon.dispose();
               this._levelIcon = null;
            }
            if(Boolean(this._marriedIcon))
            {
               this._marriedIcon.dispose();
               this._marriedIcon = null;
            }
         }
      }
      
      private function checkDDTKingGradeGuide() : void
      {
         if(PlayerManager.Instance.Self.isNewOnceFinish(160))
         {
         }
      }
      
      private function updatePersonInfo() : void
      {
         var _local_2:* = undefined;
         var _local_3:int = 0;
         var _local_1:int = 0;
         var _local_4:int = 0;
         if(this._info == null)
         {
            return;
         }
         if(PlayerInfoViewControl._isBattle)
         {
            this._attackTxt.htmlText = this.getHtmlTextByString(String(this._info.Attack <= 0 ? "" : this._info.Attack),0);
            this._defenceTxt.htmlText = this.getHtmlTextByString(String(this._info.Defence <= 0 ? "" : this._info.Defence),0);
            this._agilityTxt.htmlText = this.getHtmlTextByString(String(this._info.Agility <= 0 ? "" : this._info.Agility),0);
            this._luckTxt.htmlText = this.getHtmlTextByString(String(this._info.Luck <= 0 ? "" : this._info.Luck),0);
            this._magicAttackTxt.htmlText = this.getHtmlTextByString(String(this._info.MagicAttack <= 0 ? "" : this._info.MagicAttack),0);
            this._magicDefenceTxt.htmlText = this.getHtmlTextByString(String(this._info.MagicDefence <= 0 ? "" : this._info.MagicDefence),0);
            this._damageTxt.htmlText = this.getHtmlTextByString(String(this._info.Damage),1);
            this._armorTxt.htmlText = this.getHtmlTextByString(String(this._info.Guard),1);
            this._HPText.htmlText = this.getHtmlTextByString(String(this._info.Blood),1);
            this._vitality.htmlText = this.getHtmlTextByString(String(this._info.Energy),1);
            return;
         }
         this.__onUpdatePlayerProperty(null);
         this._reputeField.text = this._info == null ? "" : this._info.Repute.toString();
         this._gesteField.text = this._info == null ? "" : this._info.Offer.toString();
         this._dutyField.text = this._info.DutyName == null || this._info.DutyName == "" ? "" : (this._info.ConsortiaID > 0 ? "< " + this._info.DutyName + " >" : "");
         this._honorNameTxt.text = this._info.honor == null ? "" : this._info.honor;
         this._nickNameTxt.text = this._info.NickName == null ? "" : this._info.NickName;
         if(this._info.IsVIP)
         {
            ObjectUtils.disposeObject(this._vipName);
            this._vipName = VipController.instance.getVipNameTxt(114,this._info.typeVIP);
            this._vipName.x = this._nickNameTxt.x;
            this._vipName.y = this._nickNameTxt.y;
            this._vipName.text = this._nickNameTxt.text;
            this._vipName.visible = this._isVisible;
            addChild(this._vipName);
            DisplayUtils.removeDisplay(this._nickNameTxt);
         }
         else
         {
            addChild(this._nickNameTxt);
            DisplayUtils.removeDisplay(this._vipName);
         }
         this._consortiaTxt.text = this._info.ConsortiaName == null ? "" : (this._info.ConsortiaID > 0 ? this._info.ConsortiaName : "");
         this._dutyField.x = this._consortiaTxt.x + this._consortiaTxt.width + 6;
         if(this._dutyField.x + this._dutyField.width > 267)
         {
            this._dutyField.autoSize = "none";
            this._dutyField.isAutoFitLength = true;
            _local_3 = 260 - this._dutyField.x;
            this._dutyField.width = _local_3;
         }
         if(this._info.ID == PlayerManager.Instance.Self.ID)
         {
            this._gesteField.visible = true;
            this._gongxunbg.visible = true;
            this._bg2.visible = this._showSelfOperation;
         }
         else
         {
            this._storeBtn.visible = true;
            this._storeBtn.enable = false;
            this._gesteField.visible = false;
            this._gongxunbg.visible = false;
            this._bg2.visible = false;
         }
         if(this._info.ConsortiaID > 0 && this._dutyField.x + this._dutyField.width > this._offerSourcePosition.x)
         {
            this._offerLabel.x = this._dutyField.x + this._dutyField.width;
         }
         else
         {
            this._offerLabel.x = this._offerSourcePosition.x + 32;
         }
         PowerUpMovieManager.isInPlayerInfoView = true;
         if(this._info.ZoneID != 0 && this._info.ZoneID != PlayerManager.Instance.Self.ZoneID)
         {
            this._attackTxt.htmlText = this.getHtmlTextByString(String(this._info.Attack <= 0 ? "" : this._info.Attack),0);
            this._defenceTxt.htmlText = this.getHtmlTextByString(String(this._info.Defence <= 0 ? "" : this._info.Defence),0);
            this._agilityTxt.htmlText = this.getHtmlTextByString(String(this._info.Agility <= 0 ? "" : this._info.Agility),0);
            this._luckTxt.htmlText = this.getHtmlTextByString(String(this._info.Luck <= 0 ? "" : this._info.Luck),0);
            this._magicAttackTxt.htmlText = this.getHtmlTextByString(String(this._info.MagicAttack <= 0 ? "" : this._info.MagicAttack),0);
            this._magicDefenceTxt.htmlText = this.getHtmlTextByString(String(this._info.MagicDefence <= 0 ? "" : this._info.MagicDefence),0);
            this._damageTxt.htmlText = this.getHtmlTextByString(String(Math.round(StaticFormula.getDamage(this._info)) <= 0 ? "" : Math.round(StaticFormula.getDamage(this._info))),1);
            this._armorTxt.htmlText = this.getHtmlTextByString(String(StaticFormula.getRecovery(this._info) <= 0 ? "" : StaticFormula.getRecovery(this._info)),1);
            this._HPText.htmlText = this.getHtmlTextByString(String(StaticFormula.getMaxHp(this._info)),1);
            this._vitality.htmlText = this.getHtmlTextByString(String(StaticFormula.getEnergy(this._info) <= 0 ? "" : StaticFormula.getEnergy(this._info)),1);
            if(this._info.isSelf)
            {
               this._battle.htmlText = this.getHtmlTextByString(String(this._info.FightPower_Show),2);
            }
            else if(StateManager.currentStateType == "fighting" || StateManager.currentStateType == "fighting3d" || StateManager.currentStateType == "trainer1" || StateManager.currentStateType == "trainer2" || StateManager.currentStateType == "fightLabGameView")
            {
               _local_2 = getDefinitionByName("gameCommon.GameControl");
               if(_local_2)
               {
                  if(_local_2.Instance.Current.findLivingByPlayerID(this._info.ID,this._info.ZoneID) != null && _local_2.Instance.Current.findLivingByPlayerID(this._info.ID,this._info.ZoneID).team == _local_2.Instance.Current.selfGamePlayer.team)
                  {
                     this._battle.htmlText = this.getHtmlTextByString(this._info == null ? "" : this._info.FightPower_Show.toString(),2);
                  }
                  else
                  {
                     this._battle.htmlText = "";
                  }
               }
            }
            else
            {
               this._battle.htmlText = this.getHtmlTextByString(this._info == null ? "" : this._info.FightPower.toString(),2);
            }
         }
         else
         {
            if(StateManager.currentStateType == "fighting" || StateManager.currentStateType == "fighting3d" || StateManager.currentStateType == "trainer1" || StateManager.currentStateType == "trainer2" || StateManager.currentStateType == "fightLabGameView")
            {
               if(RoomManager.Instance.current.selfRoomPlayer.playerInfo.ID == this._info.ID)
               {
                  this._battle.htmlText = this.getHtmlTextByString(this._info == null ? "" : this._info.FightPower.toString(),2);
               }
               else
               {
                  _local_2 = getDefinitionByName("gameCommon.GameControl");
                  if(_local_2)
                  {
                     if(_local_2.Instance.Current.findLivingByPlayerID(this._info.ID,this._info.ZoneID) != null && _local_2.Instance.Current.findLivingByPlayerID(this._info.ID,this._info.ZoneID).team == _local_2.Instance.Current.selfGamePlayer.team)
                     {
                        this._battle.htmlText = this.getHtmlTextByString(this._info == null ? "" : this._info.FightPower.toString(),2);
                     }
                     else
                     {
                        this._battle.htmlText = "";
                     }
                  }
               }
            }
            else
            {
               this._battle.htmlText = this.getHtmlTextByString(this._info == null ? "" : this._info.FightPower_Show.toString(),2);
            }
            this._attackTxt.htmlText = this._info == null ? "" : this.getHtmlTextByString(String(this._info.Attack < 0 ? 0 : this._info.Attack),0);
            this._agilityTxt.htmlText = this._info == null ? "" : this.getHtmlTextByString(String(this._info.Agility < 0 ? 0 : this._info.Agility),0);
            this._defenceTxt.htmlText = this._info == null ? "" : this.getHtmlTextByString(String(this._info.Defence < 0 ? 0 : this._info.Defence),0);
            this._luckTxt.htmlText = this._info == null ? "" : this.getHtmlTextByString(String(this._info.Luck < 0 ? 0 : this._info.Luck),0);
            this._magicAttackTxt.htmlText = this.getHtmlTextByString(String(this._info.MagicAttack <= 0 ? 0 : this._info.MagicAttack),0);
            this._magicDefenceTxt.htmlText = this.getHtmlTextByString(String(this._info.MagicDefence <= 0 ? 0 : this._info.MagicDefence),0);
            this._damageTxt.htmlText = this._info == null ? "" : this.getHtmlTextByString(String(Math.round(StaticFormula.getDamage(this._info))),1);
            this._armorTxt.htmlText = this._info == null ? "" : this.getHtmlTextByString(String(StaticFormula.getRecovery(this._info)),1);
            this._HPText.htmlText = this._info == null ? "" : this.getHtmlTextByString(String(StaticFormula.getMaxHp(this._info)),1);
            this._vitality.htmlText = this._info == null ? "" : this.getHtmlTextByString(String(StaticFormula.getEnergy(this._info)),1);
         }
         if(Boolean(this._info))
         {
            this._progressLevel.setProgress(Experience.getExpPercent(this._info.Grade,this._info.GP) * 100,100);
            _local_1 = Experience.expericence[this._info.Grade] - Experience.expericence[this._info.Grade - 1];
            _local_4 = this._info.GP - Experience.expericence[this._info.Grade - 1];
            if(this._info.Grade < Experience.expericence.length)
            {
               _local_4 = _local_4 > _local_1 ? _local_1 : _local_4;
            }
            if((StateManager.currentStateType == "fighting" || StateManager.currentStateType == "fighting3d") && this._info.ZoneID != 0 && this._info.ZoneID != PlayerManager.Instance.Self.ZoneID)
            {
               this._progressLevel.tipData = "0/" + _local_1;
            }
            else if(_local_4 > 0 && this._info.Grade < Experience.expericence.length)
            {
               this._progressLevel.tipData = _local_4 + "/" + _local_1;
            }
            else if(this._info.Grade == Experience.expericence.length)
            {
               this._progressLevel.tipData = _local_4 + "/0";
            }
            else
            {
               this._progressLevel.tipData = "0/" + _local_1;
            }
         }
         if(Boolean(this._info) && this._info.ID == PlayerManager.Instance.Self.ID)
         {
            this._definitionGroupChange();
         }
         this._attestBtn.visible = this._info.isAttest;
      }
      
      private function setTexpViewProTxt() : void
      {
         var _local_4:DictionaryData = this._info.getPropertyAdditionByType("Attack");
         var _local_3:DictionaryData = this._info.getPropertyAdditionByType("Defence");
         var _local_1:DictionaryData = this._info.getPropertyAdditionByType("Agility");
         var _local_2:DictionaryData = this._info.getPropertyAdditionByType("Luck");
         if(!_local_1)
         {
            return;
         }
         this._attackTxt1.htmlText = this._info == null ? "" : this.getHtmlTextByString(String(_local_4["Card"] < 0 ? 0 : _local_4["Card"]),0);
         this._agilityTxt1.htmlText = this._info == null ? "" : this.getHtmlTextByString(String(_local_1["Card"] < 0 ? 0 : _local_4["Card"]),0);
         this._defenceTxt1.htmlText = this._info == null ? "" : this.getHtmlTextByString(String(_local_3["Card"] < 0 ? 0 : _local_3["Card"]),0);
         this._luckTxt1.htmlText = this._info == null ? "" : this.getHtmlTextByString(String(_local_2["Card"] < 0 ? 0 : _local_2["Card"]),0);
      }
      
      private function getHtmlTextByString(_arg_1:String, _arg_2:int) : String
      {
         var _local_4:* = null;
         var _local_3:* = null;
         switch(_arg_2)
         {
            case 0:
               _local_4 = "<TEXTFORMAT LEADING=\'-1\'><P ALIGN=\'CENTER\'><FONT FACE=\'宋体\' SIZE=\'14\' COLOR=\'#FFF6C9\' ><B>";
               _local_3 = "</B></FONT></P></TEXTFORMAT>";
               break;
            case 1:
               _local_4 = "<TEXTFORMAT LEADING=\'-1\'><P ALIGN=\'CENTER\'><FONT FACE=\'宋体\' SIZE=\'14\' COLOR=\'#FFF6C9\' LETTERSPACING=\'0\' KERNING=\'1\'><B>";
               _local_3 = "</B></FONT></P></TEXTFORMAT>";
               break;
            case 2:
               _local_4 = "<TEXTFORMAT LEADING=\'-1\'><P ALIGN=\'CENTER\'><FONT FACE=\'宋体\' SIZE=\'14\' COLOR=\'#FFF6C9\' LETTERSPACING=\'0\' KERNING=\'1\'><B>";
               _local_3 = "</B></FONT></P></TEXTFORMAT>";
         }
         return _local_4 + _arg_1 + _local_3;
      }
      
      public function dispose() : void
      {
         PowerUpMovieManager.isInPlayerInfoView = false;
         this.removeEvent();
         this.clearCells();
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
         this.removeArmShellBtn();
         ObjectUtils.disposeObject(this._attackTxt);
         this._attackTxt = null;
         ObjectUtils.disposeObject(this._agilityTxt);
         this._agilityTxt = null;
         ObjectUtils.disposeObject(this._defenceTxt);
         this._defenceTxt = null;
         ObjectUtils.disposeObject(this._luckTxt);
         this._luckTxt = null;
         ObjectUtils.disposeObject(this._magicAttackTxt);
         this._magicAttackTxt = null;
         ObjectUtils.disposeObject(this._magicDefenceTxt);
         this._magicDefenceTxt = null;
         ObjectUtils.disposeObject(this._damageTxt);
         this._damageTxt = null;
         ObjectUtils.disposeObject(this._armorTxt);
         this._armorTxt = null;
         ObjectUtils.disposeObject(this._HPText);
         this._HPText = null;
         ObjectUtils.disposeObject(this._vitality);
         this._vitality = null;
         ObjectUtils.disposeObject(this._vipIcon);
         this._vipIcon = null;
         ObjectUtils.disposeObject(this._guardCore);
         this._guardCore = null;
         ObjectUtils.disposeObject(this._iconContainer);
         this._iconContainer = null;
         this._marriedIcon = null;
         if(Boolean(this._attackButton))
         {
            ShowTipManager.Instance.removeTip(this._attackButton);
            ObjectUtils.disposeObject(this._attackButton);
            this._attackButton = null;
         }
         if(Boolean(this._agilityButton))
         {
            ShowTipManager.Instance.removeTip(this._agilityButton);
            ObjectUtils.disposeObject(this._agilityButton);
            this._agilityButton = null;
         }
         if(Boolean(this._defenceButton))
         {
            ShowTipManager.Instance.removeTip(this._defenceButton);
            ObjectUtils.disposeObject(this._defenceButton);
            this._defenceButton = null;
         }
         if(Boolean(this._luckButton))
         {
            ShowTipManager.Instance.removeTip(this._luckButton);
            ObjectUtils.disposeObject(this._luckButton);
            this._luckButton = null;
         }
         if(Boolean(this._magicAttackButton))
         {
            ShowTipManager.Instance.removeTip(this._magicAttackButton);
            ObjectUtils.disposeObject(this._magicAttackButton);
            this._magicAttackButton = null;
         }
         if(Boolean(this._magicDefenceButton))
         {
            ShowTipManager.Instance.removeTip(this._magicDefenceButton);
            ObjectUtils.disposeObject(this._magicDefenceButton);
            this._magicDefenceButton = null;
         }
         if(Boolean(this._damageButton))
         {
            ShowTipManager.Instance.removeTip(this._damageButton);
            ObjectUtils.disposeObject(this._damageButton);
            this._damageButton = null;
         }
         if(Boolean(this._armorButton))
         {
            ShowTipManager.Instance.removeTip(this._armorButton);
            ObjectUtils.disposeObject(this._armorButton);
            this._armorButton = null;
         }
         if(Boolean(this._hpButton))
         {
            ShowTipManager.Instance.removeTip(this._hpButton);
            ObjectUtils.disposeObject(this._hpButton);
            this._hpButton = null;
         }
         if(Boolean(this._vitalityBuntton))
         {
            ShowTipManager.Instance.removeTip(this._vitalityBuntton);
            ObjectUtils.disposeObject(this._vitalityBuntton);
            this._vitalityBuntton = null;
         }
         ObjectUtils.disposeObject(this._fineSuitIcon);
         this._fineSuitIcon = null;
         ObjectUtils.disposeObject(this._explorerIcon);
         this._explorerIcon = null;
         ObjectUtils.disposeObject(this._teamIcon);
         this._teamIcon = null;
         this._SywQhBtn.dispose();
         this._SywQhBtn = null;
         ObjectUtils.disposeObject(this._vipName);
         this._vipName = null;
         ObjectUtils.disposeObject(this._bg);
         this._bg = null;
         ObjectUtils.disposeObject(this._bg1);
         this._bg1 = null;
         ObjectUtils.disposeObject(this._bg11);
         this._bg11 = null;
         ObjectUtils.disposeObject(this._showEquip);
         ObjectUtils.disposeObject(this._textBg);
         this._textBg = null;
         ObjectUtils.disposeObject(this._textBg1);
         this._textBg1 = null;
         ObjectUtils.disposeObject(this._textBg2);
         this._textBg2 = null;
         ObjectUtils.disposeObject(this._textBg3);
         this._textBg3 = null;
         ObjectUtils.disposeObject(this._textBg4);
         this._textBg4 = null;
         ObjectUtils.disposeObject(this._textBg5);
         this._textBg5 = null;
         ObjectUtils.disposeObject(this._textBg6);
         this._textBg6 = null;
         ObjectUtils.disposeObject(this._bg2);
         this._bg2 = null;
         this._showEquip = null;
         ObjectUtils.disposeObject(this._showCard);
         this._showCard = null;
         ObjectUtils.disposeObject(this._cardEquipView);
         this._cardEquipView = null;
         ObjectUtils.disposeObject(this._honorNameTxt);
         this._honorNameTxt = null;
         ObjectUtils.disposeObject(this._nickNameTxt);
         this._nickNameTxt = null;
         ObjectUtils.disposeObject(this._consortiaTxt);
         this._consortiaTxt = null;
         ObjectUtils.disposeObject(this._battle);
         this._battle = null;
         ObjectUtils.disposeObject(this._character);
         this._character = null;
         ObjectUtils.disposeObject(this._characterSprite);
         this._characterSprite = null;
         ObjectUtils.disposeObject(this._progressLevel);
         this._progressLevel = null;
         ObjectUtils.disposeObject(this._reputeField);
         this._reputeField = null;
         ObjectUtils.disposeObject(this._gesteField);
         this._gesteField = null;
         ObjectUtils.disposeObject(this._dutyField);
         this._dutyField = null;
         ObjectUtils.disposeObject(this._levelIcon);
         this._levelIcon = null;
         ObjectUtils.disposeObject(this._hideGlassBtn);
         this._hideGlassBtn = null;
         ObjectUtils.disposeObject(this._hideHatBtn);
         this._hideHatBtn = null;
         ObjectUtils.disposeObject(this._hideSuitBtn);
         this._hideSuitBtn = null;
         ObjectUtils.disposeObject(this._hideWingBtn);
         this._hideWingBtn = null;
         ObjectUtils.disposeObject(this._storeBtn);
         this._storeBtn = null;
         ObjectUtils.disposeObject(this._addFriendBtn);
         this._addFriendBtn = null;
         ObjectUtils.disposeObject(this._buyAvatar);
         this._buyAvatar = null;
         ObjectUtils.disposeObject(this._bagDefinitionBtnI);
         this._bagDefinitionBtnI = null;
         ObjectUtils.disposeObject(this._bagDefinitionGroup);
         this._bagDefinitionGroup = null;
         ObjectUtils.disposeObject(this._bagDefinitionBtnII);
         this._bagDefinitionBtnII = null;
         ObjectUtils.disposeObject(this._playerInfoEffortHonorView);
         this._playerInfoEffortHonorView = null;
         ObjectUtils.disposeObject(this._cellContent);
         this._cellContent = null;
         ObjectUtils.disposeObject(this._offerLabel);
         this._offerLabel = null;
         ObjectUtils.disposeObject(this._attestBtn);
         this._attestBtn = null;
         ObjectUtils.disposeObject(this._dragDropArea);
         this._dragDropArea = null;
         ObjectUtils.disposeObject(this._openNecklacePtetrochemicalView);
         this._openNecklacePtetrochemicalView = null;
         ObjectUtils.disposeObject(this._ringSystemBtn);
         this._ringSystemBtn = null;
         ObjectUtils.disposeObject(this._amuletBtn);
         this._amuletBtn = null;
         ObjectUtils.disposeObject(this._ddtEmblemBtn);
         this._ddtEmblemBtn = null;
         ObjectUtils.disposeObject(this._forcesRelicBtn);
         this._forcesRelicBtn = null;
         ObjectUtils.disposeAllChildren(this);
         this._info = null;
         this._energyData = null;
      }
      
      public function startShine(_arg_1:ItemTemplateInfo) : void
      {
         var _local_3:int = 0;
         var _local_2:* = null;
         if(_arg_1.NeedSex == 0 || _arg_1.NeedSex == (PlayerManager.Instance.Self.Sex ? 1 : 2))
         {
            _local_2 = this.getCellIndex(_arg_1).split(",");
            _local_3 = 0;
            while(_local_3 < _local_2.length)
            {
               if(_local_2[_local_3] >= 0)
               {
                  (this._cells[_local_2[_local_3]] as PersonalInfoCell).shine();
               }
               _local_3++;
            }
         }
      }
      
      public function stopShine() : void
      {
         var _local_1:PersonalInfoCell = null;
         var _local_2:* = undefined;
         for each(_local_1 in this._cells)
         {
            (_local_1 as PersonalInfoCell).stopShine();
         }
         if(Boolean(this._cardEquipView))
         {
            _local_2 = this._cardEquipView;
            _local_2["stopShine"]();
         }
      }
      
      private function getCellIndex(_arg_1:ItemTemplateInfo) : String
      {
         if(EquipType.isWeddingRing(_arg_1))
         {
            return "9,10,16";
         }
         switch(_arg_1.CategoryID)
         {
            case 1:
               return "0";
            case 2:
               return "1";
            case 3:
               return "2";
            case 4:
               return "3";
            case 5:
               return "4";
            case 6:
               return "5";
            case 7:
               return "6";
            case 8:
            case 28:
               return "7,8";
            case 9:
            case 29:
               return "9,10";
            case 13:
               return "11";
            case 14:
               return "12";
            case 15:
               return "13";
            case 16:
               return "14";
            case 17:
               return "15";
            case 19:
            case 70:
               return "18";
            case 27:
               return "6";
            case 40:
               return "17";
            case 101:
               return "21";
            case 102:
               return "22";
            case 103:
               return "23";
            case 104:
               return "24";
            case 105:
               return "25";
            default:
               return "-1";
         }
      }
      
      public function get showSelfOperation() : Boolean
      {
         return this._showSelfOperation;
      }
      
      public function set showSelfOperation(_arg_1:Boolean) : void
      {
         this._showSelfOperation = _arg_1;
         this.updateShowOperation();
      }
      
      private function updateShowOperation() : void
      {
         this._honorNameTxt.visible = !this.showSelfOperation;
         this._playerInfoEffortHonorView.visible = this.showSelfOperation;
         this._storeBtn.visible = true;
         this._storeBtn.enable = this._showSelfOperation;
         this._buyAvatar.visible = !this._showSelfOperation && this._info != null && (this._info.ZoneID == 0 || this._info.ZoneID == PlayerManager.Instance.Self.ZoneID) && PlayerManager.Instance.Self.Grade > 2 && StateManager.currentStateType != "fighting" && StateManager.currentStateType != "fighting3d" && StateManager.currentStateType != "fightLabGameView" && StateManager.currentStateType != "trainer1" && StateManager.currentStateType != "trainer2" && StateManager.currentStateType != "hotSpringRoom" && StateManager.currentStateType != "churchRoom" && StateManager.currentStateType != "littleGame" && StateManager.currentStateType != "roomLoading";
         if(this._info is SelfInfo)
         {
            this._buyAvatar.visible = false;
         }
         else if(Boolean(this._info))
         {
            this.createVipAndKing();
         }
         var _local_1:* = this._showSelfOperation;
         this._hideWingBtn.visible = _local_1;
         this._hideSuitBtn.visible = _local_1;
         this._hideHatBtn.visible = _local_1;
         this._hideGlassBtn.visible = _local_1;
         this._addFriendBtn.visible = !this._showSelfOperation && this._info != null && this._info.ID != PlayerManager.Instance.Self.ID && (this._info.ZoneID == 0 || this._info.ZoneID == PlayerManager.Instance.Self.ZoneID);
         _local_1 = this._showSelfOperation && this._showEquip.visible;
         this._amuletBtn.visible = _local_1;
         this._ringSystemBtn.visible = _local_1;
         this._openNecklacePtetrochemicalView.visible = _local_1;
         if(StateManager.currentStateType == "fighting" || StateManager.currentStateType == "fighting3d" || StateManager.currentStateType == "fightLabGameView")
         {
            if(Boolean(this._openNecklacePtetrochemicalView.parent))
            {
               this._openNecklacePtetrochemicalView.parent.removeChild(this._openNecklacePtetrochemicalView);
            }
            if(Boolean(this._ringSystemBtn.parent))
            {
               this._ringSystemBtn.parent.removeChild(this._ringSystemBtn);
            }
            if(Boolean(this._amuletBtn.parent))
            {
               this._amuletBtn.parent.removeChild(this._amuletBtn);
            }
         }
         else
         {
            if(!this._openNecklacePtetrochemicalView.parent && Boolean(PlayerManager.Instance.Self.Bag.items[12]))
            {
               addChild(this._openNecklacePtetrochemicalView);
            }
            if(!this._ringSystemBtn.parent && Boolean(PlayerManager.Instance.Self.Bag.items[16]))
            {
               addChild(this._ringSystemBtn);
            }
            if(!this._amuletBtn.parent && Boolean(PlayerManager.Instance.Self.Bag.items[18]))
            {
               addChild(this._amuletBtn);
            }
         }
         if(!this._info || this._info.ID != PlayerManager.Instance.Self.ID || !this._showSelfOperation)
         {
            this._bagDefinitionBtnI.visible = false;
            this._bagDefinitionBtnII.visible = false;
            return;
         }
         this._bagDefinitionBtnI.visible = true;
         this._bagDefinitionBtnII.visible = true;
         if(Boolean(this._info))
         {
            if(this._info.IsShowConsortia && Boolean(this._info.ConsortiaName))
            {
               this._bagDefinitionGroup.selectIndex = 1;
            }
            else if(!this._info.IsShowConsortia && EffortManager.Instance.getHonorArray().length > 0)
            {
               this._bagDefinitionGroup.selectIndex = 0;
            }
            else if(!this._info.IsShowConsortia && Boolean(this._info.ConsortiaName))
            {
               this._bagDefinitionGroup.selectIndex = 1;
            }
            else if(this._info.IsShowConsortia && EffortManager.Instance.getHonorArray().length > 0)
            {
               this._bagDefinitionGroup.selectIndex = 0;
            }
            else
            {
               this._bagDefinitionBtnI.visible = false;
               this._bagDefinitionBtnII.visible = false;
            }
         }
      }
      
      private function createVipAndKing() : void
      {
         if(this._info.IsVIP)
         {
            if(this._vipIcon == null)
            {
               this._vipIcon = ComponentFactory.Instance.creatCustomObject("asset.bagAndInfo.VipIcon");
               this._iconContainer.addChild(this._vipIcon);
            }
            this._vipIcon.setInfo(this._info);
            if(!this._info.IsVIP)
            {
               this._vipIcon.filters = ComponentFactory.Instance.creatFilters("grayFilter");
            }
            else
            {
               this._vipIcon.filters = null;
            }
         }
         else if(Boolean(this._vipIcon))
         {
            this._vipIcon.dispose();
            this._vipIcon = null;
         }
         if(Boolean(this._marriedIcon))
         {
            if(this._iconContainer.contains(this._marriedIcon))
            {
               this._iconContainer.removeChild(this._marriedIcon);
               this._iconContainer.addChild(this._marriedIcon);
            }
         }
         if(Boolean(this._guardCore))
         {
            if(this._iconContainer.contains(this._guardCore))
            {
               this._iconContainer.removeChild(this._guardCore);
               this._iconContainer.addChild(this._guardCore);
            }
         }
      }
      
      private function getShowAcademyIcon() : Boolean
      {
         if(StateManager.currentStateType == "fighting" || StateManager.currentStateType == "fighting3d" || StateManager.currentStateType == "fightLabGameView")
         {
            if(this._info.apprenticeshipState != 0)
            {
               return true;
            }
            return false;
         }
         if(this._info.ID == PlayerManager.Instance.Self.ID)
         {
            return true;
         }
         if(this._info.apprenticeshipState != 0)
         {
            return true;
         }
         return false;
      }
      
      public function setAchvEnable(_arg_1:Boolean) : void
      {
         this._achvEnable = _arg_1;
         this.updateShowOperation();
      }
      
      private function _definitionGroupChange(_arg_1:Event = null) : void
      {
         if(_arg_1 != null)
         {
            SoundManager.instance.play("008");
         }
         var _local_2:Array = EffortManager.Instance.getHonorArray();
         if(_local_2.length < 1 && !this._info.ConsortiaName)
         {
            this._bagDefinitionBtnI.visible = false;
            this._bagDefinitionBtnII.visible = false;
            return;
         }
         if(this._bagDefinitionGroup.selectIndex == 0)
         {
            if(_local_2.length < 1)
            {
               if(Boolean(_arg_1))
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.bagInfo.notDesignation"));
               }
               this._bagDefinitionGroup.selectIndex = 1;
            }
            else if(Boolean(_arg_1))
            {
               PlayerManager.Instance.Self.IsShowConsortia = false;
               SocketManager.Instance.dispatchEvent(new NewHallEvent("newhallupdatetitle"));
            }
         }
         else if(!this._info.ConsortiaName)
         {
            if(Boolean(_arg_1))
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.bagInfo.notSociaty"));
            }
            this._bagDefinitionGroup.selectIndex = 0;
         }
         else if(Boolean(_arg_1))
         {
            PlayerManager.Instance.Self.IsShowConsortia = true;
            SocketManager.Instance.dispatchEvent(new NewHallEvent("newhallupdatetitle"));
         }
         if(Boolean(_arg_1))
         {
            SocketManager.Instance.out.sendChangeDesignation(PlayerManager.Instance.Self.IsShowConsortia);
         }
      }
      
      public function get openNecklacePtetrochemicalView() : SimpleBitmapButton
      {
         return this._openNecklacePtetrochemicalView;
      }
      
      public function set openNecklacePtetrochemicalView(_arg_1:SimpleBitmapButton) : void
      {
         this._openNecklacePtetrochemicalView = _arg_1;
      }
      
      private function __onClickDDTHonor(_arg_1:MouseEvent) : void
      {
         if(StateManager.currentStateType == "fighting" || StateManager.currentStateType == "fighting3d")
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("战斗中不可开启"));
            return;
         }
         SoundManager.instance.playButtonSound();
         DDTHonorManager.instance.show(this._info.ID);
         BagAndInfoManager.Instance.hideBagAndInfo();
      }
   }
}

