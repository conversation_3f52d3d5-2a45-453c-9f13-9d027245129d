package com.pickgliss.effect
{
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.utils.EffectUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.geom.Matrix;
   
   public class ShinerAnimation extends BaseEffect
   {
      
      public static const SPEED:String = "speed";
      
      public static const INTENSITY:String = "intensity";
      
      public static const WIDTH:String = "width";
      
      public static const EFFECT:String = "effect";
      
      public static const COLOR:String = "color";
      
      public static const BLUR_WIDTH:String = "blurWidth";
      
      public static const IS_LOOP:String = "isLoop";
      
      private var _addGlowEffect:Boolean = true;
      
      private var _alphas:Array;
      
      private var _colors:Array;
      
      private var _currentPosition:Number;
      
      private var _endPosition:Number;
      
      private var _isFinish:Boolean;
      
      private var _glowBlurWidth:Number = 3;
      
      private var _glowColorName:String = "blue";
      
      private var _isLoop:Boolean = true;
      
      private var _maskHeight:Number;
      
      private var _maskShape:Shape = new Shape();
      
      private var _maskWidth:Number;
      
      private var _percent:Array;
      
      private var _shineAnimationContainer:Sprite;
      
      private var _sourceBitmap:Bitmap;
      
      private var _shineBitmapContainer:Sprite;
      
      private var _shineIntensity:Number = 30;
      
      private var _shineMoveSpeed:Number = 15;
      
      private var _shineWidth:Number = 100;
      
      private var _startPosition:Number;
      
      public function ShinerAnimation(_arg_1:int)
      {
         super(_arg_1);
      }
      
      override public function dispose() : void
      {
         StageReferance.stage.removeEventListener("enterFrame",this.onRenderAnimation);
         ObjectUtils.disposeObject(this._shineAnimationContainer);
         ObjectUtils.disposeObject(this._sourceBitmap);
         ObjectUtils.disposeObject(this._shineBitmapContainer);
         this._shineAnimationContainer = null;
         this._sourceBitmap = null;
         this._shineBitmapContainer = null;
         super.dispose();
      }
      
      override public function initEffect(_arg_1:DisplayObject, _arg_2:Array) : void
      {
         super.initEffect(_arg_1,_arg_2);
         var _local_3:Object = _arg_2[0];
         if(Boolean(_local_3))
         {
            if(Boolean(_local_3["speed"]))
            {
               this._shineMoveSpeed = _local_3["speed"];
            }
            if(Boolean(_local_3["intensity"]))
            {
               this._shineIntensity = _local_3["intensity"];
            }
            if(Boolean(_local_3["width"]))
            {
               this._shineWidth = _local_3["width"];
            }
            if(Boolean(_local_3["effect"]))
            {
               this._addGlowEffect = _local_3["effect"];
            }
            if(Boolean(_local_3["color"]))
            {
               this._glowColorName = _local_3["color"];
            }
            if(Boolean(_local_3["blurWidth"]))
            {
               this._glowBlurWidth = _local_3["blurWidth"];
            }
            if(Boolean(_local_3["isLoop"]))
            {
               this._isLoop = _local_3["isLoop"];
            }
         }
         this.image_shiner(this._shineMoveSpeed,this._shineIntensity,this._shineWidth,this._addGlowEffect,this._glowColorName,this._glowBlurWidth,this._isLoop);
      }
      
      override public function play() : void
      {
         super.play();
         DisplayObjectContainer(target).addChild(this._shineAnimationContainer);
         StageReferance.stage.addEventListener("enterFrame",this.onRenderAnimation);
      }
      
      override public function stop() : void
      {
         super.stop();
         if(!this._shineAnimationContainer.parent)
         {
            return;
         }
         this._shineAnimationContainer.parent.removeChild(this._shineAnimationContainer);
         StageReferance.stage.removeEventListener("enterFrame",this.onRenderAnimation);
      }
      
      private function image_shiner(_arg_1:Number, _arg_2:Number, _arg_3:Number, _arg_4:Boolean, _arg_5:String, _arg_6:Number, _arg_7:Boolean) : void
      {
         this._shineAnimationContainer = new Sprite();
         this._shineBitmapContainer = new Sprite();
         this._sourceBitmap = EffectUtils.creatMcToBitmap(target,16711680);
         this._shineBitmapContainer.addChild(this._sourceBitmap);
         this._shineAnimationContainer.addChild(this._shineBitmapContainer);
         EffectUtils.imageShiner(this._shineAnimationContainer,_arg_2);
         EffectUtils.imageGlower(this._shineBitmapContainer,1,_arg_6,15,_arg_5);
         this.linear_fade(_arg_3,_arg_1,60);
      }
      
      private function linear_fade(_arg_1:Number, _arg_2:Number, _arg_3:Number) : void
      {
         this._maskShape.cacheAsBitmap = true;
         this._shineAnimationContainer.cacheAsBitmap = true;
         this._shineAnimationContainer.mask = this._maskShape;
         this._maskWidth = this._shineAnimationContainer.width + _arg_3;
         this._maskHeight = this._shineAnimationContainer.height + _arg_3;
         this._maskShape.x = this._shineAnimationContainer.x - _arg_3 / 2;
         this._maskShape.y = this._shineAnimationContainer.y - _arg_3 / 2;
         this._colors = [16777215,16777215,16777215];
         this._alphas = [0,100,0];
         this._percent = [0,127,255];
         this._startPosition = -(_arg_1 + _arg_3);
         this._currentPosition = this._startPosition;
         this._endPosition = this._shineAnimationContainer.width + _arg_1 + _arg_3;
         this._shineAnimationContainer.addChild(this._maskShape);
      }
      
      private function onRenderAnimation(_arg_1:Event) : void
      {
         this._maskShape.graphics.clear();
         var _local_2:Matrix = new Matrix();
         _local_2.createGradientBox(this._shineWidth,this._maskHeight,EffectUtils.toRadian(45),this._currentPosition,0);
         this._maskShape.graphics.beginGradientFill("linear",this._colors,this._alphas,this._percent,_local_2,"pad","linearRGB");
         this._maskShape.graphics.drawRect(0,0,this._maskWidth,this._maskHeight);
         this._maskShape.graphics.endFill();
         if(this._endPosition > this._currentPosition)
         {
            this._currentPosition += this._shineMoveSpeed;
         }
         else
         {
            this._isFinish = true;
            if(this._isLoop)
            {
               this._currentPosition = this._startPosition;
            }
            else
            {
               StageReferance.stage.removeEventListener("enterFrame",this.onRenderAnimation);
            }
         }
      }
   }
}

