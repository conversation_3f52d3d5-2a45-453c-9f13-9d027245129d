package cardNewSystem.data
{
   import flash.utils.Dictionary;
   
   public class CardModel
   {
      
      public var isInitData:Boolean = false;
      
      private var _cardMainInfo:CardMainInfo;
      
      public var cardMainTempInfos:Array;
      
      public var cardSuitTempInfos:Array;
      
      public var cardBookTempInfos:Array;
      
      public var hasCards:Dictionary;
      
      public var suitDic:Dictionary;
      
      public var suitPro:String;
      
      public var suitLevel:int;
      
      public var isOpenTen:Boolean;
      
      public function CardModel()
      {
         super();
         this._cardMainInfo = new CardMainInfo();
         this.hasCards = new Dictionary();
         this.suitDic = new Dictionary();
      }
      
      public function get cardMainInfo() : CardMainInfo
      {
         return this._cardMainInfo;
      }
      
      public function set cardMainInfo(_arg_1:CardMainInfo) : void
      {
         this._cardMainInfo = _arg_1;
      }
      
      public function getCardMainTempInfoByLevel(_arg_1:int) : CardMainTemplateInfo
      {
         var _local_2:int = 0;
         _local_2 = 0;
         while(_local_2 < this.cardMainTempInfos.length)
         {
            if((this.cardMainTempInfos[_local_2] as CardMainTemplateInfo).Level == _arg_1)
            {
               return this.cardMainTempInfos[_local_2];
            }
            _local_2++;
         }
         return null;
      }
      
      public function getCardTemplateInfoById(_arg_1:int, _arg_2:int) : CardBookTempInfo
      {
         var _local_3:int = 0;
         _local_3 = 0;
         while(_local_3 < this.cardBookTempInfos.length)
         {
            if((this.cardBookTempInfos[_local_3] as CardBookTempInfo).TemplateId == _arg_1 && (this.cardBookTempInfos[_local_3] as CardBookTempInfo).Profile == _arg_2)
            {
               return this.cardBookTempInfos[_local_3];
            }
            _local_3++;
         }
         return null;
      }
      
      public function getUseSuitId() : int
      {
         var _local_1:int = 0;
         var _local_2:* = undefined;
         for(_local_2 in this.suitDic)
         {
            if(this.suitDic[_local_2] == true)
            {
               _local_1 = _local_2;
            }
         }
         return _local_1;
      }
      
      public function getCardSuitTempInfoByid(_arg_1:int) : CardSuitTempInfo
      {
         var _local_2:int = 0;
         var _local_3:* = null;
         _local_2 = 0;
         while(_local_2 < this.cardSuitTempInfos.length)
         {
            if((this.cardSuitTempInfos[_local_2] as CardSuitTempInfo).SuitTemplateId == _arg_1)
            {
               _local_3 = this.cardSuitTempInfos[_local_2];
            }
            _local_2++;
         }
         return _local_3;
      }
      
      public function initPriSuitInfos() : void
      {
         var _local_4:int = 0;
         var _local_1:int = 0;
         var _local_3:* = null;
         var _local_2:* = null;
         _local_4 = 0;
         while(_local_4 < this.cardSuitTempInfos.length)
         {
            _local_3 = this.cardSuitTempInfos[_local_4];
            _local_2 = _local_3.NeedCardTempIds.split(",");
            if(this.suitDic[_local_3.SuitTemplateId] != null)
            {
               _local_3.isAlive = true;
               _local_3.profile = this.getSuitProfile(_local_2);
            }
            else
            {
               _local_3.isAlive = false;
               _local_1 = this.getSuitCountByNeed(_local_2);
               _local_3.canAlive = _local_1 >= _local_2.length;
               _local_3.hasNum = _local_1;
               if(Boolean(_local_3.canAlive))
               {
                  _local_3.profile = this.getSuitProfile(_local_2);
               }
            }
            _local_4++;
         }
         this.cardSuitTempInfos.sortOn(["isAlive","canAlive","hasNum","SuitTemplateId"],2);
      }
      
      public function getSuitProfile(_arg_1:Array) : int
      {
         var _local_9:int = 0;
         var _local_7:int = 0;
         var _local_5:int = 0;
         var _local_4:int = 0;
         var _local_8:* = null;
         var _local_6:* = null;
         if(_arg_1.length <= 0)
         {
            return 3;
         }
         var _local_3:Boolean = true;
         var _local_2:int = 1;
         _local_9 = 0;
         while(_local_9 < _arg_1.length)
         {
            _local_8 = this.hasCards[_arg_1[_local_9]];
            _local_7 = int(_local_8.Profile);
            if(_local_7 < 4)
            {
               _local_3 = false;
               break;
            }
            _local_9++;
         }
         if(_local_3)
         {
            return 4;
         }
         _local_5 = 0;
         while(_local_5 < _arg_1.length)
         {
            _local_6 = this.hasCards[_arg_1[_local_5]];
            _local_4 = int(_local_6.Profile);
            if(_local_4 != 4 && _local_4 > _local_2)
            {
               _local_2 = _local_4;
            }
            _local_5++;
         }
         return _local_2;
      }
      
      public function isCanAliveSuit(_arg_1:Array) : Boolean
      {
         var _local_2:int = 0;
         _local_2 = 0;
         while(_local_2 < _arg_1.length)
         {
            if(!this.hasCards[_arg_1[_local_2]])
            {
               return false;
            }
            _local_2++;
         }
         return true;
      }
      
      public function getSuitCountByNeed(_arg_1:Array) : int
      {
         var _local_2:int = 0;
         var _local_3:int = 0;
         _local_3 = 0;
         while(_local_3 < _arg_1.length)
         {
            if(Boolean(this.hasCards[_arg_1[_local_3]]))
            {
               _local_2++;
            }
            _local_3++;
         }
         return _local_2;
      }
   }
}

