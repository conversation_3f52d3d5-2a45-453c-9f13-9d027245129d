package com.pickgliss.geom
{
   public class LockDirectionTypes
   {
      
      public static const LOCK_B:int = 4;
      
      public static const LOCK_L:int = 2;
      
      public static const LOCK_R:int = 3;
      
      public static const LOCK_T:int = 1;
      
      public static const UNLOCK:int = 0;
      
      public static const NO_SCALE_T:int = 9;
      
      public static const NO_SCALE_B:int = 10;
      
      public static const NO_SCALE_L:int = 11;
      
      public static const NO_SCALE_R:int = 12;
      
      public static const NO_SCALE_TL:int = 13;
      
      public static const NO_SCALE_TR:int = 14;
      
      public static const NO_SCALE_BL:int = 15;
      
      public static const NO_SCALE_BR:int = 16;
      
      public static const NO_SCALE_C:int = 17;
      
      public static const UNLOCK_OUTSIDE:int = -1;
      
      public static const LOCK_TL:int = 5;
      
      public static const LOCK_TR:int = 6;
      
      public static const LOCK_OUTER_DOWN:int = 18;
      
      public function LockDirectionTypes()
      {
         super();
      }
   }
}

