package auditorium
{
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ClassUtils;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.utils.AssetModuleLoader;
   import flash.display.Sprite;
   
   public class AuditoriumManager
   {
      
      private static var _instance:AuditoriumManager;
      
      public function AuditoriumManager()
      {
         super();
      }
      
      public static function get instance() : AuditoriumManager
      {
         if(_instance == null)
         {
            _instance = new AuditoriumManager();
         }
         return _instance;
      }
      
      public function showAuditoriumFrame() : void
      {
         if(!this.isCanOpen)
         {
            return;
         }
         AssetModuleLoader.addModelLoader("auditorium",5);
         AssetModuleLoader.startCodeLoader(this.loadComplete);
      }
      
      private function loadComplete() : void
      {
         var _local_1:Sprite = ClassUtils.CreatInstance("auditorium.view.AuditoriumView");
         LayerManager.Instance.addToLayer(_local_1,2,true,1);
      }
      
      private function get isCanOpen() : Boolean
      {
         if(PlayerManager.Instance.Self.Grade < 14)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",14));
            return false;
         }
         return true;
      }
      
      public function enterSetup() : void
      {
      }
   }
}

