package com.pickgliss.loader
{
   import com.pickgliss.utils.ClassUtils;
   import flash.system.ApplicationDomain;
   import road7th.data.DictionaryData;
   
   public class CodeModuleLoader extends ModuleLoader
   {
      
      private static var LoadClassName:DictionaryData = new DictionaryData();
      
      public var className:String;
      
      public function CodeModuleLoader(_arg_1:int, _arg_2:String, _arg_3:ApplicationDomain)
      {
         super(_arg_1,_arg_2,_arg_3);
      }
      
      override protected function fireCompleteEvent() : void
      {
         var _local_1:* = undefined;
         var _local_2:* = undefined;
         if(!LoadClassName.hasKey(this.className))
         {
            _local_1 = ClassUtils.CreatInstance(this.className);
            if(!_local_1)
            {
               throw new Error("CodeModuleLoader :: 代码加载出错!!!!立刻检查!!" + _url);
            }
            CodeLoader.addLoadURL("4.png");
            _local_2 = _local_1;
            _local_2["setup"]();
            LoadClassName.add(this.className,true);
         }
         super.fireCompleteEvent();
      }
      
      override public function get type() : int
      {
         return 9;
      }
   }
}

