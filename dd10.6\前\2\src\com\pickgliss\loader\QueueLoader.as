package com.pickgliss.loader
{
   import com.pickgliss.ui.core.Disposeable;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   
   [Event(name="complete",type="flash.events.Event")]
   [Event(name="change",type="flash.events.Event")]
   [Event(name="progress",type="flash.events.Event")]
   public class QueueLoader extends EventDispatcher implements Disposeable
   {
      
      public static const PROGRESS:String = "progress";
      
      private var _loaders:Vector.<BaseLoader>;
      
      private var _progress:Number;
      
      private var _allProgress:Number;
      
      public function QueueLoader()
      {
         super();
         this._loaders = new Vector.<BaseLoader>();
      }
      
      public function addLoader(_arg_1:BaseLoader) : void
      {
         this._loaders.push(_arg_1);
      }
      
      public function dispose() : void
      {
         this.removeEvent();
         this._loaders = null;
      }
      
      public function removeEvent() : void
      {
         var _local_1:int = 0;
         _local_1 = 0;
         while(_local_1 < this._loaders.length)
         {
            this._loaders[_local_1].removeEventListener("complete",this.__loadNext);
            _local_1++;
         }
      }
      
      public function isAllComplete() : Boolean
      {
         var _local_1:int = 0;
         _local_1 = 0;
         while(_local_1 < this._loaders.length)
         {
            if(!this._loaders[_local_1].isComplete)
            {
               return false;
            }
            _local_1++;
         }
         return true;
      }
      
      public function isLoading() : Boolean
      {
         var _local_1:int = 0;
         _local_1 = 0;
         while(_local_1 < this._loaders.length)
         {
            if(this._loaders[_local_1].isLoading)
            {
               return true;
            }
            _local_1++;
         }
         return false;
      }
      
      public function get completeCount() : int
      {
         var _local_2:int = 0;
         var _local_1:int = 0;
         _local_2 = 0;
         while(_local_2 < this._loaders.length)
         {
            if(this._loaders[_local_2].isComplete)
            {
               _local_1++;
            }
            _local_2++;
         }
         return _local_1;
      }
      
      public function get length() : int
      {
         return this._loaders.length;
      }
      
      public function get loaders() : Vector.<BaseLoader>
      {
         return this._loaders;
      }
      
      public function start() : void
      {
         this.tryLoadNext();
      }
      
      private function __loadNext(_arg_1:LoaderEvent) : void
      {
         var _local_2:BaseLoader = _arg_1.loader as BaseLoader;
         _local_2.removeEventListener("complete",this.__loadNext);
         _local_2.removeEventListener("progress",this.__progress);
         dispatchEvent(new Event("change"));
         this.tryLoadNext();
      }
      
      public function get progress() : Number
      {
         return this._progress;
      }
      
      public function get allProgress() : Number
      {
         return this._allProgress;
      }
      
      private function __progress(_arg_1:LoaderEvent) : void
      {
         var _local_2:int = 0;
         var _local_4:int = 0;
         var _local_3:Number = NaN;
         if(Boolean(this._loaders))
         {
            this._progress = _arg_1.loader.progress;
            _local_2 = int(this._loaders.length);
            _local_4 = this.completeCount;
            _local_3 = (_local_4 / _local_2 + 1 / _local_2 * _arg_1.loader.progress) * 100;
            this._allProgress = _local_3;
            dispatchEvent(new Event("progress"));
         }
      }
      
      private function tryLoadNext() : void
      {
         var _local_2:int = 0;
         if(this._loaders == null)
         {
            return;
         }
         var _local_1:int = int(this._loaders.length);
         _local_2 = 0;
         while(_local_2 < _local_1)
         {
            if(!this._loaders[_local_2].isComplete)
            {
               this._loaders[_local_2].addEventListener("complete",this.__loadNext);
               this._loaders[_local_2].addEventListener("progress",this.__progress);
               LoadResourceManager.Instance.startLoad(this._loaders[_local_2]);
               return;
            }
            _local_2++;
         }
         dispatchEvent(new Event("complete"));
      }
   }
}

