package bagAndInfo
{
   import bagAndInfo.bag.ring.data.RingDataAnalyzer;
   import bagAndInfo.bag.ring.data.RingSystemData;
   import bagAndInfo.bag.trailelite.data.TrailEliteModel;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.ui.vo.AlertInfo;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.events.CrazyTankSocketEvent;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.AssetModuleLoader;
   import ddt.view.bossbox.AwardsView;
   import explorerManual.ExplorerManualManager;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.utils.Dictionary;
   import game.GameManager;
   import honor.HonorManager;
   import road7th.comm.PackageIn;
   
   public class BagAndInfoManager extends EventDispatcher
   {
      
      private static var _instance:BagAndInfoManager;
      
      public static const BAGANDINFO:int = 0;
      
      public static const TEXPVIEW:int = 1;
      
      public static const CARDVIEW:int = 2;
      
      public static const TOTEMVIEW:int = 3;
      
      public static const BEADVIEW:int = 4;
      
      public static const AVATARCOLLECTIONVIEW:int = 5;
      
      public static const MAGICSTONEVIEW:int = 6;
      
      public static const HOME_TEMPLE:int = 7;
      
      public static const MARK:int = 8;
      
      public var RingData:Dictionary;
      
      public var isInBagAndInfoView:Boolean;
      
      public var isUpgradePack:Boolean;
      
      private var _observerDictionary:Dictionary;
      
      public var trialEliteModel:TrailEliteModel;
      
      private var _bagAndGiftFrame:BagAndGiftFrame;
      
      private var _frame:BaseAlerFrame;
      
      private var _type:int = 0;
      
      private var infos:Array;
      
      private var name:String;
      
      private var bagtype:int = 0;
      
      public function BagAndInfoManager(_arg_1:SingletonForce)
      {
         super();
         this._observerDictionary = new Dictionary();
         this.trialEliteModel = new TrailEliteModel();
      }
      
      public static function get Instance() : BagAndInfoManager
      {
         if(_instance == null)
         {
            _instance = new BagAndInfoManager(new SingletonForce());
         }
         return _instance;
      }
      
      public function get isShown() : Boolean
      {
         if(!this._bagAndGiftFrame)
         {
            return false;
         }
         return true;
      }
      
      public function getBagAndGiftFrame() : BagAndGiftFrame
      {
         return this._bagAndGiftFrame;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(63),this.__openPreviewListFrame);
         SocketManager.Instance.addEventListener("trail_elite",this._trailEliteHanlder);
      }
      
      private function _trailEliteHanlder(_arg_1:CrazyTankSocketEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_3.readInt();
         switch(_local_2)
         {
            case 3:
               this.trialEliteModel.isOpen = _local_3.readBoolean();
               this.trialEliteModel.lastDays = _local_3.readInt();
               return;
            case 1:
            case 2:
               this.trialEliteModel.isOpen = _local_3.readBoolean();
               this.trialEliteModel.battleRank = _local_3.readInt();
               PlayerManager.Instance.Self.trailEliteLevel = this.trialEliteModel.battleRank;
               this.trialEliteModel.battleScore = _local_3.readInt();
               this.trialEliteModel.totalCount = _local_3.readInt();
               this.trialEliteModel.totalWin = _local_3.readInt();
               this.trialEliteModel.rankUpCount = _local_3.readInt();
               this.trialEliteModel.rankUpWin = _local_3.readInt();
               this.trialEliteModel.isRankUp = _local_3.readInt();
               this.trialEliteModel.lastDays = _local_3.readInt();
         }
      }
      
      public function registerOnPreviewFrameCloseHandler(_arg_1:String, _arg_2:Function) : void
      {
         if(this._observerDictionary[_arg_1] != null)
         {
            return;
         }
         this._observerDictionary[_arg_1] = _arg_2;
      }
      
      public function unregisterOnPreviewFrameCloseHandler(_arg_1:String) : void
      {
         if(this._observerDictionary[_arg_1] != null)
         {
            delete this._observerDictionary[_arg_1];
         }
      }
      
      protected function __openPreviewListFrame(_arg_1:PkgEvent) : void
      {
         var _local_11:int = 0;
         var _local_4:int = 0;
         var _local_10:* = null;
         var _local_8:PackageIn = _arg_1.pkg;
         _local_8.position = 20;
         var _local_2:String = _local_8.readUTF();
         var _local_5:int = _local_8.readInt();
         this.infos = [];
         _local_11 = 0;
         while(_local_11 < _local_5)
         {
            _local_10 = new InventoryItemInfo();
            _local_10.TemplateID = _local_8.readInt();
            _local_10 = ItemManager.fill(_local_10);
            _local_10.Count = _local_8.readInt();
            _local_10.IsBinds = _local_8.readBoolean();
            _local_10.ValidDate = _local_8.readInt();
            _local_10.StrengthenLevel = _local_8.readInt();
            _local_10.AttackCompose = _local_8.readInt();
            _local_10.DefendCompose = _local_8.readInt();
            _local_10.AgilityCompose = _local_8.readInt();
            _local_10.LuckCompose = _local_8.readInt();
            if(EquipType.isMagicStone(_local_10.CategoryID))
            {
               _local_10.Level = _local_10.StrengthenLevel;
               _local_10.Attack = _local_10.AttackCompose;
               _local_10.Defence = _local_10.DefendCompose;
               _local_10.Agility = _local_10.AgilityCompose;
               _local_10.Luck = _local_10.LuckCompose;
               _local_10.Level = _local_10.StrengthenLevel;
               _local_10.MagicAttack = _local_8.readInt();
               _local_10.MagicDefence = _local_8.readInt();
            }
            else
            {
               _local_8.readInt();
               _local_8.readInt();
            }
            _local_10.Hole1 = _local_8.readInt();
            _local_10.ItemID = _local_8.readInt();
            this.infos.push(_local_10);
            _local_11++;
         }
         var _local_6:int = _local_8.readInt();
         var _local_9:int = _local_8.readInt();
         var _local_7:int = _local_8.readInt();
         var _local_3:Array = [];
         _local_4 = 0;
         while(_local_4 < _local_7)
         {
            _local_3.push(_local_8.readInt());
            _local_4++;
         }
         if(_local_3.length > 0)
         {
            ExplorerManualManager.instance.cachNewChapter = _local_3;
         }
         if(_local_9 == 72 || _local_9 == 71)
         {
            this.explorerManualPrompt(_local_6,_local_2);
         }
         else
         {
            this.infos = this.mergeInfos(this.infos);
            this.showPreviewFrame(_local_2,this.infos);
         }
      }
      
      private function explorerManualPrompt(_arg_1:int, _arg_2:String) : void
      {
         var _local_3:String = LanguageMgr.GetTranslation("explorerManual.manualOpen.goodPrompt",_arg_1,_arg_2);
         MessageTipManager.getInstance().show(_local_3,0,true);
      }
      
      private function mergeInfos(_arg_1:Array) : Array
      {
         var _local_8:int = 0;
         var _local_2:InventoryItemInfo = null;
         var _local_6:* = null;
         var _local_3:Dictionary = new Dictionary();
         var _local_5:Array = [];
         var _local_7:int = int(this.infos.length);
         _local_8 = 0;
         while(_local_8 < _local_7)
         {
            _local_6 = this.infos[_local_8];
            if(_local_6.CategoryID == 69)
            {
               _local_5.push(_local_6);
            }
            else if(_local_3[_local_6.TemplateID] == null)
            {
               _local_3[_local_6.TemplateID] = this.infos[_local_8];
            }
            else
            {
               _local_3[_local_6.TemplateID].Count += this.infos[_local_8].Count;
            }
            _local_8++;
         }
         _arg_1.length = 0;
         _arg_1 = null;
         var _local_4:Array = [];
         for each(_local_2 in _local_3)
         {
            _local_4.push(_local_2);
         }
         return _local_4.concat(_local_5);
      }
      
      public function showPreviewFrame(_arg_1:String, _arg_2:Array) : BaseAlerFrame
      {
         var _local_5:AwardsView = new AwardsView();
         _local_5.goodsList = _arg_2;
         _local_5.boxType = 4;
         var _local_4:FilterFrameText = ComponentFactory.Instance.creat("wtm.awardsFFT");
         if(this.isUpgradePack)
         {
            this.isUpgradePack = false;
            _local_4.text = LanguageMgr.GetTranslation("ddt.bagandinfo.awardsTitle2");
            _local_4.x = 30;
         }
         else
         {
            _local_4.text = LanguageMgr.GetTranslation("ddt.bagandinfo.awardsTitle");
            _local_4.x = 81;
         }
         this._frame = ComponentFactory.Instance.creatComponentByStylename("wtm.ItemPreviewListFrame");
         var _local_3:AlertInfo = new AlertInfo(_arg_1);
         _local_3.showCancel = false;
         _local_3.moveEnable = false;
         this._frame.info = _local_3;
         this._frame.addToContent(_local_5);
         this._frame.addToContent(_local_4);
         this._frame.addEventListener("response",this.__frameClose);
         LayerManager.Instance.addToLayer(this._frame,3,true,1);
         return this._frame;
      }
      
      private function __frameClose(_arg_1:FrameEvent) : void
      {
         var _local_3:* = undefined;
         var _local_4:* = null;
         var _local_2:* = null;
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               SoundManager.instance.play("008");
               _local_4 = _arg_1.currentTarget as BaseAlerFrame;
               _local_4.removeEventListener("response",this.__frameClose);
               _local_2 = this.infos;
               _local_4.dispose();
               SocketManager.Instance.out.sendClearStoreBag();
               for each(_local_3 in this._observerDictionary)
               {
                  _local_3(_local_2);
               }
               this.infos = null;
         }
      }
      
      public function showBagAndInfo(_arg_1:int = 0, _arg_2:String = "", _arg_3:int = 0) : void
      {
         this._type = _arg_1;
         this.name = _arg_2;
         this.bagtype = _arg_3;
         if(Boolean(this._bagAndGiftFrame))
         {
            this._bagAndGiftFrame.show(_arg_1);
            dispatchEvent(new Event("open"));
         }
         else
         {
            this.loadModule();
         }
      }
      
      private function loadModule() : void
      {
         if(!HonorManager.ins.isLoadHonorTempData)
         {
            AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatHonorTempLoader());
         }
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatSywStoreInfoLoader());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatTexpExpLoader());
         AssetModuleLoader.addModelLoader("ddtbagandinfo",6);
         AssetModuleLoader.addModelLoader("ddtbead",6);
         AssetModuleLoader.addModelLoader("gemstone",6);
         AssetModuleLoader.addModelLoader("ddtstore",6);
         AssetModuleLoader.addModelLoader("uigeneral",7);
         AssetModuleLoader.startCodeLoader(this.createBagAndGiftFrame);
      }
      
      private function createBagAndGiftFrame() : void
      {
         this._bagAndGiftFrame = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame");
         this._bagAndGiftFrame.show(this._type);
         dispatchEvent(new Event("open"));
      }
      
      private function createFrame() : void
      {
         this._bagAndGiftFrame = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame");
         if(GameManager.exploreOver)
         {
            this._bagAndGiftFrame.show(8,this.name,this.bagtype);
         }
         else
         {
            this._bagAndGiftFrame.show(this._type,this.name,this.bagtype);
         }
         dispatchEvent(new Event("open"));
      }
      
      public function hideBagAndInfo() : void
      {
         if(Boolean(this._bagAndGiftFrame))
         {
            this._bagAndGiftFrame.dispose();
            this._bagAndGiftFrame = null;
            dispatchEvent(new Event("close"));
         }
      }
      
      public function clearReference() : void
      {
         this._bagAndGiftFrame = null;
         dispatchEvent(new Event("close"));
      }
      
      public function loadRingSystemInfo(_arg_1:RingDataAnalyzer) : void
      {
         this.RingData = _arg_1.data;
      }
      
      public function getCurrentRingData() : RingSystemData
      {
         var _local_3:int = 0;
         var _local_2:* = null;
         var _local_1:int = PlayerManager.Instance.Self.RingExp;
         _local_3 = 1;
         while(_local_3 <= RingSystemData.TotalLevel)
         {
            if(_local_1 <= 0)
            {
               _local_2 = this.RingData[1];
               break;
            }
            if(_local_1 < this.RingData[_local_3].Exp)
            {
               _local_2 = this.RingData[_local_3 - 1];
               break;
            }
            if(_local_3 == RingSystemData.TotalLevel && _local_1 >= this.RingData[_local_3].Exp)
            {
               _local_2 = this.RingData[_local_3];
            }
            _local_3++;
         }
         return _local_2;
      }
      
      public function getRingData(_arg_1:int) : RingSystemData
      {
         var _local_4:int = 0;
         var _local_3:* = null;
         var _local_2:* = _arg_1;
         _local_4 = 1;
         while(_local_4 <= RingSystemData.TotalLevel)
         {
            if(_local_2 <= 0)
            {
               _local_3 = this.RingData[1];
               break;
            }
            if(_local_2 < this.RingData[_local_4].Exp)
            {
               _local_3 = this.RingData[_local_4 - 1];
               break;
            }
            if(_local_4 == RingSystemData.TotalLevel && _local_2 >= this.RingData[_local_4].Exp)
            {
               _local_3 = this.RingData[_local_4];
            }
            _local_4++;
         }
         return _local_3;
      }
   }
}

class SingletonForce
{
   
   public function SingletonForce()
   {
      super();
   }
}

class _DataOnClickOKButton
{
   
   public var id:String;
   
   public var func:Function;
   
   public function _DataOnClickOKButton(_arg_1:String, _arg_2:Function)
   {
      super();
      this.id = _arg_1;
      this.func = _arg_2;
   }
}
