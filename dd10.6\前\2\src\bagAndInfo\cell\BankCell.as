package bagAndInfo.cell
{
   import com.pickgliss.ui.ComponentFactory;
   import ddt.data.goods.ItemTemplateInfo;
   import flash.display.DisplayObject;
   
   public class BankCell extends BagCell
   {
      
      public function BankCell(_arg_1:int, _arg_2:ItemTemplateInfo = null, _arg_3:<PERSON>olean = true, _arg_4:DisplayObject = null, _arg_5:<PERSON>olean = true)
      {
         super(_arg_1,null,true,ComponentFactory.Instance.creatBitmap("asset.bagAndInfo.bankCellBg"),true);
      }
   }
}

