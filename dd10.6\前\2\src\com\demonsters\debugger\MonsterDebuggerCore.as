package com.demonsters.debugger
{
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.display.Stage;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.external.ExternalInterface;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.system.Capabilities;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import flash.utils.Timer;
   import flash.utils.getDefinitionByName;
   import starling.core.Starling;
   import starling.display.DisplayObject;
   import starling.display.Stage;
   
   internal class MonsterDebuggerCore
   {
      
      private static var _monitorTimer:Timer;
      
      private static var _monitorSprite:Sprite;
      
      private static var _monitorTime:Number;
      
      private static var _monitorStart:Number;
      
      private static var _monitorFrames:int;
      
      private static var _highlight:Sprite;
      
      private static var _highlightInfo:TextField;
      
      private static var _highlightTarget:flash.display.DisplayObject;
      
      private static var _starlingHighlightTarget:starling.display.DisplayObject;
      
      private static var _highlightMouse:Boolean;
      
      private static var _highlightUpdate:Boolean;
      
      private static const MONITOR_UPDATE:int = 1000;
      
      private static const HIGHLITE_COLOR:uint = 3381759;
      
      internal static const ID:String = "com.demonsters.debugger.core";
      
      private static var _base:Object = null;
      
      private static var _stage:flash.display.Stage = null;
      
      private static var _starlingStage:starling.display.Stage = null;
      
      public function MonsterDebuggerCore()
      {
         super();
      }
      
      internal static function initialize() : void
      {
         _monitorTime = new Date().time;
         _monitorStart = new Date().time;
         _monitorFrames = 0;
         _monitorTimer = new Timer(1000);
         _monitorTimer.addEventListener("timer",monitorTimerCallback,false,0,true);
         _monitorTimer.start();
         if(_base.hasOwnProperty("stage") && _base["stage"] != null && _base["stage"] is flash.display.Stage)
         {
            _stage = _base["stage"] as flash.display.Stage;
         }
         if(_base.hasOwnProperty("stage") && _base["stage"] != null && _base["stage"] is starling.display.Stage)
         {
            _starlingStage = _base["stage"] as starling.display.Stage;
         }
         _monitorSprite = new Sprite();
         _monitorSprite.addEventListener("enterFrame",frameHandler,false,0,true);
         var _local_1:TextFormat = new TextFormat();
         _local_1.font = "Arial";
         _local_1.color = 16777215;
         _local_1.size = 11;
         _local_1.leftMargin = 5;
         _local_1.rightMargin = 5;
         _highlightInfo = new TextField();
         _highlightInfo.embedFonts = false;
         _highlightInfo.autoSize = "left";
         _highlightInfo.mouseWheelEnabled = false;
         _highlightInfo.mouseEnabled = false;
         _highlightInfo.condenseWhite = false;
         _highlightInfo.embedFonts = false;
         _highlightInfo.multiline = false;
         _highlightInfo.selectable = false;
         _highlightInfo.wordWrap = false;
         _highlightInfo.defaultTextFormat = _local_1;
         _highlightInfo.text = "";
         _highlight = new Sprite();
         _highlightMouse = false;
         _highlightTarget = null;
         _starlingHighlightTarget = null;
         _highlightUpdate = false;
      }
      
      internal static function get base() : *
      {
         return _base;
      }
      
      internal static function set base(_arg_1:*) : void
      {
         _base = _arg_1;
      }
      
      internal static function trace(_arg_1:*, _arg_2:*, _arg_3:String = "", _arg_4:String = "", _arg_5:uint = 0, _arg_6:int = 5) : void
      {
         var _local_8:* = null;
         var _local_7:* = null;
         if(MonsterDebugger.enabled)
         {
            _local_8 = XML(MonsterDebuggerUtils.parse(_arg_2,"",1,_arg_6,false));
            _local_7 = {
               "command":"TRACE",
               "memory":MonsterDebuggerUtils.getMemory(),
               "date":new Date(),
               "target":_arg_1,
               "reference":MonsterDebuggerUtils.getReferenceID(_arg_1),
               "xml":_local_8,
               "person":_arg_3,
               "label":_arg_4,
               "color":_arg_5
            };
            send(_local_7);
         }
      }
      
      internal static function snapshot(_arg_1:*, _arg_2:flash.display.DisplayObject, _arg_3:String = "", _arg_4:String = "") : void
      {
         var _local_6:* = null;
         var _local_5:* = null;
         var _local_7:* = null;
         if(MonsterDebugger.enabled)
         {
            _local_6 = MonsterDebuggerUtils.snapshot(_arg_2);
            if(_local_6 != null)
            {
               _local_5 = _local_6.getPixels(new Rectangle(0,0,_local_6.width,_local_6.height));
               _local_7 = {
                  "command":"SNAPSHOT",
                  "memory":MonsterDebuggerUtils.getMemory(),
                  "date":new Date(),
                  "target":_arg_1,
                  "reference":MonsterDebuggerUtils.getReferenceID(_arg_1),
                  "bytes":_local_5,
                  "width":_local_6.width,
                  "height":_local_6.height,
                  "person":_arg_3,
                  "label":_arg_4
               };
               send(_local_7);
            }
         }
      }
      
      internal static function breakpoint(_arg_1:*, _arg_2:String = "breakpoint") : void
      {
         var _local_4:* = null;
         var _local_3:* = null;
         if(MonsterDebugger.enabled && MonsterDebuggerConnection.connected)
         {
            _local_4 = MonsterDebuggerUtils.stackTrace();
            _local_3 = {
               "command":"PAUSE",
               "memory":MonsterDebuggerUtils.getMemory(),
               "date":new Date(),
               "target":_arg_1,
               "reference":MonsterDebuggerUtils.getReferenceID(_arg_1),
               "stack":_local_4,
               "id":_arg_2
            };
            send(_local_3);
            MonsterDebuggerUtils.pause();
         }
      }
      
      internal static function inspect(_arg_1:*) : void
      {
         var _local_3:* = undefined;
         var _local_2:* = null;
         if(MonsterDebugger.enabled)
         {
            _base = _arg_1;
            _local_3 = MonsterDebuggerUtils.getObject(_base,"",0);
            if(_local_3 != null)
            {
               _local_2 = XML(MonsterDebuggerUtils.parse(_local_3,"",1,2,true));
               send({
                  "command":"BASE",
                  "xml":_local_2
               });
            }
         }
      }
      
      internal static function clear() : void
      {
         if(MonsterDebugger.enabled)
         {
            send({"command":"CLEAR_TRACES"});
         }
      }
      
      internal static function sendInformation() : void
      {
         var _local_10:* = undefined;
         var _local_3:* = undefined;
         var _local_14:* = undefined;
         var _local_6:int = 0;
         var _local_13:Boolean = false;
         var _local_15:* = null;
         var _local_2:* = null;
         var _local_8:* = null;
         var _local_5:* = null;
         var _local_16:* = null;
         var _local_7:String = Capabilities.playerType;
         var _local_11:String = Capabilities.version;
         var _local_12:Boolean = Capabilities.isDebugger;
         var _local_1:String = "";
         var _local_4:String = "";
         try
         {
            _local_10 = getDefinitionByName("mx.core::UIComponent");
            if(_local_10 != null)
            {
               _local_13 = true;
            }
         }
         catch(e1:Error)
         {
         }
         if(_base is flash.display.DisplayObject && _base.hasOwnProperty("loaderInfo"))
         {
            if(flash.display.DisplayObject(_base).loaderInfo != null)
            {
               _local_4 = unescape(flash.display.DisplayObject(_base).loaderInfo.url);
            }
         }
         if(_base.hasOwnProperty("stage"))
         {
            if(_base["stage"] != null && _base["stage"] is flash.display.Stage)
            {
               _local_4 = unescape(flash.display.Stage(_base["stage"]).loaderInfo.url);
            }
         }
         if(_local_7 == "ActiveX" || _local_7 == "PlugIn")
         {
            if(ExternalInterface.available)
            {
               try
               {
                  _local_15 = ExternalInterface.call("window.location.href.toString");
                  _local_2 = ExternalInterface.call("window.document.title.toString");
                  if(_local_15 != null)
                  {
                     _local_4 = _local_15;
                  }
                  if(_local_2 != null)
                  {
                     _local_1 = _local_2;
                  }
               }
               catch(e2:Error)
               {
               }
            }
         }
         if(_local_7 == "Desktop")
         {
            try
            {
               _local_3 = getDefinitionByName("flash.desktop::NativeApplication");
               if(_local_3 != null)
               {
                  _local_8 = _local_3["nativeApplication"]["applicationDescriptor"];
                  _local_5 = _local_8.namespace();
                  _local_16 = _local_8._local_5::filename;
                  _local_14 = getDefinitionByName("flash.filesystem::File");
                  if(Capabilities.os.toLowerCase().indexOf("windows") != -1)
                  {
                     _local_16 += ".exe";
                     _local_4 = _local_14["applicationDirectory"]["resolvePath"](_local_16)["nativePath"];
                  }
                  else if(Capabilities.os.toLowerCase().indexOf("mac") != -1)
                  {
                     _local_16 += ".app";
                     _local_4 = _local_14["applicationDirectory"]["resolvePath"](_local_16)["nativePath"];
                  }
               }
            }
            catch(e3:Error)
            {
            }
         }
         if(_local_1 == "" && _local_4 != "")
         {
            _local_6 = Math.max(_local_4.lastIndexOf("\\"),_local_4.lastIndexOf("/"));
            if(_local_6 != -1)
            {
               _local_1 = _local_4.substring(_local_6 + 1,_local_4.lastIndexOf("."));
            }
            else
            {
               _local_1 = _local_4;
            }
         }
         if(_local_1 == "")
         {
            _local_1 = "Application";
         }
         var _local_9:Object = {
            "command":"INFO",
            "debuggerVersion":3.02,
            "playerType":_local_7,
            "playerVersion":_local_11,
            "isDebugger":_local_12,
            "isFlex":_local_13,
            "fileLocation":_local_4,
            "fileTitle":_local_1
         };
         send(_local_9,true);
         MonsterDebuggerConnection.processQueue();
      }
      
      internal static function handle(_arg_1:MonsterDebuggerData) : void
      {
         if(MonsterDebugger.enabled)
         {
            if(_arg_1.id == null || _arg_1.id == "")
            {
               return;
            }
            if(_arg_1.id == "com.demonsters.debugger.core")
            {
               handleInternal(_arg_1);
            }
         }
      }
      
      private static function handleInternal(_arg_1:MonsterDebuggerData) : void
      {
         var _local_6:* = undefined;
         var _local_5:* = null;
         var _local_4:* = null;
         var _local_7:* = null;
         var _local_3:* = null;
         var _local_2:* = null;
         switch(_arg_1.data["command"])
         {
            case "HELLO":
               sendInformation();
               return;
            case "BASE":
               _local_6 = MonsterDebuggerUtils.getObject(_base,"",0);
               if(_local_6 != null)
               {
                  _local_5 = XML(MonsterDebuggerUtils.parse(_local_6,"",1,2,true));
                  send({
                     "command":"BASE",
                     "xml":_local_5
                  });
               }
               return;
            case "INSPECT":
               _local_6 = MonsterDebuggerUtils.getObject(_base,_arg_1.data["target"],0);
               if(_local_6 != null)
               {
                  _base = _local_6;
                  _local_5 = XML(MonsterDebuggerUtils.parse(_local_6,"",1,2,true));
                  send({
                     "command":"BASE",
                     "xml":_local_5
                  });
               }
               return;
            case "GET_OBJECT":
               _local_6 = MonsterDebuggerUtils.getObject(_base,_arg_1.data["target"],0);
               if(_local_6 != null)
               {
                  _local_5 = XML(MonsterDebuggerUtils.parse(_local_6,_arg_1.data["target"],1,2,true));
                  send({
                     "command":"GET_OBJECT",
                     "xml":_local_5
                  });
               }
               return;
            case "GET_PROPERTIES":
               _local_6 = MonsterDebuggerUtils.getObject(_base,_arg_1.data["target"],0);
               if(_local_6 != null)
               {
                  _local_5 = XML(MonsterDebuggerUtils.parse(_local_6,_arg_1.data["target"],1,1,false));
                  send({
                     "command":"GET_PROPERTIES",
                     "xml":_local_5
                  });
               }
               return;
            case "GET_FUNCTIONS":
               _local_6 = MonsterDebuggerUtils.getObject(_base,_arg_1.data["target"],0);
               if(_local_6 != null)
               {
                  _local_5 = XML(MonsterDebuggerUtils.parseFunctions(_local_6,_arg_1.data["target"]));
                  send({
                     "command":"GET_FUNCTIONS",
                     "xml":_local_5
                  });
               }
               return;
            case "SET_PROPERTY":
               _local_6 = MonsterDebuggerUtils.getObject(_base,_arg_1.data["target"],1);
               if(_local_6 != null)
               {
                  try
                  {
                     _local_6[_arg_1.data["name"]] = _arg_1.data["value"];
                     send({
                        "command":"SET_PROPERTY",
                        "target":_arg_1.data["target"],
                        "value":_local_6[_arg_1.data["name"]]
                     });
                  }
                  catch(e1:Error)
                  {
                  }
               }
               return;
            case "GET_PREVIEW":
               _local_6 = MonsterDebuggerUtils.getObject(_base,_arg_1.data["target"],0);
               if(_local_6 != null && MonsterDebuggerUtils.isDisplayObject(_local_6))
               {
                  _local_7 = _local_6 as flash.display.DisplayObject;
                  _local_3 = MonsterDebuggerUtils.snapshot(_local_7,new Rectangle(0,0,300,300));
                  if(_local_3 != null)
                  {
                     _local_2 = _local_3.getPixels(new Rectangle(0,0,_local_3.width,_local_3.height));
                     send({
                        "command":"GET_PREVIEW",
                        "bytes":_local_2,
                        "width":_local_3.width,
                        "height":_local_3.height
                     });
                  }
               }
               return;
            case "CALL_METHOD":
               _local_4 = MonsterDebuggerUtils.getObject(_base,_arg_1.data["target"],0);
               if(_local_4 != null && _local_4 is Function)
               {
                  if(_arg_1.data["returnType"] == "void")
                  {
                     _local_4.apply(null,_arg_1.data["arguments"]);
                  }
                  else
                  {
                     try
                     {
                        _local_6 = _local_4.apply(null,_arg_1.data["arguments"]);
                        _local_5 = XML(MonsterDebuggerUtils.parse(_local_6,"",1,5,false));
                        send({
                           "command":"CALL_METHOD",
                           "id":_arg_1.data["id"],
                           "xml":_local_5
                        });
                     }
                     catch(e2:Error)
                     {
                     }
                  }
               }
               return;
            case "PAUSE":
               MonsterDebuggerUtils.pause();
               send({"command":"PAUSE"});
               return;
            case "RESUME":
               MonsterDebuggerUtils.resume();
               send({"command":"RESUME"});
               return;
            case "HIGHLIGHT":
               _local_6 = MonsterDebuggerUtils.getObject(_base,_arg_1.data["target"],0);
               if(_local_6 != null && MonsterDebuggerUtils.isDisplayObject(_local_6))
               {
                  if(flash.display.DisplayObject(_local_6).stage != null && flash.display.DisplayObject(_local_6).stage is flash.display.Stage)
                  {
                     _stage = _local_6["stage"];
                  }
                  if(_stage != null)
                  {
                     highlightClear();
                     send({"command":"STOP_HIGHLIGHT"});
                     _highlight.removeEventListener("click",highlightClicked);
                     _highlight.mouseEnabled = false;
                     _highlightTarget = flash.display.DisplayObject(_local_6);
                     _starlingHighlightTarget = null;
                     _highlightMouse = false;
                     _highlightUpdate = true;
                  }
               }
               else if(_local_6 != null && MonsterDebuggerUtils.isStarlingDisplayObject(_local_6))
               {
                  if(starling.display.DisplayObject(_local_6).stage != null && starling.display.DisplayObject(_local_6).stage is starling.display.Stage)
                  {
                     _starlingStage = _local_6["stage"] as starling.display.Stage;
                  }
                  if(_starlingStage != null)
                  {
                     highlightClear();
                     send({"command":"STOP_HIGHLIGHT"});
                     _highlight.removeEventListener("click",highlightClicked);
                     _highlight.mouseEnabled = false;
                     _highlightTarget = null;
                     _starlingHighlightTarget = starling.display.DisplayObject(_local_6);
                     _highlightMouse = false;
                     _highlightUpdate = true;
                  }
               }
               return;
            case "START_HIGHLIGHT":
               highlightClear();
               _highlight.addEventListener("click",highlightClicked,false,0,true);
               _highlight.mouseEnabled = true;
               _highlightTarget = null;
               _starlingHighlightTarget = null;
               _highlightMouse = true;
               _highlightUpdate = true;
               send({"command":"START_HIGHLIGHT"});
               return;
            case "STOP_HIGHLIGHT":
               highlightClear();
               _highlight.removeEventListener("click",highlightClicked);
               _highlight.mouseEnabled = false;
               _highlightTarget = null;
               _starlingHighlightTarget = null;
               _highlightMouse = false;
               _highlightUpdate = false;
               send({"command":"STOP_HIGHLIGHT"});
               return;
            default:
               return;
         }
      }
      
      private static function monitorTimerCallback(_arg_1:TimerEvent) : void
      {
         var _local_2:Number = NaN;
         var _local_5:Number = NaN;
         var _local_4:uint = 0;
         var _local_3:uint = 0;
         var _local_6:* = null;
         if(MonsterDebugger.enabled)
         {
            _local_2 = new Date().time;
            _local_5 = _local_2 - _monitorTime;
            _local_4 = uint(uint(_monitorFrames / _local_5 * 1000));
            _local_3 = 0;
            if(_stage == null)
            {
               if(_base.hasOwnProperty("stage") && _base["stage"] != null && _base["stage"] is flash.display.Stage)
               {
                  _stage = flash.display.Stage(_base["stage"]);
               }
            }
            if(_starlingStage == null)
            {
               if(_base.hasOwnProperty("stage") && _base["stage"] != null && _base["stage"] is starling.display.Stage)
               {
                  _starlingStage = starling.display.Stage(_base["stage"]);
               }
            }
            if(_stage != null)
            {
               _local_3 = _stage.frameRate;
            }
            _monitorFrames = 0;
            _monitorTime = _local_2;
            if(MonsterDebuggerConnection.connected)
            {
               _local_6 = {
                  "command":"MONITOR",
                  "memory":MonsterDebuggerUtils.getMemory(),
                  "fps":_local_4,
                  "fpsMovie":_local_3,
                  "time":_local_2
               };
               send(_local_6);
            }
         }
      }
      
      private static function frameHandler(_arg_1:Event) : void
      {
         if(MonsterDebugger.enabled)
         {
            ++_monitorFrames;
            if(_highlightUpdate)
            {
               highlightUpdate();
            }
         }
      }
      
      private static function highlightClicked(_arg_1:MouseEvent) : void
      {
         var _local_4:Number = NaN;
         var _local_6:Number = NaN;
         var _local_7:Number = NaN;
         var _local_3:* = null;
         var _local_5:* = null;
         var _local_2:* = null;
         _arg_1.preventDefault();
         _arg_1.stopImmediatePropagation();
         highlightClear();
         if(_stage != null)
         {
            _highlightTarget = MonsterDebuggerUtils.getObjectUnderPoint(_stage,new Point(_stage.mouseX,_stage.mouseY));
            if(_starlingStage != null && _highlightTarget is flash.display.Stage)
            {
               _highlightTarget = null;
            }
         }
         if(_highlightTarget == null && _starlingStage != null)
         {
            _local_3 = getStarlingForStage(_starlingStage);
            _local_5 = _local_3.nativeStage;
            _local_2 = _local_3.viewPort;
            _local_4 = Number(_local_3.contentScaleFactor);
            _local_6 = (_local_5.mouseX - _local_2.x) / _local_4;
            _local_7 = (_local_5.mouseY - _local_2.y) / _local_4;
            _starlingHighlightTarget = MonsterDebuggerUtils.getStarlingObjectUnderPoint(_starlingStage,new Point(_local_6,_local_7));
         }
         _highlightMouse = false;
         _highlight.removeEventListener("click",highlightClicked);
         _highlight.mouseEnabled = false;
         if(_highlightTarget != null)
         {
            inspect(_highlightTarget);
            highlightDraw(false);
         }
         else if(_starlingHighlightTarget != null)
         {
            inspect(_starlingHighlightTarget);
            highlightDraw(false);
         }
         send({"command":"STOP_HIGHLIGHT"});
      }
      
      private static function highlightUpdate() : void
      {
         var _local_1:* = undefined;
         var _local_4:* = undefined;
         var _local_6:int = 0;
         var _local_10:int = 0;
         var _local_5:Number = NaN;
         var _local_8:Number = NaN;
         var _local_9:Number = NaN;
         var _local_3:* = null;
         var _local_7:* = null;
         var _local_2:* = null;
         highlightClear();
         if(_highlightMouse)
         {
            if(_base.hasOwnProperty("stage") && _base["stage"] != null && _base["stage"] is flash.display.Stage)
            {
               _stage = _base["stage"] as flash.display.Stage;
            }
            if(_base.hasOwnProperty("stage") && _base["stage"] != null && _base["stage"] is starling.display.Stage)
            {
               _starlingStage = _base["stage"] as starling.display.Stage;
            }
            if(Capabilities.playerType == "Desktop")
            {
               _local_1 = getDefinitionByName("flash.desktop::NativeApplication");
               if(_local_1 != null && _local_1["nativeApplication"]["activeWindow"] != null)
               {
                  _stage = _local_1["nativeApplication"]["activeWindow"]["stage"];
                  if(Object(Starling).hasOwnProperty("all"))
                  {
                     _local_4 = Starling["all"] as Vector.<Starling>;
                     _local_6 = int(_local_4.length);
                     _local_10 = 0;
                     while(_local_10 < _local_6)
                     {
                        _local_3 = _local_4[_local_10];
                        if(_local_3.nativeStage == _stage)
                        {
                           _starlingStage = _local_3.stage;
                           break;
                        }
                        _local_10++;
                     }
                  }
               }
            }
            if(_stage == null && _starlingStage == null)
            {
               _highlight.removeEventListener("click",highlightClicked);
               _highlight.mouseEnabled = false;
               _highlightTarget = null;
               _starlingHighlightTarget = null;
               _highlightMouse = false;
               _highlightUpdate = false;
               return;
            }
            if(_stage != null)
            {
               _highlightTarget = MonsterDebuggerUtils.getObjectUnderPoint(_stage,new Point(_stage.mouseX,_stage.mouseY));
               if(_starlingStage != null && _highlightTarget is flash.display.Stage)
               {
                  _highlightTarget = null;
               }
               if(_highlightTarget != null)
               {
                  highlightDraw(true);
               }
            }
            if(_highlightTarget == null && _starlingStage != null)
            {
               _local_3 = getStarlingForStage(_starlingStage);
               _local_7 = _local_3.nativeStage;
               _local_2 = _local_3.viewPort;
               _local_5 = Number(_local_3.contentScaleFactor);
               _local_8 = (_local_7.mouseX - _local_2.x) / _local_5;
               _local_9 = (_local_7.mouseY - _local_2.y) / _local_5;
               _starlingHighlightTarget = MonsterDebuggerUtils.getStarlingObjectUnderPoint(_starlingStage,new Point(_local_8,_local_9));
               if(_starlingHighlightTarget != null)
               {
                  highlightDraw(true);
               }
            }
            return;
         }
         if(_highlightTarget != null)
         {
            if(_highlightTarget.stage == null || _highlightTarget.parent == null)
            {
               _highlight.removeEventListener("click",highlightClicked);
               _highlight.mouseEnabled = false;
               _highlightTarget = null;
               _starlingHighlightTarget = null;
               _highlightMouse = false;
               _highlightUpdate = false;
               return;
            }
            highlightDraw(false);
         }
         else if(_starlingHighlightTarget != null)
         {
            if(_starlingHighlightTarget.stage == null || _starlingHighlightTarget.parent == null)
            {
               _highlight.removeEventListener("click",highlightClicked);
               _highlight.mouseEnabled = false;
               _highlightTarget = null;
               _starlingHighlightTarget = null;
               _highlightMouse = false;
               _highlightUpdate = false;
               return;
            }
            highlightDraw(false);
         }
      }
      
      private static function highlightDraw(_arg_1:Boolean) : void
      {
         var _local_6:Number = NaN;
         var _local_7:* = null;
         var _local_8:* = null;
         var _local_4:* = null;
         var _local_3:* = null;
         if(_highlightTarget == null && _starlingHighlightTarget == null)
         {
            return;
         }
         if(_highlightTarget != null)
         {
            if(_highlightTarget == _stage)
            {
               _local_8 = new Rectangle(0,0,_stage.stageWidth,_stage.stageHeight);
            }
            else
            {
               _local_8 = _highlightTarget.getBounds(_stage);
            }
            _local_7 = _stage;
         }
         else if(_starlingHighlightTarget != null)
         {
            _local_4 = getStarlingForStage(_starlingStage);
            _local_7 = _local_4.nativeStage;
            _local_3 = _local_4.viewPort;
            _local_6 = Number(_local_4.contentScaleFactor);
            if(_starlingHighlightTarget == _starlingStage)
            {
               _local_8 = new Rectangle(_local_3.x,_local_3.y,_starlingStage.stageWidth * _local_6,_starlingStage.stageHeight * _local_6);
            }
            else
            {
               _local_8 = _starlingHighlightTarget.getBounds(_starlingStage);
               _local_8.setTo(_local_8.x * _local_6 + _local_3.x,_local_8.y * _local_6 + _local_3.y,_local_8.width * _local_6,_local_8.height * _local_6);
            }
         }
         if(_highlightTarget != null && _highlightTarget != _stage || _starlingHighlightTarget != null && _starlingHighlightTarget != _starlingStage)
         {
            _local_8.x += 0.5;
            _local_8.y += 0.5;
            _local_8.width += 0.5;
            _local_8.height += 0.5;
         }
         var _local_2:Rectangle = _local_8.clone();
         _local_2.x += 2;
         _local_2.y += 2;
         _local_2.width -= 4;
         _local_2.height -= 4;
         if(_local_2.width < 0)
         {
            _local_2.width = 0;
         }
         if(_local_2.height < 0)
         {
            _local_2.height = 0;
         }
         _highlight.graphics.clear();
         _highlight.graphics.beginFill(3381759,1);
         _highlight.graphics.drawRect(_local_8.x,_local_8.y,_local_8.width,_local_8.height);
         _highlight.graphics.drawRect(_local_2.x,_local_2.y,_local_2.width,_local_2.height);
         if(_arg_1)
         {
            _highlight.graphics.beginFill(3381759,0.25);
            _highlight.graphics.drawRect(_local_2.x,_local_2.y,_local_2.width,_local_2.height);
         }
         if(Boolean(_highlightTarget))
         {
            if(_highlightTarget.name != null)
            {
               _highlightInfo.text = _highlightTarget.name + " - " + String(MonsterDebuggerDescribeType.get(_highlightTarget).@name);
            }
            else
            {
               _highlightInfo.text = String(MonsterDebuggerDescribeType.get(_highlightTarget).@name);
            }
         }
         else if(Boolean(_starlingHighlightTarget))
         {
            if(_starlingHighlightTarget.name != null)
            {
               _highlightInfo.text = _starlingHighlightTarget.name + " - " + String(MonsterDebuggerDescribeType.get(_starlingHighlightTarget).@name);
            }
            else
            {
               _highlightInfo.text = String(MonsterDebuggerDescribeType.get(_starlingHighlightTarget).@name);
            }
         }
         var _local_5:Rectangle = new Rectangle(_local_8.x,_local_8.y - (_highlightInfo.textHeight + 3),_highlightInfo.textWidth + 15,_highlightInfo.textHeight + 5);
         if(_local_5.y < 0)
         {
            _local_5.y = _local_8.y + _local_8.height;
         }
         if(_local_5.y + _local_5.height > _local_7.stageHeight)
         {
            _local_5.y = _local_7.stageHeight - _local_5.height;
         }
         if(_local_5.x < 0)
         {
            _local_5.x = 0;
         }
         if(_local_5.x + _local_5.width > _local_7.stageWidth)
         {
            _local_5.x = _local_7.stageWidth - _local_5.width;
         }
         _highlight.graphics.beginFill(3381759,1);
         _highlight.graphics.drawRect(_local_5.x,_local_5.y,_local_5.width,_local_5.height);
         _highlight.graphics.endFill();
         _highlightInfo.x = _local_5.x;
         _highlightInfo.y = _local_5.y;
         try
         {
            _local_7.addChild(_highlight);
            _local_7.addChild(_highlightInfo);
         }
         catch(e:Error)
         {
         }
      }
      
      private static function highlightClear() : void
      {
         if(_highlight != null && _highlight.parent != null)
         {
            _highlight.parent.removeChild(_highlight);
            _highlight.graphics.clear();
            _highlight.x = 0;
            _highlight.y = 0;
         }
         if(_highlightInfo != null && _highlightInfo.parent != null)
         {
            _highlightInfo.parent.removeChild(_highlightInfo);
            _highlightInfo.x = 0;
            _highlightInfo.y = 0;
         }
      }
      
      private static function send(_arg_1:Object, _arg_2:Boolean = false) : void
      {
         if(MonsterDebugger.enabled)
         {
            MonsterDebuggerConnection.send("com.demonsters.debugger.core",_arg_1,_arg_2);
         }
      }
      
      private static function getStarlingForStage(_arg_1:starling.display.Stage) : Starling
      {
         var _local_3:* = undefined;
         var _local_4:int = 0;
         var _local_5:int = 0;
         var _local_2:* = null;
         if(Object(Starling).hasOwnProperty("all"))
         {
            _local_3 = Starling["all"] as Vector.<Starling>;
            _local_4 = int(_local_3.length);
            _local_5 = 0;
            while(_local_5 < _local_4)
            {
               _local_2 = _local_3[_local_5];
               if(_local_2.stage == _arg_1)
               {
                  return _local_2;
               }
               _local_5++;
            }
         }
         return Starling.current;
      }
   }
}

