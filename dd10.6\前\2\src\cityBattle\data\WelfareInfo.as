package cityBattle.data
{
   public class WelfareInfo
   {
      
      public var ID:int;
      
      public var Quality:int;
      
      public var Probability:int;
      
      public var TemplateID:int;
      
      public var ValidDate:int;
      
      public var Count:int = 1;
      
      public var IsBind:Boolean;
      
      public var StrengthLevel:int;
      
      public var AttackCompose:int;
      
      public var DefendCompose:int;
      
      public var AgilityCompose:int;
      
      public var LuckCompose:int;
      
      public var NeedScore:int;
      
      public var ExchangeCount:int;
      
      public var myExchangeCount:int = 0;
      
      public function WelfareInfo()
      {
         super();
      }
   }
}

