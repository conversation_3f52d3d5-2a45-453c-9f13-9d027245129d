# 圣物升级功能修复说明

## 问题描述
游戏中点击圣物的"修炼"按钮后显示"暂时禁止升级，还没写完！"的提示，圣物升级功能无法正常使用。

## 问题原因分析
1. **前端问题**：在 `ForcesRelicDescView.as` 中，修炼按钮的条件判断错误，使用了 `if(have >= 0)` 而不是 `if(have > 0)`
2. **后端问题**：在 `forcesbattle.cs` 中，升级数量被硬编码为 `99999`，导致材料检查失败
3. **配置问题**：`GameProperties.RelicUpgradeItem` 配置类型不匹配，定义为 `int` 但实际需要 `string` 来解析物品ID和经验值
4. **物品ID问题**：配置中使用的圣物升级石ID与数据库中新添加的物品ID不匹配

## 修复内容

### 1. 前端修复 (`ForcesRelicDescView.as`)
- 修复材料数量判断条件：`if(have >= 0)` → `if(have > 0)`
- 添加材料不足时的提示信息显示
- 更新 `ServerConfigManager.as` 中的默认配置：`"11024,50"` → `"386298,50"`

### 2. 后端修复 (`forcesbattle.cs`)
- 修复升级数量读取：从数据包中正确读取 `templateID` 和 `num` 参数
- 添加配置解析逻辑：正确解析 `GameProperties.RelicUpgradeItem` 配置
- 更新错误提示信息：将"暂时禁止升级，还没写完！" → "圣物升级石数量不足！"

### 3. 配置修复 (`GameProperties.cs`)
- 修改配置类型：`int RelicUpgradeItem` → `string RelicUpgradeItem`
- 更新默认值：`50` → `"386298,50"`

### 4. 配置文件更新
- **ServerConfig.xml**: 更新 `RelicUpgradeItem` 配置为 `"386298,50"`
- **Server_Config.sql**: 更新数据库配置，移除重复项
- **language.txt**: 添加新的语言项 `tank.forceRelic.txt36` 用于材料不足提示

### 5. 新增物品配置
根据您提供的数据，已添加以下圣物相关物品：
- **386298**: 圣物升级石 (用于提升圣物等级)
- **386299**: 圣物进阶石 (用于提升圣物品质)
- **386300**: 圣物精华 (圣物分解后获得)
- **386301**: 圣物强化石 (用于强化圣物属性)

## 修改的文件列表

### 前端文件
1. `需要修改dd3.6/前端/2/src/forcesbattle/views/relic/ForcesRelicDescView.as`
2. `需要修改dd3.6/前端/2/src/ddt/manager/ServerConfigManager.as`

### 后端文件
1. `需要修改dd3.6/后端/Road.Service/Game/Server/Packets/Client/forcesbattle.cs`
2. `需要修改dd3.6/后端/Road.Service/Bussiness/GameProperties.cs`

### 配置文件
1. `需要修改dd3.6/服务端/Web/Request/ServerConfig.xml`
2. `需要修改dd3.6/数据库表/Db_Tank_S1/Server_Config.sql`
3. `需要修改dd3.6/服务端/Web/Resource/Flash/ui/zh_cn/language.txt`

### 测试文件
1. `需要修改dd3.6/测试脚本/添加圣物升级石.sql` (新建)

## 测试建议

1. **重新编译前端和后端代码**
2. **更新服务器配置**：应用 ServerConfig.xml 和数据库配置更新
3. **添加测试物品**：使用提供的SQL脚本给测试账号添加圣物升级石
4. **功能测试**：
   - 验证修炼按钮可以正常点击
   - 验证材料不足时显示正确提示
   - 验证使用圣物升级石可以正常增加圣物经验
   - 验证圣物等级提升功能

## 注意事项

1. 确保数据库中已经添加了新的物品模板 (386298-386301)
2. 如果使用其他版本的代码，可能需要检查对应文件的路径和内容
3. 建议在测试环境中先验证功能正常后再部署到生产环境
4. 配置更新后需要重启游戏服务器才能生效
