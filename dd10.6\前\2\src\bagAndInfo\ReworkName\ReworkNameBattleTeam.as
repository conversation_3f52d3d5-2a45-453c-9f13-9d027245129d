package bagAndInfo.ReworkName
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.image.ScaleBitmapImage;
   import com.pickgliss.ui.image.ScaleFrameImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import ddt.events.PkgEvent;
   import ddt.manager.LanguageMgr;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.PositionUtils;
   import flash.events.MouseEvent;
   
   public class ReworkNameBattleTeam extends Frame
   {
      
      private var _nameErrorTxt:FilterFrameText = null;
      
      private var _tagErrorTxt:FilterFrameText = null;
      
      private var _nameInput:FilterFrameText = null;
      
      private var _tagInput:FilterFrameText = null;
      
      private var _nameCheck:TextButton = null;
      
      private var _tagCheck:TextButton = null;
      
      private var _submit:TextButton = null;
      
      private var _tagErrorImg:ScaleFrameImage = null;
      
      private var _nameErrorImg:ScaleFrameImage = null;
      
      private var _bagType:int = -1;
      
      private var _place:int = -1;
      
      public function ReworkNameBattleTeam()
      {
         super();
         this.initView();
         this.initEvent();
      }
      
      public function initialize(_arg_1:int, _arg_2:int) : void
      {
         this._bagType = _arg_1;
         this._place = _arg_2;
      }
      
      private function initEvent() : void
      {
         addEventListener("response",this.__responseHandler);
         this._submit.addEventListener("click",this.onClickHander);
         this._nameCheck.addEventListener("click",this.onClickHander);
         this._tagCheck.addEventListener("click",this.onClickHander);
         SocketManager.Instance.addEventListener(PkgEvent.format(390,17),this.__onCheckInput);
         SocketManager.Instance.addEventListener(PkgEvent.format(390,32),this.__onChangeResult);
      }
      
      private function __onCheckInput(_arg_1:PkgEvent) : void
      {
         var _local_4:* = undefined;
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         var _local_2:int = _arg_1.pkg.readByte();
         _local_4 = _local_2 == 0;
         if(_local_3)
         {
            this._nameErrorImg.visible = true;
            this._nameErrorTxt.setFrame(_local_4 ? 1 : 2);
            this._nameErrorImg.setFrame(_local_4 ? 1 : 2);
            this._nameErrorTxt.text = LanguageMgr.GetTranslation(this.getErrorDesc(1,_local_4,_local_2));
            PositionUtils.setPos(this._nameErrorTxt,"reworkNameBattleTeam.nameErrorPos");
            this._nameErrorTxt.visible = !_local_4;
         }
         else
         {
            this._tagErrorImg.visible = true;
            this._tagErrorTxt.setFrame(_local_4 ? 1 : 2);
            this._tagErrorImg.setFrame(_local_4 ? 1 : 2);
            this._tagErrorTxt.text = LanguageMgr.GetTranslation(this.getErrorDesc(2,_local_4,_local_2));
            PositionUtils.setPos(this._tagErrorTxt,"reworkNameBattleTeam.tagErrorPos");
            this._tagErrorTxt.visible = !_local_4;
         }
      }
      
      private function __onChangeResult(_arg_1:PkgEvent) : void
      {
         this.dispose();
      }
      
      private function getErrorDesc(_arg_1:int, _arg_2:Boolean, _arg_3:int = 0) : String
      {
         var _local_4:String = "team.change.ok";
         if(!_arg_2)
         {
            _local_4 = "team.create.inputError" + _arg_3;
            if(_arg_3 == 2)
            {
               _local_4 += _arg_1;
            }
         }
         return _local_4;
      }
      
      private function onClickHander(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         switch(_arg_1.target)
         {
            case this._submit:
               SocketManager.Instance.out.sendChangeTeamName(this._bagType,this._place,this._nameInput.text,this._tagInput.text);
               return;
            case this._nameCheck:
               SocketManager.Instance.out.sendTeamCheckInput(true,this._nameInput.text);
               return;
            case this._tagCheck:
               SocketManager.Instance.out.sendTeamCheckInput(false,this._tagInput.text);
               return;
            default:
               return;
         }
      }
      
      protected function __responseHandler(_arg_1:FrameEvent) : void
      {
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               return;
            case 0:
            case 1:
            case 4:
               this.dispose();
         }
      }
      
      private function removeEvent() : void
      {
         removeEventListener("response",this.__responseHandler);
         this._submit.removeEventListener("click",this.onClickHander);
         this._nameCheck.removeEventListener("click",this.onClickHander);
         this._tagCheck.removeEventListener("click",this.onClickHander);
         SocketManager.Instance.removeEventListener(PkgEvent.format(390,17),this.__onCheckInput);
         SocketManager.Instance.removeEventListener(PkgEvent.format(390,32),this.__onChangeResult);
      }
      
      private function initView() : void
      {
         this.titleText = LanguageMgr.GetTranslation("battleTeam.title");
         var _local_6:ScaleBitmapImage = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.bg");
         addToContent(_local_6);
         var _local_5:ScaleBitmapImage = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.bg2");
         addToContent(_local_5);
         var _local_2:ScaleBitmapImage = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.inputBg1");
         addToContent(_local_2);
         var _local_3:ScaleBitmapImage = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.inputBg2");
         addToContent(_local_3);
         var _local_4:FilterFrameText = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.nameTitle");
         _local_4.text = LanguageMgr.GetTranslation("battleTeam.nameTitle");
         addToContent(_local_4);
         var _local_1:FilterFrameText = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.tagTitle");
         _local_1.text = LanguageMgr.GetTranslation("battleTeam.tagTitle");
         addToContent(_local_1);
         this._nameInput = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.name");
         addToContent(this._nameInput);
         this._tagInput = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.tag");
         addToContent(this._tagInput);
         this._nameCheck = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.nameCheck");
         this._nameCheck.text = LanguageMgr.GetTranslation("battleTeam.check");
         addToContent(this._nameCheck);
         this._tagCheck = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.tagCheck");
         this._tagCheck.text = LanguageMgr.GetTranslation("battleTeam.check");
         addToContent(this._tagCheck);
         this._nameErrorTxt = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.nameError");
         this._nameErrorTxt.text = LanguageMgr.GetTranslation("battleTeam.nameRemaind");
         addToContent(this._nameErrorTxt);
         this._tagErrorTxt = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.tagError");
         this._tagErrorTxt.text = LanguageMgr.GetTranslation("battleTeam.tagRemaind");
         addToContent(this._tagErrorTxt);
         this._submit = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.submit");
         this._submit.text = LanguageMgr.GetTranslation("battleTeam.submit");
         addToContent(this._submit);
         this._tagErrorImg = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.tagErrorImg");
         addToContent(this._tagErrorImg);
         this._tagErrorImg.visible = false;
         this._nameErrorImg = ComponentFactory.Instance.creatComponentByStylename("reworkNameBattleTeam.nameErrorImg");
         addToContent(this._nameErrorImg);
         this._nameErrorImg.visible = false;
      }
      
      override public function dispose() : void
      {
         super.dispose();
         this.removeEvent();
      }
   }
}

