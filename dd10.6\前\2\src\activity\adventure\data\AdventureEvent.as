package activity.adventure.data
{
   import flash.events.Event;
   
   public class AdventureEvent extends Event
   {
      
      public static var PLAY_DICE:String = "play_dice";
      
      public static var UPDATE_POS:String = "update_pos";
      
      public static var UPDATE_INFO:String = "pudate_info";
      
      public static var NEW_EVENT:String = "new_event";
      
      public static var UPDATE_BOX:String = "update_box";
      
      public static var SHOP_BUY:String = "shop_buy";
      
      public static var RESET:String = "reset";
      
      public static var UPDATE_RANK:String = "update_rank";
      
      public static var REFRESH:String = "refresh";
      
      private var _data:Object;
      
      public function AdventureEvent(_arg_1:String, _arg_2:Object = null, _arg_3:Boolean = false, _arg_4:<PERSON><PERSON><PERSON> = false)
      {
         this._data = _arg_2;
         super(_arg_1,_arg_3,_arg_4);
      }
      
      public function get data() : Object
      {
         return this._data;
      }
   }
}

