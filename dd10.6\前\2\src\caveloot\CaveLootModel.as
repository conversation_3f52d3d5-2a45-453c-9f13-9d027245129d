package caveloot
{
   import caveloot.data.CaveLootCnfInfo;
   import road7th.data.DictionaryData;
   
   public class CaveLootModel
   {
      
      public static const BAG_TYPE_1:int = 1;
      
      public static const BAG_TYPE_2:int = 2;
      
      public var activityState:int;
      
      public var dayFreeNum:int;
      
      public var bagDic:DictionaryData;
      
      public var totalMineNum:DictionaryData;
      
      public var floorBonusGotArr:DictionaryData;
      
      public var floorBonusMineRecord:DictionaryData;
      
      public var shopCoin1:int;
      
      public var shopCoin2:int;
      
      public var curResultArr:Array;
      
      public var serverProgress:int;
      
      public var myProgressNum:int;
      
      public var progressStateArr:Array;
      
      public var rankList:Array;
      
      public var myRank:int = -1;
      
      public var myScore:int;
      
      public var cnfInfo:CaveLootCnfInfo;
      
      public var minePoolCnfDic:DictionaryData;
      
      public var minePoolCnfItemIdDic:DictionaryData;
      
      public var floorBonusCnfDic:DictionaryData;
      
      public var progressCnfDic:DictionaryData;
      
      public var tipsItemArr:Array;
      
      public function CaveLootModel()
      {
         super();
         this.curResultArr = [];
         this.progressStateArr = [];
         this.rankList = [];
         this.tipsItemArr = [];
         this.floorBonusGotArr = new DictionaryData();
         this.bagDic = new DictionaryData();
         this.totalMineNum = new DictionaryData();
         this.floorBonusMineRecord = new DictionaryData();
         this.minePoolCnfDic = new DictionaryData();
         this.minePoolCnfItemIdDic = new DictionaryData();
         this.floorBonusCnfDic = new DictionaryData();
         this.progressCnfDic = new DictionaryData();
      }
   }
}

