package angelInvestment
{
   import angelInvestment.data.AngelInvestmentDataAnalyzer;
   import angelInvestment.data.AngelInvestmentModel;
   import ddt.loader.LoaderCreate;
   import ddt.utils.AssetModuleLoader;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import hallIcon.HallIconManager;
   
   public class AngelInvestmentManager extends EventDispatcher
   {
      
      private static var _instance:AngelInvestmentManager;
      
      private var _model:AngelInvestmentModel;
      
      public function AngelInvestmentManager(_arg_1:inner)
      {
         super(null);
         this._model = new AngelInvestmentModel();
      }
      
      public static function get instance() : AngelInvestmentManager
      {
         if(_instance == null)
         {
            _instance = new AngelInvestmentManager(new inner());
         }
         return _instance;
      }
      
      public function initHall(_arg_1:Boolean) : void
      {
         HallIconManager.instance.updateSwitchHandler("angelInvestment",_arg_1);
      }
      
      public function onClickIcon() : void
      {
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createAngelInvestmentLoader());
         AssetModuleLoader.addModelLoader("angelInvestment",6);
         AssetModuleLoader.startCodeLoader(this.showFrame);
      }
      
      private function showFrame() : void
      {
         dispatchEvent(new Event("complete"));
      }
      
      public function onDataComplete(_arg_1:AngelInvestmentDataAnalyzer) : void
      {
         this._model.data = _arg_1.data;
      }
      
      public function get model() : AngelInvestmentModel
      {
         return this._model;
      }
   }
}

class inner
{
   
   public function inner()
   {
      super();
   }
}
