package cardNewSystem.analyzer
{
   import cardNewSystem.data.CardSuitTempInfo;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   
   public class CarSuitAnalyzer extends DataAnalyzer
   {
      
      private var _arr:Array;
      
      public function CarSuitAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
         this._arr = [];
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_3:XML = null;
         var _local_2:* = null;
         var _local_5:* = null;
         var _local_4:XML = XML(_arg_1);
         if(_local_4.@value == "true")
         {
            _local_2 = _local_4..Item;
            for each(_local_3 in _local_2)
            {
               _local_5 = new CardSuitTempInfo();
               ObjectUtils.copyPorpertiesByXML(_local_5,_local_3);
               this._arr.push(_local_5);
            }
         }
         else
         {
            message = _local_4.@message;
            onAnalyzeError();
         }
         onAnalyzeComplete();
      }
      
      public function get infoArr() : Array
      {
         return this._arr;
      }
   }
}

