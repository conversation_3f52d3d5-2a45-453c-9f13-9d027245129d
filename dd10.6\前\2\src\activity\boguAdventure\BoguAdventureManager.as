package activity.boguAdventure
{
   import ddt.events.CEvent;
   import ddt.manager.ActivityTimeManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import hallIcon.HallIconManager;
   
   public class BoguAdventureManager
   {
      
      private static var _this:BoguAdventureManager;
      
      private var _isOpen:Boolean;
      
      private var _isShow:<PERSON>olean;
      
      public function BoguAdventureManager()
      {
         super();
      }
      
      public static function get instance() : BoguAdventureManager
      {
         if(_this == null)
         {
            _this = new BoguAdventureManager();
         }
         return _this;
      }
      
      public function setup() : void
      {
         ActivityTimeManager.instance.addEventListener("boguadventure",this.__onActivityState);
      }
      
      private function __onActivityState(_arg_1:CEvent) : void
      {
         var _local_2:* = _arg_1.data;
         this._isOpen = _local_2 == 1;
         this._isShow = _local_2 == 1 || _local_2 == 2 || _local_2 == 3;
         this.checkOpen();
      }
      
      public function checkOpen() : void
      {
         var _local_1:int = 0;
         if(this._isShow)
         {
            _local_1 = ServerConfigManager.instance.boguAdventureOpenLevel;
            if(PlayerManager.Instance.Self.Grade >= _local_1)
            {
               HallIconManager.instance.updateSwitchHandler("boguadventure",true);
            }
            else
            {
               HallIconManager.instance.executeCacheRightIconLevelLimit("boguadventure",true,10);
            }
         }
         else
         {
            HallIconManager.instance.updateSwitchHandler("boguadventure",false);
            HallIconManager.instance.executeCacheRightIconLevelLimit("boguadventure",false);
         }
      }
      
      public function get isOpen() : Boolean
      {
         return this._isOpen;
      }
      
      public function get isShow() : Boolean
      {
         return this._isShow;
      }
   }
}

