package bagAndInfo.cell
{
   import beadSystem.controls.BeadCell;
   import com.pickgliss.ui.ShowTipManager;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.data.goods.ShopCarItemInfo;
   import ddt.data.goods.ShopItemInfo;
   import ddt.interfaces.ICell;
   import ddt.interfaces.ICellFactory;
   import ddt.manager.ItemManager;
   import ddt.manager.ShopManager;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import magicHouse.treasureHouse.MagicHouseTreasureCell;
   import shop.view.ShopItemCell;
   import shop.view.ShopPlayerCell;
   import vip.view.VipBankViewCell;
   
   public class CellFactory implements ICellFactory
   {
      
      private static var _instance:CellFactory;
      
      public function CellFactory()
      {
         super();
      }
      
      public static function get instance() : CellFactory
      {
         if(_instance == null)
         {
            _instance = new CellFactory();
         }
         return _instance;
      }
      
      public function createBagCell(_arg_1:int, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true, _arg_4:DisplayObject = null) : ICell
      {
         var _local_5:BagCell = new BagCell(_arg_1,_arg_2,_arg_3,_arg_4);
         this.fillTipProp(_local_5);
         return _local_5;
      }
      
      public function creteLockableBagCell(_arg_1:int, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true, _arg_4:DisplayObject = null) : ICell
      {
         var _local_5:LockableBagCell = new LockableBagCell(_arg_1,_arg_2,_arg_3,_arg_4);
         this.fillTipProp(_local_5);
         return _local_5;
      }
      
      public function createBankCell(_arg_1:int, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true) : ICell
      {
         var _local_4:BankCell = new BankCell(_arg_1,_arg_2,_arg_3);
         this.fillTipProp(_local_4);
         return _local_4;
      }
      
      public function createPersonalInfoCell(_arg_1:int, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true) : ICell
      {
         var _local_4:BagCell = new PersonalInfoCell(_arg_1,_arg_2,_arg_3);
         this.fillTipProp(_local_4);
         return _local_4;
      }
      
      public function createTreasureCell(_arg_1:int, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true) : ICell
      {
         var _local_4:MagicHouseTreasureCell = new MagicHouseTreasureCell(_arg_1,_arg_2,_arg_3);
         this.fillTipProp(_local_4);
         return _local_4;
      }
      
      public function createBeadCell(_arg_1:int, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true) : ICell
      {
         var _local_4:BeadCell = new BeadCell(_arg_1,_arg_2,_arg_3);
         this.fillTipProp(_local_4);
         return _local_4;
      }
      
      public function createVipBankCell(_arg_1:int, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true) : ICell
      {
         var _local_4:VipBankViewCell = new VipBankViewCell(_arg_1,_arg_2,_arg_3);
         this.fillTipProp(_local_4);
         return _local_4;
      }
      
      public function createShopPlayerItemCell() : ICell
      {
         var _local_1:Sprite = new Sprite();
         _local_1.graphics.beginFill(********,0);
         _local_1.graphics.drawRect(0,0,45,45);
         _local_1.graphics.endFill();
         var _local_2:ShopPlayerCell = new ShopPlayerCell(_local_1);
         this.fillTipProp(_local_2);
         return _local_2;
      }
      
      public function createShopCartItemCell() : ICell
      {
         var _local_1:Sprite = new Sprite();
         _local_1.graphics.beginFill(********,0);
         _local_1.graphics.drawRect(0,0,64,64);
         _local_1.graphics.endFill();
         var _local_2:ShopPlayerCell = new ShopPlayerCell(_local_1);
         this.fillTipProp(_local_2);
         return _local_2;
      }
      
      public function createShopColorItemCell() : ICell
      {
         var _local_1:Sprite = new Sprite();
         _local_1.graphics.beginFill(********,0);
         _local_1.graphics.drawRect(0,0,90,90);
         _local_1.graphics.endFill();
         var _local_2:ShopPlayerCell = new ShopPlayerCell(_local_1);
         this.fillTipProp(_local_2);
         return _local_2;
      }
      
      public function createShopItemCell(_arg_1:DisplayObject, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true, _arg_4:Boolean = true) : ICell
      {
         var _local_5:ShopItemCell = new ShopItemCell(_arg_1,_arg_2,_arg_3,_arg_4);
         this.fillTipProp(_local_5);
         return _local_5;
      }
      
      public function fillTipProp(_arg_1:ICell) : void
      {
         _arg_1.tipDirctions = "7,6,2,1,5,4,0,3,6";
         _arg_1.tipGapV = 10;
         _arg_1.tipGapH = 10;
         _arg_1.tipStyle = "core.GoodsTip";
      }
      
      public function createWeeklyItemCell(_arg_1:DisplayObject, _arg_2:int) : ICell
      {
         var _local_3:* = null;
         var _local_5:* = ShopManager.Instance.getShopItemByGoodsID(_arg_2);
         if(!_local_5)
         {
            _local_5 = ItemManager.Instance.getTemplateById(_arg_2);
         }
         var _local_4:ShopPlayerCell = new ShopPlayerCell(_arg_1);
         if(_local_5 is ItemTemplateInfo)
         {
            _local_4.info = _local_5;
         }
         if(_local_5 is ShopItemInfo)
         {
            _local_3 = new ShopCarItemInfo(_local_5.GoodsID,_local_5.TemplateID);
            ObjectUtils.copyProperties(_local_3,_local_5);
            _local_4.shopItemInfo = _local_3;
         }
         ShowTipManager.Instance.removeTip(_local_4);
         return _local_4;
      }
   }
}

