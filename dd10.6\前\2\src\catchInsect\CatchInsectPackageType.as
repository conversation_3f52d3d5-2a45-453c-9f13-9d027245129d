package catchInsect
{
   public class CatchInsectPackageType
   {
      
      public static const OPEN_OR_CLOSE:int = 128;
      
      public static const ENTER_SCENE:int = 143;
      
      public static const ADDPLAYER:int = 129;
      
      public static const MOVE:int = 141;
      
      public static const PLAYER_STATUE:int = 131;
      
      public static const REMOVE_PLAYER:int = 130;
      
      public static const MONSTER:int = 133;
      
      public static const FIGHT_MONSTER:int = 140;
      
      public static const UPDATE_INFO:int = 135;
      
      public static const GET_PRIZE:int = 139;
      
      public static const UPDATE_AREA_RANK:int = 137;
      
      public static const AREA_SELF_INFO:int = 132;
      
      public static const UPDATE_LOCAL_RANK:int = 136;
      
      public static const LOCAL_SELF_INFO:int = 138;
      
      public static const CAKE_STATUS:int = 134;
      
      public static const WHISTLE_USE:int = 154;
      
      public static const WHISTLE_BUY:int = 155;
      
      public function CatchInsectPackageType()
      {
         super();
      }
   }
}

