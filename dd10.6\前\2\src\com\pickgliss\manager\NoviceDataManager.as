package com.pickgliss.manager
{
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import flash.net.URLVariables;
   
   public class NoviceDataManager extends EventDispatcher
   {
      
      private static var _instance:NoviceDataManager;
      
      public var firstEnterGame:Boolean = false;
      
      public function NoviceDataManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get instance() : NoviceDataManager
      {
         if(!_instance)
         {
            _instance = new NoviceDataManager();
         }
         return _instance;
      }
      
      public function saveNoviceData(_arg_1:int, _arg_2:String, _arg_3:String) : void
      {
         var _local_5:URLVariables = new URLVariables();
         _local_5["nodeID"] = _arg_1;
         _local_5["userName"] = _arg_2;
         var _local_4:BaseLoader = LoadResourceManager.Instance.createLoader(_arg_3 + "NoviceNodeData.ashx",6,_local_5);
         LoadResourceManager.Instance.startLoad(_local_4);
      }
   }
}

