package bagAndInfo.bag.trailelite
{
   import bagAndInfo.BagAndInfoManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.text.FilterFrameText;
   import ddt.manager.LanguageMgr;
   import flash.display.Bitmap;
   import flash.events.MouseEvent;
   
   public class TrailEliteView extends Frame
   {
      
      private var bg:Bitmap;
      
      private var jinjisai:Bitmap;
      
      private var zhanyi:Bitmap;
      
      private var levelIcon:Bitmap;
      
      private var contributionTxt:FilterFrameText;
      
      private var radioTxt:FilterFrameText;
      
      private var curlevelTxt:FilterFrameText;
      
      private var curLevelValueTxt:FilterFrameText;
      
      private var rateTxt:FilterFrameText;
      
      private var rateValueTxt:FilterFrameText;
      
      private var leftDay:FilterFrameText;
      
      private var leftDayValue:FilterFrameText;
      
      private var confirmBtn:TextButton;
      
      public function TrailEliteView()
      {
         super();
         this.initView();
         this.addEvent();
      }
      
      private function initView() : void
      {
         this.bg = ComponentFactory.Instance.creat("asset.trailelite.bg" + BagAndInfoManager.Instance.trialEliteModel.battleRank);
         addToContent(this.bg);
         this.zhanyi = ComponentFactory.Instance.creat("asset.trailelite.zhanyi");
         this.zhanyi.x = 38;
         this.zhanyi.y = 173;
         addToContent(this.zhanyi);
         this.jinjisai = ComponentFactory.Instance.creat("asset.trailelite.jinjisai");
         this.jinjisai.x = 54;
         this.jinjisai.y = 200;
         addToContent(this.jinjisai);
         this.contributionTxt = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.TrailEliteView.contribution");
         this.contributionTxt.text = BagAndInfoManager.Instance.trialEliteModel.battleScore + "/500";
         addToContent(this.contributionTxt);
         this.radioTxt = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.TrailEliteView.radio");
         if(Boolean(BagAndInfoManager.Instance.trialEliteModel.isRankUp))
         {
            this.radioTxt.text = BagAndInfoManager.Instance.trialEliteModel.rankUpWin + "/" + (BagAndInfoManager.Instance.trialEliteModel.rankUpCount - BagAndInfoManager.Instance.trialEliteModel.rankUpWin);
         }
         else
         {
            this.radioTxt.text = "--/--";
         }
         addToContent(this.radioTxt);
         this.curlevelTxt = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.TrailEliteView.level");
         this.curlevelTxt.text = LanguageMgr.GetTranslation("game.trailelite.curlevel") + ":";
         addToContent(this.curlevelTxt);
         this.rateTxt = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.TrailEliteView.rate");
         this.rateTxt.text = LanguageMgr.GetTranslation("game.trailelite.rate") + ":";
         addToContent(this.rateTxt);
         this.leftDay = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.TrailEliteView.leftDay");
         this.leftDay.text = LanguageMgr.GetTranslation("game.trailelite.leftDay") + ":";
         addToContent(this.leftDay);
         this.leftDayValue = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.TrailEliteView.leftDayValue");
         this.leftDayValue.text = LanguageMgr.GetTranslation("game.trailelite.leftDayValue",BagAndInfoManager.Instance.trialEliteModel.lastDays);
         addToContent(this.leftDayValue);
         this.curLevelValueTxt = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.TrailEliteView.levelValue");
         this.curLevelValueTxt.text = LanguageMgr.GetTranslation("game.trailelite.level" + BagAndInfoManager.Instance.trialEliteModel.battleRank);
         addToContent(this.curLevelValueTxt);
         this.rateValueTxt = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.TrailEliteView.rateValue");
         if(Boolean(BagAndInfoManager.Instance.trialEliteModel.totalCount))
         {
            this.rateValueTxt.text = int(BagAndInfoManager.Instance.trialEliteModel.totalWin * 100 / BagAndInfoManager.Instance.trialEliteModel.totalCount) + "%";
         }
         else
         {
            this.rateValueTxt.text = "0%";
         }
         addToContent(this.rateValueTxt);
         this.confirmBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.TrailEliteView.confirmBtn");
         this.confirmBtn.text = LanguageMgr.GetTranslation("game.trailelite.confirm");
         addToContent(this.confirmBtn);
      }
      
      private function addEvent() : void
      {
         this.confirmBtn.addEventListener("click",this._confirmBtnClickHandler);
      }
      
      private function _confirmBtnClickHandler(_arg_1:MouseEvent) : void
      {
         onResponse(0);
      }
   }
}

