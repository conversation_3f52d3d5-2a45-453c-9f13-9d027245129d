package AvatarCollection.data
{
   import AvatarCollection.AvatarCollectionManager;
   import com.pickgliss.ui.controls.cell.INotSameHeightListCellData;
   
   public class AvatarCollectionUnitVo implements INotSameHeightListCellData
   {
      
      private var _selected:Boolean = false;
      
      private var _id:int;
      
      public var sex:int;
      
      public var name:String;
      
      public var Attack:int;
      
      public var Defence:int;
      
      public var Agility:int;
      
      public var Luck:int;
      
      public var Blood:int;
      
      public var Damage:int;
      
      public var Guard:int;
      
      public var needHonor:int;
      
      public var endTime:Date;
      
      public var Type:int = 1;
      
      public var OneAttack:int = 0;
      
      public var OneDefend:int = 0;
      
      public var OneAgility:int = 0;
      
      public var OneLuck:int = 0;
      
      public var OneBlood:int = 0;
      
      public var OneDamage:int = 0;
      
      public var OneGuard:int = 0;
      
      public function AvatarCollectionUnitVo()
      {
         super();
      }
      
      public function get selected() : Boolean
      {
         return this._selected;
      }
      
      public function set selected(_arg_1:<PERSON>olean) : void
      {
         var _local_2:int = int(this.totalItemList.length / 2);
         if(this.totalActivityItemCount >= _local_2)
         {
            this._selected = _arg_1;
         }
         else
         {
            this._selected = false;
         }
      }
      
      public function get id() : int
      {
         return this._id;
      }
      
      public function set id(_arg_1:int) : void
      {
         this._id = _arg_1;
      }
      
      public function get totalItemList() : Array
      {
         return AvatarCollectionManager.instance.getItemListById(this.sex,this.id,this.Type);
      }
      
      public function get totalActivityItemCount() : int
      {
         var _local_3:int = 0;
         var _local_1:AvatarCollectionItemVo = null;
         var _local_2:Array = this.totalItemList;
         for each(_local_1 in _local_2)
         {
            if(_local_1.isActivity)
            {
               _local_3++;
            }
         }
         return _local_3;
      }
      
      public function get canActivityCount() : int
      {
         var _local_3:int = 0;
         var _local_1:AvatarCollectionItemVo = null;
         var _local_2:Array = this.totalItemList;
         for each(_local_1 in _local_2)
         {
            if(!_local_1.isActivity && _local_1.isHas)
            {
               _local_3++;
            }
         }
         return _local_3;
      }
      
      public function get canBuyCount() : int
      {
         var _local_3:int = 0;
         var _local_1:AvatarCollectionItemVo = null;
         var _local_2:Array = this.totalItemList;
         for each(_local_1 in _local_2)
         {
            if(!_local_1.isActivity && !_local_1.isHas && _local_1.canBuyStatus == 1)
            {
               _local_3++;
            }
         }
         return _local_3;
      }
      
      public function getCellHeight() : Number
      {
         return 37;
      }
   }
}

