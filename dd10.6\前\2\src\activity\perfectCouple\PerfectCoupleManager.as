package activity.perfectCouple
{
   import activity.perfectCouple.analyzer.PerfectCoupleAchvAnalyzer;
   import activity.perfectCouple.analyzer.PerfectCouplePetAnalyzer;
   import activity.perfectCouple.data.PerfectCoupleAchvInfo;
   import activity.perfectCouple.data.PerfectCoupleAchvTemp;
   import activity.perfectCouple.data.PerfectCouplePetTemp;
   import activity.perfectCouple.model.PerfectCoupleModel;
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ClassUtils;
   import ddt.events.PkgEvent;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PathManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.utils.AssetModuleLoader;
   import flash.display.Sprite;
   import flash.events.EventDispatcher;
   import flash.utils.Dictionary;
   import road7th.data.DictionaryData;
   
   public class PerfectCoupleManager extends EventDispatcher
   {
      
      private static var _instance:PerfectCoupleManager;
      
      private var _model:PerfectCoupleModel;
      
      private var _petTemp:DictionaryData;
      
      private var _achvTemp:Dictionary;
      
      public function PerfectCoupleManager()
      {
         super();
      }
      
      public static function get instance() : PerfectCoupleManager
      {
         if(!_instance)
         {
            _instance = new PerfectCoupleManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
      }
      
      private function __onPetInfo(_arg_1:PkgEvent) : void
      {
         var _local_3:int = 0;
         var _local_5:int = 0;
         var _local_2:* = null;
         var _local_4:* = null;
         if(Boolean(this._model))
         {
            this._model.petLevel = _arg_1.pkg.readInt();
            if(this._model.hasPet)
            {
               this._model.petExp = _arg_1.pkg.readInt();
               this._model.petRewardState = _arg_1.pkg.readUTF();
               this._model.giveWifeGiftNum = _arg_1.pkg.readInt();
               this._model.giveFriendGiftNum = _arg_1.pkg.readInt();
               this._model.getWifeGiftNum = _arg_1.pkg.readInt();
               this._model.getFriendGiftNum = _arg_1.pkg.readInt();
               this._model.achvScore = _arg_1.pkg.readInt();
               this._model.rank = _arg_1.pkg.readInt();
               _local_2 = [];
               _local_3 = _arg_1.pkg.readInt();
               _local_5 = 0;
               while(_local_5 < _local_3)
               {
                  _local_4 = new PerfectCoupleAchvInfo();
                  _local_4.id = _arg_1.pkg.readInt();
                  _local_4.progress = _arg_1.pkg.readInt();
                  _local_4.isComplete = _arg_1.pkg.readBoolean();
                  _local_4.createDate = _arg_1.pkg.readDate();
                  _local_4.completeDate = _arg_1.pkg.readDate();
                  _local_2.push(_local_4);
                  _local_5++;
               }
               this._model.achvList = _local_2;
            }
            dispatchEvent(new PerfectCoupleEvent("updateinfo"));
         }
      }
      
      private function __onWifeInfo(_arg_1:PkgEvent) : void
      {
         if(Boolean(this._model))
         {
            this._model.wifeName = _arg_1.pkg.readUTF();
            this._model.wifePetLevel = _arg_1.pkg.readInt();
            this._model.wifePetExp = _arg_1.pkg.readInt();
            this._model.wifeAchvScore = _arg_1.pkg.readInt();
            this._model.wifeRank = _arg_1.pkg.readInt();
            dispatchEvent(new PerfectCoupleEvent("updatewifeinfo"));
         }
      }
      
      public function show() : void
      {
         var _local_1:int = ServerConfigManager.instance.perfectCoupleLevelLimit;
         if(PlayerManager.Instance.Self.Grade < _local_1)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",_local_1));
            return;
         }
         if(!this._petTemp)
         {
            AssetModuleLoader.addRequestLoader(this.createPetTempLoader());
         }
         if(!this._achvTemp)
         {
            AssetModuleLoader.addRequestLoader(this.creatAchvTempLoader());
         }
         AssetModuleLoader.addModelLoader("perfectcouple",5);
         AssetModuleLoader.startCodeLoader(this.onLoadComplete);
      }
      
      private function onLoadComplete() : void
      {
         this.clearModel();
         this._model = new PerfectCoupleModel();
         SocketManager.Instance.addEventListener(PkgEvent.format(656,2),this.__onPetInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(656,9),this.__onWifeInfo);
         var _local_1:Sprite = ClassUtils.CreatInstance("activity.perfectCouple.view.PerfectCoupleMainView");
         LayerManager.Instance.addToLayer(_local_1,3,true,1);
      }
      
      public function clearModel() : void
      {
         SocketManager.Instance.removeEventListener(PkgEvent.format(656,2),this.__onPetInfo);
         SocketManager.Instance.removeEventListener(PkgEvent.format(656,9),this.__onWifeInfo);
         this._model = null;
      }
      
      private function petTempAnalyze(_arg_1:PerfectCouplePetAnalyzer) : void
      {
         this._petTemp = _arg_1.data;
      }
      
      private function achvTempAnalyze(_arg_1:PerfectCoupleAchvAnalyzer) : void
      {
         this._achvTemp = _arg_1.data;
      }
      
      public function get petList() : Array
      {
         return this._petTemp.list.sortOn("Level",0x10 | 2);
      }
      
      public function getPetTemp(_arg_1:int) : PerfectCouplePetTemp
      {
         return this._petTemp[_arg_1];
      }
      
      public function getAchvTemp(_arg_1:int) : PerfectCoupleAchvTemp
      {
         return this._achvTemp[_arg_1];
      }
      
      public function createPetTempLoader() : BaseLoader
      {
         var _local_1:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("TS_PerfectCouplePet.xml"),5);
         _local_1.loadErrorMessage = "loadPerfectCouplePetTempFail";
         _local_1.analyzer = new PerfectCouplePetAnalyzer(this.petTempAnalyze);
         return _local_1;
      }
      
      public function creatAchvTempLoader() : BaseLoader
      {
         var _local_1:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("TS_PerfectCoupleAchv.xml"),5);
         _local_1.loadErrorMessage = "loadPerfectCoupleAchvTempFail";
         _local_1.analyzer = new PerfectCoupleAchvAnalyzer(this.achvTempAnalyze);
         return _local_1;
      }
      
      public function getPetLevelByExp(_arg_1:int) : int
      {
         var _local_4:int = 0;
         var _local_3:* = null;
         var _local_2:int = 1;
         _local_4 = 1;
         while(_local_4 <= this._petTemp.list.length)
         {
            _local_3 = this.getPetTemp(_local_4);
            if(_arg_1 < _local_3.GP)
            {
               break;
            }
            _local_2 = int(_local_3.Level);
            _local_4++;
         }
         return _local_2;
      }
      
      public function get model() : PerfectCoupleModel
      {
         return this._model;
      }
   }
}

