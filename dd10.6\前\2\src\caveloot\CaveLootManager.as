package caveloot
{
   import activity.ActivityRankInfo;
   import caveloot.cnf.CaveLootMinePoolAnalyzer;
   import caveloot.cnf.CaveLootProgressAwardAnalyzer;
   import caveloot.data.CaveLootCnfInfo;
   import caveloot.data.CaveLootProgressCnfInfo;
   import caveloot.data.CaveLootRankInfo;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.utils.AssetModuleLoader;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import hallIcon.HallIconManager;
   import newActivity.NewActivityCnfInfo;
   import newActivity.NewActivityManager;
   import road7th.comm.PackageIn;
   import road7th.data.DictionaryData;
   
   public class CaveLootManager extends EventDispatcher
   {
      
      private static var _instance:CaveLootManager;
      
      private var _model:CaveLootModel;
      
      private var _isLoadXml:Boolean = false;
      
      public function CaveLootManager()
      {
         super();
      }
      
      public static function get instance() : CaveLootManager
      {
         if(_instance == null)
         {
            _instance = new CaveLootManager();
         }
         return _instance;
      }
      
      public function setUp() : void
      {
         this._model = new CaveLootModel();
         this.addEvent();
      }
      
      private function addEvent() : void
      {
         NewActivityManager.instance.addEventListener("loadCNFXmlComplete",this.loadActivityXmlComplete);
         SocketManager.Instance.addEventListener(PkgEvent.format(772,1),this.__onGetActivity);
         SocketManager.Instance.addEventListener(PkgEvent.format(772,2),this.__onGetBaseInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(772,4),this.__onGetDrawReslut);
         SocketManager.Instance.addEventListener(PkgEvent.format(772,18),this.__onGetRankInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(772,7),this.__onGetProgressInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(772,8),this.__onGetProgressInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(772,9),this.__onUpdateShopCoin);
      }
      
      private function __onGetActivity(_arg_1:PkgEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_3.readInt();
         this._model.activityState = _local_2;
         this.checkBtn();
      }
      
      private function __onGetBaseInfo(_arg_1:PkgEvent) : void
      {
         var _local_12:int = 0;
         var _local_10:int = 0;
         var _local_4:int = 0;
         var _local_5:int = 0;
         var _local_7:* = null;
         var _local_2:* = null;
         var _local_11:* = null;
         this._model.bagDic.clear();
         this._model.totalMineNum.clear();
         this._model.totalMineNum = new DictionaryData();
         var _local_8:PackageIn = _arg_1.pkg;
         this._model.dayFreeNum = _local_8.readInt();
         this._model.myScore = _local_8.readInt();
         var _local_9:int = _local_8.readInt();
         _local_12 = 0;
         while(_local_12 < _local_9)
         {
            _local_10 = _local_8.readInt();
            _local_4 = _local_8.readInt();
            _local_7 = _local_8.readUTF();
            _local_2 = _local_8.readUTF();
            _local_11 = _local_8.readUTF();
            this.setBagInfoByTypeAndStr(_local_10,_local_7);
            this.setFloorBonusData(_local_10,_local_4,_local_11,_local_2);
            _local_5 = _local_8.readInt();
            if(_local_10 == 1)
            {
               this._model.shopCoin1 = _local_5;
            }
            else if(_local_10 == 2)
            {
               this._model.shopCoin2 = _local_5;
            }
            _local_12++;
         }
         dispatchEvent(new CaveLootEvent("updateMineView"));
      }
      
      public function __onGetDrawReslut(_arg_1:PkgEvent) : void
      {
         var _local_9:int = 0;
         var _local_4:int = 0;
         var _local_2:int = 0;
         var _local_8:* = undefined;
         var _local_10:Boolean = false;
         this._model.curResultArr = [];
         var _local_5:PackageIn = _arg_1.pkg;
         var _local_7:int = _local_5.readInt();
         var _local_3:int = _local_5.readInt();
         var _local_6:int = _local_5.readInt();
         _local_9 = 0;
         while(_local_9 < _local_6)
         {
            _local_4 = _local_5.readInt();
            _local_2 = _local_5.readInt();
            _local_8 = {};
            _local_8["id"] = _local_4;
            _local_8["num"] = _local_2;
            this._model.curResultArr.push(_local_8);
            if(this._model.tipsItemArr.indexOf(_local_4) >= 0)
            {
               _local_10 = true;
            }
            _local_9++;
         }
         dispatchEvent(new CaveLootEvent("getMineReslut",[_local_7,_local_10,_local_3]));
      }
      
      public function __onGetRankInfo(_arg_1:PkgEvent) : void
      {
         var _local_11:int = 0;
         var _local_7:int = 0;
         var _local_2:int = 0;
         var _local_6:Boolean = false;
         var _local_4:* = null;
         var _local_5:* = null;
         var _local_3:* = null;
         var _local_10:* = null;
         this._model.rankList = [];
         var _local_8:PackageIn = _arg_1.pkg;
         var _local_9:int = _local_8.readInt();
         _local_11 = 0;
         while(_local_11 < _local_9)
         {
            _local_4 = _local_8.readUTF();
            _local_5 = _local_8.readUTF();
            _local_7 = _local_8.readInt();
            _local_2 = _local_8.readInt();
            _local_3 = _local_8.readUTF();
            _local_6 = _local_8.readBoolean();
            _local_10 = new CaveLootRankInfo();
            _local_10.areaName = _local_4;
            _local_10.name = _local_5;
            _local_10.score = _local_7;
            _local_10.rank = _local_2;
            this._model.rankList.push(_local_10);
            _local_11++;
         }
         this._model.myRank = _local_8.readInt();
         dispatchEvent(new CaveLootEvent("updateRankView"));
      }
      
      public function __onGetProgressInfo(_arg_1:PkgEvent) : void
      {
         var _local_2:* = null;
         this._model.progressStateArr = [];
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_3:String = _local_4.readUTF();
         var _local_5:int = _local_4.readInt();
         var _local_6:int = _local_4.readInt();
         this._model.myProgressNum = _local_5;
         this._model.serverProgress = _local_6;
         if(_local_3 != "")
         {
            _local_2 = _local_3.split(",");
            this._model.progressStateArr = _local_2;
         }
         dispatchEvent(new CaveLootEvent("updateProgressView"));
      }
      
      private function __onUpdateShopCoin(_arg_1:PkgEvent) : void
      {
         var _local_3:int = _arg_1.pkg.readInt();
         var _local_2:int = _arg_1.pkg.readInt();
         if(_local_3 == 1)
         {
            this._model.shopCoin1 = _local_2;
         }
         else if(_local_3 == 2)
         {
            this._model.shopCoin2 = _local_2;
         }
         dispatchEvent(new CaveLootEvent("updateShopView"));
      }
      
      private function setBagInfoByTypeAndStr(_arg_1:int, _arg_2:String) : void
      {
         var _local_7:int = 0;
         var _local_6:* = undefined;
         var _local_5:* = null;
         var _local_3:* = null;
         var _local_4:DictionaryData = new DictionaryData();
         if(_arg_2 != "")
         {
            _local_5 = _arg_2.split("|");
            _local_7 = 0;
            while(_local_7 < _local_5.length)
            {
               _local_3 = (_local_5[_local_7] as String).split(",");
               _local_6 = {};
               _local_6["id"] = _local_3[0];
               _local_6["num"] = _local_3[1];
               _local_4.add(_local_6["id"],_local_6);
               _local_7++;
            }
         }
         this._model.bagDic[_arg_1] = _local_4;
      }
      
      private function setFloorBonusData(_arg_1:int, _arg_2:int, _arg_3:String, _arg_4:String) : void
      {
         var _local_9:int = 0;
         var _local_8:int = 0;
         var _local_5:int = 0;
         var _local_7:* = null;
         this._model.totalMineNum.add(_arg_1,_arg_2);
         this._model.floorBonusGotArr.add(_arg_1,_arg_3.split(","));
         this._model.floorBonusMineRecord.add(_arg_1,new DictionaryData());
         var _local_6:Array = _arg_4.split("|");
         _local_9 = 0;
         while(_local_9 < _local_6.length)
         {
            _local_7 = (_local_6[_local_9] as String).split(",");
            _local_8 = int(_local_7[0]);
            _local_5 = int(_local_7[1]);
            (this._model.floorBonusMineRecord[_arg_1] as DictionaryData).add(_local_8,_local_5);
            _local_9++;
         }
      }
      
      public function getFloorBonusListItemState(_arg_1:int, _arg_2:int) : Array
      {
         var _local_4:int = 0;
         var _local_5:Array = [0,0];
         var _local_6:int = -1;
         var _local_3:int = int(this._model.totalMineNum[_arg_1]);
         if((this._model.floorBonusGotArr[_arg_1] as Array).indexOf(_arg_2 + "") >= 0)
         {
            return [_local_3,1];
         }
         if(Boolean(this._model.floorBonusMineRecord[_arg_1]) && (this._model.floorBonusMineRecord[_arg_1] as DictionaryData).hasKey(_arg_2))
         {
            _local_4 = int(this._model.floorBonusMineRecord[_arg_1][_arg_2]);
            _local_5 = [_local_3 - _local_4,-1];
         }
         else
         {
            _local_5 = [_local_3,-1];
         }
         return _local_5;
      }
      
      public function getRankAwardsCnfArr() : Array
      {
         var _local_10:int = 0;
         var _local_1:int = 0;
         var _local_3:int = 0;
         var _local_6:int = 0;
         var _local_2:* = null;
         var _local_9:* = null;
         var _local_8:* = null;
         var _local_4:Array = [];
         if(!this._model.cnfInfo)
         {
            return _local_4;
         }
         var _local_5:Array = this._model.cnfInfo.RankAreaAward.split("|");
         var _local_7:int = int(_local_5.length);
         _local_10 = 0;
         while(_local_10 < _local_7)
         {
            _local_2 = (_local_5[_local_10] as String).split(",");
            _local_9 = (_local_2[0] as String).split("-");
            _local_1 = int(_local_9[0]);
            _local_3 = int(_local_9[1]);
            _local_6 = int(_local_2[1]);
            _local_8 = new ActivityRankInfo();
            _local_8.MinRank = _local_1;
            _local_8.MaxRank = _local_3;
            _local_8.TemplateID = _local_6;
            _local_4.push(_local_8);
            _local_10++;
         }
         return _local_4;
      }
      
      public function getProgressStateByTypeAndID(_arg_1:int, _arg_2:int) : int
      {
         var _local_4:int = 0;
         var _local_3:CaveLootProgressCnfInfo = this._model.progressCnfDic[_arg_1][_arg_2];
         if(this._model.progressStateArr.indexOf(_arg_2 + "") >= 0)
         {
            return 3;
         }
         if(_local_3.Type == 2 && _local_3.Progress > 0)
         {
            if(this._model.serverProgress >= _local_3.Progress && this._model.myProgressNum >= _local_3.Number)
            {
               _local_4 = 2;
            }
            else
            {
               _local_4 = 1;
            }
         }
         else if(_local_3.Type == 1)
         {
            if(this._model.myProgressNum >= _local_3.Number)
            {
               _local_4 = 2;
            }
            else
            {
               _local_4 = 1;
            }
         }
         return _local_4;
      }
      
      private function isGetProgressAward() : Boolean
      {
         return false;
      }
      
      public function getMinePriceArrByTypeAndIndex(_arg_1:int, _arg_2:int) : Array
      {
         var _local_3:String = _arg_1 == 1 ? this._model.cnfInfo.Params1 : this._model.cnfInfo.Params2;
         return (_local_3.split("|")[_arg_2] as String).split(",");
      }
      
      public function getCurBagValueByType(_arg_1:int) : int
      {
         var _local_8:int = 0;
         var _local_5:int = 0;
         var _local_2:int = 0;
         var _local_4:int = 0;
         var _local_7:* = null;
         var _local_6:* = null;
         var _local_3:Array = (this._model.bagDic[_arg_1] as DictionaryData).list;
         _local_8 = 0;
         while(_local_8 < _local_3.length)
         {
            _local_7 = _local_3[_local_8];
            if(_local_7["id"] != "")
            {
               _local_5 = int(_local_7["id"]);
               _local_2 = int(_local_7["num"]);
               _local_6 = this._model.minePoolCnfItemIdDic[_arg_1][_local_5];
               _local_4 += _local_2 * _local_6.Discount;
            }
            _local_8++;
         }
         return _local_4;
      }
      
      public function checkCanOperate() : Boolean
      {
         var _local_1:Boolean = false;
         if(this._model.activityState == 2)
         {
            _local_1 = false;
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.magicTailor.rank.text14"));
         }
         else if(this._model.activityState == 1)
         {
            _local_1 = true;
         }
         else if(this._model.activityState == 0)
         {
            _local_1 = false;
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.countDown.txt2"));
         }
         return _local_1;
      }
      
      public function checkBtn() : void
      {
         if(this._model.activityState != 0)
         {
            HallIconManager.instance.updateSwitchHandler("CaveLootIcon",true);
         }
         else
         {
            HallIconManager.instance.updateSwitchHandler("CaveLootIcon",false);
         }
      }
      
      public function openView() : void
      {
         var _local_1:* = null;
         if(PlayerManager.Instance.Self.Grade >= 20 && (PlayerManager.Instance.Self.VIPLevel >= 6 && PlayerManager.Instance.Self.IsVIP) || PlayerManager.Instance.Self.Grade >= 30)
         {
            SocketManager.Instance.out.sendCaveLootGetBaseInfo();
            SocketManager.Instance.out.sendCaveLootGetProgressInfo();
            SocketManager.Instance.out.sendCaveLootGetRankInfo();
            this.loadRes();
            return;
         }
         _local_1 = LanguageMgr.GetTranslation("tank.caveloot.txt19",30,20,6);
         MessageTipManager.getInstance().show(_local_1,0,true,1);
      }
      
      private function loadRes() : void
      {
         AssetModuleLoader.addModelLoader("caveloot",5);
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createCaveLootLotteryPoolXmlLoader());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createCaveLootProgressAwardXmlLoader());
         AssetModuleLoader.startCodeLoader(this.showFrame);
      }
      
      private function showFrame() : void
      {
         var _local_1:Sprite = ClassUtils.CreatInstance("caveloot.view.CaveLootMainView") as Sprite;
         LayerManager.Instance.addToLayer(_local_1,3,true,1);
         _local_1.y = 5;
      }
      
      public function loadActivityXmlComplete(_arg_1:Event = null) : void
      {
         var _local_2:NewActivityCnfInfo = NewActivityManager.instance.getActCnfByNum(11) as NewActivityCnfInfo;
         if(!_local_2)
         {
            return;
         }
         this._model.cnfInfo = new CaveLootCnfInfo();
         ObjectUtils.copyProperties(this._model.cnfInfo,_local_2);
      }
      
      public function loadPoolXmlComplete(_arg_1:CaveLootMinePoolAnalyzer) : void
      {
         this._model.minePoolCnfDic = _arg_1.poolDic;
         this._model.minePoolCnfItemIdDic = _arg_1.poolItemIdDic;
         this._model.floorBonusCnfDic = _arg_1.floorBonusDic;
         this._model.tipsItemArr = _arg_1.tipsItemArr;
      }
      
      public function loadProgressXmlCmplete(_arg_1:CaveLootProgressAwardAnalyzer) : void
      {
         this._model.progressCnfDic = _arg_1.progressDic;
      }
      
      public function get model() : CaveLootModel
      {
         return this._model;
      }
   }
}

