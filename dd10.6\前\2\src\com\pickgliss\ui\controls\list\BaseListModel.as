package com.pickgliss.ui.controls.list
{
   import com.pickgliss.utils.ArrayUtils;
   
   public class BaseListModel
   {
      
      private var listeners:Array;
      
      public function BaseListModel()
      {
         super();
         this.listeners = [];
      }
      
      public function addListDataListener(_arg_1:ListDataListener) : void
      {
         this.listeners.push(_arg_1);
      }
      
      public function removeListDataListener(_arg_1:ListDataListener) : void
      {
         ArrayUtils.removeFromArray(this.listeners,_arg_1);
      }
      
      protected function fireContentsChanged(_arg_1:Object, _arg_2:int, _arg_3:int, _arg_4:Array) : void
      {
         var _local_7:int = 0;
         var _local_5:* = null;
         var _local_6:ListDataEvent = new ListDataEvent(_arg_1,_arg_2,_arg_3,_arg_4);
         _local_7 = this.listeners.length - 1;
         while(_local_7 >= 0)
         {
            _local_5 = ListDataListener(this.listeners[_local_7]);
            _local_5.contentsChanged(_local_6);
            _local_7--;
         }
      }
      
      protected function fireIntervalAdded(_arg_1:Object, _arg_2:int, _arg_3:int) : void
      {
         var _local_6:int = 0;
         var _local_4:* = null;
         var _local_5:ListDataEvent = new ListDataEvent(_arg_1,_arg_2,_arg_3,[]);
         _local_6 = this.listeners.length - 1;
         while(_local_6 >= 0)
         {
            _local_4 = ListDataListener(this.listeners[_local_6]);
            _local_4.intervalAdded(_local_5);
            _local_6--;
         }
      }
      
      protected function fireIntervalRemoved(_arg_1:Object, _arg_2:int, _arg_3:int, _arg_4:Array) : void
      {
         var _local_7:int = 0;
         var _local_5:* = null;
         var _local_6:ListDataEvent = new ListDataEvent(_arg_1,_arg_2,_arg_3,_arg_4);
         _local_7 = this.listeners.length - 1;
         while(_local_7 >= 0)
         {
            _local_5 = ListDataListener(this.listeners[_local_7]);
            _local_5.intervalRemoved(_local_6);
            _local_7--;
         }
      }
   }
}

