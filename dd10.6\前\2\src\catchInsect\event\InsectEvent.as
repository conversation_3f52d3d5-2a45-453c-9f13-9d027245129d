package catchInsect.event
{
   import flash.events.Event;
   
   public class InsectEvent extends Event
   {
      
      public static const CATCHINSECT_OPENVIEW:String = "catchInsectOpenView";
      
      public static const CATCHINSECT_DISPOSEENTERICON:String = "catchInsectDisposeEnterIcon";
      
      public static const CATCHINSECT_LOADMAP:String = "catchInsectLoadMap";
      
      public static const UPDATE_MONSTER_STATE:String = "update_monster_state";
      
      public static const MONSTER_ACTIVE_START:String = "monster_active_start";
      
      public static const USE_PROP:String = "useProp";
      
      public var data:Object;
      
      public function InsectEvent(_arg_1:String, _arg_2:Object = null)
      {
         this.data = _arg_2;
         super(_arg_1);
      }
   }
}

