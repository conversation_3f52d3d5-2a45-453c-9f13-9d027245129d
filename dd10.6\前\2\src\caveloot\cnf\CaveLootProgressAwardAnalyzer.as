package caveloot.cnf
{
   import caveloot.data.CaveLootProgressCnfInfo;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import road7th.data.DictionaryData;
   
   public class CaveLootProgressAwardAnalyzer extends DataAnalyzer
   {
      
      private var _progressDic:DictionaryData;
      
      public function CaveLootProgressAwardAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         this._progressDic = new DictionaryData();
         var _local_2:XML = new XML(_arg_1);
         if(_local_2.@value == "true")
         {
            _local_3 = _local_2..Item;
            _local_5 = 0;
            while(_local_5 < _local_3.length())
            {
               _local_4 = new CaveLootProgressCnfInfo();
               ObjectUtils.copyPorpertiesByXML(_local_4,_local_3[_local_5]);
               if(!this._progressDic.hasKey(_local_4.Type))
               {
                  this._progressDic.add(_local_4.Type,new DictionaryData());
               }
               (this._progressDic[_local_4.Type] as DictionaryData).add(_local_4.ID,_local_4);
               _local_5++;
            }
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
         }
         onAnalyzeComplete();
      }
      
      public function get progressDic() : DictionaryData
      {
         return this._progressDic;
      }
   }
}

