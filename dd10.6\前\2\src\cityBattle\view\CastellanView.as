package cityBattle.view
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   
   public class CastellanView extends Sprite implements Disposeable
   {
      
      private var _bg:Bitmap;
      
      private var building1:CastellanBuilding;
      
      private var building2:CastellanBuilding;
      
      private var building3:CastellanBuilding;
      
      private var building4:CastellanBuilding;
      
      private var building5:CastellanBuilding;
      
      private var building6:CastellanBuilding;
      
      private var building7:CastellanBuilding;
      
      public function CastellanView()
      {
         super();
         this.init();
      }
      
      private function init() : void
      {
         this._bg = ComponentFactory.Instance.creatBitmap("asset.cityBattle.bg1");
         addChild(this._bg);
         this.building1 = new CastellanBuilding(1);
         addChild(this.building1);
         PositionUtils.setPos(this.building1,"castellan.building1Pos");
         this.building2 = new CastellanBuilding(2);
         addChild(this.building2);
         PositionUtils.setPos(this.building2,"castellan.building2Pos");
         this.building3 = new CastellanBuilding(3);
         addChild(this.building3);
         PositionUtils.setPos(this.building3,"castellan.building3Pos");
         this.building4 = new CastellanBuilding(4);
         addChild(this.building4);
         PositionUtils.setPos(this.building4,"castellan.building4Pos");
         this.building5 = new CastellanBuilding(5);
         addChild(this.building5);
         PositionUtils.setPos(this.building5,"castellan.building5Pos");
         this.building7 = new CastellanBuilding(7);
         addChild(this.building7);
         PositionUtils.setPos(this.building7,"castellan.building7Pos");
         this.building6 = new CastellanBuilding(6);
         addChild(this.building6);
         PositionUtils.setPos(this.building6,"castellan.building6Pos");
      }
      
      public function dispose() : void
      {
         ObjectUtils.disposeObject(this._bg);
         this._bg = null;
         ObjectUtils.disposeObject(this.building1);
         this.building1 = null;
         ObjectUtils.disposeObject(this.building2);
         this.building2 = null;
         ObjectUtils.disposeObject(this.building3);
         this.building3 = null;
         ObjectUtils.disposeObject(this.building4);
         this.building4 = null;
         ObjectUtils.disposeObject(this.building5);
         this.building5 = null;
         ObjectUtils.disposeObject(this.building6);
         this.building6 = null;
         ObjectUtils.disposeObject(this.building7);
         this.building7 = null;
      }
   }
}

