package auditorium.event
{
   import flash.events.Event;
   
   public class BirthdayEvent extends Event
   {
      
      public static const CREATE_ROOM_VIEW:String = "createroomview";
      
      public static const SWITCH_VIEW:String = "switchview";
      
      public static const UPDATE_ALL_ROOMINFO:String = "updateallroominfo";
      
      public static const BIRTH_STATUS_CHANGE:String = "BirthStatusChange";
      
      public static const UPDATE_ROOMINFO:String = "modelupdateroominfo";
      
      public static const ROOM_CLOSE:String = "birthdayRoomClose";
      
      public static const GET_REDPACKET_CLICK:String = "getRedPacketClick";
      
      private var _data:Object;
      
      public function BirthdayEvent(_arg_1:String, _arg_2:Object = null)
      {
         this._data = _arg_2;
         super(_arg_1);
      }
      
      public function get data() : Object
      {
         return this._data;
      }
   }
}

