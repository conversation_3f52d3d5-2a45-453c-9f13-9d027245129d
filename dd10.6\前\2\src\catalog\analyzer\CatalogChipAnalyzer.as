package catalog.analyzer
{
   import catalog.data.CatalogChipTemplateData;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import road7th.data.DictionaryData;
   
   public class CatalogChipAnalyzer extends DataAnalyzer
   {
      
      private var _data:DictionaryData;
      
      public function CatalogChipAnalyzer(param1:Function = null)
      {
         super(param1);
      }
      
      override public function analyze(param1:*) : void
      {
         var _loc2_:int = 0;
         var _loc3_:XMLList = null;
         var _loc4_:int = 0;
         var _loc5_:CatalogChipTemplateData = null;
         this._data = new DictionaryData();
         var _loc6_:XML = new XML(param1);
         if(_loc6_.@value == "true")
         {
            _loc2_ = int(_loc6_.Item.length());
            _loc3_ = _loc6_.Item;
            _loc4_ = 0;
            while(_loc4_ < _loc2_)
            {
               _loc5_ = new CatalogChipTemplateData();
               ObjectUtils.copyPorpertiesByXML(_loc5_,_loc3_[_loc4_]);
               this._data[_loc5_.ItemID] = _loc5_;
               _loc4_++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _loc6_.@message;
            onAnalyzeError();
            onAnalyzeComplete();
         }
      }
      
      public function get data() : DictionaryData
      {
         return this._data;
      }
   }
}

