package com.pickgliss.utils
{
   public function handler(_arg_1:Function, ... _args) : Function
   {
      var func:* = undefined;
      var args:* = undefined;
      var callee:Function = null;
      func = undefined;
      args = undefined;
      func = _arg_1;
      args = _args;
      callee = function(... _args):void
      {
         var _local_2:Array = _args.concat(args);
         if(func.length > _local_2.length)
         {
            _local_2.push(callee);
         }
         func.apply(this,_local_2);
      };
      return callee;
   }
}

