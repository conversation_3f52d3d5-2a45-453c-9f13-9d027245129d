package cardCollectAward.data
{
   public class AwardItem
   {
      
      private var _title:String;
      
      private var _item:Vector.<ItemInfo>;
      
      public function AwardItem()
      {
         super();
         this._item = new Vector.<ItemInfo>();
      }
      
      public function addItem(_arg_1:ItemInfo) : void
      {
         if(this._item != null)
         {
            this._item.push(_arg_1);
         }
      }
      
      public function get item() : Vector.<ItemInfo>
      {
         return this._item;
      }
      
      public function get title() : String
      {
         return this._title;
      }
      
      public function set title(_arg_1:String) : void
      {
         this._title = _arg_1;
      }
   }
}

