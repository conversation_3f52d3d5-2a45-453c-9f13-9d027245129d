package accumulativeLogin.data
{
   public class AccumulativeLoginRewardData
   {
      
      public var Count:int;
      
      public var RewardItemID:int;
      
      public var ID:int;
      
      public var IsSelect:Boolean;
      
      public var IsBind:Boolean;
      
      public var RewardItemValid:int;
      
      public var RewardItemCount:int;
      
      public var StrengthenLevel:int;
      
      public var AttackCompose:int;
      
      public var DefendCompose:int;
      
      public var AgilityCompose:int;
      
      public var LuckCompose:int;
      
      public function AccumulativeLoginRewardData()
      {
         super();
      }
   }
}

