package battleSkill.event
{
   import flash.events.Event;
   
   public class BattleSkillEvent extends Event
   {
      
      public static var OPEN_SKILL_VIEW:String = "openSkillView";
      
      public static var CLOSE_VIEW:String = "closeSkillView";
      
      public static var BATTLESKILL_INFO:String = "battleSkillInfo";
      
      public static var UPDATE_SKILL:String = "updateBattleSkill";
      
      public static var BRIGHT_SKILL:String = "bringBattleSkill";
      
      public static var SKILLCELL_CLICK:String = "skillCellClick";
      
      private var _data:Object;
      
      public function BattleSkillEvent(_arg_1:String, _arg_2:Object = null, _arg_3:<PERSON><PERSON>an = false, _arg_4:<PERSON>olean = false)
      {
         super(_arg_1,_arg_3,_arg_4);
         this._data = _arg_2;
      }
      
      public function get data() : Object
      {
         return this._data;
      }
      
      override public function clone() : Event
      {
         return new BattleSkillEvent(type,bubbles,cancelable);
      }
   }
}

