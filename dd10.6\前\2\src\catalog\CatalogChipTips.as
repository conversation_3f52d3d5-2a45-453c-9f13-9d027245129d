package catalog
{
   import catalog.data.CatalogChipTemplateData;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.image.ScaleBitmapImage;
   import com.pickgliss.ui.image.ScaleFrameImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.ui.tip.BaseTip;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.QualityType;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   
   public class CatalogChipTips extends BaseTip implements Disposeable
   {
      
      private var _bg:ScaleBitmapImage;
      
      private var _titleText:FilterFrameText;
      
      private var _propertyName:FilterFrameText;
      
      private var _propertyNum:FilterFrameText;
      
      private var _line:ScaleBitmapImage;
      
      private var _characterImg:ScaleFrameImage;
      
      public function CatalogChipTips()
      {
         super();
      }
      
      override protected function init() : void
      {
         this._bg = ComponentFactory.Instance.creatComponentByStylename("catalog.catalogChipTips.bg");
         this._titleText = ComponentFactory.Instance.creatComponentByStylename("catalog.catalogChipTips.titleText");
         this._line = ComponentFactory.Instance.creatComponentByStylename("catalog.catalogChipTips.line");
         this._characterImg = ComponentFactory.Instance.creatComponentByStylename("catalog.catalogChipTips.characterImg");
         this._propertyName = ComponentFactory.Instance.creatComponentByStylename("catalog.catalogChipTips.propertyNameText");
         this._propertyNum = ComponentFactory.Instance.creatComponentByStylename("catalog.catalogChipTips.propertyNumText");
         super.init();
      }
      
      override public function set tipData(param1:Object) : void
      {
         var _loc2_:CatalogChipTemplateData = param1 as CatalogChipTemplateData;
         this.updateTips(_loc2_);
      }
      
      private function updateTips(param1:CatalogChipTemplateData) : void
      {
         var _loc2_:InventoryItemInfo = null;
         var _loc3_:Array = null;
         var _loc4_:int = 0;
         if(Boolean(param1))
         {
            _loc2_ = ItemManager.fillByID(param1.ItemID);
            this._titleText.text = _loc2_.Name;
            _loc3_ = QualityType.QUALITY_COLOR;
            this._titleText.textColor = _loc3_[param1.Character];
            this._characterImg.setFrame(param1.Character);
            this._propertyName.text = "";
            this._propertyNum.text = "";
            _loc4_ = 0;
            while(_loc4_ < 14)
            {
               if(param1["Attribute" + (_loc4_ + 1)] > 0)
               {
                  this._propertyName.text += LanguageMgr.GetTranslation("catalogView.pro" + (_loc4_ + 1)) + "\n";
                  this._propertyNum.text += "+" + param1["Attribute" + (_loc4_ + 1)] + "\n";
               }
               _loc4_++;
            }
            this._bg.height = this._propertyName.y + this._propertyName.height + 10;
            if(this._titleText.x + this._titleText.width > this._bg.width)
            {
               this._bg.width = this._titleText.x + this._titleText.width + 10;
            }
         }
      }
      
      override protected function addChildren() : void
      {
         super.addChildren();
         if(Boolean(this._bg))
         {
            addChild(this._bg);
         }
         if(Boolean(this._titleText))
         {
            addChild(this._titleText);
         }
         if(Boolean(this._line))
         {
            addChild(this._line);
         }
         if(Boolean(this._characterImg))
         {
            addChild(this._characterImg);
         }
         if(Boolean(this._propertyName))
         {
            addChild(this._propertyName);
         }
         if(Boolean(this._propertyNum))
         {
            addChild(this._propertyNum);
         }
      }
      
      override public function dispose() : void
      {
         if(Boolean(this._bg))
         {
            ObjectUtils.disposeObject(this._bg);
         }
         this._bg = null;
         if(Boolean(this._titleText))
         {
            ObjectUtils.disposeObject(this._titleText);
         }
         this._titleText = null;
         if(Boolean(this._line))
         {
            ObjectUtils.disposeObject(this._line);
         }
         this._line = null;
         if(Boolean(this._characterImg))
         {
            ObjectUtils.disposeObject(this._characterImg);
         }
         this._characterImg = null;
         if(Boolean(this._propertyName))
         {
            ObjectUtils.disposeObject(this._propertyName);
         }
         this._propertyName = null;
         if(Boolean(this._propertyNum))
         {
            ObjectUtils.disposeObject(this._propertyNum);
         }
         this._propertyNum = null;
         super.dispose();
      }
   }
}

