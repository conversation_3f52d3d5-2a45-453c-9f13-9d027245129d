package cityBattle.view
{
   import cityBattle.CityBattleManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.container.VBox;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.LanguageMgr;
   import flash.display.Sprite;
   
   public class ContentionTable extends Sprite implements Disposeable
   {
      
      private var _side:int;
      
      private var _vbox:VBox;
      
      private var _rankTxt:FilterFrameText;
      
      private var _nameTxt:FilterFrameText;
      
      private var _scoreTxt:FilterFrameText;
      
      public function ContentionTable(_arg_1:int)
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_3:* = null;
         var _local_2:* = null;
         super();
         this._side = _arg_1;
         this._vbox = new VBox();
         this._vbox.spacing = 7;
         this._vbox.y = 180;
         addChild(this._vbox);
         this._rankTxt = ComponentFactory.Instance.creatComponentByStylename("contention.rank.txt");
         this._nameTxt = ComponentFactory.Instance.creatComponentByStylename("contention.name.txt");
         this._scoreTxt = ComponentFactory.Instance.creatComponentByStylename("contention.score.txt");
         if(this._side == 0)
         {
            _local_5 = 0;
            while(_local_5 < CityBattleManager.instance.blueList.length)
            {
               _local_4 = CityBattleManager.instance.blueList[_local_5];
               _local_3 = new ContentionItem(_local_4);
               _local_3.x = 35;
               this._vbox.addChild(_local_3);
               _local_5++;
            }
         }
         else
         {
            _local_5 = 0;
            while(_local_5 < CityBattleManager.instance.redList.length)
            {
               _local_4 = CityBattleManager.instance.redList[_local_5];
               _local_2 = new ContentionItem(_local_4);
               _local_2.x = 427;
               this._vbox.addChild(_local_2);
               _local_5++;
            }
         }
         this._rankTxt.text = LanguageMgr.GetTranslation("ddt.cityBattle.rank");
         this._nameTxt.text = LanguageMgr.GetTranslation("ddt.cityBattle.name");
         this._scoreTxt.text = LanguageMgr.GetTranslation("ddt.cityBattle.score");
      }
      
      public function dispose() : void
      {
         ObjectUtils.disposeObject(this._vbox);
         this._vbox = null;
      }
   }
}

import cityBattle.data.ContentionInfo;
import com.pickgliss.ui.ComponentFactory;
import com.pickgliss.ui.FilterFrameTextWithTips;
import com.pickgliss.ui.core.Disposeable;
import com.pickgliss.ui.text.FilterFrameText;
import com.pickgliss.utils.ObjectUtils;
import flash.display.DisplayObject;
import flash.display.DisplayObjectContainer;
import flash.display.InteractiveObject;
import flash.display.Sprite;
import flash.events.EventDispatcher;

class ContentionItem extends Sprite implements Disposeable
{
   
   private var _rankTxt:FilterFrameText;
   
   private var _nameTxt:FilterFrameTextWithTips;
   
   private var _scoreTxt:FilterFrameText;
   
   public function ContentionItem(_arg_1:ContentionInfo)
   {
      super();
      this._rankTxt = ComponentFactory.Instance.creatComponentByStylename("contention.itemRank.txt");
      addChild(this._rankTxt);
      this._rankTxt.text = String(_arg_1.rank);
      this._nameTxt = ComponentFactory.Instance.creatComponentByStylename("contention.itemName.txt");
      addChild(this._nameTxt);
      this._nameTxt.mouseEnabled = true;
      this._nameTxt.selectable = false;
      this._nameTxt.tipData = _arg_1.server;
      this._nameTxt.text = _arg_1.name;
      this._scoreTxt = ComponentFactory.Instance.creatComponentByStylename("contention.itemScore.txt");
      addChild(this._scoreTxt);
      this._scoreTxt.text = String(_arg_1.socre);
   }
   
   public function dispose() : void
   {
      ObjectUtils.disposeObject(this._scoreTxt);
      this._scoreTxt = null;
      ObjectUtils.disposeObject(this._nameTxt);
      this._nameTxt = null;
      ObjectUtils.disposeObject(this._rankTxt);
      this._rankTxt = null;
   }
}
