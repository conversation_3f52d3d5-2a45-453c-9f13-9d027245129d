package com.pickgliss.ui.controls.container
{
   import flash.display.DisplayObject;
   import flash.events.Event;
   
   public class HBox extends Box<PERSON>ontainer
   {
      
      public function HBox()
      {
         super();
      }
      
      override public function arrange() : void
      {
         var _local_4:int = 0;
         var _local_1:* = undefined;
         _local_1 = null;
         _width = 0;
         _height = 0;
         var _local_2:* = 0;
         var _local_3:* = 0;
         _local_4 = 0;
         while(_local_4 < _childrenList.length)
         {
            _local_1 = _childrenList[_local_4];
            _local_1.x = _local_2;
            trace(_local_1.x);
            _local_2 += this.getItemWidth(_local_1);
            _local_2 += _spacing;
            if(_autoSize == 2 && _local_4 != 0)
            {
               _local_3 = _childrenList[0].y - (_local_1.height - _childrenList[0].height) / 2;
            }
            else if(_autoSize == 1 && _local_4 != 0)
            {
               _local_3 = _childrenList[0].y - (_local_1.height - _childrenList[0].height);
            }
            else
            {
               _local_3 = _childrenList[0].y;
            }
            _local_1.y = _local_3;
            _width += this.getItemWidth(_local_1);
            _height = Math.max(_height,_local_1.height);
            _local_4++;
         }
         _width += _spacing * (numChildren - 1);
         _width = Math.max(0,_width);
         dispatchEvent(new Event("resize"));
      }
      
      override protected function getItemWidth(_arg_1:DisplayObject) : Number
      {
         if(isStrictSize)
         {
            return _strictSize;
         }
         return _arg_1.width;
      }
   }
}

