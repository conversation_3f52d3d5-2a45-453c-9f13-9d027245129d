using System;
using Bussiness;
using Game.Base.Packets;
using Game.Server.GameUtils;
using SqlDataProvider.Data;

namespace Game.Server.Packets.Client
{
	// Token: 0x02000A26 RID: 2598
	[PacketHandler(66, "使用道具")]
	public class PropUseHandler : IPacketHandler
	{
		// Token: 0x060062BC RID: 25276 RVA: 0x001FAAD8 File Offset: 0x001F8CD8
		public int HandlePacket(GameClient client, GSPacketIn packet)
		{
			eBageType eBageType = (eBageType)packet.ReadInt();
			int num = packet.ReadInt();
			int num2 = packet.ReadInt();
			bool flag = packet.ReadBoolean();
			PlayerInventory inventory = client.Player.GetInventory(eBageType);
			bool flag2 = inventory != null;
			if (flag2)
			{
				ItemInfo itemAt = inventory.GetItemAt(num);
				bool flag3 = itemAt != null;
				if (flag3)
				{
					int num3 = packet.ReadInt();
					for (int i = 0; i < num3; i++)
					{
						int num4 = packet.ReadInt();
						int num5 = num4;
						int num6 = num5;
						bool flag4 = num6 == 11963;
						if (flag4)
						{
							int num7 = this.rand.Next(1, 1000);
							client.Player.AddGiftToken(num7);
							client.Player.SendMessage(LanguageMgr.GetTranslation("PropUseHandler.GiftToken", new object[] { num7 }));
							inventory.RemoveCountFromStack(itemAt, 1);
						}
						else if (num6 == 386301) // 圣物强化石
						{
							if (inventory.RemoveCountFromStack(itemAt, 1))
							{
								client.Player.PlayerCharacter.RelicItemInfo.ZFNum += 1;
								client.Player.SendMessage("恭喜你获得圣物增幅石*1");
							}
						}
					}
				}
			}
			return 0;
		}

		// Token: 0x04003850 RID: 14416
		private Random rand = new Random();
	}
}
