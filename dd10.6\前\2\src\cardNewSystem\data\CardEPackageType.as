package cardNewSystem.data
{
   public class CardEPackageType
   {
      
      public static var CARD_INFO:int = 1;
      
      public static var CARD_UPGRADES:int = 2;
      
      public static var CARD_ALLOTPOINT:int = 3;
      
      public static var CARD_RESETPOINT:int = 4;
      
      public static var CARD_MODPROPERTY:int = 5;
      
      public static var CARD_ACTIVESUITS:int = 6;
      
      public static var CARD_EQUIPSUITS:int = 7;
      
      public static var CARD_BREAKSUIT:int = 8;
      
      public static var CARD_REPLACE:int = 9;
      
      public static var CARD_SOUL:int = 10;
      
      public function CardEPackageType()
      {
         super();
      }
   }
}

