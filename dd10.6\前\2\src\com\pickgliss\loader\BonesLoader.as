package com.pickgliss.loader
{
   import com.crypto.NewCrypto;
   import flash.events.Event;
   import flash.net.URLVariables;
   import flash.utils.ByteArray;
   import flash.utils.getTimer;
   
   public class BonesLoader extends BaseLoader
   {
      
      public function BonesLoader(_arg_1:int, _arg_2:String, _arg_3:URLVariables = null, _arg_4:String = "GET")
      {
         super(_arg_1,_arg_2,_arg_3,_arg_4);
      }
      
      override public function loadFromBytes(_arg_1:ByteArray) : void
      {
         _starTime = getTimer();
         _loader.data = NewCrypto.decry(_arg_1);
         fireCompleteEvent();
         unload();
      }
      
      override protected function __onDataLoadComplete(_arg_1:Event) : void
      {
         super.__onDataLoadComplete(_arg_1);
         if(_loader.data.length > 0)
         {
            LoaderSavingManager.cacheFile(_url,_loader.data,true);
         }
      }
   }
}

