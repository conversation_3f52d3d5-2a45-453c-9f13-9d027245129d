package com.pickgliss.ui.controls.alert
{
   import com.pickgliss.events.InteractiveEvent;
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.ComponentSetting;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.SelectedCheckButton;
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.vo.AlertInfo;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.DisplayObject;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   
   public class BaseAlerFrame extends Frame
   {
      
      public static const P_buttonToBottom:String = "buttonToBottom";
      
      public static const P_cancelButton:String = "submitButton";
      
      public static const P_info:String = "info";
      
      public static const P_submitButton:String = "submitButton";
      
      protected var _buttonToBottom:int;
      
      protected var _cancelButton:BaseButton;
      
      protected var _cancelButtonStyle:String;
      
      protected var _info:AlertInfo;
      
      protected var _sound:*;
      
      protected var _submitButton:BaseButton;
      
      protected var _submitButtonStyle:String;
      
      protected var _isBand:Boolean;
      
      protected var _isShowTheLog:Boolean;
      
      protected var _selectedCheckBtn:SelectedCheckButton;
      
      public function BaseAlerFrame()
      {
         super();
      }
      
      public function set buttonToBottom(_arg_1:int) : void
      {
         if(this._buttonToBottom == _arg_1)
         {
            return;
         }
         this._buttonToBottom = _arg_1;
         onPropertiesChanged("buttonToBottom");
      }
      
      public function set cancelButtonEnable(_arg_1:Boolean) : void
      {
         this._cancelButton.enable = _arg_1;
      }
      
      public function set cancelButtonStyle(_arg_1:String) : void
      {
         if(this._cancelButtonStyle == _arg_1)
         {
            return;
         }
         this._cancelButtonStyle = _arg_1;
         this._cancelButton = ComponentFactory.Instance.creat(this._cancelButtonStyle);
         onPropertiesChanged("submitButton");
      }
      
      override public function dispose() : void
      {
         var _local_1:DisplayObject = StageReferance.stage.focus as DisplayObject;
         if(Boolean(_local_1) && contains(_local_1))
         {
            StageReferance.stage.focus = null;
         }
         if(Boolean(this._submitButton))
         {
            this._submitButton.removeEventListener("click",this.__onSubmitClick);
            ObjectUtils.disposeObject(this._submitButton);
            this._submitButton = null;
         }
         if(Boolean(this._cancelButton))
         {
            this._cancelButton.removeEventListener("click",this.__onCancelClick);
            ObjectUtils.disposeObject(this._cancelButton);
            this._cancelButton = null;
         }
         removeEventListener("keyDown",this.__onKeyDown);
         this._info = null;
         super.dispose();
      }
      
      public function get info() : AlertInfo
      {
         return this._info;
      }
      
      public function get isBand() : Boolean
      {
         return this._isBand;
      }
      
      public function set isBand(_arg_1:Boolean) : void
      {
         this._isBand = _arg_1;
      }
      
      public function set info(_arg_1:AlertInfo) : void
      {
         if(this._info == _arg_1)
         {
            return;
         }
         if(Boolean(this._info))
         {
            this._info.removeEventListener("stateChange",this.__onInfoChanged);
         }
         this._info = _arg_1;
         this._info.addEventListener("stateChange",this.__onInfoChanged);
         onPropertiesChanged("info");
      }
      
      public function setIsShowTheLog(_arg_1:Boolean, _arg_2:String) : void
      {
         if(this._isShowTheLog != _arg_1)
         {
            this._isShowTheLog = _arg_1;
            this._info.logText = _arg_2;
            this.creatTheLog();
         }
      }
      
      protected function creatTheLog() : void
      {
      }
      
      public function set submitButtonEnable(_arg_1:Boolean) : void
      {
         this._submitButton.enable = _arg_1;
      }
      
      public function set submitButtonStyle(_arg_1:String) : void
      {
         if(this._submitButtonStyle == _arg_1)
         {
            return;
         }
         this._submitButtonStyle = _arg_1;
         this._submitButton = ComponentFactory.Instance.creat(this._submitButtonStyle);
         onPropertiesChanged("submitButton");
      }
      
      protected function __onCancelClick(_arg_1:MouseEvent) : void
      {
         if(this._sound != null)
         {
            ComponentSetting.PLAY_SOUND_FUNC(this._sound);
         }
         this.onResponse(4);
      }
      
      override protected function __onCloseClick(_arg_1:MouseEvent) : void
      {
         if(this._sound != null)
         {
            ComponentSetting.PLAY_SOUND_FUNC(this._sound);
         }
         super.__onCloseClick(_arg_1);
      }
      
      override protected function __onKeyDown(_arg_1:KeyboardEvent) : void
      {
         if(_arg_1.keyCode == 13 && enterEnable || _arg_1.keyCode == 27 && escEnable)
         {
            if(this._sound != null)
            {
               ComponentSetting.PLAY_SOUND_FUNC(this._sound);
            }
         }
         super.__onKeyDown(_arg_1);
      }
      
      protected function __onSubmitClick(_arg_1:MouseEvent) : void
      {
         if(this._sound != null)
         {
            ComponentSetting.PLAY_SOUND_FUNC(this._sound);
         }
         this.onResponse(3);
      }
      
      override protected function addChildren() : void
      {
         super.addChildren();
         if(Boolean(this._submitButton))
         {
            addChild(this._submitButton);
         }
         if(Boolean(this._cancelButton))
         {
            addChild(this._cancelButton);
         }
      }
      
      override protected function onProppertiesUpdate() : void
      {
         if(Boolean(_changedPropeties["info"]))
         {
            this._sound = this._info.sound;
            _escEnable = this._info.escEnable;
            _enterEnable = this.info.enterEnable;
            _titleText = this._info.title;
            _changedPropeties["titleText"] = true;
            _moveEnable = this._info.moveEnable;
            _changedPropeties["moveEnable"] = true;
         }
         super.onProppertiesUpdate();
         if(Boolean(_changedPropeties["info"]) || Boolean(_changedPropeties["submitButton"]) || Boolean(_changedPropeties["submitButton"]))
         {
            if(Boolean(this._cancelButton) && Boolean(this._info))
            {
               this._cancelButton.visible = this._info.showCancel;
               this._cancelButton.enable = this._info.cancelEnabled;
               if(this._cancelButton is TextButton)
               {
                  TextButton(this._cancelButton).text = this._info.cancelLabel;
               }
               if(this._cancelButton.visible)
               {
                  this._cancelButton.addEventListener("click",this.__onCancelClick);
               }
            }
            if(Boolean(this._submitButton) && Boolean(this._info))
            {
               this._submitButton.visible = this._info.showSubmit;
               this._submitButton.enable = this._info.submitEnabled;
               if(this._submitButton is TextButton)
               {
                  TextButton(this._submitButton).text = this._info.submitLabel;
               }
               if(this._submitButton.visible)
               {
                  this._submitButton.addEventListener("click",this.__onSubmitClick);
               }
            }
         }
         if(Boolean(_changedPropeties["info"]) || Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["width"]) || Boolean(_changedPropeties["buttonToBottom"]))
         {
            this.updatePos();
         }
      }
      
      override protected function onResponse(_arg_1:int) : void
      {
         if(Boolean(this._info) && this._info.autoDispose)
         {
            this.dispose();
         }
         super.onResponse(_arg_1);
      }
      
      protected function updatePos() : void
      {
         if(this._info == null)
         {
            return;
         }
         if(Boolean(this._info.bottomGap))
         {
            this._buttonToBottom = this._info.bottomGap;
         }
         if(Boolean(this._submitButton))
         {
            this._submitButton.y = _height - this._submitButton.height - this._buttonToBottom;
         }
         if(Boolean(this._cancelButton))
         {
            this._cancelButton.y = _height - this._cancelButton.height - this._buttonToBottom;
         }
         if(this._info.showCancel || this._info.showSubmit)
         {
            if(Boolean(this._info.customPos))
            {
               if(Boolean(this._submitButton))
               {
                  this._submitButton.x = this._info.customPos.x;
                  this._submitButton.y = this._info.customPos.y;
                  if(Boolean(this._cancelButton))
                  {
                     this._cancelButton.x = this._info.customPos.x + this._cancelButton.width + this._info.buttonGape;
                     this._cancelButton.y = this._info.customPos.y;
                  }
               }
               else if(Boolean(this._cancelButton))
               {
                  this._cancelButton.x = this._info.customPos.x;
                  this._cancelButton.y = this._info.customPos.y;
               }
            }
            else
            {
               if(this._info.autoButtonGape)
               {
                  if(this._submitButton != null && this._cancelButton != null)
                  {
                     this._info.buttonGape = (_width - this._submitButton.width - this._cancelButton.width) / 2;
                  }
               }
               if(!this._info.showCancel && Boolean(this._submitButton))
               {
                  this._submitButton.x = (_width - this._submitButton.width) / 2;
               }
               else if(!this._info.showSubmit && Boolean(this._cancelButton))
               {
                  this._cancelButton.x = (_width - this._cancelButton.width) / 2;
               }
               else if(this._cancelButton != null && this._submitButton != null)
               {
                  this._submitButton.x = (_width - this._submitButton.width - this._cancelButton.width - this._info.buttonGape) / 2;
                  this._cancelButton.x = this._submitButton.x + this._submitButton.width + this._info.buttonGape;
               }
            }
         }
      }
      
      private function __onInfoChanged(_arg_1:InteractiveEvent) : void
      {
         onPropertiesChanged("info");
      }
      
      public function get selectedCheckButton() : SelectedCheckButton
      {
         return this._selectedCheckBtn;
      }
   }
}

