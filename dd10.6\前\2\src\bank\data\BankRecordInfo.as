package bank.data
{
   public class BankRecordInfo
   {
      
      private var _bankId:int;
      
      private var _tempId:int;
      
      private var _begainTime:Date;
      
      private var _Amount:int;
      
      private var _userId:int;
      
      public function BankRecordInfo()
      {
         super();
      }
      
      public function get tempId() : int
      {
         return this._tempId;
      }
      
      public function set tempId(_arg_1:int) : void
      {
         this._tempId = _arg_1;
      }
      
      public function get userId() : int
      {
         return this._userId;
      }
      
      public function set userId(_arg_1:int) : void
      {
         this._userId = _arg_1;
      }
      
      public function get Amount() : int
      {
         return this._Amount;
      }
      
      public function set Amount(_arg_1:int) : void
      {
         this._Amount = _arg_1;
      }
      
      public function get begainTime() : Date
      {
         return this._begainTime;
      }
      
      public function set begainTime(_arg_1:Date) : void
      {
         this._begainTime = _arg_1;
      }
      
      public function get bankId() : int
      {
         return this._bankId;
      }
      
      public function set bankId(_arg_1:int) : void
      {
         this._bankId = _arg_1;
      }
   }
}

