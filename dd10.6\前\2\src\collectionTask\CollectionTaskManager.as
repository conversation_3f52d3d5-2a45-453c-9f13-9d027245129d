package collectionTask
{
   import collectionTask.model.CollectionTaskAnalyzer;
   import collectionTask.vo.CollectionRobertVo;
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.loader.LoaderEvent;
   import ddt.data.quest.QuestInfo;
   import ddt.manager.PathManager;
   import ddt.manager.StateManager;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import quest.TaskManager;
   
   public class CollectionTaskManager extends EventDispatcher
   {
      
      private static var _instance:CollectionTaskManager;
      
      private const CONDITION_ID:int = 64;
      
      public var isClickCollection:Boolean;
      
      public var collectionTaskInfoList:Vector.<CollectionRobertVo>;
      
      public var isCollecting:Boolean;
      
      public var collectedId:int;
      
      public var isTaskComplete:Boolean;
      
      public var questInfo:QuestInfo;
      
      private var _mapLoader:BaseLoader;
      
      public function CollectionTaskManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get Instance() : CollectionTaskManager
      {
         if(_instance == null)
         {
            _instance = new CollectionTaskManager();
         }
         return _instance;
      }
      
      public function setUp() : void
      {
         var _local_2:QuestInfo = null;
         var _local_1:Array = TaskManager.instance.allCurrentQuest;
         for each(_local_2 in _local_1)
         {
            if(_local_2.Condition == 64)
            {
               this.questInfo = _local_2;
               break;
            }
         }
         this.loadMap();
      }
      
      public function robertDataSetup(_arg_1:CollectionTaskAnalyzer) : void
      {
         this.collectionTaskInfoList = _arg_1.collectionTaskInfoList;
      }
      
      private function loadMap() : void
      {
         this._mapLoader = LoadResourceManager.Instance.createLoader(PathManager.solveCollectionTaskSceneSourcePath("collectionScene"),4);
         this._mapLoader.addEventListener("complete",this.onMapSrcLoadedComplete);
         LoadResourceManager.Instance.startLoad(this._mapLoader);
      }
      
      private function onMapSrcLoadedComplete(_arg_1:LoaderEvent = null) : void
      {
         if(this._mapLoader.isSuccess)
         {
            StateManager.setState("collectionTaskScene");
         }
      }
   }
}

