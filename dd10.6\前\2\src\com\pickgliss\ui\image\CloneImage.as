package com.pickgliss.ui.image
{
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.BitmapData;
   import flash.geom.Matrix;
   
   public class CloneImage extends Image
   {
      
      public static const P_direction:String = "direction";
      
      public static const P_gape:String = "gape";
      
      protected var _brush:BitmapData;
      
      protected var _direction:int = -1;
      
      protected var _gape:int = 0;
      
      private var _brushHeight:Number;
      
      private var _brushWidth:Number;
      
      public function CloneImage()
      {
         super();
      }
      
      public function set direction(_arg_1:int) : void
      {
         if(this._direction == _arg_1)
         {
            return;
         }
         this._direction = _arg_1;
         onPropertiesChanged("direction");
      }
      
      override public function dispose() : void
      {
         graphics.clear();
         ObjectUtils.disposeObject(this._brush);
         this._brush = null;
         super.dispose();
      }
      
      public function set gape(_arg_1:int) : void
      {
         if(this._gape == _arg_1)
         {
            return;
         }
         this._gape = _arg_1;
         onPropertiesChanged("gape");
      }
      
      override protected function resetDisplay() : void
      {
         graphics.clear();
         this._brushWidth = _width;
         this._brushHeight = _height;
         this._brush = ClassUtils.CreatInstance(_resourceLink,[0,0]);
      }
      
      override protected function updateSize() : void
      {
         var _local_1:int = 0;
         var _local_3:int = 0;
         var _local_2:* = null;
         if(Boolean(_changedPropeties["width"]) || Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["direction"]) || Boolean(_changedPropeties["gape"]))
         {
            _local_1 = 0;
            if(this._direction != -1)
            {
               if(this._direction == 1)
               {
                  _local_1 = int(int(_height / this._brush.height));
               }
               else
               {
                  _local_1 = int(int(_width / this._brush.width));
               }
            }
            graphics.clear();
            graphics.beginBitmapFill(this._brush);
            _local_2 = new Matrix();
            _local_3 = 0;
            while(_local_3 < _local_1)
            {
               if(this._direction == 1)
               {
                  graphics.drawRect(0,_local_3 * this._brush.height + this._gape,this._brush.width,this._brush.height);
               }
               else if(this._direction > 1)
               {
                  graphics.drawRect(_local_3 * this._brush.width + this._gape,0,this._brush.width,this._brush.height);
               }
               else
               {
                  graphics.drawRect(0,0,this._brush.width,this._brush.height);
               }
               _local_3++;
            }
            graphics.endFill();
         }
      }
   }
}

