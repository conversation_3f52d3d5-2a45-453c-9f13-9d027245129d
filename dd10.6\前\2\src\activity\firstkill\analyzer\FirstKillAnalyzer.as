package activity.firstkill.analyzer
{
   import activity.firstkill.data.FirstKillDungeonInfo;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   
   public class FirstKillAnalyzer extends DataAnalyzer
   {
      
      private var _list:Array;
      
      public function FirstKillAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_3:XML = null;
         var _local_2:* = null;
         var _local_5:* = null;
         var _local_4:XML = XML(_arg_1);
         if(_local_4.@value == "true")
         {
            this._list = [];
            _local_2 = _local_4..Item;
            for each(_local_3 in _local_2)
            {
               _local_5 = new FirstKillDungeonInfo();
               ObjectUtils.copyPorpertiesByXML(_local_5,_local_3);
               this._list.push(_local_5);
            }
         }
         else
         {
            message = _local_4.@message;
            onAnalyzeError();
         }
         onAnalyzeComplete();
         this._list = null;
      }
      
      public function get list() : Array
      {
         return this._list;
      }
   }
}

