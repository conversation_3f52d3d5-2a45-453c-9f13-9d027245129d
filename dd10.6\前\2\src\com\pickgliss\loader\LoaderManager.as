package com.pickgliss.loader
{
   import flash.events.EventDispatcher;
   import flash.external.ExternalInterface;
   import flash.net.URLVariables;
   import flash.system.ApplicationDomain;
   import flash.system.LoaderContext;
   import flash.utils.ByteArray;
   import flash.utils.Dictionary;
   import flash.utils.setTimeout;
   
   public class LoaderManager extends EventDispatcher
   {
      
      private static var _instance:LoaderManager;
      
      public static const ALLOW_MUTI_LOAD_COUNT:int = 8;
      
      public static const LOAD_FROM_LOCAL:int = 2;
      
      public static const LOAD_FROM_WEB:int = 1;
      
      public static const LOAD_NOT_SET:int = 0;
      
      private var _loadMode:int = 0;
      
      private var _loaderIdCounter:int = 0;
      
      private var _loaderSaveByID:Dictionary;
      
      private var _loaderSaveByPath:Dictionary;
      
      private var _loadingLoaderList:Vector.<BaseLoader>;
      
      private var _waitingLoaderList:Vector.<BaseLoader>;
      
      public function LoaderManager()
      {
         super();
         this._loaderSaveByID = new Dictionary();
         this._loaderSaveByPath = new Dictionary();
         this._loadingLoaderList = new Vector.<BaseLoader>();
         this._waitingLoaderList = new Vector.<BaseLoader>();
         this.initLoadMode();
      }
      
      public static function get Instance() : LoaderManager
      {
         if(_instance == null)
         {
            _instance = new LoaderManager();
         }
         return _instance;
      }
      
      public function creatLoaderByType(_arg_1:String, _arg_2:int, _arg_3:URLVariables, _arg_4:String, _arg_5:ApplicationDomain) : BaseLoader
      {
         var _local_6:* = null;
         switch(_arg_2)
         {
            case 0:
               _local_6 = new BitmapLoader(this.getNextLoaderID(),_arg_1);
               break;
            case 2:
               _local_6 = new TextLoader(this.getNextLoaderID(),_arg_1,_arg_3);
               break;
            case 1:
               _local_6 = new DisplayLoader(this.getNextLoaderID(),_arg_1);
               break;
            case 3:
               _local_6 = new BaseLoader(this.getNextLoaderID(),_arg_1);
               break;
            case 5:
               _local_6 = new CompressTextLoader(this.getNextLoaderID(),_arg_1,_arg_3);
               break;
            case 4:
               _local_6 = new ModuleLoader(this.getNextLoaderID(),_arg_1,_arg_5);
               break;
            case 6:
               _local_6 = new RequestLoader(this.getNextLoaderID(),_arg_1,_arg_3,_arg_4);
               break;
            case 7:
               _local_6 = new CompressRequestLoader(this.getNextLoaderID(),_arg_1,_arg_3,_arg_4);
               break;
            case 8:
               _local_6 = new MornUIDataLoader(this.getNextLoaderID(),_arg_1,_arg_3,_arg_4);
               break;
            case 9:
               _local_6 = new CodeModuleLoader(this.getNextLoaderID(),_arg_1,_arg_5);
               break;
            case 10:
               _local_6 = new BonesLoader(this.getNextLoaderID(),_arg_1);
               break;
            case 11:
               _local_6 = new ZipLoader(this.getNextLoaderID(),_arg_1);
         }
         return _local_6;
      }
      
      public function getLoadMode() : int
      {
         return this._loadMode;
      }
      
      public function creatLoader(_arg_1:String, _arg_2:int, _arg_3:URLVariables = null, _arg_4:String = "GET", _arg_5:ApplicationDomain = null) : *
      {
         var _local_6:* = null;
         _arg_1 = LoaderNameFilter.getLoadFilePath(_arg_1);
         var _local_7:String = this.fixedVariablesURL(_arg_1,_arg_2,_arg_3);
         _local_6 = this.getLoaderByURL(_local_7,_arg_3);
         if(_local_6 == null)
         {
            _local_6 = this.creatLoaderByType(_local_7,_arg_2,_arg_3,_arg_4,_arg_5);
         }
         else
         {
            _local_6.domain = _arg_5;
         }
         if(_arg_2 != 6 && _arg_2 != 7 && _arg_2 != 0)
         {
            this._loaderSaveByID[_local_6.id] = _local_6;
            this._loaderSaveByPath[_local_6.url] = _local_6;
         }
         return _local_6;
      }
      
      public function creatLoaderOriginal(_arg_1:String, _arg_2:int, _arg_3:URLVariables = null, _arg_4:String = "GET") : *
      {
         var _local_5:* = null;
         var _local_6:* = null;
         _local_6 = _local_6(_arg_1,_arg_2,_arg_3);
         _local_5 = this.getLoaderByURL(_local_6,_arg_3);
         if(_local_5 == null)
         {
            _local_5 = this.creatLoaderByType(_local_6,_arg_2,_arg_3,_arg_4,null);
         }
         if(_arg_2 != 6 && _arg_2 != 7 && _arg_2 != 0)
         {
            this._loaderSaveByID[_local_5.id] = _local_5;
            this._loaderSaveByPath[_local_5.url] = _local_5;
         }
         return _local_5;
      }
      
      public function creatAndStartLoad(_arg_1:String, _arg_2:int, _arg_3:URLVariables = null) : BaseLoader
      {
         var _local_4:BaseLoader = this.creatLoader(_arg_1,_arg_2,_arg_3);
         this.startLoad(_local_4);
         return _local_4;
      }
      
      public function getLoaderByID(_arg_1:int) : BaseLoader
      {
         return this._loaderSaveByID[_arg_1];
      }
      
      public function clearLoader() : void
      {
         var _local_1:String = null;
         for(_local_1 in this._loaderSaveByID)
         {
            this._loaderSaveByID[_local_1].unload();
         }
      }
      
      public function getLoaderByURL(_arg_1:String, _arg_2:URLVariables) : BaseLoader
      {
         return this._loaderSaveByPath[_arg_1];
      }
      
      public function getNextLoaderID() : int
      {
         return this._loaderIdCounter++;
      }
      
      public function saveFileToLocal(_arg_1:BaseLoader) : void
      {
      }
      
      public function startLoad(_arg_1:BaseLoader, _arg_2:Boolean = false) : void
      {
         if(Boolean(_arg_1))
         {
            _arg_1.addEventListener("complete",this.__onLoadFinish);
         }
         if(_arg_1.isComplete)
         {
            _arg_1.dispatchEvent(new LoaderEvent("complete",_arg_1));
            return;
         }
         var _local_3:ByteArray = LoaderSavingManager.loadCachedFile(_arg_1.url,true);
         if(Boolean(_local_3))
         {
            _arg_1.loadFromBytes(_local_3);
            return;
         }
         if(!LoadResourceManager.Instance.isMicroClient && (this._loadingLoaderList.length >= 8 && !_arg_2 || this.getLoadMode() == 0))
         {
            if(this._waitingLoaderList.indexOf(_arg_1) == -1)
            {
               this._waitingLoaderList.push(_arg_1);
            }
         }
         else
         {
            if(this._loadingLoaderList.indexOf(_arg_1) == -1)
            {
               this._loadingLoaderList.push(_arg_1);
            }
            if(this.getLoadMode() == 1 || _arg_1.type == 2)
            {
               _arg_1.loadFromWeb();
            }
            else if(this.getLoadMode() == 2)
            {
               _arg_1.getFilePathFromExternal();
            }
         }
      }
      
      private function __onLoadFinish(_arg_1:LoaderEvent) : void
      {
         _arg_1.loader.removeEventListener("complete",this.__onLoadFinish);
         this._loadingLoaderList.splice(this._loadingLoaderList.indexOf(_arg_1.loader),1);
         this.tryLoadWaiting();
      }
      
      private function initLoadMode() : void
      {
         if(!ExternalInterface.available)
         {
            this.setFlashLoadWeb();
            return;
         }
         ExternalInterface.addCallback("SetFlashLoadExternal",this.setFlashLoadExternal);
         setTimeout(this.setFlashLoadWeb,200);
      }
      
      private function onExternalLoadStop(_arg_1:int, _arg_2:String) : void
      {
         var _local_3:BaseLoader = this.getLoaderByID(_arg_1);
         _local_3.loadFromExternal(_arg_2);
      }
      
      private function setFlashLoadExternal() : void
      {
         this._loadMode = 2;
         ExternalInterface.addCallback("ExternalLoadStop",this.onExternalLoadStop);
         this.tryLoadWaiting();
      }
      
      public function setFlashLoadWeb() : void
      {
         this._loadMode = 1;
         this.tryLoadWaiting();
      }
      
      private function tryLoadWaiting() : void
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         _local_2 = 0;
         while(_local_2 < this._waitingLoaderList.length)
         {
            if(this._loadingLoaderList.length < 8)
            {
               _local_1 = this._waitingLoaderList.shift();
               this.startLoad(_local_1);
            }
            _local_2++;
         }
      }
      
      public function setup(_arg_1:LoaderContext, _arg_2:String) : void
      {
         DisplayLoader.Context = _arg_1;
         TextLoader.TextLoaderKey = _arg_2;
         LoaderSavingManager.setup();
      }
      
      public function fixedVariablesURL(_arg_1:String, _arg_2:int, _arg_3:URLVariables) : String
      {
         var _local_6:int = 0;
         var _local_4:String = null;
         var _local_5:* = null;
         if(_arg_2 != 6 && _arg_2 != 7)
         {
            _local_5 = "";
            if(_arg_3 == null)
            {
               _arg_3 = new URLVariables();
            }
            if(_arg_2 == 3 || _arg_2 == 1 || _arg_2 == 0 || _arg_2 == 8 || _arg_2 == 10)
            {
               if(!_arg_3["lv"])
               {
                  _arg_3["lv"] = LoaderSavingManager.Version;
               }
            }
            else if(_arg_2 == 5 || _arg_2 == 2)
            {
               if(!_arg_3["rnd"])
               {
                  _arg_3["rnd"] = TextLoader.TextLoaderKey;
               }
            }
            else if(_arg_2 == 4 || _arg_2 == 9)
            {
               if(!_arg_3["lv"])
               {
                  _arg_3["lv"] = LoaderSavingManager.Version;
               }
               if(!_arg_3["rnd"])
               {
                  _arg_3["rnd"] = TextLoader.TextLoaderKey;
               }
            }
            _local_6 = 0;
            for(_local_4 in _arg_3)
            {
               if(_local_6 >= 1)
               {
                  _local_5 += "&" + _local_4 + "=" + _arg_3[_local_4];
               }
               else
               {
                  _local_5 += _local_4 + "=" + _arg_3[_local_4];
               }
               _local_6++;
            }
            return _arg_1 + "?" + _local_5;
         }
         return _arg_1;
      }
      
      public function fixedNewVariablesURL(_arg_1:String, _arg_2:int, _arg_3:URLVariables, _arg_4:int) : String
      {
         var _local_7:int = 0;
         var _local_5:String = null;
         var _local_6:* = null;
         if(_arg_2 != 6 && _arg_2 != 7)
         {
            _local_6 = "";
            if(_arg_3 == null)
            {
               _arg_3 = new URLVariables();
            }
            if(_arg_2 == 3 || _arg_2 == 1 || _arg_2 == 0 || _arg_2 == 4 || _arg_2 == 8 || _arg_2 == 9)
            {
               _arg_3["lv"] = LoaderSavingManager.Version + _arg_4;
            }
            else if(_arg_2 == 5 || _arg_2 == 2)
            {
               _arg_3["rnd"] = TextLoader.TextLoaderKey + _arg_4.toString();
            }
            _local_7 = 0;
            for(_local_5 in _arg_3)
            {
               if(_local_7 >= 1)
               {
                  _local_6 += "&" + _local_5 + "=" + _arg_3[_local_5];
               }
               else
               {
                  _local_6 += _local_5 + "=" + _arg_3[_local_5];
               }
               _local_7++;
            }
            return _arg_1 + "?" + _local_6;
         }
         return _arg_1;
      }
   }
}

