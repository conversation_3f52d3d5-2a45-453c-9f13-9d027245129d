package com.pickgliss.ui.controls.cell
{
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.controls.list.List;
   
   public class TextButtonListCellAdapter extends TextButton implements IListCell
   {
      
      public function TextButtonListCellAdapter()
      {
         super();
      }
      
      public function getCellValue() : *
      {
         return _text;
      }
      
      public function setCellValue(_arg_1:*) : void
      {
         text = _arg_1;
      }
      
      public function setListCellStatus(_arg_1:List, _arg_2:Bo<PERSON>an, _arg_3:int) : void
      {
      }
   }
}

