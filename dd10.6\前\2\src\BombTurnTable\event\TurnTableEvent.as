package BombTurnTable.event
{
   import flash.events.Event;
   
   public class TurnTableEvent extends Event
   {
      
      public static const CLICK_LOTTERY:String = "ClickLottery";
      
      public static const LOTTERY_COMPLATE:String = "lotteryComplate";
      
      public static const UPDATE_TURNTABLE_DATA:String = "updateTurntableData";
      
      private var _data:Object;
      
      public function TurnTableEvent(_arg_1:String, _arg_2:Object, _arg_3:<PERSON><PERSON><PERSON> = false, _arg_4:<PERSON><PERSON><PERSON> = false)
      {
         super(_arg_1,_arg_3,_arg_4);
         this._data = _arg_2;
      }
      
      public function get data() : Object
      {
         return this._data;
      }
      
      override public function clone() : Event
      {
         return new TurnTableEvent(type,this._data,bubbles,cancelable);
      }
   }
}

