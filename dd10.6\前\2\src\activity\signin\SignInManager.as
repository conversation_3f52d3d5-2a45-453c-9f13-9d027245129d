package activity.signin
{
   import activity.signin.analyzer.SignInRewardTemplateAnalyzer;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ClassUtils;
   import ddt.loader.LoaderCreate;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.manager.TimeManager;
   import ddt.utils.AssetModuleLoader;
   import flash.display.Sprite;
   import hallIcon.HallIconManager;
   import road7th.data.DictionaryData;
   import road7th.utils.DateUtils;
   
   public class SignInManager
   {
      
      private static var _instance:SignInManager;
      
      private static const COUNT:int = 28;
      
      private var _rewardData:DictionaryData;
      
      private var _maxID:int;
      
      public var currentID:int;
      
      public var lastDate:Date;
      
      public var doubleBeginTime:Date;
      
      public var doubleEndTime:Date;
      
      public function SignInManager()
      {
         super();
         this._rewardData = new DictionaryData();
      }
      
      public static function get instance() : SignInManager
      {
         if(!_instance)
         {
            _instance = new SignInManager();
         }
         return _instance;
      }
      
      public function showIcon() : void
      {
         HallIconManager.instance.updateSwitchHandler("signActivity",true);
      }
      
      public function get canGetReward() : Boolean
      {
         var _local_2:Date = TimeManager.Instance.Now();
         var _local_1:String = _local_2.month + " " + _local_2.date;
         var _local_3:String = this.lastDate.month + " " + this.lastDate.date;
         return _local_1 != _local_3;
      }
      
      public function get getRewardID() : int
      {
         var _local_1:int = this.currentID + 1;
         return _local_1 > this._maxID ? 1 : _local_1;
      }
      
      public function get currentPageReward() : Array
      {
         var _local_6:int = 0;
         var _local_4:Array = [];
         var _local_1:int = this.currentID;
         if(this.canGetReward)
         {
            _local_1 = this.getRewardID;
         }
         var _local_2:int = int(int((_local_1 - 1) / 28));
         var _local_3:int = _local_2 * 28 + 1;
         var _local_5:int = _local_3 + 28;
         _local_6 = _local_3;
         while(_local_6 < _local_5)
         {
            _local_4.push(this._rewardData[_local_6]);
            _local_6++;
         }
         return _local_4;
      }
      
      public function analyzer(_arg_1:SignInRewardTemplateAnalyzer) : void
      {
         this.doubleBeginTime = DateUtils.getDateByStr(ServerConfigManager.instance.signInDobuleBeginTime);
         this.doubleEndTime = DateUtils.getDateByStr(ServerConfigManager.instance.signInDobuleEndTime);
         this._rewardData = _arg_1.data;
         this._maxID = _arg_1.maxID;
      }
      
      public function show() : void
      {
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createSignInRewardTemplateLoader());
         AssetModuleLoader.addModelLoader("signin",5);
         AssetModuleLoader.startCodeLoader(this.onLoadComplete);
      }
      
      private function onLoadComplete() : void
      {
         var _local_1:Sprite = null;
         _local_1 = ClassUtils.CreatInstance("activity.signin.view.SignInMainView");
         _local_1.visible = false;
         LayerManager.Instance.addToLayer(_local_1,3,true,1);
         SocketManager.Instance.out.sendSignInData();
         PlayerManager.Instance.Self.Sign = true;
      }
      
      public function get isDoubleTimeRange() : Boolean
      {
         if(this.doubleBeginTime == null || this.doubleBeginTime == null)
         {
            return false;
         }
         var _local_1:Number = TimeManager.Instance.NowTime();
         if(_local_1 > this.doubleBeginTime.time && _local_1 < this.doubleEndTime.time)
         {
            return true;
         }
         return false;
      }
   }
}

