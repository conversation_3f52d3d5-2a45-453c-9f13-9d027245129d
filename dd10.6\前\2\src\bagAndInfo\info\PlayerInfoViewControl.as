package bagAndInfo.info
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import ddt.data.player.PlayerInfo;
   import ddt.events.PlayerPropertyEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.AssetModuleLoader;
   import honor.HonorManager;
   
   public class PlayerInfoViewControl
   {
      
      private static var _view:PlayerInfoFrame;
      
      private static var _tempInfo:PlayerInfo;
      
      public static var isOpenFromBag:Boolean;
      
      public static var isOpenFromBattle:Boolean;
      
      public static var _isBattle:Boolean;
      
      public static var currentPlayer:PlayerInfo;
      
      public function PlayerInfoViewControl()
      {
         super();
      }
      
      public static function view(_arg_1:*, _arg_2:Boolean = true, _arg_3:Boolean = false, _arg_4:<PERSON>olean = false) : void
      {
         if(!HonorManager.ins.isLoadHonorTempData)
         {
            AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatHonorTempLoader());
         }
         _isBattle = _arg_3;
         if(_arg_1 && _isBattle)
         {
            if(_view == null)
            {
               _view = ComponentFactory.Instance.creatComponentByStylename("bag.personelInfoViewFrame");
            }
            _view.info = _arg_1;
            _view.show();
            _view.setAchivEnable(_arg_2);
            _view.addEventListener("response",__responseHandler);
            return;
         }
         if(_arg_1 && _arg_1 is PlayerInfo)
         {
            if(_arg_1.Style != null)
            {
               if(_view == null)
               {
                  _view = ComponentFactory.Instance.creatComponentByStylename("bag.personelInfoViewFrame");
               }
               _view.info = _arg_1;
               _view.show();
               _view.setAchivEnable(_arg_2);
               _view.addEventListener("response",__responseHandler);
            }
            else
            {
               _arg_1.addEventListener("propertychange",__infoChange);
            }
            if(_arg_4)
            {
               SocketManager.Instance.out.updateBKingItemEquip(_arg_1.ID,_arg_1.ZoneID,0);
               SocketManager.Instance.out.updateBKingItemEquip(_arg_1.ID,_arg_1.ZoneID,1);
               SocketManager.Instance.out.updateBKingItemEquip(_arg_1.ID,_arg_1.ZoneID,2);
            }
            else
            {
               SocketManager.Instance.out.getPlayerCardInfo(_arg_1.ID);
               SocketManager.Instance.out.sendItemEquip(_arg_1.ID);
               SocketManager.Instance.out.sendUpdatePetInfo(_arg_1.ID);
            }
         }
      }
      
      private static function __infoChange(_arg_1:PlayerPropertyEvent) : void
      {
         if(Boolean(PlayerInfo(_arg_1.currentTarget).Style))
         {
            PlayerInfo(_arg_1.target).removeEventListener("propertychange",__infoChange);
            if(_view == null)
            {
               _view = ComponentFactory.Instance.creatComponentByStylename("bag.personelInfoViewFrame");
            }
            _view.info = PlayerInfo(_arg_1.target);
            _view.show();
            _view.addEventListener("response",__responseHandler);
         }
      }
      
      public static function viewByID(_arg_1:int, _arg_2:int = -1, _arg_3:Boolean = true, _arg_4:Boolean = false, _arg_5:Boolean = false) : void
      {
         var _local_6:PlayerInfo = PlayerManager.Instance.findPlayer(_arg_1,_arg_2);
         if(_arg_2 != -1)
         {
            _local_6.ZoneID = _arg_2;
         }
         view(_local_6,_arg_3,_arg_4,_arg_5);
      }
      
      public static function viewByNickName(_arg_1:String, _arg_2:int = -1, _arg_3:Boolean = true) : void
      {
         _tempInfo = new PlayerInfo();
         _tempInfo = PlayerManager.Instance.findPlayerByNickName(_tempInfo,_arg_1);
         if(Boolean(_tempInfo.ID))
         {
            view(_tempInfo,_arg_3);
         }
         else
         {
            SocketManager.Instance.out.sendItemEquip(_tempInfo.NickName,true);
            _tempInfo.addEventListener("propertychange",__IDChange);
         }
      }
      
      private static function __IDChange(_arg_1:PlayerPropertyEvent) : void
      {
         _tempInfo.removeEventListener("propertychange",__IDChange);
         view(_tempInfo);
      }
      
      private static function __responseHandler(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
               _view.dispose();
               clearView();
         }
      }
      
      public static function closeView() : void
      {
         if(Boolean(_view) && Boolean(_view.parent))
         {
            _view.removeEventListener("response",__responseHandler);
            _view.dispose();
         }
         _view = null;
      }
      
      public static function clearView() : void
      {
         if(Boolean(_view))
         {
            _view.removeEventListener("response",__responseHandler);
         }
         _view = null;
      }
   }
}

