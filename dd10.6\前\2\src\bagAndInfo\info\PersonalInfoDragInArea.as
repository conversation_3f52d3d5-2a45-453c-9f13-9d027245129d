package bagAndInfo.info
{
   import bagAndInfo.cell.DragEffect;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.interfaces.IAcceptDrag;
   import ddt.manager.DragManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import flash.display.Sprite;
   
   public class PersonalInfoDragInArea extends Sprite implements IAcceptDrag
   {
      
      private var temInfo:InventoryItemInfo;
      
      private var temEffect:DragEffect;
      
      public function PersonalInfoDragInArea()
      {
         super();
         this.init();
      }
      
      private function init() : void
      {
         graphics.beginFill(0,0);
         graphics.drawRect(0,0,450,310);
         graphics.endFill();
      }
      
      public function dragDrop(_arg_1:DragEffect) : void
      {
         var _local_2:* = null;
         if(PlayerManager.Instance.Self.bagLocked)
         {
            return;
         }
         var _local_3:InventoryItemInfo = _arg_1.data as InventoryItemInfo;
         if((_local_3.BindType == 1 || _local_3.BindType == 2 || _local_3.BindType == 3) && _local_3.IsBinds == false && _local_3.TemplateID != 11560 && _local_3.TemplateID != 11561 && _local_3.TemplateID != 11562)
         {
            _local_2 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("tank.view.bagII.BagIIView.BindsInfo"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),true,true,true,2);
            _local_2.addEventListener("response",this.__onResponse);
            this.temInfo = _local_3;
            this.temEffect = _arg_1;
            return;
         }
         if(Boolean(_local_3))
         {
            _arg_1.action = "none";
            if(_local_3.Place < 31)
            {
               DragManager.acceptDrag(this);
            }
            else if(PlayerManager.Instance.Self.canEquip(_local_3))
            {
               if(EquipType.isArmShell(_local_3))
               {
                  DragManager.acceptDrag(this,"none");
                  return;
               }
               SocketManager.Instance.out.sendMoveGoods(0,_local_3.Place,0,PlayerManager.Instance.getDressEquipPlace(_local_3),_local_3.Count);
               DragManager.acceptDrag(this,"move");
            }
            else
            {
               DragManager.acceptDrag(this);
            }
         }
      }
      
      private function __onResponse(_arg_1:FrameEvent) : void
      {
         var _local_2:BaseAlerFrame = BaseAlerFrame(_arg_1.currentTarget);
         _local_2.removeEventListener("response",this.__onResponse);
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
            case 4:
               _local_2.dispose();
               return;
            case 2:
            case 3:
               this.sendDefy();
         }
      }
      
      private function sendDefy() : void
      {
         if(Boolean(this.temInfo))
         {
            this.temEffect.action = "none";
            if(this.temInfo.Place < 31)
            {
               DragManager.acceptDrag(this);
            }
            else if(PlayerManager.Instance.Self.canEquip(this.temInfo))
            {
               SocketManager.Instance.out.sendMoveGoods(0,this.temInfo.Place,0,PlayerManager.Instance.getDressEquipPlace(this.temInfo),this.temInfo.Count);
               DragManager.acceptDrag(this,"move");
            }
            else
            {
               DragManager.acceptDrag(this);
            }
         }
      }
   }
}

