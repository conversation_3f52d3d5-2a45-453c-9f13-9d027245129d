package braveDoor
{
   import braveDoor.data.BraveDoorDuplicateInfo;
   import braveDoor.data.DuplicateInfo;
   import ddt.CoreManager;
   import ddt.data.analyze.BraveDoorDuplicateAnalyzer;
   import ddt.events.CEvent;
   import ddt.events.PkgEvent;
   import ddt.manager.ChatManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.TimeManager;
   import ddt.utils.AssetModuleLoader;
   import flash.events.IEventDispatcher;
   import flash.utils.clearTimeout;
   import flash.utils.setTimeout;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   import road7th.data.DictionaryData;
   
   public class BraveDoorManager extends CoreManager
   {
      
      private static var _instance:BraveDoorManager = null;
      
      private var _timeOut:uint = 0;
      
      private var _showFlag:Boolean = false;
      
      private var _endDate:Date;
      
      private var _currentPage:int = 0;
      
      private var _isShow:Boolean = false;
      
      private var _sweepConfig:DictionaryData;
      
      private var _duplicates:Vector.<BraveDoorDuplicateInfo>;
      
      private var _clickNum:Number = 0;
      
      public function BraveDoorManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get instance() : BraveDoorManager
      {
         if(_instance == null)
         {
            _instance = new BraveDoorManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(315,1),this._isOpen);
         SocketManager.Instance.addEventListener(PkgEvent.format(315,4),this._getAwardTime);
      }
      
      protected function _isOpen(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this._showFlag = _local_2.readBoolean();
         if(this._showFlag && PlayerManager.Instance.Self.Grade >= 20)
         {
            HallIconManager.instance.updateSwitchHandler("bravedoor",true);
            ChatManager.Instance.sysChatAmaranth(LanguageMgr.GetTranslation("game.braveDoor.openMsg"));
         }
      }
      
      private function _getAwardTime(_arg_1:PkgEvent) : void
      {
         var _local_2:Date = _arg_1.pkg.readDate();
         this.sendSweepAwardGet(Math.abs(TimeManager.Instance.TotalSecondToNow(_local_2)));
      }
      
      public function sendSweepAwardGet(_arg_1:int) : void
      {
         var _local_2:Number = NaN;
         if(_arg_1 != 0)
         {
            this.stopSendSweepAwardGet();
            _local_2 = _arg_1 * 1000 + 2000;
            this._timeOut = setTimeout(this.stopSendSweepAwardGetComplete,_local_2);
         }
      }
      
      public function stopSendSweepAwardGet() : void
      {
         if(this._timeOut != 0)
         {
            clearTimeout(this._timeOut);
            this._timeOut = 0;
         }
      }
      
      private function stopSendSweepAwardGetComplete() : void
      {
         this.stopSendSweepAwardGet();
         SocketManager.Instance.out.sendBraveDoorSweepGetAward();
      }
      
      public function openView_Handler() : void
      {
         SoundManager.instance.playButtonSound();
         var _local_1:Number = new Date().time;
         if(_local_1 - this._clickNum < 1000)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.storeIIStrength.startStrengthClickTimerMsg"));
            return;
         }
         this._clickNum = _local_1;
         show();
      }
      
      override protected function start() : void
      {
         AssetModuleLoader.addModelLoader("braveDoor",6);
         AssetModuleLoader.startCodeLoader(this.__complainShow);
      }
      
      private function __complainShow() : void
      {
         if(this._duplicates != null)
         {
            this.openView();
            return;
         }
      }
      
      public function setupDuplicateTemplate(_arg_1:BraveDoorDuplicateAnalyzer) : void
      {
         this._duplicates = _arg_1.list;
      }
      
      public function openView() : void
      {
         if(!this._isShow)
         {
            this._isShow = true;
            dispatchEvent(new CEvent("openBraveDoorView"));
         }
      }
      
      public function getDuplicateInfoByDupID(_arg_1:int) : DuplicateInfo
      {
         var _local_3:BraveDoorDuplicateInfo = null;
         var _local_2:* = null;
         if(this._duplicates != null && this._duplicates.length > 0)
         {
            for each(_local_3 in this._duplicates)
            {
               _local_2 = _local_3.getDunplicateInfoByDupID(_arg_1);
               if(_local_2 != null)
               {
                  break;
               }
            }
         }
         return _local_2;
      }
      
      public function getDuplicateTemInfo() : Vector.<BraveDoorDuplicateInfo>
      {
         return this._duplicates;
      }
      
      public function getSweepConfigByDupID(_arg_1:int) : Array
      {
         var _local_4:int = 0;
         var _local_2:* = null;
         var _local_3:* = null;
         if(this._sweepConfig == null)
         {
            this._sweepConfig = new DictionaryData();
            _local_2 = ServerConfigManager.instance.BraveDoorConfig;
            _local_4 = 0;
            while(_local_4 < _local_2.length)
            {
               _local_3 = _local_2[_local_4].split(",");
               this._sweepConfig.add(_local_3[0],_local_3);
               _local_4++;
            }
         }
         if(this._sweepConfig.hasKey(_arg_1))
         {
            return this._sweepConfig[_arg_1];
         }
         return null;
      }
      
      public function get currentPage() : int
      {
         return this._currentPage;
      }
      
      public function set currentPage(_arg_1:int) : void
      {
         this._currentPage = _arg_1;
      }
      
      public function get moduleIsShow() : Boolean
      {
         return this._isShow;
      }
      
      public function set moduleIsShow(_arg_1:Boolean) : void
      {
         this._isShow = _arg_1;
      }
      
      public function get isOpen() : Boolean
      {
         return this._showFlag;
      }
   }
}

