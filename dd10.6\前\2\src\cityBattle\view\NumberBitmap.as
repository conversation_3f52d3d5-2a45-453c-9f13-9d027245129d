package cityBattle.view
{
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class NumberBitmap extends Sprite implements Disposeable
   {
      
      private var _box:Sprite;
      
      public function NumberBitmap()
      {
         super();
         this._box = new Sprite();
         addChild(this._box);
         var _local_1:MovieClip = ClassUtils.CreatInstance("asset.cityBattle.Number");
         addChild(_local_1);
      }
      
      public function dispose() : void
      {
         while(Boolean(numChildren))
         {
            ObjectUtils.disposeObject(getChildAt(0));
         }
         if(Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
      }
   }
}

