package activity.Syw
{
   public class SywInfo
   {
      
      private var _ID:int;
      
      private var _TemplateID:int;
      
      private var _Level:int;
      
      private var _Attack:int;
      
      private var _Defend:int;
      
      private var _Agility:int;
      
      private var _Luck:int;
      
      private var _MagicAttack:int;
      
      private var _MagicDefence:int;
      
      private var _FireAttack:int;
      
      private var _WindAttack:int;
      
      private var _WaterAttack:int;
      
      private var _LandAttack:int;
      
      private var _GuangAattack:int;
      
      private var _AnAttack:int;
      
      private var _SkillID:String;
      
      public function SywInfo()
      {
         super();
      }
      
      public function get ID() : int
      {
         return this._ID;
      }
      
      public function set ID(value:int) : void
      {
         this._ID = value;
      }
      
      public function get TemplateID() : int
      {
         return this._TemplateID;
      }
      
      public function set TemplateID(value:int) : void
      {
         this._TemplateID = value;
      }
      
      public function get Level() : int
      {
         return this._Level;
      }
      
      public function set Level(value:int) : void
      {
         this._Level = value;
      }
      
      public function get Attack() : int
      {
         return this._Attack;
      }
      
      public function set Attack(value:int) : void
      {
         this._Attack = value;
      }
      
      public function get Defend() : int
      {
         return this._Defend;
      }
      
      public function set Defend(value:int) : void
      {
         this._Defend = value;
      }
      
      public function get Agility() : int
      {
         return this._Agility;
      }
      
      public function set Agility(value:int) : void
      {
         this._Agility = value;
      }
      
      public function get Luck() : int
      {
         return this._Luck;
      }
      
      public function set Luck(value:int) : void
      {
         this._Luck = value;
      }
      
      public function get MagicAttack() : int
      {
         return this._MagicAttack;
      }
      
      public function set MagicAttack(value:int) : void
      {
         this._MagicAttack = value;
      }
      
      public function get MagicDefence() : int
      {
         return this._MagicDefence;
      }
      
      public function set MagicDefence(value:int) : void
      {
         this._MagicDefence = value;
      }
      
      public function get FireAttack() : int
      {
         return this._FireAttack;
      }
      
      public function set FireAttack(value:int) : void
      {
         this._FireAttack = value;
      }
      
      public function get WindAttack() : int
      {
         return this._WindAttack;
      }
      
      public function set WindAttack(value:int) : void
      {
         this._WindAttack = value;
      }
      
      public function get WaterAttack() : int
      {
         return this._WaterAttack;
      }
      
      public function set WaterAttack(value:int) : void
      {
         this._WaterAttack = value;
      }
      
      public function get LandAttack() : int
      {
         return this._LandAttack;
      }
      
      public function set LandAttack(value:int) : void
      {
         this._LandAttack = value;
      }
      
      public function get GuangAattack() : int
      {
         return this._GuangAattack;
      }
      
      public function set GuangAattack(value:int) : void
      {
         this._GuangAattack = value;
      }
      
      public function get AnAttack() : int
      {
         return this._AnAttack;
      }
      
      public function set AnAttack(value:int) : void
      {
         this._AnAttack = value;
      }
      
      public function get SkillID() : String
      {
         return this._SkillID;
      }
      
      public function set SkillID(value:String) : void
      {
         this._SkillID = value;
      }
   }
}

