package com.hurlant.math
{
   use namespace bi_internal;
   
   internal class MontgomeryReduction implements IReduction
   {
      
      private var um:int;
      
      private var mp:int;
      
      private var mph:int;
      
      private var mpl:int;
      
      private var mt2:int;
      
      private var m:BigInteger;
      
      public function MontgomeryReduction(_arg_1:BigInteger)
      {
         super();
         this.m = _arg_1;
         this.mp = _arg_1.bi_internal::invDigit();
         this.mpl = this.mp & 0x7FFF;
         this.mph = this.mp >> 15;
         this.um = (1 << BigInteger.DB - 15) - 1;
         this.mt2 = 2 * _arg_1.t;
      }
      
      public function mulTo(_arg_1:BigInteger, _arg_2:BigInteger, _arg_3:BigInteger) : void
      {
         _arg_1.bi_internal::multiplyTo(_arg_2,_arg_3);
         this.reduce(_arg_3);
      }
      
      public function revert(_arg_1:BigInteger) : BigInteger
      {
         var _local_2:BigInteger = null;
         _local_2 = new BigInteger();
         _arg_1.bi_internal::copyTo(_local_2);
         this.reduce(_local_2);
         return _local_2;
      }
      
      public function convert(_arg_1:BigInteger) : BigInteger
      {
         var _local_2:BigInteger = null;
         _local_2 = new BigInteger();
         _arg_1.abs().bi_internal::dlShiftTo(this.m.t,_local_2);
         _local_2.bi_internal::divRemTo(this.m,null,_local_2);
         if(_arg_1.bi_internal::s < 0 && _local_2.compareTo(BigInteger.ZERO) > 0)
         {
            this.m.bi_internal::subTo(_local_2,_local_2);
         }
         return _local_2;
      }
      
      public function reduce(_arg_1:BigInteger) : void
      {
         var _local_2:int = 0;
         var _local_3:int = 0;
         var _local_4:int = 0;
         var _local_5:* = undefined;
         while(_arg_1.t <= this.mt2)
         {
            _local_5 = _arg_1.t++;
            _arg_1.bi_internal::a[_local_5] = 0;
         }
         _local_2 = 0;
         while(_local_2 < this.m.t)
         {
            _local_3 = _arg_1.bi_internal::a[_local_2] & 0x7FFF;
            _local_4 = _local_3 * this.mpl + ((_local_3 * this.mph + (_arg_1.bi_internal::a[_local_2] >> 15) * this.mpl & this.um) << 15) & BigInteger.DM;
            _local_3 = _local_2 + this.m.t;
            _arg_1.bi_internal::a[_local_3] += this.m.bi_internal::am(0,_local_4,_arg_1,_local_2,0,this.m.t);
            while(_arg_1.bi_internal::a[_local_3] >= BigInteger.DV)
            {
               _arg_1.bi_internal::a[_local_3] -= BigInteger.DV;
               ++_arg_1.bi_internal::a[++_local_3];
            }
            _local_2++;
         }
         _arg_1.bi_internal::clamp();
         _arg_1.bi_internal::drShiftTo(this.m.t,_arg_1);
         if(_arg_1.compareTo(this.m) >= 0)
         {
            _arg_1.bi_internal::subTo(this.m,_arg_1);
         }
      }
      
      public function sqrTo(_arg_1:BigInteger, _arg_2:BigInteger) : void
      {
         _arg_1.bi_internal::squareTo(_arg_2);
         this.reduce(_arg_2);
      }
   }
}

