package com.pickgliss.loader
{
   import flash.events.Event;
   import flash.net.URLVariables;
   import flash.utils.ByteArray;
   import flash.utils.getDefinitionByName;
   
   public class MornUIDataLoader extends BaseLoader
   {
      
      private var _content:*;
      
      public function MornUIDataLoader(_arg_1:int, _arg_2:String, _arg_3:URLVariables = null, _arg_4:String = "GET")
      {
         if(_arg_3 == null)
         {
            _arg_3 = new URLVariables();
         }
         if(_arg_3["rnd"] == null)
         {
            _arg_3["rnd"] = TextLoader.TextLoaderKey;
         }
         super(_arg_1,_arg_2,_arg_3,_arg_4);
      }
      
      override protected function __onDataLoadComplete(_arg_1:Event) : void
      {
         removeEvent();
         unload();
         var _local_2:ByteArray = _loader.data;
         _local_2.uncompress();
         this._content = _local_2.readObject();
         this._content = _local_2;
         this.analysisMornUIData();
         fireCompleteEvent();
      }
      
      private function analysisMornUIData() : void
      {
         var _local_2:String = null;
         var _local_1:Object = getDefinitionByName("morn.core.components.View");
         if(Boolean(_local_1))
         {
            for(_local_2 in this._content)
            {
               _local_1.uiMap[_local_2] = this._content[_local_2];
            }
         }
         else
         {
            trace("加载.ui文件异常，未导入morn.core.components.View包~~~~~~~~~" + url);
         }
      }
      
      override public function get content() : *
      {
         return this._content;
      }
      
      override public function get type() : int
      {
         return 8;
      }
   }
}

