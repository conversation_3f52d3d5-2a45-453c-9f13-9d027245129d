package activity.newyear.data
{
   public class NewYearModel
   {
      
      public var selfScore:int;
      
      public var selfTotalScore:int;
      
      public var selfRank:int;
      
      public var rate:int;
      
      public var limitCount:int;
      
      public var progressRewards:int;
      
      public var activityDesc:String;
      
      public var buyFightCount:int;
      
      public var fightContend:Array = [];
      
      public var playerRankList:Array = [];
      
      public var buyLimitPrice:int;
      
      public var date:String;
      
      public var endTime:Number;
      
      private var _activiteState:int;
      
      private var _rankReward:Array;
      
      private var _scoreReward:Array;
      
      public function NewYearModel()
      {
         super();
      }
      
      public function get isOpen() : Boolean
      {
         return this._activiteState != 0 && this._activiteState != 4;
      }
      
      public function get isFight() : Boolean
      {
         return this._activiteState == 1;
      }
      
      public function set activiteState(_arg_1:int) : void
      {
         this._activiteState = _arg_1;
      }
      
      public function isGetReward(_arg_1:int) : Boolean
      {
         return (this.progressRewards & 1 << _arg_1) > 0;
      }
      
      public function get rankReward() : Array
      {
         return this._rankReward;
      }
      
      public function set rankReward(_arg_1:Array) : void
      {
         this._rankReward = _arg_1;
      }
      
      public function get scoreReward() : Array
      {
         return this._scoreReward;
      }
      
      public function set scoreReward(_arg_1:Array) : void
      {
         this._scoreReward = _arg_1;
      }
   }
}

