package com.pickgliss.ui.controls
{
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.ComponentSetting;
   import com.pickgliss.ui.core.Component;
   import com.pickgliss.utils.DisplayUtils;
   import com.pickgliss.utils.ObjectUtils;
   import com.pickgliss.utils.PNGHitAreaFactory;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   
   [Event(name="change",type="flash.events.Event")]
   public class BaseButton extends Component
   {
      
      public static const P_backStyle:String = "backStyle";
      
      public static const P_backgoundRotation:String = "backgoundRotation";
      
      public static const P_pressEnable:String = "pressEnable";
      
      public static const P_transparentEnable:String = "transparentEnable";
      
      public static const P_autoSizeAble:String = "autoSizeAble";
      
      public static const P_stopMovieAtLastFrame:String = "stopMovieAtLastFrame";
      
      private var _offsetCount:int;
      
      protected var _PNGHitArea:Sprite;
      
      protected var _back:DisplayObject;
      
      protected var _backStyle:String;
      
      protected var _backgoundRotation:int;
      
      protected var _currentFrameIndex:int = 1;
      
      protected var _enable:Boolean = true;
      
      protected var _filterString:String;
      
      protected var _frameFilter:Array;
      
      protected var _pressEnable:Boolean;
      
      protected var _stopMovieAtLastFrame:Boolean;
      
      private var _displacementEnable:Boolean = true;
      
      private var _pressStartTimer:Timer;
      
      private var _pressStepTimer:Timer;
      
      protected var _transparentEnable:Boolean;
      
      protected var _autoSizeAble:Boolean = true;
      
      private var _useLogID:int = 0;
      
      private var _focusFrameStyle:String;
      
      public var _focusFrame:Bitmap;
      
      private var _autoFrame:Boolean = true;
      
      public function BaseButton()
      {
         super();
         this.init();
         this.addEvent();
      }
      
      public function get autoFrame() : Boolean
      {
         return this._autoFrame;
      }
      
      public function set autoFrame(_arg_1:Boolean) : void
      {
         this._autoFrame = _arg_1;
      }
      
      public function get focusFrameStyle() : String
      {
         return this._focusFrameStyle;
      }
      
      public function set focusFrameStyle(_arg_1:String) : void
      {
         if(_arg_1 == "")
         {
            return;
         }
         var _local_2:Array = _arg_1.split("|");
         if(_local_2.length > 0)
         {
            this._focusFrame = ComponentFactory.Instance.creatBitmap(_local_2[0]);
            this._focusFrame.visible = false;
         }
         if(_local_2.length > 2)
         {
            this._focusFrame.x = _local_2[1];
            this._focusFrame.y = _local_2[2];
         }
         else
         {
            this._focusFrame.x = -5;
            this._focusFrame.y = -3;
         }
         addChild(this._focusFrame);
      }
      
      public function set useLogID(_arg_1:int) : void
      {
         this._useLogID = _arg_1;
      }
      
      public function get useLogID() : int
      {
         return this._useLogID;
      }
      
      public function get frameFilter() : Array
      {
         return this._frameFilter;
      }
      
      public function set frameFilter(_arg_1:Array) : void
      {
         this._frameFilter = _arg_1;
      }
      
      public function set autoSizeAble(_arg_1:Boolean) : void
      {
         if(this._autoSizeAble == _arg_1)
         {
            return;
         }
         this._autoSizeAble = _arg_1;
         onPropertiesChanged("autoSizeAble");
      }
      
      public function get backStyle() : String
      {
         return this._backStyle;
      }
      
      public function set backStyle(_arg_1:String) : void
      {
         if(_arg_1 == this._backStyle)
         {
            return;
         }
         this._backStyle = _arg_1;
         this.backgound = ComponentFactory.Instance.creat(this._backStyle);
         onPropertiesChanged("backStyle");
      }
      
      public function set backgound(_arg_1:DisplayObject) : void
      {
         if(this._back == _arg_1)
         {
            return;
         }
         ObjectUtils.disposeObject(this._back);
         this._back = _arg_1;
         _width = this._back.width;
         _height = this._back.height;
         onPropertiesChanged("backStyle");
      }
      
      public function get backgound() : DisplayObject
      {
         return this._back;
      }
      
      public function set backgoundRotation(_arg_1:int) : void
      {
         if(this._backgoundRotation == _arg_1)
         {
            return;
         }
         this._backgoundRotation = _arg_1;
         onPropertiesChanged("backgoundRotation");
      }
      
      public function get displacement() : Boolean
      {
         return this._displacementEnable;
      }
      
      public function set displacement(_arg_1:Boolean) : void
      {
         this._displacementEnable = _arg_1;
      }
      
      override public function dispose() : void
      {
         this.removeEvent();
         if(Boolean(this._back))
         {
            this._back.filters = null;
         }
         ObjectUtils.disposeObject(this._back);
         ObjectUtils.disposeObject(this._PNGHitArea);
         this._PNGHitArea = null;
         this._back = null;
         this._frameFilter = null;
         this._pressStepTimer = null;
         this._pressStartTimer = null;
         super.dispose();
      }
      
      public function get enable() : Boolean
      {
         return this._enable;
      }
      
      public function set enable(_arg_1:Boolean) : void
      {
         if(this._enable == _arg_1)
         {
            return;
         }
         this._enable = _arg_1;
         mouseEnabled = this._enable;
         if(this._enable)
         {
            this.setFrame(1);
         }
         else
         {
            this.setFrame(4);
         }
         this.updatePosition();
      }
      
      private function updatePosition() : void
      {
         x += ComponentSetting.DISPLACEMENT_OFFSET * -this._offsetCount;
         y += ComponentSetting.DISPLACEMENT_OFFSET * -this._offsetCount;
         this._offsetCount = 0;
      }
      
      public function set filterString(_arg_1:String) : void
      {
         if(this._filterString == _arg_1)
         {
            return;
         }
         this._filterString = _arg_1;
         this._frameFilter = ComponentFactory.Instance.creatFrameFilters(this._filterString);
      }
      
      public function set pressEnable(_arg_1:Boolean) : void
      {
         if(this._pressEnable == _arg_1)
         {
            return;
         }
         this._pressEnable = _arg_1;
         onPropertiesChanged("pressEnable");
      }
      
      public function get transparentEnable() : Boolean
      {
         return this._transparentEnable;
      }
      
      public function set transparentEnable(_arg_1:Boolean) : void
      {
         if(this._transparentEnable == _arg_1)
         {
            return;
         }
         this._transparentEnable = _arg_1;
         onPropertiesChanged("transparentEnable");
      }
      
      protected function __onMouseClick(_arg_1:MouseEvent) : void
      {
         if(!this._enable)
         {
            _arg_1.stopImmediatePropagation();
         }
         else if(this._useLogID != 0 && ComponentSetting.SEND_USELOG_ID != null)
         {
            ComponentSetting.SEND_USELOG_ID(this._useLogID);
         }
      }
      
      protected function adaptHitArea() : void
      {
         this._PNGHitArea.x = this._back.x;
         this._PNGHitArea.y = this._back.y;
      }
      
      override protected function addChildren() : void
      {
         if(Boolean(this._back))
         {
            addChild(this._back);
         }
      }
      
      protected function addEvent() : void
      {
         addEventListener("click",this.__onMouseClick);
         addEventListener("rollOver",this.__onMouseRollover);
         addEventListener("rollOut",this.__onMouseRollout);
         addEventListener("mouseDown",this.__onMousedown);
      }
      
      public function set stopMovieAtLastFrame(_arg_1:Boolean) : void
      {
         if(this._stopMovieAtLastFrame == _arg_1)
         {
            return;
         }
         this._stopMovieAtLastFrame = _arg_1;
         onPropertiesChanged("stopMovieAtLastFrame");
      }
      
      public function get stopMovieAtLastFrame() : Boolean
      {
         return this._stopMovieAtLastFrame;
      }
      
      protected function drawHitArea() : void
      {
         if(Boolean(this._PNGHitArea) && contains(this._PNGHitArea))
         {
            removeChild(this._PNGHitArea);
         }
         if(this._back == null)
         {
            return;
         }
         if(this._transparentEnable)
         {
            this._PNGHitArea = PNGHitAreaFactory.drawHitArea(DisplayUtils.getDisplayBitmapData(this._back));
            hitArea = this._PNGHitArea;
            this._PNGHitArea.alpha = 0;
            this.adaptHitArea();
            addChild(this._PNGHitArea);
         }
         else if(Boolean(this._PNGHitArea) && contains(this._PNGHitArea))
         {
            removeChild(this._PNGHitArea);
         }
      }
      
      override protected function init() : void
      {
         super.init();
         mouseChildren = false;
         buttonMode = true;
      }
      
      override protected function onProppertiesUpdate() : void
      {
         var _local_4:int = 0;
         var _local_3:* = null;
         var _local_2:* = null;
         var _local_1:* = null;
         super.onProppertiesUpdate();
         if(Boolean(_changedPropeties["pressEnable"]))
         {
            if(this._pressEnable == true)
            {
               this._pressStartTimer = new Timer(ComponentSetting.BUTTON_PRESS_START_TIME,1);
               this._pressStepTimer = new Timer(ComponentSetting.BUTTON_PRESS_STEP_TIME);
            }
         }
         if(Boolean(_changedPropeties["backStyle"]) && this._autoSizeAble)
         {
            if(Boolean(this._back) && (this._back.width > 0 || this._back.height > 0))
            {
               _width = this._back.width;
               _height = this._back.height;
            }
         }
         if(Boolean(_changedPropeties["width"]) || Boolean(_changedPropeties["height"]))
         {
            if(Boolean(this._back))
            {
               this._back.width = _width;
               this._back.height = _height;
            }
         }
         if(Boolean(_changedPropeties["backgoundRotation"]))
         {
            if(Boolean(this._back))
            {
               this._back.rotation = this._backgoundRotation;
               _local_3 = this._back.getRect(this);
               this._back.x = -_local_3.x;
               this._back.y = -_local_3.y;
            }
         }
         if(Boolean(_changedPropeties["width"]) || Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["backStyle"]) || Boolean(_changedPropeties["backgoundRotation"]) || Boolean(_changedPropeties["transparentEnable"]))
         {
            this.drawHitArea();
         }
         this.setFrame(this._currentFrameIndex);
         if(Boolean(_changedPropeties["stopMovieAtLastFrame"]) && this._stopMovieAtLastFrame)
         {
            _local_2 = this._back as MovieClip;
            if(_local_2 != null)
            {
               _local_4 = 0;
               while(_local_4 < _local_2.numChildren)
               {
                  _local_1 = _local_2.getChildAt(_local_4) as MovieClip;
                  if(_local_1)
                  {
                     _local_1.gotoAndStop(_local_1.totalFrames);
                  }
                  _local_4++;
               }
            }
         }
      }
      
      protected function removeEvent() : void
      {
         removeEventListener("click",this.__onMouseClick);
         removeEventListener("rollOver",this.__onMouseRollover);
         removeEventListener("rollOut",this.__onMouseRollout);
         StageReferance.stage.removeEventListener("mouseUp",this.__onMouseup);
         removeEventListener("mouseDown",this.__onMousedown);
         if(Boolean(this._pressStartTimer))
         {
            this._pressStartTimer.removeEventListener("timer",this.__onPressedStart);
         }
         if(Boolean(this._pressStepTimer))
         {
            this._pressStepTimer.removeEventListener("timer",this.__onPressStepTimer);
         }
      }
      
      public function setFrame(_arg_1:int) : void
      {
         if(this._autoFrame)
         {
            this._currentFrameIndex = _arg_1;
            DisplayUtils.setFrame(this._back,this._currentFrameIndex);
         }
         if(this._frameFilter == null || _arg_1 <= 0 || _arg_1 > this._frameFilter.length)
         {
            return;
         }
         filters = this._frameFilter[_arg_1 - 1];
      }
      
      protected function __onMouseRollout(_arg_1:MouseEvent) : void
      {
         if(this._enable && !_arg_1.buttonDown)
         {
            this.setFrame(1);
         }
         if(Boolean(this._focusFrame))
         {
            this._focusFrame.visible = false;
         }
      }
      
      protected function __onMouseRollover(_arg_1:MouseEvent) : void
      {
         if(this._enable && !_arg_1.buttonDown)
         {
            this.setFrame(2);
         }
         if(Boolean(this._focusFrame))
         {
            this._focusFrame.visible = true;
         }
      }
      
      private function __onMousedown(_arg_1:MouseEvent) : void
      {
         if(this._enable)
         {
            this.setFrame(3);
         }
         if(Boolean(this._focusFrame))
         {
            this._focusFrame.visible = false;
         }
         if(this._displacementEnable && this._offsetCount < 1)
         {
            x += ComponentSetting.DISPLACEMENT_OFFSET;
            y += ComponentSetting.DISPLACEMENT_OFFSET;
            ++this._offsetCount;
         }
         StageReferance.stage.addEventListener("mouseUp",this.__onMouseup);
         if(this._pressEnable)
         {
            this.__onPressStepTimer(null);
            this._pressStartTimer.addEventListener("timer",this.__onPressedStart);
            this._pressStartTimer.start();
         }
      }
      
      private function __onMouseup(_arg_1:MouseEvent) : void
      {
         StageReferance.stage.removeEventListener("mouseUp",this.__onMouseup);
         if(!this._enable)
         {
            return;
         }
         if(this._displacementEnable && this._offsetCount > -1)
         {
            x -= ComponentSetting.DISPLACEMENT_OFFSET;
            y -= ComponentSetting.DISPLACEMENT_OFFSET;
            --this._offsetCount;
         }
         if(!(_arg_1.target is DisplayObject))
         {
            this.setFrame(1);
         }
         if(_arg_1.target == this)
         {
            this.setFrame(2);
         }
         else
         {
            this.setFrame(1);
         }
         if(this._pressEnable)
         {
            this._pressStartTimer.stop();
            this._pressStepTimer.stop();
            this._pressStepTimer.removeEventListener("timer",this.__onPressStepTimer);
         }
      }
      
      private function __onPressStepTimer(_arg_1:TimerEvent) : void
      {
         dispatchEvent(new Event("change"));
      }
      
      private function __onPressedStart(_arg_1:TimerEvent) : void
      {
         this._pressStartTimer.removeEventListener("timer",this.__onPressedStart);
         this._pressStartTimer.reset();
         this._pressStartTimer.stop();
         this._pressStepTimer.start();
         this._pressStepTimer.addEventListener("timer",this.__onPressStepTimer);
      }
   }
}

