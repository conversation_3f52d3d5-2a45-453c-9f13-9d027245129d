-- 给玩家添加圣物升级石用于测试
-- 请将 {玩家ID} 替换为实际的玩家ID

-- 添加圣物升级石 (ID: 386298) 数量: 100个
INSERT INTO [User_Items] ([UserID], [TemplateID], [Count], [IsUsed], [BeginDate], [ValidDate], [IsExist], [Place], [BagType])
VALUES ({玩家ID}, 386298, 100, 0, GETDATE(), '2099-12-31 23:59:59', 1, 0, 1);

-- 添加圣物进阶石 (ID: 386299) 数量: 50个
INSERT INTO [User_Items] ([UserID], [TemplateID], [Count], [IsUsed], [BeginDate], [ValidDate], [IsExist], [Place], [BagType])
VALUES ({玩家ID}, 386299, 50, 0, GETDATE(), '2099-12-31 23:59:59', 1, 0, 1);

-- 添加圣物精华 (ID: 386300) 数量: 200个
INSERT INTO [User_Items] ([UserID], [TemplateID], [Count], [IsUsed], [BeginDate], [ValidDate], [IsExist], [Place], [BagType])
VALUES ({玩家ID}, 386300, 200, 0, GETDATE(), '2099-12-31 23:59:59', 1, 0, 1);

-- 添加圣物强化石 (ID: 386301) 数量: 50个
INSERT INTO [User_Items] ([UserID], [TemplateID], [Count], [IsUsed], [BeginDate], [ValidDate], [IsExist], [Place], [BagType])
VALUES ({玩家ID}, 386301, 50, 0, GETDATE(), '2099-12-31 23:59:59', 1, 0, 1);

-- 直接增加圣物增幅石数量（用于测试圣物增幅功能）
-- 注意：圣物增幅功能使用的是特殊计数器，不是普通物品
UPDATE [Sys_User_RelicItemInfo]
SET ZFNum = ZFNum + 100,  -- 增加100个圣物增幅石
    GJZFNum = GJZFNum + 50  -- 增加50个高级圣物增幅石
WHERE UserID = {玩家ID};

-- 如果玩家没有圣物信息记录，则创建一个
IF NOT EXISTS (SELECT 1 FROM [Sys_User_RelicItemInfo] WHERE UserID = {玩家ID})
BEGIN
    INSERT INTO [Sys_User_RelicItemInfo] ([UserID], [XiuLianNum], [ZFNum], [GJZFNum], [AdvanceNum], [GJAdvanceNum], [shopScore], [RelicInfo], [ManualInfo], [OpenCount])
    VALUES ({玩家ID}, 0, 100, 50, 0, 0, 0, N'0,0,0,0,0,0', N'[{"type":0,"level":1,"exp":0},{"type":1,"level":1,"exp":0},{"type":2,"level":1,"exp":0},{"type":3,"level":1,"exp":0},{"type":4,"level":1,"exp":0},{"type":5,"level":1,"exp":0}]', 0);
END

-- 查询玩家背包中的圣物相关物品
SELECT
    ui.ItemID,
    ui.TemplateID,
    it.Name,
    ui.Count,
    ui.BeginDate,
    ui.ValidDate
FROM [User_Items] ui
INNER JOIN [Items_Template] it ON ui.TemplateID = it.TemplateID
WHERE ui.UserID = {玩家ID}
    AND ui.TemplateID IN (386298, 386299, 386300, 386301)
    AND ui.IsExist = 1
ORDER BY ui.TemplateID;

-- 查询玩家的圣物增幅石数量
SELECT
    UserID,
    ZFNum as '圣物增幅石数量',
    GJZFNum as '高级圣物增幅石数量',
    XiuLianNum as '修炼石数量',
    AdvanceNum as '进阶石数量',
    GJAdvanceNum as '高级进阶石数量'
FROM [Sys_User_RelicItemInfo]
WHERE UserID = {玩家ID};
