-- 给玩家添加圣物升级石用于测试
-- 请将 {玩家ID} 替换为实际的玩家ID

-- 添加圣物升级石 (ID: 386298) 数量: 100个
INSERT INTO [User_Items] ([UserID], [TemplateID], [Count], [IsUsed], [BeginDate], [ValidDate], [IsExist], [Place], [BagType])
VALUES ({玩家ID}, 386298, 100, 0, GETDATE(), '2099-12-31 23:59:59', 1, 0, 1);

-- 添加圣物进阶石 (ID: 386299) 数量: 50个
INSERT INTO [User_Items] ([UserID], [TemplateID], [Count], [IsUsed], [BeginDate], [ValidDate], [IsExist], [Place], [BagType])
VALUES ({玩家ID}, 386299, 50, 0, GETDATE(), '2099-12-31 23:59:59', 1, 0, 1);

-- 添加圣物精华 (ID: 386300) 数量: 200个
INSERT INTO [User_Items] ([UserID], [TemplateID], [Count], [IsUsed], [BeginDate], [ValidDate], [IsExist], [Place], [BagType])
VALUES ({玩家ID}, 386300, 200, 0, GETDATE(), '2099-12-31 23:59:59', 1, 0, 1);

-- 添加圣物强化石 (ID: 386301) 数量: 50个
INSERT INTO [User_Items] ([UserID], [TemplateID], [Count], [IsUsed], [BeginDate], [ValidDate], [IsExist], [Place], [BagType])
VALUES ({玩家ID}, 386301, 50, 0, GETDATE(), '2099-12-31 23:59:59', 1, 0, 1);

-- 查询玩家背包中的圣物相关物品
SELECT 
    ui.ItemID,
    ui.TemplateID,
    it.Name,
    ui.Count,
    ui.BeginDate,
    ui.ValidDate
FROM [User_Items] ui
INNER JOIN [Items_Template] it ON ui.TemplateID = it.TemplateID
WHERE ui.UserID = {玩家ID} 
    AND ui.TemplateID IN (386298, 386299, 386300, 386301)
    AND ui.IsExist = 1
ORDER BY ui.TemplateID;
