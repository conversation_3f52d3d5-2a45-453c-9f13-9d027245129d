package com.pickgliss.loader
{
   import flash.utils.Dictionary;
   
   public class LoaderNameFilter
   {
      
      private static var _pathList:Dictionary;
      
      private static var _loadNameList:Dictionary = null;
      
      public function LoaderNameFilter()
      {
         super();
      }
      
      public static function setLoadNameContent(_arg_1:XML) : void
      {
         var _local_7:int = 0;
         var _local_2:* = null;
         var _local_4:* = null;
         var _local_3:* = null;
         _loadNameList = new Dictionary();
         _pathList = new Dictionary();
         var _local_6:XMLList = _arg_1..Item;
         var _local_5:int = int(_local_6.length());
         _local_7 = 0;
         while(_local_7 < _local_5)
         {
            _local_2 = _local_6[_local_7].@name;
            _local_4 = _local_6[_local_7].@loadName;
            _local_3 = (String(_local_6[_local_7].@path) + _local_2).replace(/\\|\//g,"_");
            _loadNameList[_local_2] = _local_4;
            if(_local_3 != "" && _pathList[_local_3] == null)
            {
               _pathList[_local_3] = true;
            }
            _local_7++;
         }
      }
      
      private static function isFilter(_arg_1:String) : Boolean
      {
         var _local_2:String = null;
         var _local_3:String = _arg_1.replace(/\\|\//g,"_").toLocaleLowerCase();
         for(_local_2 in _pathList)
         {
            if(_local_3.indexOf(_local_2.toLocaleLowerCase()) != -1)
            {
               return true;
            }
         }
         return false;
      }
      
      public static function getLoadFilePath(_arg_1:String) : String
      {
         var _local_4:String = null;
         var _local_2:* = null;
         if(_loadNameList == null || !isFilter(_arg_1))
         {
            return _arg_1;
         }
         var _local_3:* = _arg_1;
         for(_local_4 in _loadNameList)
         {
            _local_2 = "/" + _local_4;
            if(_local_3.indexOf(_local_2) != -1)
            {
               _local_3 = _local_3.replace(_local_4,_loadNameList[_local_4]);
               break;
            }
         }
         return _local_3;
      }
      
      public static function getRealFilePath(_arg_1:String) : String
      {
         var _local_2:String = null;
         if(_loadNameList == null)
         {
            return _arg_1;
         }
         var _local_3:* = _arg_1;
         for(_local_2 in _loadNameList)
         {
            if(_arg_1.indexOf(_loadNameList[_local_2]) != -1)
            {
               _local_3 = _arg_1.replace(_loadNameList[_local_2],_local_2);
               break;
            }
         }
         return _local_3;
      }
   }
}

