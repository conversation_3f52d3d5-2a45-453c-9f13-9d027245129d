package com.pickgliss.utils
{
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import flash.geom.Point;
   
   public class PNGHitAreaFactory
   {
      
      private static var points1:Vector.<Point>;
      
      private static var points2:Vector.<Point>;
      
      private static var coord:Vector.<Number>;
      
      private static var commands:Vector.<int>;
      
      public function PNGHitAreaFactory()
      {
         super();
      }
      
      public static function drawHitArea(_arg_1:BitmapData) : Sprite
      {
         var _local_5:int = 0;
         var _local_6:Point = null;
         var _local_4:Point = null;
         var _local_3:uint = 0;
         getPointAroundImage(_arg_1);
         for each(_local_6 in points1)
         {
            coord.push(_local_6.x);
            coord.push(_local_6.y);
         }
         for each(_local_4 in points2)
         {
            coord.push(_local_4.x);
            coord.push(_local_4.y);
         }
         _local_3 = points1.length + points2.length;
         commands.push(1);
         _local_5 = 1;
         while(_local_5 < _local_3)
         {
            commands.push(2);
            _local_5++;
         }
         var _local_2:Sprite = new Sprite();
         _local_2.graphics.beginFill(16711680);
         _local_2.graphics.drawPath(commands,coord);
         _local_2.graphics.endFill();
         return _local_2;
      }
      
      private static function getPointAroundImage(_arg_1:BitmapData) : void
      {
         var _local_8:int = 0;
         var _local_5:int = 0;
         var _local_6:int = 0;
         var _local_4:uint = 0;
         var _local_3:uint = 0;
         points1 = new Vector.<Point>();
         points2 = new Vector.<Point>();
         commands = new Vector.<int>();
         coord = new Vector.<Number>();
         var _local_2:int = _arg_1.width;
         var _local_7:int = _arg_1.height;
         _local_8 = 1;
         while(_local_8 <= _local_2)
         {
            _local_5 = 1;
            while(_local_5 <= _local_7)
            {
               _local_4 = uint(_arg_1.getPixel32(_local_8,_local_5) >> 24 & 0xFF);
               if(_local_4 != 0)
               {
                  points1.push(new Point(_local_8,_local_5));
                  break;
               }
               _local_5++;
            }
            _local_6 = _local_7;
            while(_local_6 > 0)
            {
               _local_3 = uint(_arg_1.getPixel32(_local_8,_local_6) >> 24 & 0xFF);
               if(_local_3 != 0 && _local_8 != _local_6)
               {
                  points2.unshift(new Point(_local_8,_local_6));
                  break;
               }
               _local_6--;
            }
            _local_8++;
         }
      }
   }
}

