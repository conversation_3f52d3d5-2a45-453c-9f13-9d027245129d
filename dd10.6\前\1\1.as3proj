<?xml version="1.0" encoding="utf-8"?>
<project version="2">
  <!-- Generated with JPEXS Free Flash Decompiler v.24.0.1 -->
  <!-- Output SWF options -->
  <output>
    <movie outputType="Application" />
    <movie input="" />
    <movie path="1.swf" />
    <movie fps="24" />
    <movie width="500" />
    <movie height="375" />
    <movie version="17" />
    <movie minorVersion="0" />
    <movie platform="Flash Player" />
    <movie background="#ffffff" />
  </output>
  <!-- Other classes to be compiled into your SWF -->
  <classpaths>
    <class path="src" />
  </classpaths>
  <!-- Build options -->
  <build>
    <option accessible="False" />
    <option allowSourcePathOverlap="False" />
    <option benchmark="False" />
    <option es="False" />
    <option loadConfig="" />
    <option optimize="True" />
    <option showActionScriptWarnings="True" />
    <option showBindingWarnings="True" />
    <option showDeprecationWarnings="True" />
    <option showUnusedTypeSelectorWarnings="True" />
    <option strict="True" />
    <option useNetwork="True" />
    <option useResourceBundleMetadata="True" />
    <option warnings="True" />
    <option verboseStackTraces="False" />
    <option additional="-swf-version=28" />
    <option customSDK="" />
  </build>
  <!-- Class files to compile (other referenced classes will automatically be included) -->
  <compileTargets>
    <!-- example: <compile path="classes\Main.as" /> -->
  </compileTargets>
  <!-- Paths to exclude from the Project Explorer tree -->
  <hiddenPaths>
    <!-- example: <hidden path="..." /> -->
  </hiddenPaths>
  <!-- Executed before build -->
  <preBuildCommand />
  <!-- Executed after build -->
  <postBuildCommand alwaysRun="False" />
  <!-- Other project options -->
  <options>
    <option showHiddenPaths="False" />
    <option testMovie="Default" />
  </options>
</project>