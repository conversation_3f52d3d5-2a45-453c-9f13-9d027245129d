package bank.data
{
   import road7th.data.DictionaryData;
   
   public class BankInvestmentModel
   {
      
      private var _data:DictionaryData;
      
      private var _list:Array;
      
      public function BankInvestmentModel()
      {
         super();
         this._list = [];
      }
      
      public function get data() : DictionaryData
      {
         return this._data;
      }
      
      public function set data(_arg_1:DictionaryData) : void
      {
         this._data = _arg_1;
      }
      
      public function get list() : Array
      {
         return this._list;
      }
      
      public function set list(_arg_1:Array) : void
      {
         this._list = _arg_1;
      }
   }
}

