package bagAndInfo.cell
{
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.view.PropItemView;
   import ddt.view.character.ILayer;
   import ddt.view.character.ILayerFactory;
   import ddt.view.character.LayerFactory;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import flash.events.Event;
   import times.utils.timerManager.TimerJuggler;
   import times.utils.timerManager.TimerManager;
   
   public class CellContentCreator extends Sprite implements Disposeable
   {
      
      protected var _factory:ILayerFactory;
      
      protected var _loader:ILayer;
      
      protected var _callBack:Function;
      
      protected var _timer:TimerJuggler;
      
      protected var _info:ItemTemplateInfo;
      
      private var _w:Number;
      
      private var _h:Number;
      
      public function CellContentCreator()
      {
         super();
         this._factory = LayerFactory.instance;
      }
      
      public function set info(_arg_1:ItemTemplateInfo) : void
      {
         this._info = _arg_1;
      }
      
      public function loadSync(_arg_1:Function) : void
      {
         var _local_2:* = null;
         this._callBack = _arg_1;
         if(this._info.CategoryID == 10)
         {
            this._timer = TimerManager.getInstance().addTimerJuggler(100,1);
            this._timer.addEventListener("timerComplete",this.__timerComplete);
            this._timer.start();
         }
         else
         {
            if(this._info is InventoryItemInfo)
            {
               _local_2 = EquipType.isEditable(this._info) && InventoryItemInfo(this._info).Color != null ? InventoryItemInfo(this._info).Color : "";
               this._loader = this._factory.createLayer(this._info,this._info.NeedSex == 1,_local_2,"icon");
            }
            else
            {
               this._loader = this._factory.createLayer(this._info,this._info.NeedSex == 1,"","icon");
            }
            this._loader.load(this.loadComplete);
         }
      }
      
      public function clearLoader() : void
      {
         if(this._loader != null)
         {
            this._loader.dispose();
            this._loader = null;
         }
      }
      
      protected function __timerComplete(_arg_1:Event) : void
      {
         if(Boolean(this._timer))
         {
            this._timer.removeEventListener("timerComplete",this.__timerComplete);
            this._timer.stop();
            TimerManager.getInstance().removeTimerJuggler(this._timer.id);
            this._timer = null;
         }
         addChild(PropItemView.createView(this._info.Pic) as Bitmap);
         this._callBack();
      }
      
      protected function loadComplete(_arg_1:ILayer) : void
      {
         addChild(_arg_1.getContent());
         this._callBack();
      }
      
      public function setColor(_arg_1:*) : Boolean
      {
         if(this._loader != null)
         {
            return this._loader.setColor(_arg_1);
         }
         return false;
      }
      
      public function get editLayer() : int
      {
         if(this._loader == null)
         {
            return 1;
         }
         return this._loader.currentEdit;
      }
      
      override public function set width(_arg_1:Number) : void
      {
         super.width = _arg_1;
         this._w = _arg_1;
      }
      
      override public function set height(_arg_1:Number) : void
      {
         super.height = _arg_1;
         this._h = _arg_1;
      }
      
      public function dispose() : void
      {
         this._factory = null;
         if(this._loader != null)
         {
            this._loader.dispose();
         }
         this._loader = null;
         if(this._timer != null)
         {
            this._timer.removeEventListener("timerComplete",this.__timerComplete);
            this._timer.stop();
            TimerManager.getInstance().removeTimerJuggler(this._timer.id);
            this._timer = null;
         }
         this._callBack = null;
         ObjectUtils.disposeAllChildren(this);
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

