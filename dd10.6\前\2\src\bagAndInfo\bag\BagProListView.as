package bagAndInfo.bag
{
   import bagAndInfo.BagAndInfoManager;
   import bagAndInfo.cell.BagCell;
   import bagAndInfo.cell.BaseCell;
   import bagAndInfo.cell.CellFactory;
   import bagAndInfo.cell.DragEffect;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.DoubleClickManager;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.events.CellEvent;
   import ddt.manager.DragManager;
   import ddt.manager.PlayerManager;
   import flash.events.MouseEvent;
   import flash.utils.Dictionary;
   
   public class BagProListView extends BagListView
   {
      
      public var _startIndex:int;
      
      public var _stopIndex:int;
      
      protected var _pageUpBtn:BagPageButton;
      
      protected var _pageDownBtn:BagPageButton;
      
      public function BagProListView(_arg_1:int, _arg_2:int = 31, _arg_3:int = 80, _arg_4:int = 7, _arg_5:int = 1)
      {
         this._startIndex = _arg_2;
         this._stopIndex = _arg_3;
         _page = _arg_5;
         this.initPageBtn();
         BagAndInfoManager.Instance.addEventListener("bagpage",this.__pageChange);
         super(_arg_1,_arg_4);
      }
      
      private function initPageBtn() : void
      {
         this._pageUpBtn = ComponentFactory.Instance.creatComponentByStylename("core.bag.upBtn");
         this._pageUpBtn.addEventListener("click",this.__onPageChange);
         this._pageDownBtn = ComponentFactory.Instance.creatComponentByStylename("core.bag.downBtn");
         this._pageDownBtn.addEventListener("click",this.__onPageChange);
      }
      
      override protected function createCells() : void
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         _cells = new Dictionary();
         _cellMouseOverBg = ComponentFactory.Instance.creatBitmap("bagAndInfo.cell.bagCellOverBgAsset");
         _local_2 = this._startIndex;
         while(_local_2 < this._stopIndex)
         {
            _local_1 = CellFactory.instance.createBagCell(_local_2) as BagCell;
            _local_1.mouseOverEffBoolean = false;
            addChild(_local_1);
            _local_1.addEventListener("interactive_click",__clickHandler);
            _local_1.addEventListener("mouseOver",_cellOverEff);
            _local_1.addEventListener("mouseOut",_cellOutEff);
            _local_1.addEventListener("interactive_double_click",__doubleClickHandler);
            DoubleClickManager.Instance.enableDoubleClick(_local_1);
            _local_1.addEventListener("lockChanged",__cellChanged);
            _local_1.bagType = _bagType;
            _local_1.addEventListener("lockChanged",__cellChanged);
            _cells[_local_1.place] = _local_1;
            _cellVec.push(_local_1);
            _local_2++;
         }
         if(_local_2 == this._stopIndex)
         {
            if(_page == 1)
            {
               if(Boolean(this._pageDownBtn) && Boolean(this._pageDownBtn.parent))
               {
                  this._pageDownBtn.parent.removeChild(this._pageDownBtn);
               }
               addChild(this._pageUpBtn);
            }
            else if(_page == 2)
            {
               if(Boolean(this._pageUpBtn) && Boolean(this._pageUpBtn.parent))
               {
                  this._pageUpBtn.parent.removeChild(this._pageUpBtn);
               }
               addChild(this._pageDownBtn);
            }
         }
      }
      
      protected function __pageChange(_arg_1:CellEvent) : void
      {
         var _local_2:DragEffect = _arg_1.data as DragEffect;
         if(!(_local_2.data is InventoryItemInfo) || _local_2.data.BagType != 1)
         {
            return;
         }
         DragManager.startDrag(_local_2.source.getSource(),_local_2.data,(_local_2.source as BaseCell).createDragImg(),stage.mouseX,stage.mouseY,"move",false);
         this.__onPageChange(null);
      }
      
      private function __onPageChange(_arg_1:MouseEvent) : void
      {
         var _local_2:BagCell = null;
         var _local_3:BagCell = null;
         for each(_local_2 in _cells)
         {
            _local_2.removeEventListener("interactive_click",__clickHandler);
            _local_2.removeEventListener("interactive_double_click",__doubleClickHandler);
            DoubleClickManager.Instance.disableDoubleClick(_local_2);
            _local_2.removeEventListener("lockChanged",__cellChanged);
         }
         for each(_local_3 in _cells)
         {
            _local_3.removeEventListener("interactive_click",__clickHandler);
            _local_3.removeEventListener("lockChanged",__cellChanged);
            _local_3.removeEventListener("mouseOver",_cellOverEff);
            _local_3.removeEventListener("mouseOut",_cellOutEff);
            _local_3.removeEventListener("interactive_double_click",__doubleClickHandler);
            DoubleClickManager.Instance.disableDoubleClick(_local_3);
            _local_3.dispose();
         }
         _cells = null;
         _cellVec = null;
         if(_page == 1)
         {
            _page = 2;
            this._startIndex = 48;
            this._stopIndex = 96;
            _cellVec = [];
            this.createCells();
            setData(PlayerManager.Instance.Self.PropBag);
         }
         else
         {
            _page = 1;
            this._startIndex = 0;
            this._stopIndex = 48;
            _cellVec = [];
            this.createCells();
            setData(PlayerManager.Instance.Self.PropBag);
         }
      }
      
      override public function setCellInfo(_arg_1:int, _arg_2:InventoryItemInfo) : void
      {
         if(_arg_1 >= this._startIndex && _arg_1 < this._stopIndex)
         {
            if(_arg_2 == null)
            {
               _cells[String(_arg_1)].info = null;
               return;
            }
            if(_arg_2.Count == 0)
            {
               _cells[String(_arg_1)].info = null;
            }
            else
            {
               _cells[String(_arg_1)].info = _arg_2;
            }
         }
      }
      
      override public function dispose() : void
      {
         BagAndInfoManager.Instance.removeEventListener("bagpage",this.__pageChange);
         if(Boolean(this._pageUpBtn))
         {
            this._pageUpBtn.removeEventListener("click",this.__onPageChange);
            ObjectUtils.disposeObject(this._pageUpBtn);
            this._pageUpBtn = null;
         }
         if(Boolean(this._pageDownBtn))
         {
            this._pageDownBtn.removeEventListener("click",this.__onPageChange);
            ObjectUtils.disposeObject(this._pageDownBtn);
            this._pageDownBtn = null;
         }
         super.dispose();
         if(Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
      }
   }
}

