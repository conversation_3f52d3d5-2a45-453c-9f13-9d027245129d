package bagAndInfo.bag
{
   import bagAndInfo.BagAndInfoManager;
   import bagAndInfo.cell.BagCell;
   import bagAndInfo.cell.BaseCell;
   import bagAndInfo.cell.CellFactory;
   import bagAndInfo.cell.DragEffect;
   import com.pickgliss.events.InteractiveEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.DoubleClickManager;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.events.CellEvent;
   import ddt.manager.DragManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SoundManager;
   import flash.events.MouseEvent;
   import flash.utils.Dictionary;
   
   public class BagEquipListView extends BagListView
   {
      
      public var _startIndex:int;
      
      public var _stopIndex:int;
      
      protected var _pageUpBtn:BagPageButton;
      
      protected var _pageDownBtn:BagPageButton;
      
      public function BagEquipListView(_arg_1:int, _arg_2:int = 31, _arg_3:int = 80, _arg_4:int = 7, _arg_5:int = 1)
      {
         this._startIndex = _arg_2;
         this._stopIndex = _arg_3;
         _page = _arg_5;
         this.initPageBtn();
         BagAndInfoManager.Instance.addEventListener("bagpage",this.__pageChange);
         super(_arg_1,_arg_4);
      }
      
      private function initPageBtn() : void
      {
         this._pageUpBtn = ComponentFactory.Instance.creatComponentByStylename("core.bag.upBtn");
         this._pageUpBtn.addEventListener("click",this.__onPageChange);
         this._pageDownBtn = ComponentFactory.Instance.creatComponentByStylename("core.bag.downBtn");
         this._pageDownBtn.addEventListener("click",this.__onPageChange);
      }
      
      override protected function createCells() : void
      {
         var _local_1:int = 0;
         _cells = new Dictionary();
         _cellMouseOverBg = ComponentFactory.Instance.creatBitmap("bagAndInfo.cell.bagCellOverBgAsset");
         if(_page == 2)
         {
            this.creatCell(79);
         }
         _local_1 = this._startIndex;
         while(_local_1 < this._stopIndex)
         {
            this.creatCell(_local_1);
            _local_1++;
         }
         if(_local_1 == this._stopIndex)
         {
            if(_page == 1)
            {
               if(Boolean(this._pageDownBtn) && Boolean(this._pageDownBtn.parent))
               {
                  this._pageDownBtn.parent.removeChild(this._pageDownBtn);
               }
               addChild(this._pageUpBtn);
            }
            else if(_page == 2)
            {
               if(Boolean(this._pageUpBtn) && Boolean(this._pageUpBtn.parent))
               {
                  this._pageUpBtn.parent.removeChild(this._pageUpBtn);
               }
               addChild(this._pageDownBtn);
            }
         }
      }
      
      private function creatCell(_arg_1:int) : void
      {
         var _local_2:BagCell = CellFactory.instance.createBagCell(_arg_1) as BagCell;
         _local_2.mouseOverEffBoolean = false;
         addChild(_local_2);
         _local_2.addEventListener("interactive_click",this.__clickHandler);
         _local_2.addEventListener("interactive_double_click",this.__doubleClickHandler);
         DoubleClickManager.Instance.enableDoubleClick(_local_2);
         _local_2.bagType = _bagType;
         _local_2.addEventListener("lockChanged",__cellChanged);
         _cells[_local_2.place] = _local_2;
         _cellVec.push(_local_2);
      }
      
      protected function __pageChange(_arg_1:CellEvent) : void
      {
         var _local_2:DragEffect = _arg_1.data as DragEffect;
         if(!(_local_2.data is InventoryItemInfo) || _local_2.data.BagType != 0)
         {
            return;
         }
         DragManager.startDrag(_local_2.source.getSource(),_local_2.data,(_local_2.source as BaseCell).createDragImg(),stage.mouseX,stage.mouseY,"move",false);
         this.__onPageChange(null);
      }
      
      private function __onPageChange(_arg_1:MouseEvent) : void
      {
         var _local_2:BagCell = null;
         var _local_3:BagCell = null;
         for each(_local_2 in _cells)
         {
            _local_2.removeEventListener("interactive_click",this.__clickHandler);
            _local_2.removeEventListener("interactive_double_click",this.__doubleClickHandler);
            DoubleClickManager.Instance.disableDoubleClick(_local_2);
            _local_2.removeEventListener("lockChanged",__cellChanged);
         }
         for each(_local_3 in _cells)
         {
            _local_3.removeEventListener("interactive_click",this.__clickHandler);
            _local_3.removeEventListener("lockChanged",__cellChanged);
            _local_3.removeEventListener("mouseOver",_cellOverEff);
            _local_3.removeEventListener("mouseOut",_cellOutEff);
            _local_3.removeEventListener("interactive_double_click",this.__doubleClickHandler);
            DoubleClickManager.Instance.disableDoubleClick(_local_3);
            _local_3.dispose();
         }
         _cells = null;
         _cellVec = null;
         if(_page == 1)
         {
            _page = 2;
            this._startIndex = 80;
            this._stopIndex = 127;
            _cellVec = [];
            this.createCells();
            setData(PlayerManager.Instance.Self.Bag);
         }
         else
         {
            _page = 1;
            this._startIndex = 31;
            this._stopIndex = 79;
            _cellVec = [];
            this.createCells();
            setData(PlayerManager.Instance.Self.Bag);
         }
      }
      
      override protected function __doubleClickHandler(_arg_1:InteractiveEvent) : void
      {
         if((_arg_1.currentTarget as BagCell).info != null)
         {
            SoundManager.instance.play("008");
            dispatchEvent(new CellEvent("doubleclick",_arg_1.currentTarget));
         }
      }
      
      override protected function __clickHandler(_arg_1:InteractiveEvent) : void
      {
         if(Boolean(_arg_1.currentTarget))
         {
            dispatchEvent(new CellEvent("itemclick",_arg_1.currentTarget,false,false,_arg_1.ctrlKey));
         }
      }
      
      protected function __cellClick(_arg_1:MouseEvent) : void
      {
      }
      
      override public function setCellInfo(_arg_1:int, _arg_2:InventoryItemInfo) : void
      {
         if(_page == 2 && _arg_1 == 79)
         {
            if(_arg_2 == null)
            {
               _cells[String(_arg_1)].info = null;
               return;
            }
            if(_arg_2.Count == 0)
            {
               _cells[String(_arg_1)].info = null;
            }
            else
            {
               _cells[String(_arg_1)].info = _arg_2;
            }
         }
         else if(_arg_1 >= this._startIndex && _arg_1 < this._stopIndex)
         {
            if(_arg_2 == null)
            {
               _cells[String(_arg_1)].info = null;
               return;
            }
            if(_arg_2.Count == 0)
            {
               _cells[String(_arg_1)].info = null;
            }
            else
            {
               _cells[String(_arg_1)].info = _arg_2;
            }
         }
      }
      
      override public function dispose() : void
      {
         var _local_1:BagCell = null;
         BagAndInfoManager.Instance.removeEventListener("bagpage",this.__pageChange);
         if(Boolean(this._pageUpBtn))
         {
            this._pageUpBtn.removeEventListener("click",this.__onPageChange);
            ObjectUtils.disposeObject(this._pageUpBtn);
            this._pageUpBtn = null;
         }
         if(Boolean(this._pageDownBtn))
         {
            this._pageDownBtn.removeEventListener("click",this.__onPageChange);
            ObjectUtils.disposeObject(this._pageDownBtn);
            this._pageDownBtn = null;
         }
         for each(_local_1 in _cells)
         {
            _local_1.removeEventListener("interactive_click",this.__clickHandler);
            _local_1.removeEventListener("interactive_double_click",this.__doubleClickHandler);
            DoubleClickManager.Instance.disableDoubleClick(_local_1);
            _local_1.removeEventListener("lockChanged",__cellChanged);
         }
         _cellMouseOverBg = null;
         super.dispose();
         if(Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
      }
   }
}

