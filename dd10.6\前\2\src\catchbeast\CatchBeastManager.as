package catchbeast
{
   import com.pickgliss.ui.image.MovieImage;
   import ddt.CoreManager;
   import ddt.events.CrazyTankSocketEvent;
   import ddt.manager.SocketManager;
   import flash.events.Event;
   import hall.HallStateView;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   
   public class CatchBeastManager extends CoreManager
   {
      
      private static var _instance:CatchBeastManager;
      
      public static const CATCHBEAST_OPENVIEW:String = "catchBeastOpenView";
      
      public var RoomType:int = 0;
      
      private var _isBegin:Boolean;
      
      private var _hallView:HallStateView;
      
      private var _catchBeastIcon:MovieImage;
      
      public function CatchBeastManager()
      {
         super();
      }
      
      public static function get instance() : CatchBeastManager
      {
         if(!_instance)
         {
            _instance = new CatchBeastManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener("catchbeast_begin",this.__addCatchBeastBtn);
      }
      
      protected function __addCatchBeastBtn(_arg_1:CrazyTankSocketEvent) : void
      {
         var _local_3:CrazyTankSocketEvent = null;
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_2:int = _arg_1._cmd;
         switch(_local_2)
         {
            case 32:
               this.openOrclose(_local_4);
               break;
            case 33:
               _local_3 = new CrazyTankSocketEvent("catchbeast_viewinfo",_local_4);
               break;
            case 36:
               _local_3 = new CrazyTankSocketEvent("catchbeast_getaward",_local_4);
               break;
            case 35:
               _local_3 = new CrazyTankSocketEvent("catchbeast_buybuff",_local_4);
               break;
            case 34:
               _local_3 = new CrazyTankSocketEvent("catchbeast_challenge",_local_4);
         }
         if(Boolean(_local_3))
         {
            dispatchEvent(_local_3);
         }
      }
      
      private function openOrclose(_arg_1:PackageIn) : void
      {
         this._isBegin = _arg_1.readBoolean();
         this.updateCatchBeastBtn();
      }
      
      public function updateCatchBeastBtn() : void
      {
         HallIconManager.instance.updateSwitchHandler("catchBeast",this._isBegin);
      }
      
      override protected function start() : void
      {
         dispatchEvent(new Event("catchBeastOpenView"));
      }
      
      public function get catchBeastIcon() : MovieImage
      {
         return this._catchBeastIcon;
      }
      
      public function get isBegin() : Boolean
      {
         return this._isBegin;
      }
      
      public function set isBegin(_arg_1:Boolean) : void
      {
         this._isBegin = _arg_1;
      }
   }
}

