package cloudBuyLottery.view
{
   import bagAndInfo.cell.CellFactory;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.manager.LanguageMgr;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import flash.events.Event;
   import shop.view.ShopItemCell;
   
   public class WinningLogListItem extends Sprite implements Disposeable
   {
      
      private var _itemCell:ShopItemCell;
      
      private var _shopItemInfo:WinningLogItemInfo;
      
      private var _itemID:int;
      
      private var _bg:Bitmap;
      
      private var _cellImg:Bitmap;
      
      private var _nameTxt:FilterFrameText;
      
      private var _txt:FilterFrameText;
      
      public function WinningLogListItem()
      {
         super();
      }
      
      public function initView(_arg_1:String, _arg_2:int = 0) : void
      {
         if(++_arg_2 % 2 == 0)
         {
            this._bg = ComponentFactory.Instance.creatBitmap("asset.IndividualLottery.cellBg");
            addChild(this._bg);
         }
         this._nameTxt = ComponentFactory.Instance.creatComponentByStylename("WinningLogListItem.nameTxt");
         this._nameTxt.text = _arg_1;
         this._txt = ComponentFactory.Instance.creatComponentByStylename("WinningLogListItem.txt");
         this._txt.text = LanguageMgr.GetTranslation("WinningLogListItem.txt.LG");
         this._cellImg = ComponentFactory.Instance.creatBitmap("asset.IndividualLottery.goodsCell");
         this._itemCell = this.creatItemCell();
         this._itemCell.buttonMode = true;
         this._itemCell.cellSize = 46;
         PositionUtils.setPos(this._itemCell,"WinningLogListItem.cellPos");
         addChild(this._cellImg);
         addChild(this._itemCell);
         addChild(this._nameTxt);
         addChild(this._txt);
      }
      
      protected function creatItemCell() : ShopItemCell
      {
         var _local_1:Sprite = new Sprite();
         _local_1.graphics.beginFill(16777215,0);
         _local_1.graphics.drawRect(0,0,46,46);
         _local_1.graphics.endFill();
         return CellFactory.instance.createShopItemCell(_local_1,null,true,true) as ShopItemCell;
      }
      
      public function set shopItemInfo(_arg_1:WinningLogItemInfo) : void
      {
         var _local_2:* = null;
         if(Boolean(this._shopItemInfo))
         {
            this._shopItemInfo.removeEventListener("change",this.__updateShopItem);
         }
         this._shopItemInfo = _arg_1;
         if(Boolean(this._shopItemInfo))
         {
            _local_2 = new InventoryItemInfo();
            ObjectUtils.copyProperties(_local_2,this._shopItemInfo.TemplateInfo);
            _local_2.ValidDate = this._shopItemInfo.validate;
            _local_2.StrengthenLevel = this._shopItemInfo.property[0];
            _local_2.AttackCompose = this._shopItemInfo.property[1];
            _local_2.DefendCompose = this._shopItemInfo.property[2];
            _local_2.LuckCompose = this._shopItemInfo.property[3];
            _local_2.AgilityCompose = this._shopItemInfo.property[4];
            _local_2.IsBinds = true;
            this._itemID = this._shopItemInfo.TemplateID;
            this._itemCell.info = _local_2;
            this._itemCell.buttonMode = true;
            this._shopItemInfo.addEventListener("change",this.__updateShopItem);
         }
         else
         {
            this._itemCell.info = null;
            this._itemCell.buttonMode = false;
         }
      }
      
      private function __updateShopItem(_arg_1:Event) : void
      {
         this._itemCell.info = this._shopItemInfo.TemplateInfo;
      }
      
      public function get itemID() : int
      {
         return this._itemID;
      }
      
      public function set itemID(_arg_1:int) : void
      {
         this._itemID = _arg_1;
      }
      
      public function dispose() : void
      {
         ObjectUtils.disposeObject(this._itemCell);
         this._itemCell = null;
         this._shopItemInfo = null;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

