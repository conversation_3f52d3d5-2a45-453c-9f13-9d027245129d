package cityBattle.view
{
   import cityBattle.CityBattleManager;
   import cityBattle.event.CityBattleEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.BossBoxManager;
   import ddt.manager.ItemManager;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class WelfareView extends Sprite implements Disposeable
   {
      
      private var _bg:Bitmap;
      
      private var _myScoreTxt:FilterFrameText;
      
      private var _box:Sprite;
      
      private var _winBtn:MovieClip;
      
      private var _prize:WinnerPrizeView;
      
      public function WelfareView()
      {
         super();
         this.init();
      }
      
      private function init() : void
      {
         var _local_7:int = 0;
         var _local_5:int = 0;
         var _local_3:int = 0;
         var _local_1:int = 0;
         var _local_6:* = null;
         var _local_4:* = null;
         var _local_2:* = null;
         this._bg = ComponentFactory.Instance.creatBitmap("asset.cityBattle.bg3");
         addChild(this._bg);
         this._myScoreTxt = ComponentFactory.Instance.creatComponentByStylename("welfare.myscore.txt");
         addChild(this._myScoreTxt);
         this._myScoreTxt.text = String(CityBattleManager.instance.myScore);
         this._winBtn = ClassUtils.CreatInstance("asset.cityBattle.box");
         if(CityBattleManager.instance.now < 8 && CityBattleManager.instance.now > 0)
         {
            addChild(this._winBtn);
         }
         PositionUtils.setPos(this._winBtn,"welfare.boxPos");
         this._winBtn.addEventListener("click",this.clickHandler);
         this._winBtn.addEventListener("mouseOver",this.overHandler);
         this._winBtn.addEventListener("mouseOut",this.outHandler);
         this._box = new Sprite();
         addChild(this._box);
         for(_local_7 = 0; _local_7 < CityBattleManager.instance.welfareList.length; _local_7++)
         {
            _local_6 = CityBattleManager.instance.welfareList[_local_7];
            if(_local_6.Quality == 2)
            {
               if(CityBattleManager.instance.now < 8 && CityBattleManager.instance.now > 0)
               {
                  _local_5 = CityBattleManager.instance.now;
               }
               else
               {
                  _local_5 = 7;
               }
               if(_local_6.Probability == _local_5)
               {
                  _local_4 = new WelfareCell();
                  _local_4.info = _local_6;
                  _local_4.x = 94 * _local_3 + 319;
                  _local_4.y = 180;
                  this._box.addChild(_local_4);
                  _local_3++;
                  continue;
               }
            }
            if(_local_6.Quality == 3)
            {
               _local_2 = new WelfareCell();
               _local_2.info = _local_6;
               if(_local_1 <= 2)
               {
                  _local_2.x = 94 * _local_1 + 368;
                  _local_2.y = 365;
               }
               else
               {
                  _local_2.x = 94 * (_local_1 - 3) + 318;
                  _local_2.y = 455;
               }
               this._box.addChild(_local_2);
               _local_1++;
            }
         }
         CityBattleManager.instance.addEventListener("scoreChange",this._scoreChange);
      }
      
      private function _scoreChange(_arg_1:CityBattleEvent) : void
      {
         this._myScoreTxt.text = String(CityBattleManager.instance.myScore);
      }
      
      private function overHandler(_arg_1:MouseEvent) : void
      {
         this._winBtn.gotoAndStop(2);
      }
      
      private function outHandler(_arg_1:MouseEvent) : void
      {
         this._winBtn.gotoAndStop(1);
      }
      
      private function clickHandler(_arg_1:MouseEvent) : void
      {
         var _local_7:int = 0;
         var _local_4:int = 0;
         var _local_5:* = null;
         var _local_2:* = null;
         var _local_6:* = null;
         this._winBtn.gotoAndStop(1);
         var _local_3:Array = [];
         _local_7 = 0;
         while(_local_7 < CityBattleManager.instance.welfareList.length)
         {
            _local_5 = CityBattleManager.instance.welfareList[_local_7];
            if(_local_5.Quality == 5 && _local_5.Probability == CityBattleManager.instance.now)
            {
               _local_4 = 0;
               while(_local_4 < BossBoxManager.instance.cityBattleTempInfoList[_local_5.TemplateID].length)
               {
                  _local_2 = BossBoxManager.instance.cityBattleTempInfoList[_local_5.TemplateID][_local_4];
                  _local_6 = ItemManager.fillByID(_local_2.TemplateId);
                  _local_6.IsBinds = _local_2.IsBind;
                  _local_6.LuckCompose = _local_2.LuckCompose;
                  _local_6.DefendCompose = _local_2.DefendCompose;
                  _local_6.AttackCompose = _local_2.AttackCompose;
                  _local_6.AgilityCompose = _local_2.AgilityCompose;
                  _local_6.StrengthenLevel = _local_2.StrengthenLevel;
                  _local_6.ValidDate = _local_2.ItemValid;
                  _local_6.Count = _local_2.ItemCount;
                  _local_3.push(_local_6);
                  _local_4++;
               }
            }
            _local_7++;
         }
         this._prize = ComponentFactory.Instance.creat("welfare.winnerPrizeView");
         this._prize.goodsList = _local_3;
         LayerManager.Instance.addToLayer(this._prize,3,true,1);
      }
      
      public function dispose() : void
      {
         CityBattleManager.instance.removeEventListener("scoreChange",this._scoreChange);
         this._winBtn.removeEventListener("click",this.clickHandler);
         ObjectUtils.disposeObject(this._bg);
         this._bg = null;
         ObjectUtils.disposeObject(this._myScoreTxt);
         this._myScoreTxt = null;
         ObjectUtils.disposeObject(this._box);
         this._box = null;
         ObjectUtils.disposeObject(this._winBtn);
         this._winBtn = null;
      }
   }
}

