package com.pickgliss.utils
{
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class TextUtils
   {
      
      public function TextUtils()
      {
         super();
      }
      
      public static function textBold(_arg_1:TextField) : void
      {
         var _local_2:TextFormat = _arg_1.defaultTextFormat;
         if(_local_2 != null)
         {
            _local_2.bold = true;
            _arg_1.defaultTextFormat = _local_2;
            _arg_1.setTextFormat(_local_2);
         }
      }
   }
}

