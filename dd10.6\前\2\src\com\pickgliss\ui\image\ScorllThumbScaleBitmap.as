package com.pickgliss.ui.image
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import org.bytearray.display.ScaleBitmap;
   
   public class ScorllThumbScaleBitmap extends Image
   {
      
      protected var _resource:BitmapData;
      
      protected var _middleBmp:Bitmap;
      
      protected var _middleRect:Rectangle = null;
      
      protected var _boderRect:Rectangle = null;
      
      protected var _isMiddle:Boolean = true;
      
      public function ScorllThumbScaleBitmap(_arg_1:BitmapData = null, _arg_2:String = null, _arg_3:String = null, _arg_4:Boolean = true)
      {
         super();
         this._isMiddle = _arg_4;
         this.resource = _arg_1;
         this.middleRect = _arg_2;
         this.boderRect = _arg_3;
      }
      
      public function set middleRect(_arg_1:String) : void
      {
         if(_arg_1 == null)
         {
            return;
         }
         var _local_2:Array = ComponentFactory.parasArgs(_arg_1);
         this._middleRect = new Rectangle(_local_2[0],_local_2[1],_local_2[2],_local_2[3]);
         this.updateThumb();
      }
      
      public function set boderRect(_arg_1:String) : void
      {
         if(_arg_1 == null)
         {
            return;
         }
         var _local_2:Array = ComponentFactory.parasArgs(_arg_1);
         this._boderRect = new Rectangle(_local_2[0],_local_2[1],_local_2[2],_local_2[3]);
         this.updateThumb();
      }
      
      override public function set width(_arg_1:Number) : void
      {
         if(_arg_1 != width)
         {
            _width = _arg_1;
            if(Boolean(_display))
            {
               _display.width = _width;
            }
            this.updateMiddlePos();
            onPropertiesChanged("width");
         }
      }
      
      override public function set height(_arg_1:Number) : void
      {
         if(_arg_1 != height)
         {
            _height = _arg_1;
            if(Boolean(_display))
            {
               _display.height = _height;
            }
            this.updateMiddlePos();
            onPropertiesChanged("height");
         }
      }
      
      public function updateMiddlePos() : void
      {
         if(this._isMiddle && Boolean(_display))
         {
            this._middleBmp.x = _display.x + (_display.width - this._middleBmp.width) / 2;
            this._middleBmp.y = _display.y + (_display.height - this._middleBmp.height) / 2;
         }
      }
      
      public function set resource(_arg_1:BitmapData) : void
      {
         if(_arg_1 == this._resource)
         {
            return;
         }
         this._resource = _arg_1;
         onPropertiesChanged("resourceLink");
      }
      
      public function get resource() : BitmapData
      {
         return this._resource;
      }
      
      override protected function resetDisplay() : void
      {
         if(this._resource == null)
         {
            this._resource = ClassUtils.CreatInstance(_resourceLink);
         }
         this.updateThumb();
      }
      
      protected function updateThumb() : void
      {
         var _local_1:* = null;
         if(!this._resource || !this._middleRect || !this._boderRect)
         {
            return;
         }
         ObjectUtils.disposeObject(_display);
         ObjectUtils.disposeObject(this._middleBmp);
         var _local_2:BitmapData = new BitmapData(this._resource.width,this._resource.height - this._middleRect.height,true,0);
         _local_2.copyPixels(this._resource,new Rectangle(0,0,_local_2.width,this._middleRect.y),new Point(0,0));
         _local_2.copyPixels(this._resource,new Rectangle(0,this._middleRect.y + this._middleRect.height,_local_2.width,this._resource.height),new Point(0,this._middleRect.y));
         _display = new ScaleBitmap(_local_2);
         _display.scale9Grid = new Rectangle(this._boderRect.x,this._boderRect.y,_local_2.width - (this._boderRect.x + this._boderRect.width),this._middleRect.y - this._boderRect.y);
         if(this._isMiddle)
         {
            _local_1 = new BitmapData(this._middleRect.width,this._middleRect.height,true,0);
            _local_1.copyPixels(this._resource,this._middleRect,new Point(0,0));
            this._middleBmp = new Bitmap(_local_1);
            addChild(this._middleBmp);
         }
         if(_width > 0)
         {
            _display.width = _width;
         }
         else
         {
            _display.width = this._resource.width;
         }
         if(_height > 0)
         {
            _display.height = _height;
         }
         else
         {
            _display.height = this._resource.height;
         }
         this.updateMiddlePos();
      }
      
      override protected function addChildren() : void
      {
         if(Boolean(_display))
         {
            addChild(_display);
         }
         if(Boolean(this._middleBmp))
         {
            addChild(this._middleBmp);
         }
      }
      
      override public function dispose() : void
      {
         super.dispose();
         ObjectUtils.disposeObject(this._resource);
         this._resource = null;
         ObjectUtils.disposeObject(this._middleBmp);
         this._middleBmp = null;
      }
   }
}

