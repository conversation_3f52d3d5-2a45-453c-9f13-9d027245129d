package bible.data
{
   import AvatarCollection.AvatarCollectionManager;
   import beadSystem.beadSystemManager;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.BagInfo;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.player.SelfInfo;
   import ddt.manager.FineSuitManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.TimeManager;
   import flash.utils.Dictionary;
   import gemstone.GemstoneManager;
   import homeTemple.HomeTempleManager;
   import horse.HorseManager;
   import horse.data.HorsePicCherishVo;
   import horse.data.HorseSkillExpVo;
   import horse.data.HorseSkillGetVo;
   import magicStone.MagicStoneManager;
   import mark.MarkMgr;
   import petsSystem.PetsManager;
   import petsSystem.data.PetInfo;
   import road7th.utils.DateUtils;
   import store.FineEvolutionManager;
   import storefineseal.StoreFineSealManager;
   import storefineseal.data.StoreFineSealInfo;
   import texpSystem.controller.TexpManager;
   import totem.TotemManager;
   
   public class BibleModel
   {
      
      public var tempInfoArr:Array;
      
      public var strongDic:Dictionary;
      
      public var todayTodoDic:Dictionary;
      
      private var rateArr:Array = [2,1,0.5,0];
      
      public var getBackArr:Array;
      
      public var isHide:Boolean = true;
      
      public var cardLevel:int;
      
      public var exploreLevel:int;
      
      public function BibleModel()
      {
         super();
         this.strongDic = new Dictionary();
         this.strongDic[1] = [];
         this.strongDic[2] = [];
         this.strongDic[3] = [];
         this.strongDic[4] = [];
         this.todayTodoDic = new Dictionary();
         this.todayTodoDic[1] = [];
         this.todayTodoDic[2] = [];
         this.todayTodoDic[3] = [];
         this.todayTodoDic[4] = [];
      }
      
      public function initData() : void
      {
         var _local_1:Number = NaN;
         var _local_8:int = 0;
         var _local_6:int = 0;
         var _local_2:StoreFineSealInfo = null;
         var _local_3:* = null;
         var _local_7:* = null;
         var _local_5:* = null;
         this.clear(this.strongDic);
         this.clear(this.todayTodoDic);
         var _local_4:SelfInfo = PlayerManager.Instance.Self;
         for(_local_8 = 0; _local_8 < this.tempInfoArr.length; )
         {
            _local_3 = this.tempInfoArr[_local_8];
            if(_local_3.MainType != 3)
            {
               _local_7 = new BibleTempInfo();
               ObjectUtils.copyProperties(_local_7,_local_3);
               if(_local_7.MainType == 1)
               {
                  if(!this.isLevel(_local_7))
                  {
                     _local_7.Weight = -100;
                     _local_7.isOpenLevel = false;
                     this.strongDic[_local_7.SonsType].push(_local_7);
                  }
                  else
                  {
                     _local_7.isOpenLevel = true;
                     switch(_local_7.ItemType)
                     {
                        case 1:
                           _local_1 = this.getTexpCount();
                           break;
                        case 2:
                           _local_1 = this.cardLevel;
                           break;
                        case 3:
                           _local_6 = TotemManager.instance.getTotemPointLevel(_local_4.totemId);
                           _local_1 = TotemManager.instance.getCurrentLv(_local_6);
                           break;
                        case 4:
                           _local_1 = beadSystemManager.Instance.getBeadTotleLevel();
                           break;
                        case 5:
                           _local_1 = AvatarCollectionManager.instance.getTotleProgress();
                           break;
                        case 6:
                           _local_1 = MagicStoneManager.instance.getEquipTotleLevel();
                           break;
                        case 7:
                           _local_1 = HomeTempleManager.instance.maxLevel;
                           break;
                        case 8:
                           _local_1 = MarkMgr.inst.getCurSchemeTotleLevel();
                           break;
                        case 9:
                           _local_1 = _local_4.guardCoreGrade;
                           break;
                        case 10:
                           _local_1 = this.exploreLevel;
                           break;
                        case 11:
                           _local_1 = _local_4.Grade;
                           break;
                        case 12:
                           _local_1 = this.getTotleStrongLevel(0);
                           break;
                        case 13:
                           _local_1 = this.getTotleStrongLevel(1);
                           break;
                        case 14:
                           _local_1 = this.getMergeProTotle();
                           break;
                        case 15:
                           _local_1 = this.getEquipGoldNum();
                           break;
                        case 16:
                           _local_1 = this.getEquipMagicTotalLevel();
                           break;
                        case 17:
                           _local_1 = FineSuitManager.Instance.getSuitVoByExp(PlayerManager.Instance.Self.fineSuitExp).level;
                           break;
                        case 18:
                           _local_1 = this.getJewelryLevel();
                           break;
                        case 19:
                           _local_1 = this.getEvolutionLevel();
                           break;
                        case 20:
                           _local_1 = this.getQilingLevel();
                           break;
                        case 21:
                           _local_5 = StoreFineSealManager.ins.model.sealInfoDic;
                           _local_1 = 0;
                           for each(_local_2 in _local_5)
                           {
                              if(Boolean(_local_2) && _local_2.isSeal)
                              {
                                 _local_1++;
                              }
                           }
                           break;
                        case 22:
                           _local_1 = this.getGemstoneLevel();
                           break;
                        case 23:
                           _local_1 = this.getPotential();
                           break;
                        case 24:
                           _local_1 = this.getPetSuitCount();
                           break;
                        case 25:
                           _local_1 = this.getPetTalentCount();
                           break;
                        case 26:
                           _local_1 = PetsManager.instance.petsModel.isActivatedCount();
                           break;
                        case 27:
                           _local_1 = this.getPetStarOrLevelCount();
                           break;
                        case 28:
                           _local_1 = this.getPetStarOrLevelCount(1);
                           break;
                        case 29:
                           _local_1 = HorseManager.instance.curLevel;
                           break;
                        case 30:
                           _local_1 = this.getHorseSkillCount();
                           break;
                        case 31:
                           _local_1 = this.getHorseSuitCount();
                           break;
                        case 32:
                           _local_1 = _local_4.horseAmuletHp;
                     }
                     _local_7.level = _local_1;
                     _local_7.upType = this.getUpType(_local_1,_local_7.BuildLevels);
                     _local_7.Weight *= this.rateArr[_local_7.upType];
                     this.strongDic[_local_7.SonsType].push(_local_7);
                  }
               }
               else if(_local_7.MainType == 2)
               {
                  _local_7.isOpenTime = true;
                  _local_7.isOpenLevel = true;
                  if(!this.isLevel(_local_7))
                  {
                     _local_7.Weight = -100;
                     _local_7.isOpenLevel = false;
                  }
                  if(!this.isTime(_local_7))
                  {
                     _local_7.isOpenTime = false;
                     _local_7.Weight = _local_7.Weight > 1 ? 1 : _local_7.Weight;
                  }
                  this.todayTodoDic[_local_7.SonsType].push(_local_7);
               }
            }
            _local_8++;
         }
      }
      
      public function sort() : void
      {
         var _local_1:* = null;
         for each(_local_1 in this.strongDic)
         {
            _local_1.sortOn(["Weight","ItemType"],0x10 | 2);
         }
         for each(_local_1 in this.todayTodoDic)
         {
            _local_1.sortOn(["Weight","ItemType"],0x10 | 2);
         }
      }
      
      public function isLevel(_arg_1:BibleTempInfo) : Boolean
      {
         var _local_3:Boolean = false;
         var _local_2:int = PlayerManager.Instance.Self.Grade;
         if(_local_2 >= _arg_1.OpenLevel && _local_2 <= _arg_1.StopLevel)
         {
            _local_3 = true;
         }
         return _local_3;
      }
      
      private function isTime(_arg_1:BibleTempInfo) : Boolean
      {
         var _local_10:Boolean = false;
         var _local_5:Boolean = false;
         var _local_12:int = 0;
         var _local_2:int = 0;
         var _local_9:int = 0;
         var _local_13:Boolean = false;
         var _local_4:int = 0;
         var _local_3:* = null;
         var _local_6:* = null;
         var _local_11:* = null;
         var _local_7:* = null;
         var _local_8:* = null;
         if(_arg_1.BuildLevels == "-1" || _arg_1.BuildLevels == null)
         {
            _local_5 = true;
            _local_10 = true;
         }
         else
         {
            _local_3 = _arg_1.BuildLevels.split("|");
            _local_6 = _local_3[0];
            _local_11 = TimeManager.Instance.Now();
            _local_12 = int(_local_11.day);
            if(_local_3[1] == "-1")
            {
               _local_5 = true;
            }
            else
            {
               _local_7 = _local_3[1].split(",");
               _local_2 = int(_local_7.indexOf(String(_local_12)));
               _local_5 = _local_2 != -1;
            }
            _arg_1.isToday = _local_5;
            if(_local_6 == "-1")
            {
               _arg_1.activeTime = "-1";
               return _local_5;
            }
            _local_8 = _local_6.split(",");
            if(_local_8.length >= 2)
            {
               trace(_arg_1.ItemName + ":" + _local_8);
            }
            _local_9 = 0;
            while(_local_9 < _local_8.length)
            {
               _local_13 = false;
               _local_4 = DateUtils.checkTimeRangeState(_local_8[_local_9],_local_11);
               _arg_1.state = _local_4;
               if(_local_4 <= 1)
               {
                  _arg_1.activeTime = _local_8[_local_9];
                  _local_10 = _local_4 == 1;
                  break;
               }
               if(_local_9 == _local_8.length - 1)
               {
                  _arg_1.activeTime = _local_8[_local_9];
               }
               _local_9++;
            }
         }
         return _local_5 && _local_10;
      }
      
      private function getUpType(_arg_1:Number, _arg_2:String) : int
      {
         var _local_5:int = 0;
         var _local_4:int = 3;
         if(_arg_2 == null)
         {
            _arg_2 = "5,10,15";
         }
         var _local_3:Array = _arg_2.split(",");
         _local_5 = 0;
         while(_local_5 < _local_3.length)
         {
            if(_arg_1 < _local_3[_local_5])
            {
               _local_4 = _local_5;
               break;
            }
            _local_5++;
         }
         return _local_4;
      }
      
      public function clear(_arg_1:Dictionary) : void
      {
         var _local_2:* = null;
         for each(_local_2 in _arg_1)
         {
            while(_local_2 && Boolean(_local_2.length))
            {
               _local_2.shift();
            }
         }
      }
      
      private function getTotleStrongLevel(_arg_1:int = 0) : int
      {
         var _local_6:int = 0;
         var _local_2:int = 0;
         var _local_5:* = null;
         var _local_4:BagInfo = PlayerManager.Instance.Self.getBag(0);
         var _local_3:Array = [6,15,0,4];
         _local_6 = 0;
         while(_local_6 < _local_3.length)
         {
            _local_5 = _local_4.getItemAt(_local_3[_local_6]);
            _local_2 += this.getItemLevel(_local_5,_arg_1);
            _local_6++;
         }
         return _local_2;
      }
      
      private function getItemLevel(_arg_1:InventoryItemInfo, _arg_2:int = 0) : int
      {
         var _local_3:int = 0;
         if(!_arg_1)
         {
            _local_3 = 0;
         }
         else if(_arg_2 == 0)
         {
            _local_3 = _arg_1.StrengthenLevel > 12 ? 12 : _arg_1.StrengthenLevel;
         }
         else
         {
            _local_3 = _arg_1.StrengthenLevel <= 12 ? 0 : _arg_1.StrengthenLevel - 12;
         }
         return _local_3;
      }
      
      private function getMergeProTotle() : int
      {
         var _local_1:int = 0;
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:Array = [6,0,4,1,7,8,9,10,16];
         var _local_3:BagInfo = PlayerManager.Instance.Self.getBag(0);
         _local_5 = 0;
         while(_local_5 < _local_2.length)
         {
            _local_4 = _local_3.getItemAt(_local_2[_local_5]);
            if(_local_4)
            {
               _local_1 += _local_4.AttackCompose;
               _local_1 += _local_4.DefendCompose;
               _local_1 += _local_4.LuckCompose;
               _local_1 += _local_4.AgilityCompose;
            }
            _local_5++;
         }
         return _local_1;
      }
      
      private function getEquipGoldNum() : int
      {
         var _local_1:int = 0;
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:Array = [6,4,0];
         var _local_3:BagInfo = PlayerManager.Instance.Self.getBag(0);
         _local_5 = 0;
         while(_local_5 < _local_2.length)
         {
            _local_4 = _local_3.getItemAt(_local_2[_local_5]);
            if(_local_4 && Boolean(_local_4.isGold))
            {
               _local_1++;
            }
            _local_5++;
         }
         return _local_1;
      }
      
      private function getEquipMagicTotalLevel() : int
      {
         var _local_1:int = 0;
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:Array = [7,8,9,10,16];
         var _local_3:BagInfo = PlayerManager.Instance.Self.getBag(0);
         _local_5 = 0;
         while(_local_5 < _local_2.length)
         {
            _local_4 = _local_3.getItemAt(_local_2[_local_5]);
            if(_local_4)
            {
               _local_1 += _local_4.MagicLevel;
            }
            _local_5++;
         }
         return _local_1;
      }
      
      private function getJewelryLevel() : int
      {
         var _local_1:int = 0;
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:Array = [7,8,9,10];
         var _local_3:BagInfo = PlayerManager.Instance.Self.getBag(0);
         _local_5 = 0;
         while(_local_5 < _local_2.length)
         {
            _local_4 = _local_3.getItemAt(_local_2[_local_5]);
            if(_local_4)
            {
               _local_1 += _local_4.Property1;
            }
            _local_5++;
         }
         return _local_1;
      }
      
      private function getEvolutionLevel() : int
      {
         var _local_1:int = 0;
         var _local_4:* = null;
         var _local_2:BagInfo = PlayerManager.Instance.Self.getBag(0);
         var _local_3:InventoryItemInfo = _local_2.getItemAt(15);
         if(Boolean(_local_3))
         {
            _local_4 = FineEvolutionManager.Instance.GetEvolutionDataByExp(_local_3.curExp);
            if(_local_4)
            {
               _local_1 += _local_4.Level;
            }
         }
         return _local_1;
      }
      
      private function getQilingLevel() : int
      {
         var _local_1:int = 0;
         var _local_4:int = 0;
         var _local_3:* = null;
         var _local_2:Array = [1,5,7];
         _local_4 = 0;
         while(_local_4 < _local_2.length)
         {
            _local_3 = PlayerManager.Instance.Self.getGhostDataByCategoryID(_local_2[_local_4]);
            if(_local_3)
            {
               _local_1 += _local_3.level;
            }
            _local_4++;
         }
         return _local_1;
      }
      
      private function getGemstoneLevel() : int
      {
         return GemstoneManager.Instance.getTotleLevel();
      }
      
      private function getPotential() : int
      {
         var _local_2:int = 0;
         var _local_8:int = 0;
         var _local_6:int = 0;
         var _local_5:* = null;
         var _local_1:* = null;
         var _local_7:* = null;
         var _local_3:Array = [1,2,3,5,11,13];
         var _local_4:BagInfo = PlayerManager.Instance.Self.getBag(0);
         _local_8 = 0;
         while(_local_8 < _local_3.length)
         {
            _local_5 = _local_4.getItemAt(_local_3[_local_8]);
            if(_local_5)
            {
               _local_1 = _local_5.latentEnergyCurStr;
               _local_7 = _local_1.split(",");
               _local_6 = 0;
               while(_local_6 < _local_7.length)
               {
                  _local_2 += _local_7[_local_6];
                  _local_6++;
               }
            }
            _local_8++;
         }
         return _local_2;
      }
      
      private function getPetSuitCount() : int
      {
         return 0;
      }
      
      private function getPetTalentCount() : int
      {
         var _local_1:int = 0;
         var _local_3:PetInfo = null;
         var _local_2:* = null;
         for each(_local_3 in PlayerManager.Instance.Self.pets)
         {
            if(_local_3.IsEquip)
            {
               _local_2 = _local_3;
            }
         }
         return _local_1;
      }
      
      private function getPetStarOrLevelCount(_arg_1:int = 0) : int
      {
         var _local_2:int = 0;
         var _local_4:PetInfo = null;
         var _local_3:* = null;
         for each(_local_4 in PlayerManager.Instance.Self.pets)
         {
            if(_local_4.IsEquip)
            {
               _local_3 = _local_4;
            }
         }
         if(_local_3)
         {
            _local_2 = _arg_1 == 0 ? int(_local_3.StarLevel) : int(_local_3.Level);
         }
         return _local_2;
      }
      
      private function getHorseSkillCount() : int
      {
         var _local_3:int = 0;
         var _local_5:int = 0;
         var _local_7:int = 0;
         var _local_6:int = 0;
         var _local_4:HorseSkillExpVo = null;
         var _local_1:Vector.<HorseSkillExpVo> = HorseManager.instance.curHasSkillList;
         var _local_2:Array = HorseManager.instance.horseSkillGetArray;
         for each(_local_4 in _local_1)
         {
            _local_5 = int(_local_2.length);
            _local_7 = 0;
            while(_local_7 < _local_5)
            {
               _local_6 = 0;
               while(_local_6 < _local_2[_local_7].length)
               {
                  if(_local_4.skillId == _local_2[_local_7][_local_6].SkillID)
                  {
                     _local_3 += (_local_2[_local_7][_local_6] as HorseSkillGetVo).Level;
                  }
                  _local_6++;
               }
               _local_7++;
            }
         }
         return _local_3;
      }
      
      private function getHorseSuitCount() : int
      {
         var _local_1:int = 0;
         var _local_3:int = 0;
         var _local_2:Vector.<HorsePicCherishVo> = HorseManager.instance.getHorsePicCherishData();
         _local_3 = 0;
         while(_local_3 < _local_2.length)
         {
            if(PlayerManager.Instance.Self.horsePicCherishDic.hasKey(_local_2[_local_3].ID))
            {
               _local_1++;
            }
            _local_3++;
         }
         return _local_1;
      }
      
      private function getTexpCount() : int
      {
         var _local_1:int = 0;
         var _local_3:int = 0;
         var _local_2:* = null;
         _local_3 = 0;
         while(_local_3 < 14)
         {
            _local_2 = TexpManager.Instance.getInfo(_local_3,TexpManager.Instance.getExp(_local_3));
            if(_local_2)
            {
               _local_1 += _local_2.lv;
            }
            _local_3++;
         }
         return _local_1;
      }
      
      public function getBackTempInfoById(_arg_1:int) : BibleTempInfo
      {
         var _local_2:int = 0;
         var _local_3:* = null;
         _local_2 = 0;
         while(_local_2 < this.tempInfoArr.length)
         {
            _local_3 = this.tempInfoArr[_local_2];
            if(_local_3.MainType == 3 && _local_3.ItemType == _arg_1)
            {
               break;
            }
            _local_2++;
         }
         return _local_3;
      }
   }
}

