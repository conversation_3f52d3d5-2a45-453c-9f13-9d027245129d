package beadSystem.data
{
   import ddt.data.goods.ItemTemplateInfo;
   import flash.events.Event;
   
   public class BeadEvent extends Event
   {
      
      public static const EQUIPBEAD:String = "equip";
      
      public static const UNEQUIPBEAD:String = "unequip";
      
      public static const LIGHTBTN:String = "lightButton";
      
      public static const BEADCELLCHANGED:String = "beadCellChanged";
      
      public static const OPENBEADHOLE:String = "openBeadHole";
      
      public static const BEADBAGCELLCHANGED:String = "beadBagCellChanged";
      
      public static const AUTOOPENBEAD:String = "autoOpenBead";
      
      public static const PLAYUPGRADEMC:String = "playUpgradeMC";
      
      public static const SELECT_OPEN_CELL:String = "selectOpenCell";
      
      private var _cellID:int;
      
      private var _beadInfo:ItemTemplateInfo;
      
      public function BeadEvent(_arg_1:String, _arg_2:int = -1, _arg_3:ItemTemplateInfo = null, _arg_4:<PERSON><PERSON><PERSON> = false, _arg_5:<PERSON>olean = false)
      {
         super(_arg_1,_arg_4,_arg_5);
         this._cellID = _arg_2;
         this._beadInfo = _arg_3;
      }
      
      public function get CellId() : int
      {
         return this._cellID;
      }
   }
}

