package bagAndInfo.amulet
{
   import bagAndInfo.BagAndInfoManager;
   import bagAndInfo.amulet.vo.EquipAmuletActivateGradeVo;
   import bagAndInfo.amulet.vo.EquipAmuletPhaseVo;
   import bagAndInfo.amulet.vo.EquipAmuletVo;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.CoreManager;
   import road7th.data.DictionaryData;
   
   public class EquipAmuletManager extends CoreManager
   {
      
      private static var _instance:EquipAmuletManager;
      
      private var _infoData:DictionaryData;
      
      private var _activateGradeData:DictionaryData;
      
      private var _phaseData:DictionaryData;
      
      public var buyStiveNum:int;
      
      public var lockNum:int;
      
      private var _mainFrame:Frame;
      
      public function EquipAmuletManager()
      {
         super();
      }
      
      public static function get Instance() : EquipAmuletManager
      {
         if(_instance == null)
         {
            _instance = new EquipAmuletManager();
         }
         return _instance;
      }
      
      override protected function start() : void
      {
         if(this._mainFrame == null)
         {
            this._mainFrame = ComponentFactory.Instance.creatComponentByStylename("equipAmulet.mainFrame");
            this.lockNum = 0;
            LayerManager.Instance.addToLayer(this._mainFrame,3,true,1);
            BagAndInfoManager.Instance.hideBagAndInfo();
         }
      }
      
      public function closeFrame() : void
      {
         if(Boolean(this._mainFrame))
         {
            ObjectUtils.disposeObject(this._mainFrame);
            this._mainFrame = null;
            BagAndInfoManager.Instance.showBagAndInfo();
         }
      }
      
      public function analyzer(_arg_1:EquipAmuletDataAnalyzer) : void
      {
         this._infoData = _arg_1.data;
      }
      
      public function analyzerActivateGrade(_arg_1:EquipAmuletActivateGradeDataAnalyzer) : void
      {
         this._activateGradeData = _arg_1.data;
      }
      
      public function analyzerPhase(_arg_1:EquipAmuletPhaseDataAnalyzer) : void
      {
         this._phaseData = _arg_1.data;
      }
      
      public function getAmuletVo(_arg_1:int) : EquipAmuletVo
      {
         return this._infoData[_arg_1];
      }
      
      public function getAmuletPhaseVo(_arg_1:int) : EquipAmuletPhaseVo
      {
         return this._phaseData[_arg_1];
      }
      
      public function getAmuletPhaseVoByGrade(_arg_1:int) : EquipAmuletPhaseVo
      {
         return this._phaseData[this.getAmuletPhaseByGrade(_arg_1)];
      }
      
      public function getAmuletActivateGradeVo(_arg_1:int) : EquipAmuletActivateGradeVo
      {
         var _local_3:int = 0;
         var _local_2:* = null;
         _local_3 = 1;
         while(_local_3 <= 10)
         {
            _local_2 = this._activateGradeData[_local_3] as EquipAmuletActivateGradeVo;
            if(_arg_1 < _local_2.WahsTimes)
            {
               return this._activateGradeData[_local_3 - 1];
            }
            _local_3++;
         }
         return this._activateGradeData[10];
      }
      
      public function getAmuletActivateNeedCount(_arg_1:int) : int
      {
         var _local_3:int = 0;
         var _local_2:* = null;
         _local_3 = 1;
         while(_local_3 <= 10)
         {
            _local_2 = this._activateGradeData[_local_3] as EquipAmuletActivateGradeVo;
            if(_arg_1 < _local_2.WahsTimes)
            {
               return this._activateGradeData[_local_3].WahsTimes;
            }
            _local_3++;
         }
         return this._activateGradeData[10].WahsTimes;
      }
      
      public function getAmuletHpByGrade(_arg_1:int) : int
      {
         _arg_1 = _arg_1 <= 0 ? 1 : _arg_1;
         var _local_2:EquipAmuletVo = this._infoData[_arg_1];
         return _local_2.HP;
      }
      
      public function getAmuletPhaseByGrade(_arg_1:int) : int
      {
         _arg_1 = _arg_1 <= 0 ? 1 : _arg_1;
         var _local_2:EquipAmuletVo = this._infoData[_arg_1];
         return _local_2.phase;
      }
      
      public function getAmuletPhaseGradeByCount(_arg_1:int) : int
      {
         var _local_3:int = 0;
         var _local_2:* = null;
         _local_3 = 1;
         while(_local_3 <= 10)
         {
            _local_2 = this._activateGradeData[_local_3] as EquipAmuletActivateGradeVo;
            if(_arg_1 < _local_2.WahsTimes)
            {
               return _local_3 - 1;
            }
            _local_3++;
         }
         return 10;
      }
   }
}

