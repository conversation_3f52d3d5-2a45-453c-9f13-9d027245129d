package bagAndInfo.cell
{
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import flash.display.DisplayObject;
   
   public class LockableBagCell extends BagCell
   {
      
      private var _lockDisplayObject:DisplayObject;
      
      private var _cellLocked:<PERSON>olean = false;
      
      public function LockableBagCell(_arg_1:int, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true, _arg_4:DisplayObject = null, _arg_5:Boolean = true)
      {
         super(_arg_1,_arg_2,_arg_3,_arg_4,_arg_5);
         this._cellLocked = false;
         mouseChildren = false;
         _isShowIsUsedBitmap = true;
      }
      
      public function get lockDisplayObject() : DisplayObject
      {
         return this._lockDisplayObject;
      }
      
      public function set lockDisplayObject(_arg_1:DisplayObject) : void
      {
         this._lockDisplayObject = _arg_1;
      }
      
      public function get cellLocked() : <PERSON><PERSON><PERSON>
      {
         return this._cellLocked;
      }
      
      public function set cellLocked(_arg_1:<PERSON><PERSON><PERSON>) : void
      {
         this._cellLocked = _arg_1;
         if(this._lockDisplayObject == null)
         {
            return;
         }
         if(_arg_1 == true)
         {
            addChild(this._lockDisplayObject);
         }
         else
         {
            this._lockDisplayObject.parent && removeChild(this._lockDisplayObject);
         }
      }
      
      override public function set info(_arg_1:ItemTemplateInfo) : void
      {
         super.info = _arg_1;
         if(_arg_1 == null)
         {
            this.cellLocked = false;
         }
         else
         {
            this.cellLocked = (_arg_1 as InventoryItemInfo).cellLocked;
         }
      }
   }
}

