package com.demonsters.debugger
{
   import flash.events.Event;
   import flash.events.ProgressEvent;
   import flash.events.TimerEvent;
   import flash.net.Socket;
   import flash.system.Security;
   import flash.utils.ByteArray;
   import flash.utils.Timer;
   
   internal class MonsterDebuggerConnectionDefault implements IMonsterDebuggerConnection
   {
      
      private const MAX_QUEUE_LENGTH:int = 500;
      
      private var _socket:Socket;
      
      private var _connecting:Boolean;
      
      private var _process:Boolean;
      
      private var _bytes:ByteArray;
      
      private var _package:ByteArray;
      
      private var _length:uint;
      
      private var _retry:Timer;
      
      private var _timeout:Timer;
      
      private var _address:String;
      
      private var _port:int;
      
      private var _queue:Array = [];
      
      public function MonsterDebuggerConnectionDefault()
      {
         super();
         this._socket = new Socket();
         this._socket.addEventListener("connect",this.connectHandler,false,0,false);
         this._socket.addEventListener("close",this.closeHandler,false,0,false);
         this._socket.addEventListener("ioError",this.closeHandler,false,0,false);
         this._socket.addEventListener("securityError",this.closeHandler,false,0,false);
         this._socket.addEventListener("socketData",this.dataHandler,false,0,false);
         this._connecting = false;
         this._process = false;
         this._address = "127.0.0.1";
         this._port = 5840;
         this._timeout = new Timer(2000,1);
         this._timeout.addEventListener("timer",this.closeHandler,false,0,false);
         this._retry = new Timer(1000,1);
         this._retry.addEventListener("timer",this.retryHandler,false,0,false);
      }
      
      public function set address(_arg_1:String) : void
      {
         this._address = _arg_1;
      }
      
      public function get connected() : Boolean
      {
         if(this._socket == null)
         {
            return false;
         }
         return this._socket.connected;
      }
      
      public function processQueue() : void
      {
         if(!this._process)
         {
            this._process = true;
            if(this._queue.length > 0)
            {
               this.next();
            }
         }
      }
      
      public function send(_arg_1:String, _arg_2:Object, _arg_3:Boolean = false) : void
      {
         var _local_4:* = null;
         if(_arg_3 && _arg_1 == "com.demonsters.debugger.core" && this._socket.connected)
         {
            _local_4 = new MonsterDebuggerData(_arg_1,_arg_2).bytes;
            this._socket.writeUnsignedInt(_local_4.length);
            this._socket.writeBytes(_local_4);
            this._socket.flush();
            return;
         }
         this._queue.push(new MonsterDebuggerData(_arg_1,_arg_2));
         if(this._queue.length > 500)
         {
            this._queue.shift();
         }
         if(this._queue.length > 0)
         {
            this.next();
         }
      }
      
      public function connect() : void
      {
         if(!this._connecting && MonsterDebugger.enabled)
         {
            try
            {
               Security.loadPolicyFile("xmlsocket://" + this._address + ":" + this._port);
               this._connecting = true;
               this._socket.connect(this._address,this._port);
               this._retry.stop();
               this._timeout.reset();
               this._timeout.start();
            }
            catch(e:Error)
            {
               closeHandler();
            }
         }
      }
      
      private function next() : void
      {
         if(!MonsterDebugger.enabled)
         {
            return;
         }
         if(!this._process)
         {
            return;
         }
         if(!this._socket.connected)
         {
            this.connect();
            return;
         }
         var _local_1:ByteArray = MonsterDebuggerData(this._queue.shift()).bytes;
         this._socket.writeUnsignedInt(_local_1.length);
         this._socket.writeBytes(_local_1);
         this._socket.flush();
         _local_1 = null;
         if(this._queue.length > 0)
         {
            this.next();
         }
      }
      
      private function connectHandler(_arg_1:Event) : void
      {
         this._timeout.stop();
         this._retry.stop();
         this._connecting = false;
         this._bytes = new ByteArray();
         this._package = new ByteArray();
         this._length = 0;
         this._socket.writeUTFBytes("<hello/>\n");
         this._socket.writeByte(0);
         this._socket.flush();
      }
      
      private function retryHandler(_arg_1:TimerEvent) : void
      {
         this._retry.stop();
         this.connect();
      }
      
      private function closeHandler(_arg_1:Event = null) : void
      {
         MonsterDebuggerUtils.resume();
         if(!this._retry.running)
         {
            this._connecting = false;
            this._process = false;
            this._timeout.stop();
            this._retry.reset();
            this._retry.start();
         }
      }
      
      private function dataHandler(_arg_1:ProgressEvent) : void
      {
         this._bytes = new ByteArray();
         this._socket.readBytes(this._bytes,0,this._socket.bytesAvailable);
         this._bytes.position = 0;
         this.processPackage();
      }
      
      private function processPackage() : void
      {
         var _local_2:uint = 0;
         var _local_1:* = null;
         if(this._bytes.bytesAvailable == 0)
         {
            return;
         }
         if(this._length == 0)
         {
            this._length = this._bytes.readUnsignedInt();
            this._package = new ByteArray();
         }
         if(this._package.length < this._length && this._bytes.bytesAvailable > 0)
         {
            _local_2 = this._bytes.bytesAvailable;
            if(_local_2 > this._length - this._package.length)
            {
               _local_2 = uint(this._length - this._package.length);
            }
            this._bytes.readBytes(this._package,this._package.length,_local_2);
         }
         if(this._length != 0 && this._package.length == this._length)
         {
            _local_1 = MonsterDebuggerData.read(this._package);
            if(_local_1.id != null)
            {
               MonsterDebuggerCore.handle(_local_1);
            }
            this._length = 0;
            this._package = null;
         }
         if(this._length == 0 && this._bytes.bytesAvailable > 0)
         {
            this.processPackage();
         }
      }
   }
}

