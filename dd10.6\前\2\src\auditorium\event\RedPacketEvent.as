package auditorium.event
{
   import flash.events.Event;
   
   public class RedPacketEvent extends Event
   {
      
      public static const UPDATE_REDPACKET_INFO:String = "updateRedPacketInfo";
      
      private var _data:Object;
      
      public function RedPacketEvent(_arg_1:String, _arg_2:Object = null, _arg_3:<PERSON><PERSON>an = false, _arg_4:<PERSON><PERSON><PERSON> = false)
      {
         this._data = _arg_2;
         super(_arg_1,_arg_3,_arg_4);
      }
      
      public function get data() : Object
      {
         return this._data;
      }
   }
}

