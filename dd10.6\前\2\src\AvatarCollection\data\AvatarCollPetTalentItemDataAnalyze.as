package AvatarCollection.data
{
   import com.pickgliss.loader.DataAnalyzer;
   import road7th.data.DictionaryData;
   
   public class AvatarCollPetTalentItemDataAnalyze extends DataAnalyzer
   {
      
      private var _talentItemDic:DictionaryData;
      
      public function AvatarCollPetTalentItemDataAnalyze(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var index:int = 0;
         var listInfo:* = undefined;
         var _local_1:AvatarCollPetTalentItemVo = null;
         var _local_3:int = 0;
         var _local_2:XML = new XML(_arg_1);
         this._talentItemDic = new DictionaryData();
         if(_local_2.@value == "true")
         {
            listInfo = _local_2..Item;
            index = 0;
            while(index < listInfo.length())
            {
               _local_1 = new AvatarCollPetTalentItemVo();
               _local_1.type = listInfo[index].@Types;
               _local_1.level = listInfo[index].@Level;
               _local_1.groupID = listInfo[index].@GroupID;
               _local_1.templateName = listInfo[index].@TemplateName;
               _local_1.needCount = listInfo[index].@NeedCount;
               _local_1.attack = listInfo[index].@Attack;
               _local_1.defence = listInfo[index].@Defence;
               _local_1.agility = listInfo[index].@Agility;
               _local_1.luck = listInfo[index].@Luck;
               _local_1.blood = listInfo[index].@Blood;
               _local_3 = _local_1.type;
               if(!this._talentItemDic[_local_3])
               {
                  this._talentItemDic[_local_3] = new DictionaryData();
               }
               this._talentItemDic[_local_3].add(_local_1.level,_local_1);
               index++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
         }
      }
      
      public function get talentItemDic() : DictionaryData
      {
         return this._talentItemDic;
      }
   }
}

