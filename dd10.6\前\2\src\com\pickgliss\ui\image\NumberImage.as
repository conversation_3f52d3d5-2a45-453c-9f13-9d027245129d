package com.pickgliss.ui.image
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.core.Component;
   import com.pickgliss.utils.ObjectUtils;
   
   public class NumberImage extends Component
   {
      
      private var _countList:Vector.<ScaleFrameImage>;
      
      private var _count:String;
      
      private var _stylename:String;
      
      private var _countWidth:int;
      
      private var _space:int;
      
      public function NumberImage()
      {
         super();
         mouseEnabled = mouseChildren = false;
         this._countList = new Vector.<ScaleFrameImage>();
      }
      
      public function set imageStyle(_arg_1:String) : void
      {
         this._stylename = _arg_1;
      }
      
      public function set countWidth(_arg_1:int) : void
      {
         this._countWidth = _arg_1;
      }
      
      public function set space(_arg_1:int) : void
      {
         this._space = _arg_1;
      }
      
      public function set count(_arg_1:int) : void
      {
         if(this._count == _arg_1.toString())
         {
            return;
         }
         this._count = _arg_1.toString();
         this.updateView();
      }
      
      public function set countStr(_arg_1:String) : void
      {
         if(this._count == _arg_1)
         {
            return;
         }
         this._count = _arg_1;
         this.updateView();
      }
      
      public function get count() : int
      {
         return int(this._count);
      }
      
      private function updateView() : void
      {
         var _local_5:int = 0;
         var _local_2:int = 0;
         var _local_1:int = 0;
         var _local_4:int = this._count.length;
         var _local_3:Array = this._count.split("");
         while(_local_4 > this._countList.length)
         {
            this._countList.unshift(this.createCountImage(10));
         }
         while(_local_4 < this._countList.length)
         {
            ObjectUtils.disposeObject(this._countList.shift());
         }
         if(this._countWidth > 0)
         {
            _local_1 = int((this._countWidth - this._countList[0].width * _local_4) / 2);
         }
         _local_5 = 0;
         while(_local_5 < _local_4)
         {
            this._countList[_local_5].x = _local_1;
            _local_2 = _local_3[_local_5] == 0 ? 10 : int(_local_3[_local_5]);
            this._countList[_local_5].setFrame(_local_2);
            _local_1 += this._countList[_local_5].width + this._space;
            _local_5++;
         }
         width = _local_1;
      }
      
      private function createCountImage(_arg_1:int = 0) : ScaleFrameImage
      {
         var _local_2:ScaleFrameImage = ComponentFactory.Instance.creatComponentByStylename(this._stylename);
         _local_2.setFrame(_arg_1);
         addChild(_local_2);
         return _local_2;
      }
      
      override public function dispose() : void
      {
         super.dispose();
         while(Boolean(this._countList.length))
         {
            ObjectUtils.disposeObject(this._countList.shift());
         }
         this._countList = null;
      }
   }
}

