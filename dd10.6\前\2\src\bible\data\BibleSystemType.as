package bible.data
{
   public class BibleSystemType
   {
      
      public static const MAIN_STRONG_TYPE:int = 1;
      
      public static const SECOND_PEOPLE_TYPE:int = 1;
      
      public static const SECOND_EQUIP_TYPE:int = 2;
      
      public static const SECOND_PET_TYPE:int = 3;
      
      public static const SECOND_HORSE_TYPE:int = 4;
      
      public static const MAIN_TODAY_TODO:int = 2;
      
      public static const SECOND_ACTIVE_TYPE:int = 1;
      
      public static const SECOND_GAME_TYPE:int = 2;
      
      public static const SECOND_PK_TYPE:int = 3;
      
      public static const SECOND_PROFIT_TYPE:int = 4;
      
      public static const PRACTICE_TYPE:int = 1;
      
      public static const CARD_TYPE:int = 2;
      
      public static const TOTEM_TYPE:int = 3;
      
      public static const BEAD_TYPE:int = 4;
      
      public static const SUIT_TYPE:int = 5;
      
      public static const MAGICSTONE_TYPE:int = 6;
      
      public static const TEMPLE_TYPE:int = 7;
      
      public static const MARK_TYPE:int = 8;
      
      public static const DEFENDE_TYPE:int = 9;
      
      public static const EXPLORER_TYPE:int = 10;
      
      public static const PEOPLE_LEVEL_TYPE:int = 11;
      
      public static const STRENGTH_TYPE:int = 12;
      
      public static const SUPER_TYPE:int = 13;
      
      public static const MERGE_TYPE:int = 14;
      
      public static const GOLD_TYPE:int = 15;
      
      public static const ENCHANT_TYPE:int = 16;
      
      public static const BUILD_TYPE:int = 17;
      
      public static const CURTIVATE_TYPE:int = 18;
      
      public static const EVOLUTION_TYPE:int = 19;
      
      public static const QILING_TYPE:int = 20;
      
      public static const SEAL_TYPE:int = 21;
      
      public static const ZHANHUN_TYPE:int = 22;
      
      public static const POTENTIAL_TYPE:int = 23;
      
      public static const PETSUIT_TYPE:int = 24;
      
      public static const PETTALENT_TYPE:int = 25;
      
      public static const PETXINGTAI_TYPE:int = 26;
      
      public static const PETSTARUP_TYPE:int = 27;
      
      public static const PETLEVEL_TYPE:int = 28;
      
      public static const HORSELEVEL_TYPE:int = 29;
      
      public static const HORSESKILL_TYPE:int = 30;
      
      public static const HORSESUIT_TYPE:int = 31;
      
      public static const HORSEFUWEN_TYPE:int = 32;
      
      public static const PETEVOLUTION_TYPE:int = 33;
      
      public static const PETGERMINATION_TYPE:int = 34;
      
      public static const HORSEQILING_TYPE:int = 35;
      
      public static const WORLDBOSS1_TYPE:int = 1;
      
      public static const WORLDBOSS2_TYPE:int = 2;
      
      public static const WORLDBOSS3_TYPE:int = 3;
      
      public static const HEOR_TYPE:int = 4;
      
      public static const PETFARM_TYPE:int = 5;
      
      public static const TREASUREMAP_TYPE:int = 6;
      
      public static const NORMALTASK_TYPE:int = 7;
      
      public static const FLYHAPPY_TYPE:int = 8;
      
      public static const REVERSE_TYPE:int = 9;
      
      public static const ADVENTURE_TYPE:int = 10;
      
      public static const DEFENDEDANDAO_TYPE:int = 11;
      
      public static const PIRATE_TYPE:int = 12;
      
      public static const WARRIOR_TYPE:int = 13;
      
      public static const DIXUE_TYPE:int = 14;
      
      public static const SHILIAN_TYPE:int = 15;
      
      public static const LEAGUE_TYPE:int = 16;
      
      public static const TEAMBATTLE_TYPE:int = 17;
      
      public static const OPENBEAD_TYPE:int = 18;
      
      public static const CONTIALGIFT_TYPE:int = 19;
      
      public static const MARKTREASURE_TYPE:int = 20;
      
      public static const FARMDRAW_TYPE:int = 21;
      
      public static const VIPSHOP_TYPE:int = 22;
      
      public static const DDMONEY_TYPE:int = 23;
      
      public static const DDXUANXIU_TYPE:int = 24;
      
      public static const MINES_TYPE:int = 25;
      
      public static const MAIN_GET_BACK:int = 3;
      
      public function BibleSystemType()
      {
         super();
      }
   }
}

