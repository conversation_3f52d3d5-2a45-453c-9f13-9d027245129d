package bones.loader
{
   import bones.model.BoneVo;
   import flash.events.Event;
   
   public class BonesLoaderEvent extends Event
   {
      
      public static const COMPLETE:String = "complete";
      
      public static const BONES_STYLE_COMPLETE:String = "bonesstylecompelete";
      
      public static const ERROR:String = "error";
      
      private var _data:Object;
      
      public function BonesLoaderEvent(_arg_1:String, _arg_2:Object = null)
      {
         this._data = _arg_2;
         super(_arg_1);
      }
      
      public function get vo() : BoneVo
      {
         return this._data as BoneVo;
      }
      
      public function get data() : Object
      {
         return this._data;
      }
   }
}

