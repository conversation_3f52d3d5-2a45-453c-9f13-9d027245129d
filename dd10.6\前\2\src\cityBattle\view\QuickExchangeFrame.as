package cityBattle.view
{
   import cityBattle.CityBattleManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.command.QuickBuyAlertBase;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.PositionUtils;
   import flash.events.MouseEvent;
   
   public class QuickExchangeFrame extends QuickBuyAlertBase
   {
      
      protected var _exchangeTxt:FilterFrameText;
      
      protected var _restTitleText:FilterFrameText;
      
      protected var _restText:FilterFrameText;
      
      public var type:int;
      
      public function QuickExchangeFrame()
      {
         super();
      }
      
      override protected function initView() : void
      {
         super.initView();
         titleText = LanguageMgr.GetTranslation("ddt.cityBattle.exchangeBtn");
         _totalTipText.text = LanguageMgr.GetTranslation("ddt.cityBattle.costScore");
         _submitButton.text = LanguageMgr.GetTranslation("ddt.cityBattle.exchangeBtn");
         this._restTitleText = ComponentFactory.Instance.creatComponentByStylename("ddtcore.TotalTipsText");
         this._restTitleText.text = LanguageMgr.GetTranslation("ddt.cityBattle.canBuyNum");
         addToContent(this._restTitleText);
         PositionUtils.setPos(this._restTitleText,"quickExchange.restTitlePos");
         this._restText = ComponentFactory.Instance.creatComponentByStylename("ddtcore.TotalText");
         addToContent(this._restText);
         PositionUtils.setPos(this._restText,"quickExchange.restPos");
         _sprite.visible = false;
      }
      
      override protected function refreshNumText() : void
      {
         var _local_1:String = String(_number.number * _perPrice);
         var _local_2:String = LanguageMgr.GetTranslation("ddt.cityBattle.score");
         totalText.text = _local_1 + " " + _local_2;
      }
      
      override public function setData(_arg_1:int, _arg_2:int, _arg_3:int) : void
      {
         var _local_7:int = 0;
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_6:* = null;
         super.setData(_arg_1,_arg_2,_arg_3);
         _local_7 = 0;
         while(_local_7 < CityBattleManager.instance.welfareList.length)
         {
            if(CityBattleManager.instance.welfareList[_local_7].ID == _shopGoodsId)
            {
               _local_4 = CityBattleManager.instance.welfareList[_local_7];
            }
            _local_7++;
         }
         _local_5 = 0;
         while(_local_5 < CityBattleManager.instance.myExchangeInfo.length)
         {
            if(CityBattleManager.instance.myExchangeInfo[_local_5].ID == _shopGoodsId)
            {
               _local_6 = CityBattleManager.instance.myExchangeInfo[_local_5];
            }
            _local_5++;
         }
         this._restText.text = String(_local_4.ExchangeCount - _local_6.myExchangeCount);
         _number.maximum = _local_4.ExchangeCount - _local_6.ExchangeCount;
      }
      
      override protected function __buy(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:int = getNeedMoney();
         if(parseInt(this._restText.text) < _number.number)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.cityBattle.noEnoughNum"));
            return;
         }
         if(CityBattleManager.instance.myScore < _local_2)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.cityBattle.noEnoughScore"));
            return;
         }
         if(this.type == 3)
         {
            SocketManager.Instance.out.exchangeScore(0,_shopGoodsId,_number.number);
         }
         else
         {
            if(CityBattleManager.instance.now < 8 && CityBattleManager.instance.now > 0)
            {
               SocketManager.Instance.out.exchangeScore(CityBattleManager.instance.now,_shopGoodsId,_number.number);
            }
            SocketManager.Instance.out.exchangeScore(7,_shopGoodsId,_number.number);
         }
         this.dispose();
      }
      
      override public function dispose() : void
      {
         super.dispose();
         ObjectUtils.disposeObject(this._restTitleText);
         this._restTitleText = null;
         ObjectUtils.disposeObject(this._restText);
         this._restText = null;
      }
   }
}

