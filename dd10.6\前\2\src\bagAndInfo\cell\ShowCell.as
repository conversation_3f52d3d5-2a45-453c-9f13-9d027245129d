package bagAndInfo.cell
{
   import beadSystem.beadSystemManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.ShowTipManager;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.core.ITipedDisplay;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.manager.BeadTemplateManager;
   import ddt.manager.ServerConfigManager;
   import ddt.utils.PositionUtils;
   import ddt.view.tips.GoodTipInfo;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import magicStone.MagicStoneManager;
   
   [Event(name="change",type="flash.events.Event")]
   public class ShowCell extends Sprite implements ITipedDisplay, Disposeable
   {
      
      protected var _bg:DisplayObject;
      
      protected var _contentHeight:Number;
      
      protected var _contentWidth:Number;
      
      protected var _info:ItemTemplateInfo;
      
      protected var _loadingasset:MovieClip;
      
      protected var _pic:CellContentCreator;
      
      protected var _picPos:Point;
      
      protected var _showLoading:Boolean;
      
      protected var _showTip:Boolean;
      
      protected var _tipData:Object;
      
      protected var _tipDirection:String;
      
      protected var _tipGapH:int;
      
      protected var _tipGapV:int;
      
      protected var _tipStyle:String;
      
      protected var _tbxCount:FilterFrameText;
      
      private var _grayFlag:Boolean;
      
      public function ShowCell(_arg_1:DisplayObject = null, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true, _arg_4:Boolean = true)
      {
         super();
         this._bg = Boolean(_arg_1) ? _arg_1 : ComponentFactory.Instance.creatBitmap("bagAndInfo.cell.bagCellBgAsset");
         this._showLoading = _arg_3;
         this._showTip = _arg_4;
         this.init();
         this.initTip();
         this.initEvent();
         this.info = _arg_2;
      }
      
      public function set PicPos(_arg_1:Point) : void
      {
         this._picPos = _arg_1;
         this.updateSize(this._pic);
      }
      
      public function asDisplayObject() : DisplayObject
      {
         return this;
      }
      
      public function dispose() : void
      {
         this.removeEvent();
         ObjectUtils.disposeObject(this._bg);
         this._bg = null;
         this.clearLoading();
         this.clearCreatingContent();
         this._info = null;
         ShowTipManager.Instance.removeTip(this);
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
         if(Boolean(this._tbxCount))
         {
            ObjectUtils.disposeObject(this._tbxCount);
         }
         this._tbxCount = null;
      }
      
      public function dragDrop(_arg_1:DragEffect) : void
      {
      }
      
      public function set grayFilters(_arg_1:Boolean) : void
      {
         this._grayFlag = _arg_1;
         if(_arg_1)
         {
            filters = ComponentFactory.Instance.creatFilters("grayFilter");
         }
         else
         {
            filters = null;
         }
      }
      
      public function get grayFlag() : Boolean
      {
         return this._grayFlag;
      }
      
      override public function get height() : Number
      {
         return this._bg.height + this._bg.y * 2;
      }
      
      public function get info() : ItemTemplateInfo
      {
         if(this._info == null)
         {
            return null;
         }
         return this._info;
      }
      
      public function set info(_arg_1:ItemTemplateInfo) : void
      {
         if(this._info == _arg_1 && !this._info)
         {
            return;
         }
         if(Boolean(this._info))
         {
            this.clearCreatingContent();
            ObjectUtils.disposeObject(this._pic);
            this._pic = null;
            this.clearLoading();
            this._tipData = null;
         }
         this._info = _arg_1;
         if(Boolean(this._info))
         {
            if(this._showLoading)
            {
               this.createLoading();
            }
            this._pic = new CellContentCreator();
            this._pic.info = this._info;
            this._pic.loadSync(this.createContentComplete);
            addChild(this._pic);
            this.setDefaultTipData();
         }
         dispatchEvent(new Event("change"));
      }
      
      protected function setDefaultTipData() : void
      {
         var _local_1:* = null;
         if(EquipType.isCardBox(this._info))
         {
            this.tipStyle = "core.CardBoxTipPanel";
            this.tipData = this._info;
         }
         else if(this._info.CategoryID != 26)
         {
            this.tipStyle = "core.GoodsTip";
            this._tipData = new GoodTipInfo();
            GoodTipInfo(this._tipData).itemInfo = this._info;
            _local_1 = this._info as InventoryItemInfo;
            if(this._info.CategoryID == 75)
            {
               return;
            }
            if(this._info.CategoryID == 107)
            {
               return;
            }
            if(EquipType.isBeadByTemplateInfo(this._info))
            {
               if(_local_1 && _local_1.Hole2 > 0)
               {
                  GoodTipInfo(this._tipData).exp = _local_1.Hole2;
                  GoodTipInfo(this._tipData).upExp = ServerConfigManager.instance.getBeadUpgradeExp()[_local_1.Hole1 + 1];
                  GoodTipInfo(this._tipData).beadName = _local_1.Name + "-" + beadSystemManager.Instance.getBeadName(_local_1);
               }
               else
               {
                  GoodTipInfo(this._tipData).exp = ServerConfigManager.instance.getBeadUpgradeExp()[BeadTemplateManager.Instance.GetBeadInfobyID(this.info.TemplateID).BaseLevel];
                  GoodTipInfo(this._tipData).upExp = ServerConfigManager.instance.getBeadUpgradeExp()[BeadTemplateManager.Instance.GetBeadInfobyID(this.info.TemplateID).BaseLevel + 1];
                  GoodTipInfo(this._tipData).beadName = this._info.Name + "-" + BeadTemplateManager.Instance.GetBeadInfobyID(this._info.TemplateID).Name + "Lv" + BeadTemplateManager.Instance.GetBeadInfobyID(this.info.TemplateID).BaseLevel;
               }
            }
            else if(this.info.Property1 == "81" && (this.info.CategoryID == 61 || this.info.CategoryID == 90))
            {
               if(_local_1 && _local_1.StrengthenExp > 0)
               {
                  GoodTipInfo(this._tipData).exp = _local_1.StrengthenExp - MagicStoneManager.instance.getNeedExp(this.info.TemplateID,_local_1.StrengthenLevel);
               }
               else
               {
                  GoodTipInfo(this._tipData).exp = 0;
               }
               GoodTipInfo(this._tipData).upExp = MagicStoneManager.instance.getNeedExpPerLevel(this.info.TemplateID,this.info.Level + 1);
               GoodTipInfo(this._tipData).beadName = this.info.Name + "Lv" + this.info.Level;
            }
         }
      }
      
      public function setColor(_arg_1:*) : Boolean
      {
         return this._pic.setColor(_arg_1);
      }
      
      public function setContentSize(_arg_1:Number, _arg_2:Number) : void
      {
         this._contentWidth = _arg_1;
         this._contentHeight = _arg_2;
         this.updateSize(this._pic);
      }
      
      public function get tipData() : Object
      {
         return this._tipData;
      }
      
      public function set tipData(_arg_1:Object) : void
      {
         this._tipData = _arg_1;
      }
      
      public function get tipDirctions() : String
      {
         return this._tipDirection;
      }
      
      public function set tipDirctions(_arg_1:String) : void
      {
         this._tipDirection = _arg_1;
      }
      
      public function get tipGapH() : int
      {
         return this._tipGapH;
      }
      
      public function set tipGapH(_arg_1:int) : void
      {
         this._tipGapH = _arg_1;
      }
      
      public function get tipGapV() : int
      {
         return this._tipGapV;
      }
      
      public function set tipGapV(_arg_1:int) : void
      {
         this._tipGapV = _arg_1;
      }
      
      public function get tipStyle() : String
      {
         return this._tipStyle;
      }
      
      public function set tipStyle(_arg_1:String) : void
      {
         this._tipStyle = _arg_1;
      }
      
      override public function get width() : Number
      {
         return this._bg.width;
      }
      
      protected function clearCreatingContent() : void
      {
         if(this._pic == null)
         {
            return;
         }
         if(Boolean(this._pic.parent))
         {
            this._pic.parent.removeChild(this._pic);
         }
         this._pic.clearLoader();
         this._pic.dispose();
         this._pic = null;
      }
      
      protected function createChildren() : void
      {
         this._contentWidth = this._bg.width - 2;
         this._contentHeight = this._bg.height - 2;
         addChildAt(this._bg,0);
         this._pic = new CellContentCreator();
         this._tbxCount = ComponentFactory.Instance.creatComponentByStylename("BagCellCountText");
         this._tbxCount.mouseEnabled = false;
         addChild(this._tbxCount);
      }
      
      protected function createContentComplete() : void
      {
         this.clearLoading();
         this.updateSize(this._pic);
      }
      
      public function createDragImg() : DisplayObject
      {
         var _local_1:* = null;
         if(this._pic && this._pic.width > 0 && this._pic.height > 0)
         {
            _local_1 = new Bitmap(new BitmapData(this._pic.width / this._pic.scaleX,this._pic.height / this._pic.scaleY,true,0));
            _local_1.bitmapData.draw(this._pic);
            return _local_1;
         }
         return null;
      }
      
      protected function createLoading() : void
      {
         if(this._loadingasset == null)
         {
            this._loadingasset = ComponentFactory.Instance.creat("bagAndInfo.cell.BaseCellLoadingAsset");
         }
         this.updateSizeII(this._loadingasset);
         PositionUtils.setPos(this._loadingasset,"ddt.core.baseCell.loadingPos");
         addChild(this._loadingasset);
      }
      
      protected function init() : void
      {
         if(this._showTip)
         {
            ShowTipManager.Instance.addTip(this);
         }
         this.createChildren();
      }
      
      protected function initTip() : void
      {
         this.tipDirctions = "7,6,2,1,5,4,0,3,6";
         this.tipGapV = 10;
         this.tipGapH = 10;
      }
      
      protected function initEvent() : void
      {
         addEventListener("click",this.onMouseClick);
      }
      
      protected function onMouseClick(_arg_1:MouseEvent) : void
      {
      }
      
      protected function removeEvent() : void
      {
         removeEventListener("click",this.onMouseClick);
      }
      
      protected function updateSize(_arg_1:DisplayObject) : void
      {
         if(Boolean(_arg_1))
         {
            _arg_1.width = this._contentWidth - 2;
            _arg_1.height = this._contentHeight - 2;
            if(this._picPos != null)
            {
               _arg_1.x = this._picPos.x;
            }
            else
            {
               _arg_1.x = Math.abs(_arg_1.width - this._contentWidth) / 2;
            }
            if(this._picPos != null)
            {
               _arg_1.y = this._picPos.y;
            }
            else
            {
               _arg_1.y = Math.abs(_arg_1.height - this._contentHeight) / 2;
            }
         }
      }
      
      protected function clearLoading() : void
      {
         if(Boolean(this._loadingasset))
         {
            this._loadingasset.stop();
         }
         ObjectUtils.disposeObject(this._loadingasset);
         this._loadingasset = null;
      }
      
      protected function updateSizeII(_arg_1:Sprite) : void
      {
      }
      
      public function setCount(_arg_1:*) : void
      {
         if(Boolean(this._tbxCount))
         {
            this._tbxCount.text = _arg_1;
            this._tbxCount.visible = true;
            this._tbxCount.x = this._contentWidth - this._tbxCount.width;
            this._tbxCount.y = this._contentHeight - this._tbxCount.height;
            addChild(this._tbxCount);
         }
      }
      
      public function getCount() : int
      {
         return int(this._tbxCount.text);
      }
      
      public function refreshTbxPos() : void
      {
         this._tbxCount.x = this._pic.x + this._contentWidth - this._tbxCount.width - 4;
         this._tbxCount.y = this._pic.y + this._contentHeight - this._tbxCount.height - 2;
      }
      
      public function setCountNotVisible() : void
      {
         if(Boolean(this._tbxCount))
         {
            this._tbxCount.visible = false;
         }
      }
      
      public function updateCount() : void
      {
         if(Boolean(this._tbxCount))
         {
            if(this._info && this.itemInfo && this.itemInfo.MaxCount > 1)
            {
               this._tbxCount.text = this.itemInfo.Count.toString();
               this._tbxCount.visible = true;
               addChild(this._tbxCount);
            }
            else
            {
               this._tbxCount.visible = false;
            }
         }
      }
      
      public function get tbxCount() : FilterFrameText
      {
         return this._tbxCount;
      }
      
      public function get itemInfo() : InventoryItemInfo
      {
         return this._info as InventoryItemInfo;
      }
      
      public function setBgVisible(_arg_1:Boolean) : void
      {
         this._bg.visible = _arg_1;
      }
   }
}

