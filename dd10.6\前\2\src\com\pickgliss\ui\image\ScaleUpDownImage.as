package com.pickgliss.ui.image
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.Bitmap;
   
   public class ScaleUpDownImage extends Image
   {
      
      private var _bitmaps:Vector.<Bitmap>;
      
      private var _imageLinks:Array;
      
      public function ScaleUpDownImage()
      {
         super();
      }
      
      override public function dispose() : void
      {
         this.removeImages();
         graphics.clear();
         this._bitmaps = null;
         super.dispose();
      }
      
      override protected function addChildren() : void
      {
         if(this._bitmaps == null)
         {
            return;
         }
         addChild(this._bitmaps[0]);
         addChild(this._bitmaps[2]);
      }
      
      override protected function resetDisplay() : void
      {
         this._imageLinks = ComponentFactory.parasArgs(_resourceLink);
         this.removeImages();
         this.creatImages();
      }
      
      override protected function updateSize() : void
      {
         if(Boolean(_changedPropeties["width"]) || Boolean(_changedPropeties["height"]))
         {
            this.drawImage();
         }
      }
      
      private function creatImages() : void
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         this._bitmaps = new Vector.<Bitmap>();
         _local_2 = 0;
         while(_local_2 < this._imageLinks.length)
         {
            _local_1 = ComponentFactory.Instance.creat(this._imageLinks[_local_2]);
            this._bitmaps.push(_local_1);
            _local_2++;
         }
      }
      
      private function drawImage() : void
      {
         graphics.clear();
         graphics.beginBitmapFill(this._bitmaps[1].bitmapData);
         graphics.drawRect(0,this._bitmaps[0].height,_width,_height - this._bitmaps[0].height - this._bitmaps[2].height);
         graphics.endFill();
         this._bitmaps[2].y = _height - this._bitmaps[2].height;
      }
      
      private function removeImages() : void
      {
         var _local_1:int = 0;
         if(this._bitmaps == null)
         {
            return;
         }
         _local_1 = 0;
         while(_local_1 < this._bitmaps.length)
         {
            ObjectUtils.disposeObject(this._bitmaps[_local_1]);
            _local_1++;
         }
      }
   }
}

