package com.pickgliss.ui.image
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class ScaleLeftRightImage extends Image
   {
      
      private var _bitmaps:Vector.<Bitmap>;
      
      private var _imageLinks:Array;
      
      public var cutRect:String;
      
      public function ScaleLeftRightImage()
      {
         super();
      }
      
      override public function dispose() : void
      {
         this.removeImages();
         graphics.clear();
         this._bitmaps = null;
         super.dispose();
      }
      
      override protected function addChildren() : void
      {
         if(this._bitmaps == null)
         {
            return;
         }
         addChild(this._bitmaps[0]);
         addChild(this._bitmaps[2]);
      }
      
      override protected function resetDisplay() : void
      {
         this._imageLinks = ComponentFactory.parasArgs(_resourceLink);
         this.removeImages();
         this.creatImages();
      }
      
      override protected function updateSize() : void
      {
         if(Boolean(_changedPropeties["width"]) || Boolean(_changedPropeties["height"]))
         {
            this.drawImage();
         }
      }
      
      private function creatImages() : void
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         this._bitmaps = new Vector.<Bitmap>();
         _local_2 = 0;
         while(_local_2 < this._imageLinks.length)
         {
            _local_1 = ComponentFactory.Instance.creat(this._imageLinks[_local_2]);
            this._bitmaps.push(_local_1);
            _local_2++;
         }
         if(this._imageLinks.length == 1 && Boolean(this.cutRect))
         {
            this.cutImages();
         }
         _height = this._bitmaps[1].bitmapData.height;
         _changedPropeties["height"] = true;
      }
      
      private function cutImages() : void
      {
         var _local_4:* = null;
         var _local_3:Bitmap = this._bitmaps.shift();
         var _local_6:Array = this.cutRect.split(",");
         var _local_5:Rectangle = new Rectangle(0,0,_local_6[0],_local_3.height);
         var _local_2:Rectangle = new Rectangle(_local_5.width,0,_local_6[1],_local_3.height);
         var _local_1:Rectangle = new Rectangle(_local_2.x + _local_2.width,0,_local_3.width - _local_5.width - _local_2.width,_local_3.height);
         _local_4 = new BitmapData(_local_5.width,_local_5.height);
         _local_4.copyPixels(_local_3.bitmapData,_local_5,new Point(0,0));
         this._bitmaps[0] = new Bitmap(_local_4);
         _local_4 = new BitmapData(_local_2.width,_local_2.height);
         _local_4.copyPixels(_local_3.bitmapData,_local_2,new Point(0,0));
         this._bitmaps[1] = new Bitmap(_local_4);
         _local_4 = new BitmapData(_local_1.width,_local_1.height);
         _local_4.copyPixels(_local_3.bitmapData,_local_1,new Point(0,0));
         this._bitmaps[2] = new Bitmap(_local_4);
         ObjectUtils.disposeObject(_local_3);
      }
      
      private function drawImage() : void
      {
         graphics.clear();
         graphics.beginBitmapFill(this._bitmaps[1].bitmapData);
         graphics.drawRect(this._bitmaps[0].width,0,_width - this._bitmaps[0].width - this._bitmaps[2].width,_height);
         graphics.endFill();
         this._bitmaps[2].x = _width - this._bitmaps[2].width;
      }
      
      private function removeImages() : void
      {
         var _local_1:int = 0;
         if(this._bitmaps == null)
         {
            return;
         }
         _local_1 = 0;
         while(_local_1 < this._bitmaps.length)
         {
            ObjectUtils.disposeObject(this._bitmaps[_local_1]);
            _local_1++;
         }
      }
   }
}

