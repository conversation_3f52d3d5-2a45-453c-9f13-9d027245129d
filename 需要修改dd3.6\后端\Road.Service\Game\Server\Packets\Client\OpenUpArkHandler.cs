﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Bussiness;
using Bussiness.Managers;
using EntityDatabase.PlayerModels;
using Game.Base.Packets;
using Game.Server.GameUtils;
using SqlDataProvider.Data;

namespace Game.Server.Packets.Client
{
	// Token: 0x02000A19 RID: 2585
	[PacketHandler(63, "打开物品")]
	public class OpenUpArkHandler : IPacketHandler
	{
		// Token: 0x0600629D RID: 25245 RVA: 0x001F7C08 File Offset: 0x001F5E08
		public int HandlePacket(GameClient client, GSPacketIn packet)
		{
			int num = (int)packet.ReadByte();
			int num2 = packet.ReadInt();
			int num3 = packet.ReadInt();
			PlayerInventory inventory = client.Player.GetInventory((eBageType)num);
			ItemInfo itemAt = inventory.GetItemAt(num2);
			GSPacketIn gspacketIn = packet.Clone();
			gspacketIn.ClearContext();
			if (itemAt.Template.Name.Contains("级战令激活卡") && itemAt.Template.Property2 >= 0 && itemAt.Template.Property2 < 3)
			{
				if (client.Player.PlayerCharacter.zhanlingVipType >= itemAt.Template.Property2)
				{
					client.Player.SendMessage("当前级别战令已开通或现有战令级别为更高级的战令");
					return 0;
				}
				client.Player.PlayerCharacter.zhanlingVipType = itemAt.Template.Property2;
				string text = null;
				if (itemAt.Template.Property2 == 1)
				{
					text = "恭喜玩家[" + client.Player.PlayerCharacter.NickName + "]成功开通高级战令，太令人羡慕了！";
				}
				else if (itemAt.Template.Property2 == 2)
				{
					text = "恭喜玩家[" + client.Player.PlayerCharacter.NickName + "]成功开通超级战令，太令人羡慕了！";
				}
				new ManageBussiness().SystemNotice(text);
				client.Player.SendMessage(text);
				client.Player.SavePlayerInfo();
				AbstractPacketLib abstractPacketLib = new AbstractPacketLib(client);
				client.Player.UpdateProperties();
				abstractPacketLib.SendZhanling(client.Player);
				abstractPacketLib.SendZhanlingDispaly(client.Player);
			}
			if (itemAt.Template.Name.Contains("经验战令卡"))
			{
				client.Player.PlayerCharacter.zhanlingExp += itemAt.Template.Property2;
				if (client.Player.PlayerCharacter.zhanlingExp >= 100)
				{
					client.Player.PlayerCharacter.zhanlingExp -= 100;
					PlayerInfo playerCharacter = client.Player.PlayerCharacter;
					int zhanlingLevel = playerCharacter.zhanlingLevel;
					playerCharacter.zhanlingLevel = zhanlingLevel + 1;
					client.Player.SendMessage("恭喜你战令提升了一个等级!");
				}
				client.Player.SavePlayerInfo();
				AbstractPacketLib abstractPacketLib2 = new AbstractPacketLib(client);
				client.Player.UpdateProperties();
				abstractPacketLib2.SendZhanling(client.Player);
				abstractPacketLib2.SendZhanlingDispaly(client.Player);
			}
			if (itemAt != null && itemAt.IsValidItem() && itemAt.Template.CategoryID == 11 && itemAt.Template.Property1 == 66 && itemAt.TemplateID != 112019 && itemAt.TemplateID != 112047 && itemAt.TemplateID != 112100 && itemAt.TemplateID != 112101 && itemAt.TemplateID != 190000 && client.Player.PlayerCharacter.Grade >= itemAt.Template.NeedLevel)
			{
				if (num3 < 1 || num3 > itemAt.Count)
				{
					num3 = 1;
				}
				StringBuilder stringBuilder = new StringBuilder();
				StringBuilder stringBuilder2 = new StringBuilder();
				if (!inventory.RemoveCountFromStack(itemAt, num3))
				{
					return 0;
				}
				Dictionary<int, ItemInfo> dictionary = new Dictionary<int, ItemInfo>();
				stringBuilder2.Append(LanguageMgr.GetTranslation("OpenUpArkHandler.Start", Array.Empty<object>()));
				SpecialItemBoxInfo specialItemBoxInfo = new SpecialItemBoxInfo();
				for (int i2 = 0; i2 < num3; i2++)
				{
					List<ItemInfo> list = new List<ItemInfo>();
					ItemBoxMgr.CreateItemBox(itemAt.TemplateID, list, specialItemBoxInfo);
					packet.ReadBoolean();
					int num4 = packet.ReadInt();
					for (int j = 0; j < num4; j++)
					{
						int num5 = packet.ReadInt() - 1;
						if (num5 >= 0 && num5 < list.Count)
						{
							if (!dictionary.Keys.Contains(list[num5].TemplateID))
							{
								dictionary.Add(list[num5].TemplateID, list[num5]);
							}
							else if (dictionary.Keys.Contains(list[num5].TemplateID))
							{
								dictionary[list[num5].TemplateID].Count += list[num5].Count;
							}
						}
					}
				}
				client.Player.DirectAddValue(specialItemBoxInfo);
				if (stringBuilder.Length > 0)
				{
					stringBuilder.Remove(stringBuilder.Length - 1, 1);
					string[] array = stringBuilder.ToString().Split(new char[] { ',' });
					for (int k = 0; k < array.Length; k++)
					{
						int num6 = 1;
						for (int l = k + 1; l < array.Length; l++)
						{
							if (array[k].Contains(array[l]) && array[l].Length == array[k].Length)
							{
								num6++;
								array[l] = l.ToString();
							}
						}
						if (num6 > 1)
						{
							array[k] = array[k].Remove(array[k].Length - 1, 1);
							string[] array2 = array;
							int num7 = k;
							int num8 = num7;
							array2[num8] += num6.ToString();
						}
						if (array[k] != k.ToString())
						{
							string[] array3 = array;
							int num9 = k;
							int num10 = num9;
							array3[num10] += ",";
							stringBuilder2.Append(array[k]);
						}
					}
				}
				stringBuilder2.Remove(stringBuilder2.Length - 1, 1);
				stringBuilder2.Append(":");
				gspacketIn.WriteString(itemAt.Template.Name);
				gspacketIn.WriteInt((int)((byte)dictionary.Count));
				foreach (ItemInfo itemInfo in dictionary.Values)
				{
					stringBuilder2.Append(itemInfo.Template.Name + "x" + itemInfo.Count.ToString() + ".");
					gspacketIn.WriteInt(itemInfo.TemplateID);
					gspacketIn.WriteInt(itemInfo.Count);
					gspacketIn.WriteBoolean(itemInfo.IsBinds);
					gspacketIn.WriteInt(itemInfo.ValidDate);
					gspacketIn.WriteInt(itemInfo.StrengthenLevel);
					gspacketIn.WriteInt(itemInfo.AttackCompose);
					gspacketIn.WriteInt(itemInfo.DefendCompose);
					gspacketIn.WriteInt(itemInfo.AgilityCompose);
					gspacketIn.WriteInt(itemInfo.LuckCompose);
					if (itemInfo.Template.MaxCount == 1)
					{
						for (int m = 0; m < itemInfo.Count; m++)
						{
							ItemInfo itemInfo2 = itemInfo.Clone();
							itemInfo2.Count = 1;
							client.Player.AddTemplate(itemInfo2, itemInfo.Template.BagType, itemInfo2.Count);
						}
					}
					else
					{
						client.Player.AddTemplate(itemInfo, itemInfo.Template.BagType, itemInfo.Count);
					}
					client.Player.SendItemNotice(itemInfo, itemAt.Template.Name, 2, 3);
				}
				if (dictionary.Count > 0)
				{
					client.Player.SendTCP(gspacketIn);
				}
			}
			if (itemAt != null && itemAt.IsValidItem() && itemAt.Template.CategoryID == 11 && itemAt.Template.Property1 == 6 && itemAt.TemplateID != 112019 && itemAt.TemplateID != 112047 && itemAt.TemplateID != 112100 && itemAt.TemplateID != 112101 && itemAt.TemplateID != 190000 && client.Player.PlayerCharacter.Grade >= itemAt.Template.NeedLevel)
			{
				if (num3 < 1 || num3 > itemAt.Count)
				{
					num3 = 1;
				}
				StringBuilder stringBuilder3 = new StringBuilder();
				StringBuilder stringBuilder4 = new StringBuilder();
				if (!inventory.RemoveCountFromStack(itemAt, num3))
				{
					return 0;
				}
				Dictionary<int, ItemInfo> dictionary2 = new Dictionary<int, ItemInfo>();
				stringBuilder4.Append(LanguageMgr.GetTranslation("OpenUpArkHandler.Start", Array.Empty<object>()));
				SpecialItemBoxInfo specialItemBoxInfo2 = new SpecialItemBoxInfo();
				for (int n = 0; n < num3; n++)
				{
					List<ItemInfo> list2 = new List<ItemInfo>();
					ItemBoxMgr.CreateItemBox(itemAt.TemplateID, list2, specialItemBoxInfo2);
					foreach (ItemInfo itemInfo3 in list2)
					{
						if (!dictionary2.Keys.Contains(itemInfo3.TemplateID))
						{
							dictionary2.Add(itemInfo3.TemplateID, itemInfo3);
						}
						else if (dictionary2.Keys.Contains(itemInfo3.TemplateID))
						{
							dictionary2[itemInfo3.TemplateID].Count += itemInfo3.Count;
						}
					}
				}
				if (itemAt.TemplateID == 101000818)
				{
					if (itemAt.Count < num3)
					{
						client.Player.SendMessage("物品数量不足，无法批量开启。");
						return 0;
					}
					if (!inventory.RemoveCountFromStack(itemAt, num3))
					{
						client.Player.SendMessage("移除物品失败，请稍后再试。");
						return 0;
					}
					client.Player.PlayerCharacter.RelicItemInfo.XiuLianNum += num3;
					client.Player.SendMessage(string.Format("恭喜你获得圣物修炼石*{0}", num3));
					return 0;
				}
				else if (itemAt.TemplateID == 120000353)
				{
					if (!inventory.RemoveCountFromStack(itemAt, num3))
					{
						return 0;
					}
					client.Player.PlayerCharacter.RelicItemInfo.AdvanceNum += num3;
					client.Player.SendMessage("恭喜你获得圣物进阶石*" + num3.ToString());
					return 0;
				}
				else if (itemAt.TemplateID == 101000819)
				{
					if (!inventory.RemoveCountFromStack(itemAt, num3))
					{
						return 0;
					}
					client.Player.PlayerCharacter.RelicItemInfo.GJAdvanceNum += num3;
					client.Player.SendMessage("恭喜你获得高级圣物进阶石*" + num3.ToString());
					return 0;
				}
				else if (itemAt.TemplateID == 101000820)
				{
					if (!inventory.RemoveCountFromStack(itemAt, num3))
					{
						return 0;
					}
					client.Player.PlayerCharacter.RelicItemInfo.ZFNum += num3;
					client.Player.SendMessage("恭喜你获得圣物增幅石*" + num3.ToString());
					return 0;
				}
				else if (itemAt.TemplateID == 101000821)
				{
					if (!inventory.RemoveCountFromStack(itemAt, num3))
					{
						return 0;
					}
					client.Player.PlayerCharacter.RelicItemInfo.GJZFNum += num3;
					client.Player.SendMessage("恭喜你获得高级圣物增幅石*" + num3.ToString());
					return 0;
				}
				else if (itemAt.TemplateID == 386301)
				{
					if (!inventory.RemoveCountFromStack(itemAt, num3))
					{
						return 0;
					}
					client.Player.PlayerCharacter.RelicItemInfo.ZFNum += num3;
					client.Player.SendMessage("恭喜你获得圣物增幅石*" + num3.ToString());
					return 0;
				}
				else if (itemAt.TemplateID == 101000822)
				{
					if (client.Player.PlayerCharacter.RelicItemInfo.OpenCount + num3 > 6)
					{
						client.Player.SendMessage("开孔数量超过最大");
						return 0;
					}
					if (!inventory.RemoveCountFromStack(itemAt, num3))
					{
						return 0;
					}
					client.Player.PlayerCharacter.RelicItemInfo.OpenCount += num3;
					client.Player.SendMessage("恭喜你成功开孔" + num3.ToString());
					return 0;
				}
				else
				{
					if ((itemAt.TemplateID == 101000791 && itemAt.TemplateID <= 101000815) || (itemAt.TemplateID >= 101000823 && itemAt.TemplateID <= 101000877))
					{
						if (client.Player.PlayerCharacter.RelicItem.FirstOrDefault((Sys_User_RelicItemTemplate i) => i.itemID == itemAt.Template.Property2) != null)
						{
							int num11 = num3 * itemAt.Template.Property3;
							client.Player.PlayerCharacter.RelicItemInfo.shopScore += num11;
							client.Player.SendMessage("你获得圣物令*" + num11.ToString());
							return 0;
						}
						Sys_User_RelicItemTemplate newRelic = RelicItemMgr.GetNewRelic(itemAt.Template.Property2, itemAt.Template.Quality - 1, client.Player.PlayerId);
						client.Player.PlayerCharacter.RelicItem.Add(newRelic);
						client.Player.SendMessage("你获得圣物" + itemAt.Name);
					}
					if (itemAt.TemplateID == 1)
					{
						client.Player.PlayerCharacter.FirstPay.GoldMoney += num3;
						client.Player.SendMessage(string.Format("恭喜您获得 {0} 金豆", num3));
					}
					if (itemAt.TemplateID == 2)
					{
						try
						{
							if (client.Player != null && client.Player.PlayerCharacter != null && client.Player.PlayerCharacter.DevilTurn != null)
							{
								client.Player.PlayerCharacter.DevilTurn.HasCount += num3;
								client.Player.PlayerCharacter.DevilTurn.lotteryCount += num3;
								client.Player.SendMessage(string.Format("恭喜您获得 {0} 次恶魔宝藏抽奖次数和 {1} 次恶魔累计次数", num3, num3));
							}
							else
							{
								Console.WriteLine("出现空引用，无法进行恶魔宝藏次数增加操作。");
							}
						}
						catch (Exception ex)
						{
							Console.WriteLine("处理恶魔宝藏次数增加时出现异常：" + ex.Message);
						}
					}
					client.Player.DirectAddValue(specialItemBoxInfo2);
					if (stringBuilder3.Length > 0)
					{
						stringBuilder3.Remove(stringBuilder3.Length - 1, 1);
						string[] array4 = stringBuilder3.ToString().Split(new char[] { ',' });
						for (int num12 = 0; num12 < array4.Length; num12++)
						{
							int num13 = 1;
							for (int num14 = num12 + 1; num14 < array4.Length; num14++)
							{
								if (array4[num12].Contains(array4[num14]) && array4[num14].Length == array4[num12].Length)
								{
									num13++;
									array4[num14] = num14.ToString();
								}
							}
							if (num13 > 1)
							{
								array4[num12] = array4[num12].Remove(array4[num12].Length - 1, 1);
								string[] array5 = array4;
								int num15 = num12;
								int num16 = num15;
								array5[num16] += num13.ToString();
							}
							if (array4[num12] != num12.ToString())
							{
								string[] array6 = array4;
								int num17 = num12;
								int num18 = num17;
								array6[num18] += ",";
								stringBuilder4.Append(array4[num12]);
							}
						}
					}
					stringBuilder4.Remove(stringBuilder4.Length - 1, 1);
					stringBuilder4.Append(":");
					gspacketIn.WriteString(itemAt.Template.Name);
					gspacketIn.WriteInt((int)((byte)dictionary2.Count));
					foreach (ItemInfo itemInfo4 in dictionary2.Values)
					{
						stringBuilder4.Append(itemInfo4.Template.Name + "x" + itemInfo4.Count.ToString() + ".");
						gspacketIn.WriteInt(itemInfo4.TemplateID);
						gspacketIn.WriteInt(itemInfo4.Count);
						gspacketIn.WriteBoolean(itemInfo4.IsBinds);
						gspacketIn.WriteInt(itemInfo4.ValidDate);
						gspacketIn.WriteInt(itemInfo4.StrengthenLevel);
						gspacketIn.WriteInt(itemInfo4.AttackCompose);
						gspacketIn.WriteInt(itemInfo4.DefendCompose);
						gspacketIn.WriteInt(itemInfo4.AgilityCompose);
						gspacketIn.WriteInt(itemInfo4.LuckCompose);
						if (itemInfo4.Template.MaxCount == 1)
						{
							for (int num19 = 0; num19 < itemInfo4.Count; num19++)
							{
								ItemInfo itemInfo5 = itemInfo4.Clone();
								itemInfo5.Count = 1;
								client.Player.AddTemplate(itemInfo5, itemInfo4.Template.BagType, itemInfo5.Count);
							}
						}
						else
						{
							client.Player.AddTemplate(itemInfo4, itemInfo4.Template.BagType, itemInfo4.Count);
						}
						client.Player.SendItemNotice(itemInfo4, itemAt.Template.Name, 2, 3);
					}
					if (dictionary2.Count > 0)
					{
						client.Player.SendTCP(gspacketIn);
					}
				}
			}
			return 1;
		}
	}
}
