package campbattle
{
   import campbattle.data.CampBattleAwardsDataAnalyzer;
   import ddt.CoreManager;
   import ddt.events.CEvent;
   import ddt.events.PkgEvent;
   import ddt.manager.ChatManager;
   import ddt.manager.CheckWeaponManager;
   import ddt.manager.GameInSocketOut;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.TimeManager;
   import ddtActivityIcon.DdtActivityIconManager;
   import ddtActivityIcon.DdtIconTxt;
   import flash.display.MovieClip;
   import flash.events.IEventDispatcher;
   import flash.events.MouseEvent;
   import flash.utils.getTimer;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   
   public class CampBattleManager extends CoreManager
   {
      
      private static var _instance:CampBattleManager;
      
      public static const CAMPBATTLE_INITSECEN:String = "campbattle_initscene";
      
      public var openFlag:Boolean;
      
      public var campViewFlag:Boolean;
      
      private var _isFighting:Boolean;
      
      private var _activityTxt:DdtIconTxt;
      
      private var _entryBtn:MovieClip;
      
      private var _lastCreatTime:int;
      
      private var _endTime:Date;
      
      private var _initPkg:PackageIn;
      
      public var awardsFrameView:Boolean = true;
      
      public var mapID:int;
      
      public var goodsZone:int;
      
      public var goods:Array;
      
      public function CampBattleManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get instance() : CampBattleManager
      {
         if(!_instance)
         {
            _instance = new CampBattleManager();
         }
         return _instance;
      }
      
      public function get isFighting() : Boolean
      {
         return this._isFighting;
      }
      
      public function set isFighting(_arg_1:Boolean) : void
      {
         this._isFighting = _arg_1;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(146,6),this.__onInitSecenHander);
         SocketManager.Instance.addEventListener(PkgEvent.format(146,10),this.__onActionIsOpenHander);
      }
      
      protected function __onInitSecenHander(_arg_1:PkgEvent) : void
      {
         this._initPkg = _arg_1.pkg;
         show();
         this.campViewFlag = true;
      }
      
      override protected function start() : void
      {
         dispatchEvent(new CEvent("campbattle_initscene",this._initPkg));
      }
      
      public function addCampBtn(_arg_1:Boolean = true, _arg_2:String = null) : void
      {
         HallIconManager.instance.updateSwitchHandler("camp",false,_arg_2);
      }
      
      public function deleCanpBtn() : void
      {
         HallIconManager.instance.updateSwitchHandler("camp",false);
      }
      
      private function __onActionIsOpenHander(_arg_1:PkgEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         this.openFlag = _local_3.readBoolean();
         var _local_2:Date = _local_3.readDate();
         this._endTime = _local_3.readDate();
         if(!this.openFlag)
         {
            this.deleCanpBtn();
            DdtActivityIconManager.Instance.currObj = null;
            ChatManager.Instance.sysChatAmaranth(LanguageMgr.GetTranslation("ddt.campBattle.close"));
            SocketManager.Instance.out.outCampBatttle();
         }
         else
         {
            this.addCampBtn();
            ChatManager.Instance.sysChatAmaranth(LanguageMgr.GetTranslation("ddt.campBattle.open"));
         }
      }
      
      public function __onCampBtnHander(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.playButtonSound();
         if(PlayerManager.Instance.Self.Grade < 30)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",30));
            return;
         }
         CheckWeaponManager.instance.setFunction(this,this.__onCampBtnHander,[_arg_1]);
         if(CheckWeaponManager.instance.isNoWeapon())
         {
            CheckWeaponManager.instance.showAlert();
            return;
         }
         if(getTimer() - this._lastCreatTime > 1000)
         {
            this._lastCreatTime = getTimer();
            GameInSocketOut.sendSingleRoomBegin(5);
         }
      }
      
      public function get toEndTime() : int
      {
         if(!this._endTime)
         {
            return 0;
         }
         return this.getDateHourTime(this._endTime) - this.getDateHourTime(TimeManager.Instance.Now());
      }
      
      private function getDateHourTime(_arg_1:Date) : int
      {
         return _arg_1.hours * 3600 + _arg_1.minutes * 60 + _arg_1.seconds;
      }
      
      public function templateDataSetup(_arg_1:CampBattleAwardsDataAnalyzer) : void
      {
         this.goods = this.returnGoodsArray(_arg_1._dataList);
      }
      
      private function returnGoodsArray(_arg_1:Array) : Array
      {
         var _local_3:int = 0;
         var _local_2:Array = [];
         var _local_9:Array = [];
         var _local_7:Array = [];
         var _local_6:Array = [];
         var _local_5:Array = [];
         var _local_13:Array = [];
         var _local_12:Array = [];
         var _local_11:Array = [];
         var _local_10:Array = [];
         var _local_4:Array = [];
         var _local_8:Array = [];
         _local_3 = 0;
         while(_local_3 < _arg_1.length)
         {
            if(_arg_1[_local_3][0].MinRank == 1 && _arg_1[_local_3][0].MaxRank == 1)
            {
               _local_9.push(_arg_1[_local_3][0]);
            }
            else if(_arg_1[_local_3][0].MinRank == 2 && _arg_1[_local_3][0].MaxRank == 2)
            {
               _local_7.push(_arg_1[_local_3][0]);
            }
            else if(_arg_1[_local_3][0].MinRank == 3 && _arg_1[_local_3][0].MaxRank == 3)
            {
               _local_6.push(_arg_1[_local_3][0]);
            }
            else if(_arg_1[_local_3][0].MinRank == 4 && _arg_1[_local_3][0].MaxRank == 4)
            {
               _local_5.push(_arg_1[_local_3][0]);
            }
            else if(_arg_1[_local_3][0].MinRank == 1 && _arg_1[_local_3][0].MaxRank == 10)
            {
               _local_13.push(_arg_1[_local_3][0]);
            }
            else if(_arg_1[_local_3][0].MinRank == 11 && _arg_1[_local_3][0].MaxRank == 20)
            {
               _local_12.push(_arg_1[_local_3][0]);
            }
            else if(_arg_1[_local_3][0].MinRank == 21 && _arg_1[_local_3][0].MaxRank == 30)
            {
               _local_11.push(_arg_1[_local_3][0]);
            }
            else if(_arg_1[_local_3][0].MinRank == 31 && _arg_1[_local_3][0].MaxRank == 40)
            {
               _local_10.push(_arg_1[_local_3][0]);
            }
            else if(_arg_1[_local_3][0].MinRank == 41 && _arg_1[_local_3][0].MaxRank == 50)
            {
               _local_4.push(_arg_1[_local_3][0]);
            }
            else if(_arg_1[_local_3][0].MinRank == 51 && _arg_1[_local_3][0].MaxRank == 60)
            {
               _local_8.push(_arg_1[_local_3][0]);
            }
            _local_3++;
         }
         _local_2.push(_local_9,_local_7,_local_6,_local_5,_local_13,_local_12,_local_11,_local_10,_local_4,_local_8);
         return _local_2;
      }
      
      public function getLevelGoodsItems(_arg_1:int) : Array
      {
         return this.goods[_arg_1];
      }
      
      public function get initPkg() : PackageIn
      {
         return this._initPkg;
      }
   }
}

