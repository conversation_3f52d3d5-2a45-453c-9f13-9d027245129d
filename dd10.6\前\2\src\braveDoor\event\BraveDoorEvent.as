package braveDoor.event
{
   import BombTurnTable.event.TurnTableEvent;
   import flash.events.Event;
   
   public class BraveDoorEvent extends Event
   {
      
      private var _data:Object;
      
      public function BraveDoorEvent(_arg_1:String, _arg_2:Object = null, _arg_3:<PERSON><PERSON><PERSON> = false, _arg_4:<PERSON><PERSON><PERSON> = false)
      {
         this._data = _arg_2;
         super(_arg_1,_arg_3,_arg_4);
      }
      
      public function get data() : Object
      {
         return this._data;
      }
      
      override public function clone() : Event
      {
         return new TurnTableEvent(type,this._data,bubbles,cancelable);
      }
   }
}

