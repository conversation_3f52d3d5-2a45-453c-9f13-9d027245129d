package com.pickgliss.ui.controls.list
{
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.ComponentSetting;
   import com.pickgliss.ui.controls.cell.IDropListCell;
   import com.pickgliss.ui.controls.container.BoxContainer;
   import com.pickgliss.ui.core.Component;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.DisplayObject;
   import flash.display.InteractiveObject;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   
   public class DropList extends Component implements Disposeable
   {
      
      public static const SELECTED:String = "selected";
      
      public static const P_backgound:String = "backgound";
      
      public static const P_container:String = "container";
      
      private var _backStyle:String;
      
      private var _backGround:DisplayObject;
      
      private var _cellStyle:String;
      
      private var _containerStyle:String;
      
      private var _container:BoxContainer;
      
      private var _targetDisplay:IDropListTarget;
      
      private var _showLength:int;
      
      private var _dataList:Array;
      
      private var _items:Vector.<IDropListCell>;
      
      private var _currentSelectedIndex:int;
      
      private var _preItemIdx:int;
      
      private var _cellHeight:int;
      
      private var _cellWidth:int;
      
      private var _isListening:Boolean;
      
      private var _canUseEnter:Boolean = true;
      
      public function DropList()
      {
         super();
      }
      
      override protected function init() : void
      {
         this._items = new Vector.<IDropListCell>();
      }
      
      public function set container(_arg_1:BoxContainer) : void
      {
         if(Boolean(this._container))
         {
            ObjectUtils.disposeObject(this._container);
            this._container = null;
         }
         this._container = _arg_1;
         onPropertiesChanged("container");
      }
      
      public function set containerStyle(_arg_1:String) : void
      {
         if(this._containerStyle == _arg_1)
         {
            return;
         }
         this._containerStyle = _arg_1;
         if(Boolean(this._container))
         {
            ObjectUtils.disposeObject(this._container);
            this._container = null;
         }
         this.container = ComponentFactory.Instance.creat(this._containerStyle);
      }
      
      public function set cellStyle(_arg_1:String) : void
      {
         if(this._cellStyle == _arg_1)
         {
            return;
         }
         this._cellStyle = _arg_1;
      }
      
      public function set dataList(_arg_1:Array) : void
      {
         var _local_3:int = 0;
         if(!_arg_1)
         {
            if(Boolean(parent))
            {
               parent.removeChild(this);
            }
            return;
         }
         if(Boolean(this._targetDisplay.parent))
         {
            this._targetDisplay.parent.addChild(this);
         }
         this._dataList = _arg_1;
         var _local_2:int = Math.min(this._dataList.length,this._showLength);
         _local_3 = 0;
         while(_local_3 < _local_2)
         {
            this._items[_local_3].setCellValue(this._dataList[_local_3]);
            if(!this._container.contains(this._items[_local_3].asDisplayObject()))
            {
               this._container.addChild(this._items[_local_3].asDisplayObject());
            }
            _local_3++;
         }
         if(_local_2 == 0)
         {
            this._items[0].setCellValue(null);
            if(!this._container.contains(this._items[_local_3].asDisplayObject()))
            {
               this._container.addChild(this._items[_local_3].asDisplayObject());
            }
            _local_2 = 1;
         }
         _local_3 = _local_2;
         while(_local_3 < this._showLength)
         {
            if(this._container.contains(this._items[_local_3].asDisplayObject()))
            {
               this._container.removeChild(this._items[_local_3].asDisplayObject());
            }
            _local_3++;
         }
         this.updateBg();
         this.unSelectedAllItems();
         this._currentSelectedIndex = 0;
         this._items[this._currentSelectedIndex].selected = true;
      }
      
      private function updateBg() : void
      {
         if(this._container.numChildren == 0)
         {
            if(contains(this._backGround))
            {
               removeChild(this._backGround);
            }
         }
         else
         {
            this._backGround.width = this._cellWidth + 2 * this._container.x + 20;
            this._backGround.height = this._container.numChildren * (this._cellHeight + this._container.spacing) - this._container.spacing + 2 * this._container.y;
            addChildAt(this._backGround,0);
         }
      }
      
      private function getHightLightItemIdx() : int
      {
         var _local_1:int = 0;
         _local_1 = 0;
         while(_local_1 < this._showLength)
         {
            if(this._items[_local_1].selected)
            {
               return _local_1;
            }
            _local_1++;
         }
         return 0;
      }
      
      private function unSelectedAllItems() : int
      {
         var _local_1:int = 0;
         var _local_2:int = 0;
         _local_2 = 0;
         while(_local_2 < this._showLength)
         {
            if(this._items[_local_2].selected)
            {
               _local_1 = _local_2;
            }
            this._items[_local_2].selected = false;
            _local_2++;
         }
         return _local_1;
      }
      
      private function updateItemValue(_arg_1:Boolean = false) : void
      {
         var _local_2:int = 0;
         _local_2 = 0;
         while(_local_2 < this._showLength)
         {
            this._items[_local_2].setCellValue(this._dataList[this._currentSelectedIndex - this.getHightLightItemIdx() + _local_2]);
            _local_2++;
         }
      }
      
      private function setHightLightItem(_arg_1:Boolean = false) : void
      {
         var _local_2:int = 0;
         if(this._dataList.length > 0)
         {
            _local_2 = this.getHightLightItemIdx();
            if(!_arg_1)
            {
               if(_local_2 < this._showLength - 1)
               {
                  this.unSelectedAllItems();
                  _local_2++;
               }
               else if(_local_2 >= this._showLength - 1)
               {
                  this.updateItemValue();
               }
            }
            if(_arg_1)
            {
               if(_local_2 > 0)
               {
                  this.unSelectedAllItems();
                  _local_2--;
               }
               else if(_local_2 == 0)
               {
                  this.updateItemValue(true);
               }
            }
            this._items[_local_2].selected = true;
         }
         else
         {
            this._currentSelectedIndex = 0;
         }
         this.setTargetValue();
      }
      
      override protected function addChildren() : void
      {
         super.addChildren();
         if(Boolean(this._backGround))
         {
            addChild(this._backGround);
         }
         if(Boolean(this._container))
         {
            addChild(this._container);
         }
      }
      
      public function set targetDisplay(_arg_1:IDropListTarget) : void
      {
         if(_arg_1 == this._targetDisplay)
         {
            return;
         }
         this._targetDisplay = _arg_1;
         this._targetDisplay.addEventListener("keyDown",this.__onKeyDown);
         this._targetDisplay.addEventListener("removedFromStage",this.__onRemoveFromStage);
      }
      
      private function __onRemoveFromStage(_arg_1:Event) : void
      {
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
      
      public function set showLength(_arg_1:int) : void
      {
         var _local_2:* = undefined;
         if(this._showLength == _arg_1)
         {
            return;
         }
         this._showLength = _arg_1;
         while(this._container.numChildren > this._showLength)
         {
            this._container.removeChild(this._items.pop() as DisplayObject);
         }
         while(this._container.numChildren < this._showLength)
         {
            if(this._items.length > this._container.numChildren)
            {
               this._container.addChild(this._items[this._container.numChildren].asDisplayObject());
            }
            else
            {
               _local_2 = ComponentFactory.Instance.creat(this._cellStyle);
               _local_2.addEventListener("mouseOver",this.__onCellMouseOver);
               _local_2.addEventListener("click",this.__onCellMouseClick);
               this._items.push(_local_2);
               this._container.addChild(_local_2);
            }
         }
         this._cellHeight = _local_2.height;
         this._cellWidth = _local_2.width;
         this.updateBg();
      }
      
      private function __onCellMouseClick(_arg_1:MouseEvent) : void
      {
         ComponentSetting.PLAY_SOUND_FUNC("008");
         this.setTargetValue();
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
         dispatchEvent(new Event("selected"));
      }
      
      private function __onCellMouseOver(_arg_1:MouseEvent) : void
      {
         var _local_3:int = this.unSelectedAllItems();
         var _local_2:int = int(this._items.indexOf(_arg_1.currentTarget as IDropListCell));
         this._currentSelectedIndex += _local_2 - _local_3;
         _arg_1.currentTarget.selected = true;
      }
      
      override protected function onProppertiesUpdate() : void
      {
         super.onProppertiesUpdate();
         if(Boolean(_changedPropeties["backgound"]) || Boolean(_changedPropeties["container"]))
         {
            this.addChildren();
         }
      }
      
      private function __onKeyDown(_arg_1:KeyboardEvent) : void
      {
         if(this._dataList == null)
         {
            return;
         }
         _arg_1.stopImmediatePropagation();
         _arg_1.stopPropagation();
         if(!this._isListening && _arg_1.keyCode == 13)
         {
            this._isListening = true;
            StageReferance.stage.addEventListener("enterFrame",this.__setSelection);
         }
         switch(_arg_1.keyCode)
         {
            case 38:
               ComponentSetting.PLAY_SOUND_FUNC("008");
               if(this._currentSelectedIndex == 0)
               {
                  return;
               }
               --this._currentSelectedIndex;
               this.setHightLightItem(true);
               return;
               break;
            case 40:
               ComponentSetting.PLAY_SOUND_FUNC("008");
               if(this._currentSelectedIndex == this._dataList.length - 1)
               {
                  return;
               }
               ++this._currentSelectedIndex;
               this.setHightLightItem();
               return;
               break;
            case 13:
               if(this._canUseEnter == false)
               {
                  return;
               }
               ComponentSetting.PLAY_SOUND_FUNC("008");
               if(Boolean(parent))
               {
                  parent.removeChild(this);
               }
               this._targetDisplay.setValue(this._dataList[this._currentSelectedIndex]);
               dispatchEvent(new Event("selected"));
               return;
               break;
            default:
               return;
         }
      }
      
      public function set canUseEnter(_arg_1:Boolean) : void
      {
         this._canUseEnter = _arg_1;
      }
      
      public function get canUseEnter() : Boolean
      {
         return this._canUseEnter;
      }
      
      public function set currentSelectedIndex(_arg_1:int) : void
      {
         if(this._dataList == null)
         {
            return;
         }
         ComponentSetting.PLAY_SOUND_FUNC("008");
         if(this._currentSelectedIndex == this._dataList.length - 1 || this._currentSelectedIndex == 0)
         {
            return;
         }
         this._currentSelectedIndex += _arg_1;
         this.setHightLightItem();
      }
      
      private function setTargetValue() : void
      {
         if(!this._targetDisplay.parent)
         {
            this._targetDisplay.parent.addChild(this);
         }
         if(Boolean(this._dataList))
         {
            this._targetDisplay.setValue(this._dataList[this._currentSelectedIndex]);
         }
      }
      
      private function __setSelection(_arg_1:Event) : void
      {
         if(this._targetDisplay.caretIndex == this._targetDisplay.getValueLength())
         {
            this._isListening = false;
            StageReferance.stage.removeEventListener("enterFrame",this.__setSelection);
         }
         else
         {
            this._targetDisplay.setCursor(this._targetDisplay.getValueLength());
         }
      }
      
      public function set backStyle(_arg_1:String) : void
      {
         if(this._backStyle == _arg_1)
         {
            return;
         }
         this._backStyle = _arg_1;
         this.backgound = ComponentFactory.Instance.creat(this._backStyle);
      }
      
      public function set backgound(_arg_1:DisplayObject) : void
      {
         if(this._backGround == _arg_1)
         {
            return;
         }
         ObjectUtils.disposeObject(this._backGround);
         this._backGround = _arg_1;
         if(this._backGround is InteractiveObject)
         {
            InteractiveObject(this._backGround).mouseEnabled = true;
         }
         onPropertiesChanged("backgound");
      }
      
      override public function dispose() : void
      {
         var _local_1:int = 0;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
         StageReferance.stage.removeEventListener("keyDown",this.__onKeyDown);
         StageReferance.stage.removeEventListener("enterFrame",this.__setSelection);
         this._targetDisplay.removeEventListener("keyDown",this.__onKeyDown);
         if(Boolean(this._backGround))
         {
            ObjectUtils.disposeObject(this._backGround);
         }
         this._backGround = null;
         if(Boolean(this._container))
         {
            ObjectUtils.disposeObject(this._container);
         }
         this._container = null;
         if(Boolean(this._targetDisplay))
         {
            ObjectUtils.disposeObject(this._targetDisplay);
         }
         this._targetDisplay = null;
         _local_1 = 0;
         while(_local_1 < this._items.length)
         {
            if(Boolean(this._items[_local_1]))
            {
               ObjectUtils.disposeObject(this._items[_local_1]);
            }
            this._items[_local_1] = null;
            _local_1++;
         }
         this._dataList = null;
         super.dispose();
      }
   }
}

