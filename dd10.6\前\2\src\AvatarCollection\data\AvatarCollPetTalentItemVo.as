package AvatarCollection.data
{
   import AvatarCollection.AvatarCollectionManager;
   import ddt.manager.LanguageMgr;
   
   public class AvatarCollPetTalentItemVo
   {
      
      public var type:int;
      
      public var level:int;
      
      public var groupID:int;
      
      public var templateName:String;
      
      public var needCount:int;
      
      public var attack:int;
      
      public var defence:int;
      
      public var agility:int;
      
      public var luck:int;
      
      public var blood:int;
      
      public function AvatarCollPetTalentItemVo()
      {
         super();
      }
      
      public function getProArr() : Array
      {
         var _local_1:Array = [];
         var _local_2:Array = LanguageMgr.GetTranslation("tank.newCard.ProName").split(",");
         if(this.blood >= 0)
         {
            _local_1.push({
               "proName":_local_2[0],
               "count":this.blood
            });
         }
         if(this.attack >= 0)
         {
            _local_1.push({
               "proName":_local_2[1],
               "count":this.attack
            });
         }
         if(this.defence >= 0)
         {
            _local_1.push({
               "proName":_local_2[2],
               "count":this.defence
            });
         }
         if(this.agility >= 0)
         {
            _local_1.push({
               "proName":_local_2[3],
               "count":this.agility
            });
         }
         if(this.luck >= 0)
         {
            _local_1.push({
               "proName":_local_2[4],
               "count":this.luck
            });
         }
         return _local_1;
      }
      
      public function get isActive() : Boolean
      {
         var _local_1:Boolean = false;
         return AvatarCollectionManager.instance.getPetTalentMaxLvByType(this.type) > 0;
      }
   }
}

