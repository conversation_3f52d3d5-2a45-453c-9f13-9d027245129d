package cloudBuyLottery.view
{
   import bagAndInfo.cell.BagCell;
   import baglocked.BaglockedManager;
   import cloudBuyLottery.CloudBuyLotteryManager;
   import com.greensock.TweenLite;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.core.Component;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.LeavePageManager;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   import store.HelpFrame;
   
   public class CloudBuyLotteryFrame extends Frame
   {
      
      public static var logFrameFlag:Boolean;
      
      public static const MOVE_SPEED:Number = 0.8;
      
      private var _bg:Bitmap;
      
      private var showTimes:MovieClip;
      
      private var timeArray:Array;
      
      private var _timer:Timer;
      
      private var _helpBtn:BaseButton;
      
      private var _jubaoBtn:BaseButton;
      
      private var _buyBtn:BaseButton;
      
      private var _finish:Bitmap;
      
      private var _expBar:ExpBar;
      
      private var _luckNumTxt:FilterFrameText;
      
      private var _chanceTxt:FilterFrameText;
      
      private var _showBuyNumTxt:FilterFrameText;
      
      private var _buyNumTxt:FilterFrameText;
      
      private var _buyGoodsMoneyTxt:FilterFrameText;
      
      private var _helpTxt:FilterFrameText;
      
      private var _tipsframe:BaseAlerFrame;
      
      private var _inputBg:Bitmap;
      
      private var _inputText:FilterFrameText;
      
      private var _moneyNumText:FilterFrameText;
      
      private var _maxBtn:SimpleBitmapButton;
      
      private var _text:FilterFrameText;
      
      private var _cell:BagCell;
      
      private var _logTxt:FilterFrameText;
      
      private var _logSprite:Sprite;
      
      private var winningLogFrame:TheWinningLog;
      
      private var _infoWidth:Number;
      
      private var _numberK:Bitmap;
      
      private var _numberKTxt:FilterFrameText;
      
      private var luckLooteryFrame:IndividualLottery;
      
      private var _buyGoodsSprite:Component;
      
      public function CloudBuyLotteryFrame()
      {
         super();
         this.initView();
         this.addEvent();
      }
      
      private function initView() : void
      {
         _titleText = LanguageMgr.GetTranslation("CloudBuyLotteryFrame.title");
         this._bg = ComponentFactory.Instance.creatBitmap("asset.cloudbuy.bg");
         this.showTimes = ClassUtils.CreatInstance("asset.cloudbuy.showTime") as MovieClip;
         PositionUtils.setPos(this.showTimes,"CloudBuyLotteryFrame.showTimesMC");
         this._helpBtn = ComponentFactory.Instance.creat("CloudBuyLotteryFrame.helpBtn");
         this._jubaoBtn = ComponentFactory.Instance.creat("CloudBuyLotteryFrame.jubaoBtn");
         this._buyBtn = ComponentFactory.Instance.creat("CloudBuyLotteryFrame.buyBtn");
         this._finish = ComponentFactory.Instance.creatBitmap("asset.cloudbuy.finish");
         this._expBar = ComponentFactory.Instance.creatComponentByStylename("CloudBuyLotteryFrame.expBar");
         this._luckNumTxt = ComponentFactory.Instance.creatComponentByStylename("CloudBuyLotteryFrame.luckNumTxt");
         this._luckNumTxt.text = CloudBuyLotteryManager.Instance.model.luckCount.toString();
         this._chanceTxt = ComponentFactory.Instance.creatComponentByStylename("CloudBuyLotteryFrame.chanceTxt");
         this._chanceTxt.text = CloudBuyLotteryManager.Instance.model.luckCount + "/" + CloudBuyLotteryManager.Instance.model.maxNum;
         this._showBuyNumTxt = ComponentFactory.Instance.creatComponentByStylename("CloudBuyLotteryFrame.showBuyNumTxt");
         this._showBuyNumTxt.text = LanguageMgr.GetTranslation("CloudBuyLotteryFrame.showBuyNum");
         this._buyNumTxt = ComponentFactory.Instance.creatComponentByStylename("CloudBuyLotteryFrame.buyNumTxt");
         this._buyNumTxt.text = CloudBuyLotteryManager.Instance.model.currentNum.toString();
         this._buyGoodsMoneyTxt = ComponentFactory.Instance.creatComponentByStylename("CloudBuyLotteryFrame.buyGoodsMoneyTxt");
         this._buyGoodsMoneyTxt.text = LanguageMgr.GetTranslation("cloudBuyLotteryFrame.buyGoodsMoneyTxt",CloudBuyLotteryManager.Instance.model.buyMoney.toString());
         this._helpTxt = ComponentFactory.Instance.creatComponentByStylename("CloudBuyLotteryFrame.helpTxt");
         this._helpTxt.text = LanguageMgr.GetTranslation("CloudBuyLotteryFrame.help");
         this._logTxt = ComponentFactory.Instance.creatComponentByStylename("CloudBuyLotteryFrame.logTxt");
         this._logTxt.text = LanguageMgr.GetTranslation("CloudBuyLotteryFrame.log");
         this._logSprite = new Sprite();
         this._logSprite.addChild(this._logTxt);
         this._logSprite.buttonMode = true;
         PositionUtils.setPos(this._logSprite,"CloudBuyLotteryFrame.logSprite");
         this._numberK = ComponentFactory.Instance.creatBitmap("asset.cloudbuy.numberK");
         this._numberKTxt = ComponentFactory.Instance.creatComponentByStylename("CloudBuyLotteryFrame.numberKTxt");
         this._numberKTxt.text = CloudBuyLotteryManager.Instance.model.remainTimes.toString();
         addToContent(this._bg);
         addToContent(this.showTimes);
         addToContent(this._helpBtn);
         addToContent(this._jubaoBtn);
         addToContent(this._buyBtn);
         addToContent(this._finish);
         addToContent(this._expBar);
         addToContent(this._luckNumTxt);
         addToContent(this._chanceTxt);
         addToContent(this._showBuyNumTxt);
         addToContent(this._buyNumTxt);
         addToContent(this._buyGoodsMoneyTxt);
         addToContent(this._helpTxt);
         addToContent(this._logSprite);
         addToContent(this._numberK);
         addToContent(this._numberKTxt);
         this.timesNum();
         this.luckGoodsCell(CloudBuyLotteryManager.Instance.model.templateId);
         this.buyGoodsCell(CloudBuyLotteryManager.Instance.model.buyGoodsIDArray);
         this._timer = new Timer(1000);
         this._timer.addEventListener("timer",this.__updateTimes);
         this._timer.start();
      }
      
      private function addEvent() : void
      {
         addEventListener("response",this.__responseHandler);
         this._helpBtn.addEventListener("click",this.__onHelpClick);
         this._buyBtn.addEventListener("click",this.__onBuyClick);
         this._jubaoBtn.addEventListener("click",this.__onJuBaoClick);
         this._logSprite.addEventListener("click",this.__onLogClick);
         CloudBuyLotteryManager.Instance.addEventListener("updateInfo",this.__updateInfo);
         CloudBuyLotteryManager.Instance.addEventListener("frmeupdate",this.__frmeUpdateInfo);
      }
      
      private function removeEvent() : void
      {
         removeEventListener("response",this.__responseHandler);
         this._helpBtn.removeEventListener("click",this.__onHelpClick);
         this._buyBtn.removeEventListener("click",this.__onBuyClick);
         this._jubaoBtn.removeEventListener("click",this.__onJuBaoClick);
         this._logSprite.removeEventListener("click",this.__onLogClick);
         CloudBuyLotteryManager.Instance.removeEventListener("updateInfo",this.__updateInfo);
         CloudBuyLotteryManager.Instance.removeEventListener("frmeupdate",this.__frmeUpdateInfo);
      }
      
      override public function set width(_arg_1:Number) : void
      {
         super.width = _arg_1;
         this._infoWidth = _arg_1;
      }
      
      private function __updateInfo(_arg_1:Event) : void
      {
         this.timesNum();
         this.luckGoodsCell(CloudBuyLotteryManager.Instance.model.templateId);
         this.buyGoodsCell(CloudBuyLotteryManager.Instance.model.buyGoodsIDArray);
         this._buyNumTxt.text = CloudBuyLotteryManager.Instance.model.currentNum.toString();
         this._chanceTxt.text = CloudBuyLotteryManager.Instance.model.luckCount + "/" + CloudBuyLotteryManager.Instance.model.maxNum;
         this._luckNumTxt.text = CloudBuyLotteryManager.Instance.model.luckCount.toString();
         this._numberKTxt.text = CloudBuyLotteryManager.Instance.model.remainTimes.toString();
         var _local_2:int = CloudBuyLotteryManager.Instance.model.maxNum - CloudBuyLotteryManager.Instance.model.currentNum;
         CloudBuyLotteryManager.Instance.expBar.initBar(_local_2,CloudBuyLotteryManager.Instance.model.maxNum);
      }
      
      private function __frmeUpdateInfo(_arg_1:Event) : void
      {
         this._numberKTxt.text = CloudBuyLotteryManager.Instance.model.remainTimes.toString();
      }
      
      private function __onLogClick(_arg_1:MouseEvent) : void
      {
         var _local_2:int = 0;
         if(!logFrameFlag)
         {
            this.winningLogFrame = ComponentFactory.Instance.creatComponentByStylename("winningLogFrame.frame");
            this.winningLogFrame.addEventListener("response",this.__onclose);
            addChildAt(this.winningLogFrame,0);
            _local_2 = this._infoWidth + this.winningLogFrame.width;
            TweenLite.to(this,0.8,{"x":(StageReferance.stage.stageWidth - _local_2) / 2});
            TweenLite.to(this.winningLogFrame,0.8,{"x":this._infoWidth});
            logFrameFlag = true;
         }
         else
         {
            this.hideFrame();
         }
      }
      
      public function hideFrame() : void
      {
         if(Boolean(this.winningLogFrame))
         {
            TweenLite.to(this,0.8,{"x":(StageReferance.stage.stageWidth - this._infoWidth) / 2});
            TweenLite.to(this.winningLogFrame,0.8,{
               "x":this.winningLogFrame.width,
               "onComplete":this.releaseRight,
               "onCompleteParams":[this.winningLogFrame]
            });
            this.winningLogFrame = null;
         }
      }
      
      private function releaseRight(_arg_1:Frame) : void
      {
         logFrameFlag = false;
         if(Boolean(_arg_1))
         {
            _arg_1.removeEventListener("response",this.__onclose);
         }
         ObjectUtils.disposeObject(_arg_1);
      }
      
      protected function __onclose(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
            case 4:
               this.hideFrame();
         }
      }
      
      private function __onBuyClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(!CloudBuyLotteryManager.Instance.model.isGame)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("cloudBuyLottery.isGameOver"));
            return;
         }
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         if(PlayerManager.Instance.Self.Money < CloudBuyLotteryManager.Instance.model.buyMoney)
         {
            LeavePageManager.showFillFrame();
            return;
         }
         this._tipsframe = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("CloudBuyLotteryFrame.buy.tips"),"","",LanguageMgr.GetTranslation("cancel"),true,true,false,2,null,"SimpleAlert2");
         this._tipsframe.width = 360;
         this._tipsframe.height = 200;
         this._text = ComponentFactory.Instance.creatComponentByStylename("CloudBuyLotteryFrame.tipsframeText");
         this._text.text = LanguageMgr.GetTranslation("CloudBuyLotteryFrame.autoOpenCount.text");
         PositionUtils.setPos(this._text,"CloudBuyLotteryFrame.autoOpenCount.textPos");
         this._inputBg = ComponentFactory.Instance.creatBitmap("bagAndInfo.openBatchView.inputBg");
         this._inputText = ComponentFactory.Instance.creatComponentByStylename("CloudBuyLotteryFrame.inputTxt");
         this._inputText.text = "1";
         PositionUtils.setPos(this._inputBg,"CloudBuyLotteryFrame.autoOpenCount.textPos1");
         PositionUtils.setPos(this._inputText,"CloudBuyLotteryFrame.autoOpenCount.textPos2");
         this._maxBtn = ComponentFactory.Instance.creatComponentByStylename("cloudbuy.openBatchView.maxBtn");
         PositionUtils.setPos(this._maxBtn,"CloudBuyLotteryFrame.autoOpenCount.textPos3");
         this._moneyNumText = ComponentFactory.Instance.creatComponentByStylename("CloudBuyLotteryFrame.moneyNumText");
         this._tipsframe.addToContent(this._text);
         this._tipsframe.addToContent(this._inputBg);
         this._tipsframe.addToContent(this._inputText);
         this._tipsframe.addToContent(this._maxBtn);
         this._tipsframe.addToContent(this._moneyNumText);
         this._tipsframe.addEventListener("response",this.__onResponse);
         this._maxBtn.addEventListener("click",this.changeMaxHandler,false,0,true);
         this._inputText.addEventListener("change",this.inputTextChangeHandler,false,0,true);
         this.showMoneyNum();
      }
      
      private function changeMaxHandler(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         this._inputText.text = this._buyNumTxt.text;
         this.showMoneyNum();
      }
      
      private function inputTextChangeHandler(_arg_1:Event) : void
      {
         var _local_3:int = int(this._buyNumTxt.text);
         var _local_2:int = int(this._inputText.text);
         if(_local_2 > _local_3)
         {
            this._inputText.text = _local_3.toString();
         }
         else if(_local_2 < 1)
         {
            this._inputText.text = "1";
         }
         this.showMoneyNum();
      }
      
      private function showMoneyNum() : void
      {
         var _local_1:int = int(this._inputText.text) * CloudBuyLotteryManager.Instance.model.buyMoney;
         this._moneyNumText.htmlText = LanguageMgr.GetTranslation("CloudBuyLotteryFrame.moneyNumText.LG",_local_1);
      }
      
      private function __onResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:int = int(this._inputText.text);
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            SocketManager.Instance.out.sendYGBuyGoods(_local_2);
         }
         if(Boolean(this._tipsframe))
         {
            this._tipsframe.removeEventListener("response",this.__onResponse);
            ObjectUtils.disposeAllChildren(this._tipsframe);
            ObjectUtils.disposeObject(this._tipsframe);
            this._tipsframe = null;
         }
      }
      
      private function __onJuBaoClick(_arg_1:MouseEvent) : void
      {
         if(this.luckLooteryFrame != null)
         {
            ObjectUtils.disposeObject(this.luckLooteryFrame);
            this.luckLooteryFrame = null;
         }
         if(this.luckLooteryFrame == null)
         {
            this.luckLooteryFrame = ComponentFactory.Instance.creatComponentByStylename("individualLottery.frame");
            LayerManager.Instance.addToLayer(this.luckLooteryFrame,3,true,1);
         }
      }
      
      private function luckGoodsCell(_arg_1:int) : void
      {
         var _local_3:ItemTemplateInfo = ItemManager.Instance.getTemplateById(_arg_1) as ItemTemplateInfo;
         var _local_2:InventoryItemInfo = new InventoryItemInfo();
         ObjectUtils.copyProperties(_local_2,_local_3);
         _local_2.ValidDate = CloudBuyLotteryManager.Instance.model.validDate;
         _local_2.StrengthenLevel = CloudBuyLotteryManager.Instance.model.property[0];
         _local_2.AttackCompose = CloudBuyLotteryManager.Instance.model.property[1];
         _local_2.DefendCompose = CloudBuyLotteryManager.Instance.model.property[2];
         _local_2.LuckCompose = CloudBuyLotteryManager.Instance.model.property[3];
         _local_2.AgilityCompose = CloudBuyLotteryManager.Instance.model.property[4];
         _local_2.IsBinds = true;
         if(this._cell != null)
         {
            ObjectUtils.disposeObject(this._cell);
            this._cell = null;
         }
         this._cell = new BagCell(0,_local_2);
         PositionUtils.setPos(this._cell,"CloudBuyLotteryFrame.cellPos");
         this._cell.setContentSize(78,78);
         this._cell.setCount(CloudBuyLotteryManager.Instance.model.templatedIdCount);
         this._cell.setBgVisible(false);
         addToContent(this._cell);
      }
      
      private function buyGoodsCell(_arg_1:Array) : void
      {
         var _local_7:int = 0;
         var _local_2:int = 0;
         var _local_5:* = null;
         var _local_3:* = null;
         var _local_6:String = "";
         _local_7 = 0;
         while(_local_7 < _arg_1.length)
         {
            _local_5 = ItemManager.Instance.getTemplateById(_arg_1[_local_7]) as ItemTemplateInfo;
            _local_3 = new InventoryItemInfo();
            ObjectUtils.copyProperties(_local_3,_local_5);
            _local_2 = int(CloudBuyLotteryManager.Instance.model.buyGoodsCountArray[_local_7]);
            _local_6 += LanguageMgr.GetTranslation("cloudBuyLotteryFrame.buyGoodsTip",_local_3.Name,_local_2);
            _local_7++;
         }
         this._buyGoodsSprite = ComponentFactory.Instance.creatComponentByStylename("cloudBuyLotteryFrame.tipData");
         this._buyGoodsSprite.tipData = _local_6.substring(0,_local_6.length - 1);
         this._buyGoodsSprite.buttonMode = true;
         var _local_4:Bitmap = ComponentFactory.Instance.creatBitmap("asset.IndividualLottery.goods");
         this._buyGoodsSprite.addChild(_local_4);
         addToContent(this._buyGoodsSprite);
      }
      
      private function __updateTimes(_arg_1:TimerEvent) : void
      {
         this.timesNum();
      }
      
      private function timesNum() : void
      {
         var _local_3:int = 0;
         var _local_5:int = 0;
         var _local_4:int = 0;
         var _local_1:int = 0;
         var _local_6:int = 0;
         var _local_2:int = 0;
         if(CloudBuyLotteryManager.Instance.model.isGame)
         {
            this.timeArray = CloudBuyLotteryManager.Instance.refreshTimePlayTxt().split(":");
            _local_3 = CloudBuyLotteryManager.Instance.returnTen(this.timeArray[0]);
            _local_5 = CloudBuyLotteryManager.Instance.returnABit(this.timeArray[0]);
            _local_4 = CloudBuyLotteryManager.Instance.returnTen(this.timeArray[1]);
            _local_1 = CloudBuyLotteryManager.Instance.returnABit(this.timeArray[1]);
            _local_6 = CloudBuyLotteryManager.Instance.returnTen(this.timeArray[2]);
            _local_2 = CloudBuyLotteryManager.Instance.returnABit(this.timeArray[2]);
            this.showTimes.hourTen.gotoAndStop(_local_3);
            this.showTimes.hourBit.gotoAndStop(_local_5);
            this.showTimes.minutesTen.gotoAndStop(_local_4);
            this.showTimes.minutesBit.gotoAndStop(_local_1);
            this.showTimes.secondsTen.gotoAndStop(_local_6);
            this.showTimes.secondsBit.gotoAndStop(_local_2);
         }
         else
         {
            this._luckNumTxt.text = "0";
            this._chanceTxt.text = "0/0";
            this.showTimes.hourTen.gotoAndStop(0);
            this.showTimes.hourBit.gotoAndStop(0);
            this.showTimes.minutesTen.gotoAndStop(0);
            this.showTimes.minutesBit.gotoAndStop(0);
            this.showTimes.secondsTen.gotoAndStop(0);
            this.showTimes.secondsBit.gotoAndStop(0);
            if(this._timer != null)
            {
               this._timer.stop();
            }
         }
      }
      
      protected function __onHelpClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:DisplayObject = ComponentFactory.Instance.creat("CloudBuyLotteryFrame.HelpPrompt");
         var _local_3:HelpFrame = ComponentFactory.Instance.creat("CloudBuyLotteryFrame.HelpFrame");
         _local_3.setView(_local_2);
         _local_3.titleText = LanguageMgr.GetTranslation("store.view.HelpButtonText");
         LayerManager.Instance.addToLayer(_local_3,1,true,1);
      }
      
      private function __responseHandler(_arg_1:FrameEvent) : void
      {
         if(_arg_1.responseCode == 0 || _arg_1.responseCode == 1)
         {
            SoundManager.instance.play("008");
            this.dispose();
         }
      }
      
      public function get expBar() : ExpBar
      {
         return this._expBar;
      }
      
      override public function dispose() : void
      {
         this.removeEvent();
         if(Boolean(this._timer))
         {
            this._timer.stop();
            this._timer.removeEventListener("timer",this.__updateTimes);
            this._timer = null;
         }
         ObjectUtils.disposeAllChildren(this);
         super.dispose();
         if(Boolean(this._expBar))
         {
            ObjectUtils.disposeObject(this._expBar);
         }
         this._expBar = null;
         if(Boolean(this._cell))
         {
            ObjectUtils.disposeObject(this._cell);
         }
         this._cell = null;
         if(Boolean(this.luckLooteryFrame))
         {
            ObjectUtils.disposeObject(this.luckLooteryFrame);
         }
         this.luckLooteryFrame = null;
         if(Boolean(this.winningLogFrame))
         {
            ObjectUtils.disposeObject(this.winningLogFrame);
         }
         this.winningLogFrame = null;
         this._bg = null;
         this.showTimes = null;
      }
   }
}

