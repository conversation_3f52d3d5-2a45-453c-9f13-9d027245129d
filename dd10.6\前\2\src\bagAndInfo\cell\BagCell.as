package bagAndInfo.cell
{
   import bagAndInfo.bag.BreakGoodsBtn;
   import bagAndInfo.bag.ContinueGoodsBtn;
   import bagAndInfo.bag.SellGoodsBtn;
   import bagAndInfo.bag.SellGoodsFrame;
   import baglocked.BaglockedManager;
   import baglocked.SetPassEvent;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.ui.vo.AlertInfo;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.cmd.CmdCheckBagLockedPSWNeeds;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.events.CellEvent;
   import ddt.manager.DragManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.PositionUtils;
   import enchant.EnchantManager;
   import farm.viewx.FarmFieldBlock;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.ColorMatrixFilter;
   import flash.utils.getQualifiedClassName;
   import homeBank.HomeBankMangager;
   import homeBank.XukongBankEvent;
   import playerDress.components.DressUtils;
   import vip.VipController;
   
   public class BagCell extends BaseCell
   {
      
      protected var _place:int;
      
      protected var _tbxCount:FilterFrameText;
      
      protected var _bgOverDate:Bitmap;
      
      protected var _cellMouseOverBg:Bitmap;
      
      protected var _cellMouseOverFormer:Bitmap;
      
      private var _mouseOverEffBoolean:Boolean;
      
      protected var _bagType:int;
      
      protected var _isShowIsUsedBitmap:Boolean;
      
      protected var _isUsed:Boolean;
      
      protected var _isUsedBitmap:Bitmap;
      
      protected var _enchantMc:MovieClip;
      
      protected var _enchantMcName:String = "asset.enchant.level";
      
      protected var _enchantMcPosStr:String = "enchant.levelMcPos";
      
      private var placeArr:Array = [0,1,2];
      
      protected var temInfo:InventoryItemInfo;
      
      private var _sellFrame:SellGoodsFrame;
      
      private var _markId:int = 0;
      
      protected var _armMc:MovieClip;
      
      protected var _armMcName:String = "asset.enchant.arm.level1";
      
      protected var _chuLian:MovieClip;
      
      protected var _chuLianName:String = "asset.core.icon.ChuJiLianTi";
      
      protected var _zhongLian:MovieClip;
      
      protected var _zhongLianName:String = "asset.core.icon.ZhongJiLianTi";
      
      protected var _GaoLian:MovieClip;
      
      protected var _GaoLianName:String = "asset.core.icon.GaoJiLianTi";
      
      protected var _ChaoLian:MovieClip;
      
      protected var _ChaoLianName:String = "asset.core.icon.ChaoJiLianTi";
      
      protected var _enchantArmMcPosStr:String = "enchant.armlevelMcPos";
      
      public function BagCell(_arg_1:int, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true, _arg_4:DisplayObject = null, _arg_5:Boolean = true)
      {
         this._mouseOverEffBoolean = _arg_5;
         super(Boolean(_arg_4) ? _arg_4 : ComponentFactory.Instance.creatBitmap("bagAndInfo.cell.bagCellBgAsset"),_arg_2,_arg_3);
         this._place = _arg_1;
      }
      
      protected function addArmMc() : void
      {
         var _local_1:int = 0;
         if(Boolean(this.itemInfo))
         {
            if(int(this.itemInfo.Property4) == 10001 || int(this.itemInfo.Property7) == 10001)
            {
               this._chuLian = ComponentFactory.Instance.creat(this._chuLianName);
               PositionUtils.setPos(this._chuLian,this._enchantArmMcPosStr);
               addChild(this._chuLian);
            }
            if(int(this.itemInfo.Property4) == 10002)
            {
               this._zhongLian = ComponentFactory.Instance.creat(this._zhongLianName);
               PositionUtils.setPos(this._zhongLian,this._enchantArmMcPosStr);
               addChild(this._zhongLian);
            }
            if(int(this.itemInfo.Property4) == 10003)
            {
               this._GaoLian = ComponentFactory.Instance.creat(this._GaoLianName);
               PositionUtils.setPos(this._GaoLian,this._enchantArmMcPosStr);
               addChild(this._GaoLian);
            }
            if(int(this.itemInfo.Property4) == 10004 || int(this.itemInfo.Property7) == 10004)
            {
               this._ChaoLian = ComponentFactory.Instance.creat(this._ChaoLianName);
               PositionUtils.setPos(this._ChaoLian,this._enchantArmMcPosStr);
               addChild(this._ChaoLian);
            }
            if(Boolean(this.itemInfo.StrengthenLevel))
            {
               _local_1 = this.itemInfo.StrengthenLevel;
               if(this.itemInfo.CategoryID == 7 || this.itemInfo.CategoryID == 17)
               {
                  if(_local_1 && _local_1 >= 80 && int(this.itemInfo.Property4) < 10000)
                  {
                     this._armMc = ComponentFactory.Instance.creat(this._armMcName);
                     PositionUtils.setPos(this._armMc,this._enchantArmMcPosStr);
                     addChild(this._armMc);
                  }
               }
               if((this.itemInfo.CategoryID == 1 || this.itemInfo.CategoryID == 5) && int(this.itemInfo.Property4) < 10000)
               {
                  if(Boolean(_local_1) && _local_1 >= 50)
                  {
                     this._armMc = ComponentFactory.Instance.creat(this._armMcName);
                     PositionUtils.setPos(this._armMc,this._enchantArmMcPosStr);
                     addChild(this._armMc);
                  }
               }
            }
         }
      }
      
      public function deleteArmEnchantMc() : void
      {
         if(Boolean(this._armMc))
         {
            this._armMc.stop();
            this._armMc.parent.removeChild(this._armMc);
            this._armMc = null;
         }
         if(Boolean(this._chuLian))
         {
            this._chuLian.stop();
            this._chuLian.parent.removeChild(this._chuLian);
            this._chuLian = null;
         }
         if(Boolean(this._zhongLian))
         {
            this._zhongLian.stop();
            this._zhongLian.parent.removeChild(this._zhongLian);
            this._zhongLian = null;
         }
         if(Boolean(this._GaoLian))
         {
            this._GaoLian.stop();
            this._GaoLian.parent.removeChild(this._GaoLian);
            this._GaoLian = null;
         }
         if(Boolean(this._ChaoLian))
         {
            this._ChaoLian.stop();
            this._ChaoLian.parent.removeChild(this._ChaoLian);
            this._ChaoLian = null;
         }
      }
      
      public function setBgVisible(_arg_1:Boolean) : void
      {
         _bg.visible = _arg_1;
      }
      
      public function set mouseOverEffBoolean(_arg_1:Boolean) : void
      {
         this._mouseOverEffBoolean = _arg_1;
      }
      
      public function get bagType() : int
      {
         return this._bagType;
      }
      
      public function set bagType(_arg_1:int) : void
      {
         this._bagType = _arg_1;
      }
      
      public function get isShowIsUsedBitmap() : Boolean
      {
         return this._isShowIsUsedBitmap;
      }
      
      public function set isShowIsUsedBitmap(_arg_1:*) : void
      {
         this._isShowIsUsedBitmap = _arg_1;
         if(_arg_1 && this._isShowIsUsedBitmap && this.isUsed)
         {
            this.addIsUsedBitmap();
         }
      }
      
      override protected function createChildren() : void
      {
         super.createChildren();
         locked = false;
         this._bgOverDate = ComponentFactory.Instance.creatBitmap("bagAndInfo.cell.overDateBgAsset");
         if(this._mouseOverEffBoolean == true)
         {
            this._cellMouseOverBg = ComponentFactory.Instance.creatBitmap("bagAndInfo.cell.bagCellOverBgAsset");
            this._cellMouseOverFormer = ComponentFactory.Instance.creatBitmap("bagAndInfo.cell.bagCellOverShareBG");
            addChild(this._cellMouseOverBg);
            addChild(this._cellMouseOverFormer);
         }
         addChild(this._bgOverDate);
         this._tbxCount = ComponentFactory.Instance.creatComponentByStylename("BagCellCountText");
         this._tbxCount.mouseEnabled = false;
         addChild(this._tbxCount);
         this.updateCount();
         this.checkOverDate();
         this.updateBgVisible(false);
      }
      
      public function set isUsed(_arg_1:Boolean) : void
      {
         this._isUsed = _arg_1;
      }
      
      public function get isUsed() : Boolean
      {
         return this._isUsed;
      }
      
      protected function addIsUsedBitmap() : void
      {
         this._isUsedBitmap = ComponentFactory.Instance.creat("asset.store.isUsedBitmap");
         this._isUsedBitmap.x = 22;
         this._isUsedBitmap.y = 1;
         addChild(this._isUsedBitmap);
      }
      
      protected function addEnchantMc() : void
      {
         var _local_1:int = int(this.itemInfo.MagicLevel >= EnchantManager.instance.infoVec.length ? int(this.itemInfo.MagicLevel / 10) : int(this.itemInfo.MagicLevel / 10) + 1);
         this._enchantMc = ComponentFactory.Instance.creat(this._enchantMcName + _local_1);
         PositionUtils.setPos(this._enchantMc,this._enchantMcPosStr);
         addChild(this._enchantMc);
      }
      
      override public function set info(_arg_1:ItemTemplateInfo) : void
      {
         super.info = _arg_1;
         if(Boolean(this.itemInfo) && this.itemInfo.CategoryID != 73)
         {
            this.isUsed = this.itemInfo.BagType == 0 && (this.itemInfo.Place < 17 || this.itemInfo.Place == 18);
         }
         if(this.bagType == 43)
         {
            this.isUsed = (this.itemInfo.ItemID == PlayerManager.Instance.curcentId && this.itemInfo.getRemainDate()) > 0 ? true : false;
         }
         if(_arg_1 && this._isShowIsUsedBitmap && this.isUsed)
         {
            this.addIsUsedBitmap();
         }
         else if(!_arg_1)
         {
            if(Boolean(this._isUsedBitmap))
            {
               ObjectUtils.disposeObject(this._isUsedBitmap);
            }
            this._isUsedBitmap = null;
         }
         this.deleteEnchantMc();
         this.deleteArmEnchantMc();
         if(this.itemInfo && this.itemInfo.isCanEnchant() && this.itemInfo.MagicLevel > 0)
         {
            this.addEnchantMc();
         }
         this.updateCount();
         this.checkOverDate();
         this.addArmMc();
      }
      
      public function deleteEnchantMc() : void
      {
         if(Boolean(this._enchantMc))
         {
            this._enchantMc.stop();
            this._enchantMc.parent.removeChild(this._enchantMc);
            this._enchantMc = null;
         }
      }
      
      override protected function onMouseClick(_arg_1:MouseEvent) : void
      {
      }
      
      override protected function onMouseOver(_arg_1:MouseEvent) : void
      {
      }
      
      override protected function onMouseOut(_arg_1:MouseEvent) : void
      {
         super.onMouseOut(_arg_1);
         this.updateBgVisible(false);
      }
      
      public function onParentMouseOver(_arg_1:Bitmap) : void
      {
         if(!this._cellMouseOverBg)
         {
            this._cellMouseOverBg = _arg_1;
            addChild(this._cellMouseOverBg);
            super.setChildIndex(this._cellMouseOverBg,1);
            this.updateBgVisible(true);
         }
      }
      
      public function onParentMouseOut() : void
      {
         if(Boolean(this._cellMouseOverBg))
         {
            this.updateBgVisible(false);
            this._cellMouseOverBg = null;
         }
      }
      
      protected function updateBgVisible(_arg_1:Boolean) : void
      {
         if(Boolean(this._cellMouseOverBg))
         {
            this._cellMouseOverBg.visible = _arg_1;
            this._cellMouseOverFormer.visible = _arg_1;
            setChildIndex(this._cellMouseOverFormer,numChildren - 1);
         }
      }
      
      override public function dragDrop(_arg_1:DragEffect) : void
      {
         var _local_7:Boolean = false;
         var _local_4:InventoryItemInfo = null;
         var _local_5:* = null;
         var _local_8:* = null;
         var _local_3:* = null;
         var _local_6:* = null;
         var _local_2:* = null;
         if(Boolean(_arg_1.source))
         {
            _local_6 = getQualifiedClassName(_arg_1.source);
            if(_local_6 == "petsBag.petsAdvanced::PetsAwakenEquipCell")
            {
               return;
            }
         }
         if(Boolean(_arg_1.source))
         {
            _local_5 = getQualifiedClassName(_arg_1.source);
            if(_local_5 == "petsBag.petsAdvanced::PetsAwakenEquipCell")
            {
               return;
            }
         }
         if(new CmdCheckBagLockedPSWNeeds().excute(0) == true)
         {
            _arg_1.action = "none";
            super.dragStop(_arg_1);
            return;
         }
         if(_arg_1.data is InventoryItemInfo)
         {
            _local_8 = _arg_1.data as InventoryItemInfo;
            if(locked)
            {
               if(_local_8 == this.info)
               {
                  this.locked = false;
                  DragManager.acceptDrag(this);
               }
               else
               {
                  DragManager.acceptDrag(this,"none");
               }
            }
            else
            {
               if(this._bagType == 11 || _local_8.BagType == 11)
               {
                  if(_arg_1.action == "split")
                  {
                     _arg_1.action = "none";
                  }
                  else if(this._bagType != 11)
                  {
                     if(DressUtils.isDress(_local_8))
                     {
                        SocketManager.Instance.out.sendMoveGoods(11,_local_8.Place,0,-1,1);
                     }
                     else
                     {
                        SocketManager.Instance.out.sendMoveGoods(11,_local_8.Place,this._bagType,this.place,_local_8.Count);
                     }
                     _arg_1.action = "none";
                  }
                  else if(this._bagType == _local_8.BagType)
                  {
                     if(this.place >= PlayerManager.Instance.Self.consortiaInfo.StoreLevel * 10)
                     {
                        _arg_1.action = "none";
                     }
                     else
                     {
                        SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,_local_8.BagType,this.place,_local_8.Count);
                     }
                  }
                  else
                  {
                     _local_7 = PlayerManager.Instance.Self.ConsortiaID != 0;
                     if(!_local_7)
                     {
                        MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("magichouse.treasureView.noConsortion"));
                        _arg_1.action = "none";
                     }
                     else if(PlayerManager.Instance.Self.consortiaInfo.StoreLevel < 1)
                     {
                        MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.consortia.club.ConsortiaClubView.cellDoubleClick"));
                        _arg_1.action = "none";
                     }
                     else
                     {
                        if(_local_8.BagType == 21 || _local_8.BagType == 41)
                        {
                           MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.consortionBank.goodsNotDrag"));
                        }
                        else
                        {
                           SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,this._bagType,this.place,_local_8.Count);
                        }
                        _arg_1.action = "none";
                     }
                  }
               }
               else if(this._bagType == 53 || _local_8.BagType == 53)
               {
                  _local_7 = PlayerManager.Instance.Self.VipLeftHours <= 0;
                  if(VipController.instance.isExistNullCell)
                  {
                     if(_arg_1.action == "split")
                     {
                        _arg_1.action = "none";
                     }
                     if(_local_7)
                     {
                        if(this._bagType != 53)
                        {
                           if(DressUtils.isDress(_local_8))
                           {
                              SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,0,-1,1);
                           }
                           else
                           {
                              SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,this._bagType,this.place,_local_8.Count);
                           }
                        }
                        else
                        {
                           MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.vip.VipBankView.cellDoubleClick.msg3"));
                        }
                     }
                     else
                     {
                        SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,this._bagType,this.place,_local_8.Count);
                     }
                     _arg_1.action = "none";
                  }
                  else
                  {
                     if(this._bagType == _local_8.BagType)
                     {
                        if(_local_7)
                        {
                           MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.vip.VipBankView.cellDoubleClick.msg3"));
                        }
                        else
                        {
                           SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,this._bagType,this.place,_local_8.Count);
                        }
                     }
                     else if(this._bagType != 53)
                     {
                        if(DressUtils.isDress(_local_8))
                        {
                           SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,0,-1,1);
                        }
                        else
                        {
                           SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,this._bagType,this.place,_local_8.Count);
                        }
                        _arg_1.action = "none";
                     }
                     else
                     {
                        _local_5 = VipController.instance.getCurVipBankGridCount();
                        if(_local_7)
                        {
                           MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.vip.VipBankView.cellDoubleClick.msg3"));
                        }
                        else if(this.place <= _local_5)
                        {
                           SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,this._bagType,this.place,_local_8.Count);
                        }
                        else
                        {
                           _local_2 = VipController.instance.nextLv;
                           if(_local_2 >= 0)
                           {
                              MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.vip.VipBankView.cellDoubleClick.msg2",_local_2));
                           }
                           else
                           {
                              MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.vip.VipBankView.cellDoubleClick.msg"));
                           }
                        }
                     }
                     _arg_1.action = "none";
                  }
               }
               else if(this._bagType == 51 || _local_8.BagType == 51)
               {
                  if(this._bagType == 51 && HomeBankMangager.instance.isExistNullCell)
                  {
                     SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,this._bagType,this.place,_local_8.Count);
                     _arg_1.action = "none";
                  }
                  else if(_local_8.BagType == 51)
                  {
                     if(EquipType.isBelongToPropBag(_local_8))
                     {
                        if(this._bagType == 1)
                        {
                           SocketManager.Instance.out.sendMoveGoods(51,_local_8.Place,1,this.place,_local_8.Count);
                        }
                        else
                        {
                           SocketManager.Instance.out.sendMoveGoods(51,_local_8.Place,1,-1,_local_8.Count);
                        }
                     }
                     else if(DressUtils.isDress(_local_8))
                     {
                        SocketManager.Instance.out.sendMoveGoods(51,_local_8.Place,0,-1,1);
                     }
                     else if(this._bagType == 0)
                     {
                        SocketManager.Instance.out.sendMoveGoods(51,_local_8.Place,0,this.place,_local_8.Count);
                     }
                     else
                     {
                        SocketManager.Instance.out.sendMoveGoods(51,_local_8.Place,0,-1,_local_8.Count);
                     }
                     _arg_1.action = "none";
                  }
                  else
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("home.homeBankBagView.cellDoubleClick.msg"));
                     _arg_1.action = "none";
                  }
               }
               else if(this._bagType == 200)
               {
                  if(HomeBankMangager.instance.getXkIslock())
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.forthactive.txt6"));
                  }
                  else if(_local_8.BagType == 200 && (_arg_1.source as BagCell).bagType != 201)
                  {
                     HomeBankMangager.instance.dispatchEvent(new XukongBankEvent("exchange_place",[_local_8.Place,this.place,_local_8.Count]));
                     _arg_1.action = "none";
                  }
                  else if((_arg_1.source as BagCell).bagType == 201)
                  {
                     HomeBankMangager.instance.dispatchEvent(new XukongBankEvent("remove_get",_local_8));
                     _arg_1.action = "none";
                  }
                  else if(_local_8.BagType != 201)
                  {
                     if(!_info)
                     {
                        SocketManager.Instance.out.sendXukongBankPutItem(_local_8.BagType,_local_8.Place,this.place);
                     }
                     _arg_1.action = "none";
                  }
               }
               else if(this._bagType == 201 && _local_8.BagType == 200 && !_info)
               {
                  if(HomeBankMangager.instance.getXkIslock())
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.forthactive.txt6"));
                  }
                  else
                  {
                     HomeBankMangager.instance.dispatchEvent(new XukongBankEvent("add_get",[_local_8,this._place]));
                     _arg_1.action = "none";
                  }
               }
               else if(_local_8.BagType == this._bagType)
               {
                  if(!this.itemInfo)
                  {
                     if(Boolean(_local_8.isMoveSpace))
                     {
                        SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,_local_8.BagType,this.place,_local_8.Count);
                     }
                     _arg_1.action = "none";
                     return;
                  }
                  if(_local_8.CategoryID == this.itemInfo.CategoryID && _local_8.Place <= 30 && (_local_8.BindType == 1 || _local_8.BindType == 2 || _local_8.BindType == 3) && this.itemInfo.IsBinds == false && EquipType.canEquip(_local_8))
                  {
                     _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("tank.view.bagII.BagIIView.BindsInfo"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),true,true,true,2);
                     _local_3.addEventListener("response",this.__onCellResponse);
                     this.temInfo = _local_8;
                  }
                  else if(EquipType.isHealStone(_local_8))
                  {
                     if(PlayerManager.Instance.Self.Grade >= _local_8.Property1)
                     {
                        SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,_local_8.BagType,this.place,_local_8.Count);
                        _arg_1.action = "none";
                     }
                     else if(PlayerManager.Instance.Self.Grade < _local_8.Property1 && _local_8.Place > 30)
                     {
                        SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,_local_8.BagType,this.place,_local_8.Count);
                        _arg_1.action = "none";
                     }
                     else if(_arg_1.action == "move")
                     {
                        if(_arg_1.source is BagCell)
                        {
                           BagCell(_arg_1.source).locked = false;
                        }
                     }
                     else
                     {
                        MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.HealStone.ErrorGrade",_local_8.Property1));
                     }
                  }
                  else
                  {
                     SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,_local_8.BagType,this.place,_local_8.Count);
                     if(!this.isPetBagCellMove(_arg_1.source as BagCell,this))
                     {
                        _arg_1.action = "none";
                     }
                  }
               }
               else if(_local_8.BagType == 12)
               {
                  if(_local_8.CategoryID == 20 || _local_8.CategoryID == 53 || _local_8.CategoryID == 34 || _local_8.CategoryID == 78)
                  {
                     SocketManager.Instance.out.sendMoveGoods(_local_8.BagType,_local_8.Place,this._bagType,this.place,_local_8.Count);
                  }
                  _arg_1.action = "none";
               }
               else
               {
                  _arg_1.action = "none";
               }
               DragManager.acceptDrag(this);
            }
         }
         else if(_arg_1.data is SellGoodsBtn)
         {
            _local_6 = PlayerManager.Instance.Self.currentPet;
            if(_local_6 != null)
            {
               _local_2 = PlayerManager.Instance.Self.currentPet.equipList;
               for each(_local_4 in _local_2)
               {
                  if(_local_4.TemplateID == _info.TemplateID)
                  {
                     _arg_1.action = "none";
                     super.dragStop(_arg_1);
                     return;
                  }
               }
            }
            if(!locked && _info && this._bagType != 11 && this._bagType != 511)
            {
               locked = true;
               DragManager.acceptDrag(this);
            }
         }
         else if(_arg_1.data is ContinueGoodsBtn)
         {
            if(PlayerManager.Instance.Self.bagLocked)
            {
               BaglockedManager.Instance.show();
               return;
            }
            if(!locked && _info && this._bagType != 11 && this._bagType != 511)
            {
               locked = true;
               DragManager.acceptDrag(this,"none");
            }
         }
         else if(_arg_1.data is BreakGoodsBtn)
         {
            if(!locked && Boolean(_info))
            {
               DragManager.acceptDrag(this);
            }
         }
      }
      
      private function isPetBagCellMove(_arg_1:BagCell, _arg_2:BagCell) : Boolean
      {
         var _local_3:InventoryItemInfo = _arg_2.info as InventoryItemInfo;
         var _local_4:InventoryItemInfo = _arg_1.info as InventoryItemInfo;
         if(this.placeArr.indexOf(_local_3.Place) != -1 && this.placeArr.indexOf(_local_4.Place) == -1)
         {
            return false;
         }
         return true;
      }
      
      private function sendDefy() : void
      {
         SoundManager.instance.play("008");
         if(PlayerManager.Instance.Self.canEquip(this.temInfo))
         {
            SocketManager.Instance.out.sendMoveGoods(this.temInfo.BagType,this.temInfo.Place,this.temInfo.BagType,this.place,this.temInfo.Count);
         }
      }
      
      override public function dragStart() : void
      {
         super.dragStart();
         if(Boolean(_info) && _pic.numChildren > 0)
         {
            dispatchEvent(new CellEvent("dragStart",this.info,true));
         }
      }
      
      override public function dragStop(_arg_1:DragEffect) : void
      {
         var _local_2:int = 0;
         var _local_3:* = null;
         SoundManager.instance.play("008");
         dispatchEvent(new CellEvent("dragStop",null,true));
         var _local_4:ItemTemplateInfo = _arg_1.data as ItemTemplateInfo;
         if(_arg_1.action == "none" && _arg_1.target != null)
         {
         }
         if(_arg_1.action == "move" && _arg_1.target != null)
         {
            if(_local_4.CategoryID == 50 || _local_4.CategoryID == 51 || _local_4.CategoryID == 52)
            {
               _arg_1.action = "none";
               super.dragStop(_arg_1);
            }
            return;
         }
         if(_arg_1.action == "move" && _arg_1.target == null)
         {
            if(_local_4.CategoryID == 50 || _local_4.CategoryID == 51 || _local_4.CategoryID == 52)
            {
               _arg_1.action = "none";
               super.dragStop(_arg_1);
            }
            else if(Boolean(_local_4) && (_local_4 as InventoryItemInfo).BagType == 11)
            {
               _arg_1.action = "none";
               super.dragStop(_arg_1);
            }
            else if(Boolean(_local_4) && (_local_4 as InventoryItemInfo).BagType == 12)
            {
               locked = false;
            }
            else if(Boolean(_local_4) && (_local_4 as InventoryItemInfo).BagType == 21)
            {
               locked = false;
            }
            else if(Boolean(_local_4) && _local_4.CategoryID == 74)
            {
               locked = false;
            }
            else if(_local_4.CategoryID == 34)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.bagAndInfo.sell.CanNotSell"));
               _arg_1.action = "none";
               super.dragStop(_arg_1);
            }
            else
            {
               locked = false;
               this.sellItem(_local_4 as InventoryItemInfo);
            }
         }
         else if(_arg_1.action == "split" && _arg_1.target == null)
         {
            locked = false;
         }
         else if(_arg_1.target is FarmFieldBlock)
         {
            locked = false;
            if(_local_4.Property1 != "31")
            {
               this.sellItem();
            }
            else
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.beadSystem.beadCanntDestory"));
            }
         }
         else
         {
            super.dragStop(_arg_1);
         }
      }
      
      public function dragCountStart(_arg_1:int) : void
      {
         var _local_3:* = null;
         var _local_2:* = null;
         if(_info && !locked && stage && _arg_1 != 0)
         {
            _local_3 = this.itemInfo;
            _local_2 = "move";
            if(_arg_1 != this.itemInfo.Count)
            {
               _local_3 = new InventoryItemInfo();
               _local_3.ItemID = this.itemInfo.ItemID;
               _local_3.BagType = this.itemInfo.BagType;
               _local_3.Place = this.itemInfo.Place;
               _local_3.IsBinds = this.itemInfo.IsBinds;
               _local_3.BeginDate = this.itemInfo.BeginDate;
               _local_3.ValidDate = this.itemInfo.ValidDate;
               _local_3.Count = _arg_1;
               _local_3.NeedSex = this.itemInfo.NeedSex;
               _local_2 = "split";
            }
            if(DragManager.startDrag(this,_local_3,createDragImg(),stage.mouseX,stage.mouseY,_local_2))
            {
               locked = true;
            }
         }
      }
      
      public function splitItem(_arg_1:int) : InventoryItemInfo
      {
         var _local_2:* = null;
         if(_info && !locked && stage && _arg_1 != 0)
         {
            _local_2 = this.itemInfo;
            if(_arg_1 != this.itemInfo.Count)
            {
               _local_2 = new InventoryItemInfo();
               _local_2.ItemID = this.itemInfo.ItemID;
               _local_2.BagType = this.itemInfo.BagType;
               _local_2.Place = this.itemInfo.Place;
               _local_2.IsBinds = this.itemInfo.IsBinds;
               _local_2.BeginDate = this.itemInfo.BeginDate;
               _local_2.ValidDate = this.itemInfo.ValidDate;
               _local_2.Count = _arg_1;
               _local_2.NeedSex = this.itemInfo.NeedSex;
            }
         }
         return _local_2;
      }
      
      public function sellItem(_arg_1:InventoryItemInfo = null) : void
      {
         if(EquipType.isValuableEquip(info))
         {
            if(!PlayerManager.Instance.Self.bagPwdState)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.SellGoodsBtn.CantSellEquip"));
               return;
            }
            if(PlayerManager.Instance.Self.bagLocked)
            {
               BaglockedManager.Instance.show();
               BaglockedManager.Instance.addEventListener("cancelBtn",this.__cancelBtn);
               return;
            }
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.SellGoodsBtn.CantSellEquip"));
         }
         else if(EquipType.isPetSpeciallFood(info.TemplateID))
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.bagAndInfo.sell.CanNotSell"));
         }
         else
         {
            if(PlayerManager.Instance.Self.bagLocked)
            {
               BaglockedManager.Instance.show();
               return;
            }
            this.showSellFrame(_arg_1);
         }
      }
      
      private function showSellFrame(_arg_1:InventoryItemInfo) : void
      {
         if(this._sellFrame == null)
         {
            this._sellFrame = ComponentFactory.Instance.creatComponentByStylename("sellGoodsFrame");
            this._sellFrame.itemInfo = _arg_1;
            this._sellFrame.addEventListener("cancel",this.disposeSellFrame);
            this._sellFrame.addEventListener("ok",this.disposeSellFrame);
         }
         LayerManager.Instance.addToLayer(this._sellFrame,2,true,1);
      }
      
      private function disposeSellFrame(_arg_1:Event) : void
      {
         if(Boolean(this._sellFrame))
         {
            this._sellFrame.dispose();
            this._sellFrame.removeEventListener("cancel",this.disposeSellFrame);
            this._sellFrame.removeEventListener("ok",this.disposeSellFrame);
         }
         this._sellFrame = null;
      }
      
      protected function __onCellResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.target as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__onCellResponse);
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            if(EquipType.isHealStone(info))
            {
               if(PlayerManager.Instance.Self.Grade >= int(info.Property1))
               {
                  this.sendDefy();
               }
               else
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.HealStone.ErrorGrade",info.Property1));
               }
            }
            else
            {
               this.sendDefy();
            }
         }
      }
      
      private function getAlertInfo() : AlertInfo
      {
         var _local_1:AlertInfo = new AlertInfo();
         _local_1.autoDispose = true;
         var _local_2:* = true;
         _local_1.showSubmit = _local_2;
         _local_1.showCancel = _local_2;
         _local_1.enterEnable = true;
         _local_1.escEnable = true;
         _local_1.moveEnable = false;
         _local_1.title = LanguageMgr.GetTranslation("AlertDialog.Info");
         _local_1.data = LanguageMgr.GetTranslation("tank.view.bagII.SellGoodsBtn.sure").replace("{0}",InventoryItemInfo(_info).Count * _info.ReclaimValue + (_info.ReclaimType == 1 ? LanguageMgr.GetTranslation("shop.ShopIIShoppingCarItem.gold") : (_info.ReclaimType == 2 ? LanguageMgr.GetTranslation("tank.gameover.takecard.gifttoken") : "")));
         return _local_1;
      }
      
      private function confirmCancel() : void
      {
         locked = false;
      }
      
      public function get place() : int
      {
         return this._place;
      }
      
      public function set place(_arg_1:int) : void
      {
         this._place = _arg_1;
      }
      
      public function get itemInfo() : InventoryItemInfo
      {
         return _info as InventoryItemInfo;
      }
      
      public function replaceBg(_arg_1:Sprite) : void
      {
         _bg = _arg_1;
      }
      
      public function setCount(_arg_1:*) : void
      {
         if(Boolean(this._tbxCount))
         {
            this._tbxCount.text = _arg_1;
            this._tbxCount.visible = true;
            this._tbxCount.x = _contentWidth - this._tbxCount.width;
            this._tbxCount.y = _contentHeight - this._tbxCount.height;
            addChild(this._tbxCount);
         }
      }
      
      public function getCount() : int
      {
         return int(this._tbxCount.text);
      }
      
      public function refreshTbxPos() : void
      {
         this._tbxCount.x = _pic.x + _contentWidth - this._tbxCount.width - 4;
         this._tbxCount.y = _pic.y + _contentHeight - this._tbxCount.height - 2;
      }
      
      public function setCountNotVisible() : void
      {
         if(Boolean(this._tbxCount))
         {
            this._tbxCount.visible = false;
         }
      }
      
      public function updateCount() : void
      {
         if(Boolean(this._tbxCount))
         {
            if(_info && this.itemInfo && this.itemInfo.MaxCount > 1)
            {
               this._tbxCount.text = String(this.itemInfo.Count);
               this._tbxCount.visible = true;
               addChild(this._tbxCount);
            }
            else
            {
               this._tbxCount.visible = false;
            }
         }
      }
      
      public function checkOverDate() : void
      {
         if(Boolean(this._bgOverDate))
         {
            if(Boolean(this.itemInfo) && this.itemInfo.getRemainDate() <= 0)
            {
               this._bgOverDate.visible = true;
               addChild(this._bgOverDate);
               this.grayPic();
            }
            else
            {
               this._bgOverDate.visible = false;
               this.lightPic();
            }
         }
      }
      
      public function grayPic() : void
      {
         _pic.filters = [new ColorMatrixFilter([0.3086,0.6094,0.082,0,0,0.3086,0.6094,0.082,0,0,0.3086,0.6094,0.082,0,0,0,0,0,1,0])];
      }
      
      public function lightPic() : void
      {
         if(Boolean(_pic))
         {
            _pic.filters = [];
         }
      }
      
      public function set BGVisible(_arg_1:Boolean) : void
      {
         _bg.visible = _arg_1;
      }
      
      private function __cancelBtn(_arg_1:SetPassEvent) : void
      {
         BaglockedManager.Instance.removeEventListener("cancelBtn",this.__cancelBtn);
         this.disposeSellFrame(null);
      }
      
      override public function dispose() : void
      {
         this.deleteEnchantMc();
         this.deleteArmEnchantMc();
         if(Boolean(this._isUsedBitmap))
         {
            ObjectUtils.disposeObject(this._isUsedBitmap);
         }
         this._isUsedBitmap = null;
         if(Boolean(this._tbxCount))
         {
            ObjectUtils.disposeObject(this._tbxCount);
         }
         this._tbxCount = null;
         if(Boolean(this._bgOverDate))
         {
            ObjectUtils.disposeObject(this._bgOverDate);
         }
         this._bgOverDate = null;
         if(Boolean(this._cellMouseOverBg))
         {
            ObjectUtils.disposeObject(this._cellMouseOverBg);
         }
         this._cellMouseOverBg = null;
         if(Boolean(this._cellMouseOverFormer))
         {
            ObjectUtils.disposeObject(this._cellMouseOverFormer);
         }
         this._cellMouseOverFormer = null;
         super.dispose();
      }
      
      public function get markId() : int
      {
         return this._markId;
      }
      
      public function set markId(_arg_1:int) : void
      {
         this._markId = _arg_1;
      }
      
      public function get tbxCount() : FilterFrameText
      {
         return this._tbxCount;
      }
   }
}

