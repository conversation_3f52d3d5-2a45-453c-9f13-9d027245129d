package com.pickgliss.ui.controls
{
   import com.pickgliss.geom.InnerRectangle;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.DisplayUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.events.MouseEvent;
   
   public class MinimizeFrame extends Frame
   {
      
      public static const P_minimizeButton:String = "minimizeButton";
      
      public static const P_minimizeRect:String = "minimizeInnerRect";
      
      protected var _minimizeButton:BaseButton;
      
      protected var _minimizeInnerRect:InnerRectangle;
      
      protected var _minimizeRectString:String;
      
      protected var _minimizeStyle:String;
      
      public function MinimizeFrame()
      {
         super();
      }
      
      override protected function addChildren() : void
      {
         super.addChildren();
         if(Boolean(this._minimizeButton))
         {
            addChild(this._minimizeButton);
         }
      }
      
      public function set minimizeRectString(_arg_1:String) : void
      {
         if(this._minimizeRectString == _arg_1)
         {
            return;
         }
         this._minimizeRectString = _arg_1;
         this._minimizeInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._minimizeRectString));
         onPropertiesChanged("closeInnerRect");
      }
      
      public function get minimizeButton() : BaseButton
      {
         return this._minimizeButton;
      }
      
      public function set minimizeButton(_arg_1:BaseButton) : void
      {
         if(this._minimizeButton == _arg_1)
         {
            return;
         }
         if(Boolean(this._minimizeButton))
         {
            this._minimizeButton.removeEventListener("click",this.__onMinimizeClick);
            ObjectUtils.disposeObject(this._minimizeButton);
         }
         this._minimizeButton = _arg_1;
         onPropertiesChanged("minimizeButton");
      }
      
      public function set minimizeStyle(_arg_1:String) : void
      {
         if(this._minimizeStyle == _arg_1)
         {
            return;
         }
         this._minimizeStyle = _arg_1;
         this.minimizeButton = ComponentFactory.Instance.creat(this._minimizeStyle);
      }
      
      protected function updateMinimizePos() : void
      {
         if(Boolean(this._minimizeButton) && Boolean(this._minimizeInnerRect))
         {
            DisplayUtils.layoutDisplayWithInnerRect(this._minimizeButton,this._minimizeInnerRect,_width,_height);
         }
      }
      
      override protected function onProppertiesUpdate() : void
      {
         super.onProppertiesUpdate();
         if(Boolean(_changedPropeties["minimizeButton"]))
         {
            this._minimizeButton.addEventListener("click",this.__onMinimizeClick);
         }
         if(Boolean(_changedPropeties["minimizeButton"]) || Boolean(_changedPropeties["minimizeInnerRect"]))
         {
            this.updateMinimizePos();
         }
      }
      
      protected function __onMinimizeClick(_arg_1:MouseEvent) : void
      {
         onResponse(5);
      }
      
      override public function dispose() : void
      {
         super.dispose();
         if(Boolean(this._minimizeButton))
         {
            this._minimizeButton.removeEventListener("click",this.__onMinimizeClick);
            ObjectUtils.disposeObject(this._minimizeButton);
         }
         this._minimizeButton = null;
      }
   }
}

