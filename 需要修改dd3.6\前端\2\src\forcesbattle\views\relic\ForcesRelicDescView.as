package forcesbattle.views.relic
{
   import bagAndInfo.cell.BagCell;
   import baglocked.BaglockedManager;
   import com.greensock.TweenLite;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.BagInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.display.BitmapLoaderProxy;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PathManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.view.tips.GoodTipInfo;
   import flash.display.DisplayObject;
   import flash.geom.Rectangle;
   import forcesbattle.ForcesBattleSkillEvent;
   import forcesbattle.ForcesRelicEvent;
   import forcesbattle.ForcesRelicManager;
   import forcesbattle.data.relic.ForcesRelicCnfVo;
   import forcesbattle.data.relic.ForcesRelicExProCnfVo;
   import forcesbattle.data.relic.ForcesRelicLevelCnfVo;
   import forcesbattle.data.relic.ForcesRelicVo;
   import forcesbattle.model.ForcesRelicModel;
   import forcesbattle.mornui.forcesrelic.ForcesRelicDescViewUI;
   import forcesbattle.views.ForcesBattleAlertView;
   import morn.core.components.Box;
   import morn.core.ex.LabelEx;
   import morn.core.handlers.Handler;
   
   public class ForcesRelicDescView extends ForcesRelicDescViewUI
   {
      
      private var _curDetailID:int;
      
      private var _descRelicIcon:DisplayObject;
      
      private var _costBagCell1:BagCell;
      
      private var _costBagCell2:BagCell;
      
      private var _costBagCell3:BagCell;
      
      public function ForcesRelicDescView()
      {
         super();
      }
      
      override protected function initialize() : void
      {
         super.initialize();
         this._curDetailID = -1;
         descExProList.renderHandler = new Handler(this.__onRenderDescExProList);
         XlBtn.clickHandler = new Handler(this.__onClickXLBtn);
         upStageBtn.clickHandler = new Handler(this.__onClickUpStageBtn);
         ZFBtn.clickHandler = new Handler(this.__onClickZFBtn);
         closeBtn.clickHandler = new Handler(this.__onClickCloseBtn);
         nextCheckBox.clickHandler = new Handler(this.__onClickCheckBox);
         this.manager.addEventListener("updateRelicView",this.__onUpdateDescView);
      }
      
      private function __onUpdateDescView(_arg_1:ForcesRelicEvent) : void
      {
         this.updateDescView(this._curDetailID);
      }
      
      private function __onUpdateDescViewByID(_arg_1:ForcesBattleSkillEvent) : void
      {
         this._curDetailID = _arg_1.data as int;
         this.updateDescView(this._curDetailID);
      }
      
      public function updateDescView(_arg_1:int) : void
      {
         var _local_18:int = 0;
         var _local_29:int = 0;
         var _local_22:int = 0;
         var _local_15:int = 0;
         var _local_17:int = 0;
         var _local_32:int = 0;
         var _local_19:int = 0;
         var _local_14:int = 0;
         var _local_28:Number = NaN;
         var _local_23:int = 0;
         var _local_3:int = 0;
         var _local_4:int = 0;
         var _local_33:* = undefined;
         var _local_21:int = 0;
         var _local_5:* = null;
         var _local_20:* = null;
         var _local_27:* = null;
         var _local_25:* = null;
         var _local_10:* = null;
         var _local_26:* = null;
         var _local_31:* = null;
         var _local_13:* = null;
         var _local_12:* = null;
         var _local_16:* = null;
         this._curDetailID = _arg_1;
         if(this._curDetailID < 0)
         {
            return;
         }
         var _local_8:ForcesRelicVo = this.manager.model.relicInfoDic[this._curDetailID];
         if(!_local_8)
         {
            return;
         }
         var _local_7:ForcesRelicCnfVo = this.manager.model.relicCnfDic[this._curDetailID];
         if(!_local_7)
         {
            return;
         }
         var _local_24:ForcesRelicLevelCnfVo = this.manager.model.relicLevelCnfDic[this._curDetailID][_local_8.level];
         if(!_local_24)
         {
            return;
         }
         var _local_9:ForcesRelicLevelCnfVo = this.manager.model.relicLevelCnfDic[this._curDetailID][_local_8.level + 1];
         var _local_6:ForcesRelicExProCnfVo = this.manager.model.relicExProCnfDic[this._curDetailID][_local_8.stage + 1];
         this._clear();
         ZFBtn.disabled = _local_8.stage < 1;
         if(!this._descRelicIcon)
         {
            this._descRelicIcon = new BitmapLoaderProxy(PathManager.getForcesRelicPicUrl(this._curDetailID),new Rectangle(0,0,86,86));
            iconBox.addChild(this._descRelicIcon);
         }
         _local_33 = false;
         lvMaxBtn.visible = _local_33;
         XlBtn.visible = _local_33;
         upStageBtn.visible = _local_33;
         var _local_11:BagInfo = PlayerManager.Instance.Self.getBag(1);
         descRelicNameLab.text = _local_7.Name;
         var _local_30:String = ForcesRelicModel.RELICNAME_COLOR[_local_7.Quality - 1];
         descRelicNameLab.color = _local_30;
         descStageLab.text = LanguageMgr.GetTranslation("tank.forceRelic.txt3",_local_8.stage);
         descTypeLab.text = LanguageMgr.GetTranslation("tank.forceRelic.txt" + _local_7.Type) + LanguageMgr.GetTranslation("tank.forceRelic.txt6");
         var _local_2:String = this.manager.model.quaNameArr[_local_7.Quality];
         descLvLab.htmlText = LanguageMgr.GetTranslation("tank.forcesBattle.skillT.txt12",_local_8.level) + "   " + LanguageMgr.GetTranslation("tank.forceRelic.txt30",this.manager.changeColorToString(_local_30),LanguageMgr.GetTranslation("tank.forceRelic.txt31",_local_2)) + "    " + LanguageMgr.GetTranslation("tank.storeDeputy.txt15",_local_7.CommonProperty);
         descBaseLab.text = _local_8.getBaseProArr();
         descExProList.array = _local_8.getExProStrArr();
         if(Boolean(_local_9))
         {
            _local_21 = _local_9.NeedExp;
         }
         if(this.manager.getIsNeedAdvanceStage(_local_8))
         {
            upStageBtn.visible = true;
            _local_33 = false;
            costLab.visible = _local_33;
            haveLab.visible = _local_33;
            barNumLab.visible = _local_33;
            lvBarLab.visible = _local_33;
            expBar.visible = _local_33;
            _local_5 = _local_6.ItemCost.split("|");
            stageBox.visible = true;
            upStageBtn.disabled = true;
            _local_33 = false;
            nextCheckBox.visible = _local_33;
            lvMaxBtn.visible = _local_33;
            XlBtn.visible = _local_33;
            _local_20 = _local_5[0].split(",");
            _local_18 = int(_local_20[0]);
            _local_29 = int(_local_20[2]);
            _local_22 = int(_local_20[1]);
            if(_local_18 == 1)
            {
               _local_15 = _local_8.ShardNum;
            }
            _local_27 = ItemManager.Instance.getTemplateById(_local_22);
            _local_25 = this._getNeedStr(_local_15,_local_29);
            if(!this._costBagCell2)
            {
               this._costBagCell2 = new BagCell(0);
               this._costBagCell2.info = _local_27;
               this._costBagCell2.setBgVisible(false);
               this._costBagCell2.setContentSize(28,28);
               this._costBagCell2.setCountNotVisible();
               needBox2.addChild(this._costBagCell2);
            }
            needBox2.visible = true;
            this._costBagCell2.info = _local_27;
            _local_10 = _local_5[1].split(",");
            _local_17 = int(_local_10[0]);
            _local_32 = int(_local_10[2]);
            _local_19 = int(_local_10[1]);
            _local_14 = 0;
            if(_local_17 == 2)
            {
               _local_14 = this.manager.model.commonShard1;
            }
            else if(_local_17 == 3)
            {
               _local_14 = this.manager.model.commonShard2;
            }
            _local_26 = ItemManager.Instance.getTemplateById(_local_19);
            _local_31 = this._getNeedStr(_local_14,_local_32);
            if(!this._costBagCell3)
            {
               this._costBagCell3 = new BagCell(0);
               this._costBagCell3.info = _local_26;
               this._costBagCell3.setBgVisible(false);
               this._costBagCell3.setContentSize(28,28);
               this._costBagCell3.setCountNotVisible();
               needBox3.addChild(this._costBagCell3);
            }
            needBox3.visible = true;
            this._costBagCell3.info = _local_26;
            costLab2.x = 31;
            costLab2.text = _local_25;
            costLab3.text = _local_31;
            costLab3.visible = true;
            if(_local_15 >= _local_29)
            {
               upStageBtn.disabled = false;
            }
            else if(_local_14 >= _local_32)
            {
               upStageBtn.disabled = false;
            }
            orLab.visible = costLab3.visible;
         }
         else
         {
            _local_33 = true;
            costLab.visible = _local_33;
            haveLab.visible = _local_33;
            barNumLab.visible = _local_33;
            lvBarLab.visible = _local_33;
            expBar.visible = _local_33;
            stageBox.visible = false;
            upStageBtn.disabled = false;
            _local_33 = false;
            lvMaxBtn.visible = _local_33;
            XlBtn.visible = _local_33;
            lvBarLab.text = LanguageMgr.GetTranslation("tank.forcesBattle.skillT.txt12",_local_8.level);
            barNumLab.text = _local_8.curExp + " / " + _local_21;
            _local_28 = _local_21 > 0 ? _local_8.curExp / _local_21 : 1;
            TweenLite.to(expBar,0.3,{"value":_local_28});
            _local_13 = ServerConfigManager.instance.RelicUpgradeItem;
            _local_23 = int(_local_13[1]);
            _local_3 = int((_local_21 - _local_8.curExp) / _local_23);
            _local_4 = _local_11.getItemCountByTemplateId(_local_13[0]);
            _local_12 = ItemManager.Instance.getTemplateById(_local_13[0]);
            if(_local_8.level < this.manager.relicMaxLv)
            {
               if(_local_8.curExp < _local_21)
               {
                  XlBtn.visible = true;
                  XlBtn.disabled = _local_4 <= 0;
               }
               lvMaxBtn.visible = false;
               _local_33 = true;
               haveLab.visible = _local_33;
               costLab.visible = _local_33;
               nextCheckBox.visible = true;
               costLab.text = LanguageMgr.GetTranslation("tank.forceRelic.txt7",_local_12.Name);
               costLab.tipStyle = "ddt.view.tips.GoodTip";
               _local_16 = new GoodTipInfo();
               _local_16.itemInfo = _local_12;
               costLab.tipData = _local_16;
               costLab.tipDirctions = "0,0";
               this.updateUpgradeNeedLab();
            }
            else
            {
               _local_33 = false;
               haveLab.visible = _local_33;
               costLab.visible = _local_33;
               nextCheckBox.visible = false;
               lvMaxBtn.visible = true;
               XlBtn.visible = false;
               barNumLab.text = LanguageMgr.GetTranslation("tank.forceRelic.txt10");
            }
         }
      }
      
      private function __onRenderDescExProList(_arg_1:Box, _arg_2:int) : void
      {
         var _local_5:Boolean = false;
         var _local_7:Boolean = false;
         var _local_6:int = 0;
         var _local_3:int = 0;
         var _local_13:int = 0;
         var _local_4:* = null;
         var _local_9:* = null;
         var _local_8:* = null;
         var _local_10:* = null;
         var _local_11:* = null;
         var _local_12:* = null;
         if(_arg_2 < descExProList.array.length)
         {
            if(!_arg_1.visible)
            {
               _arg_1.visible = true;
            }
            _local_4 = descExProList.array[_arg_2];
            _local_9 = _arg_1.getChildByName("descExLab") as LabelEx;
            _local_5 = Boolean(_local_4[0]);
            _local_7 = Boolean(_local_4[1]);
            _local_8 = _local_4[2];
            _local_6 = int(_local_4[4]);
            _local_10 = ForcesRelicModel.RELICPRO_COLOR[_local_6];
            _local_3 = int(_local_4[5]);
            _local_13 = int(_local_4[6]);
            if(_local_5)
            {
               if(_local_7)
               {
                  _local_9.htmlText = LanguageMgr.GetTranslation("tank.forceRelic.txt30",_local_10,_local_11.Name + ":" + _local_11.Description);
               }
               else
               {
                  _local_9.htmlText = LanguageMgr.GetTranslation("tank.forceRelic.txt5",_local_11.Name + ":" + _local_11.Description);
               }
               _local_9.tipStyle = "ddt.view.tips.MultipleHtmlTip";
               _local_9.tipDirctions = "0";
               _local_9.tipData = _local_11.Description;
            }
            else if(_local_7)
            {
               _local_12 = _local_10;
               _local_9.htmlText = LanguageMgr.GetTranslation("tank.forceRelic.txt12",_local_12,_local_8,_local_3);
            }
            else
            {
               _local_9.htmlText = LanguageMgr.GetTranslation("tank.forceRelic.txt5",_local_8);
               _local_9.tipStyle = null;
               descBaseLab.tipData = null;
            }
         }
         else
         {
            _arg_1.visible = false;
         }
      }
      
      private function _getNeedStr(_arg_1:int, _arg_2:int) : String
      {
         var _local_3:String = "";
         if(_arg_1 >= _arg_2)
         {
            _local_3 = LanguageMgr.GetTranslation("tank.forcesBattle.txt43",_arg_1,_arg_2);
         }
         else
         {
            _local_3 = LanguageMgr.GetTranslation("tank.forcesBattle.txt44",_arg_1,_arg_2);
         }
         return _local_3;
      }
      
      private function __onClickXLBtn() : void
      {
         SoundManager.instance.playButtonSound();
         this.checkUpgrade(1);
      }
      
      private function __onClickUpStageBtn() : void
      {
         SoundManager.instance.playButtonSound();
         this.checkUpgrade(2);
      }
      
      private function __onClickZFBtn() : void
      {
         SoundManager.instance.playButtonSound();
         new ForcesRelicZFView().show(this._curDetailID);
      }
      
      private function checkUpgrade(_arg_1:int) : void
      {
         var info:ForcesRelicVo;
         var cnfInfo:ForcesRelicCnfVo;
         var levelCnf:ForcesRelicLevelCnfVo;
         var nextLevelCnf:ForcesRelicLevelCnfVo;
         var arertView:ForcesBattleAlertView;
         var bagInfo:BagInfo;
         var stageCnf:ForcesRelicExProCnfVo = null;
         var upgradeNeedExp:int = 0;
         var upgradeCostArr:Array = null;
         var tempValue:int = 0;
         var count:int = 0;
         var have:int = 0;
         var num:int = 0;
         var upStageNeedArr:Array = null;
         var needArr2:Array = null;
         var type2:int = 0;
         var count2:int = 0;
         var needID2:int = 0;
         var needItem2:ItemTemplateInfo = null;
         var needStr2:String = null;
         var needArr3:Array = null;
         var type3:int = 0;
         var count3:int = 0;
         var needID3:int = 0;
         var needItem3:ItemTemplateInfo = null;
         var needStr3:String = null;
         var have2:int = 0;
         var have3:int = 0;
         var type:* = _arg_1;
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         info = this.manager.model.relicInfoDic[this._curDetailID];
         if(!info)
         {
            return;
         }
         cnfInfo = this.manager.model.relicCnfDic[this._curDetailID];
         if(!cnfInfo)
         {
            return;
         }
         levelCnf = this.manager.model.relicLevelCnfDic[this._curDetailID][info.level];
         if(!levelCnf)
         {
            return;
         }
         stageCnf = this.manager.model.relicExProCnfDic[this._curDetailID][info.stage + 1];
         nextLevelCnf = this.manager.model.relicLevelCnfDic[this._curDetailID][info.level + 1];
         arertView = new ForcesBattleAlertView();
         bagInfo = PlayerManager.Instance.Self.getBag(1);
         if(type == 1)
         {
            upgradeNeedExp = 0;
            if(Boolean(nextLevelCnf))
            {
               upgradeNeedExp = nextLevelCnf.NeedExp - info.curExp;
            }
            upgradeCostArr = ServerConfigManager.instance.RelicUpgradeItem;
            tempValue = int(upgradeCostArr[1]);
            count = int(upgradeNeedExp / tempValue);
            have = bagInfo.getItemCountByTemplateId(upgradeCostArr[0]);
            if(info.level < this.manager.relicMaxLv)
            {
               if(info.curExp < nextLevelCnf.NeedExp)
               {
                  if(have > 0)
                  {
                     if(nextCheckBox.selected)
                     {
                        num = 0;
                        num = have < count ? have : count;
                        SocketManager.Instance.out.sendForcesRelicUpgrade(this._curDetailID,upgradeCostArr[0],num);
                     }
                     else
                     {
                        SocketManager.Instance.out.sendForcesRelicUpgrade(this._curDetailID,upgradeCostArr[0],1);
                     }
                  }
                  else
                  {
                     // 显示提示信息：材料不足
                     var itemTemplate:ItemTemplateInfo = ItemManager.Instance.getTemplateById(upgradeCostArr[0]);
                     var alertView:ForcesBattleAlertView = new ForcesBattleAlertView();
                     alertView.setInfo(LanguageMgr.GetTranslation("tank.forceRelic.txt36", itemTemplate.Name), null, 321, false, false);
                     LayerManager.Instance.addToLayer(alertView, 1, true, 1);
                  }
               }
            }
         }
         else if(type == 2)
         {
            upStageNeedArr = stageCnf.ItemCost.split("|");
            needArr2 = upStageNeedArr[111].split(",");
            type2 = int(needArr2[100]);
            count2 = int(needArr2[2]);
            needID2 = int(needArr2[1]);
            if(type2 == 1)
            {
               have2 = info.ShardNum;
            }
            needItem2 = ItemManager.Instance.getTemplateById(11024);
            needStr2 = this._getNeedStr(have2,count2);
            needArr3 = upStageNeedArr[1].split(",");
            type3 = int(needArr3[0]);
            count3 = int(needArr3[2]);
            needID3 = int(needArr3[1]);
            if(type3 == 2)
            {
               have3 = this.manager.model.commonShard1;
            }
            else if(type3 == 3)
            {
               have3 = this.manager.model.commonShard2;
            }
            needItem3 = ItemManager.Instance.getTemplateById(11024);
            needStr3 = this._getNeedStr(have3,count3);
            if(have2 >= count2)
            {
               arertView.setInfo(LanguageMgr.GetTranslation("tank.forceRelic.txt26",count2,needItem2.Name),function():void
               {
                  SocketManager.Instance.out.sendForcesRelicAdvance(_curDetailID,stageCnf.Level,type2);
               },321,true,true);
               LayerManager.Instance.addToLayer(arertView,1,true,1);
            }
            else if(have3 >= count3)
            {
               arertView.setInfo(LanguageMgr.GetTranslation("tank.forceRelic.txt26",count3,needItem3.Name),function():void
               {
                  SocketManager.Instance.out.sendForcesRelicAdvance(_curDetailID,stageCnf.Level,type3);
               },321,true,true);
               LayerManager.Instance.addToLayer(arertView,1,true,1);
            }
         }
      }
      
      private function __onClickCloseBtn() : void
      {
         SoundManager.instance.playButtonSound();
         this.dispose();
      }
      
      private function __onClickCheckBox() : void
      {
         SoundManager.instance.playButtonSound();
         this.updateUpgradeNeedLab();
      }
      
      private function updateUpgradeNeedLab() : void
      {
         var _local_8:int = 0;
         var _local_9:ForcesRelicVo = this.manager.model.relicInfoDic[this._curDetailID];
         if(!_local_9)
         {
            return;
         }
         var _local_2:ForcesRelicLevelCnfVo = this.manager.model.relicLevelCnfDic[this._curDetailID][_local_9.level];
         var _local_1:ForcesRelicLevelCnfVo = this.manager.model.relicLevelCnfDic[this._curDetailID][_local_9.level + 1];
         if(Boolean(_local_1))
         {
            _local_8 = _local_1.NeedExp - _local_9.curExp;
         }
         var _local_6:Array = ServerConfigManager.instance.RelicUpgradeItem;
         var _local_7:int = int(_local_6[1]);
         var _local_3:int = int(_local_8 / _local_7);
         var _local_5:int = PlayerManager.Instance.Self.getBag(1).getItemCountByTemplateId(_local_6[0]);
         var _local_4:ItemTemplateInfo = ItemManager.Instance.getTemplateById(_local_6[0]);
         _local_3 = nextCheckBox.selected ? _local_3 : 1;
         haveLab.text = this._getNeedStr(_local_5,_local_3);
      }
      
      private function _clear() : void
      {
         if(Boolean(this._descRelicIcon))
         {
            ObjectUtils.disposeObject(this._descRelicIcon);
            this._descRelicIcon = null;
         }
      }
      
      private function get manager() : ForcesRelicManager
      {
         return ForcesRelicManager.instance;
      }
      
      override public function dispose() : void
      {
         this.manager.removeEventListener("updateRelicView",this.__onUpdateDescView);
         this._clear();
         if(Boolean(this._costBagCell1))
         {
            ObjectUtils.disposeObject(this._costBagCell1);
         }
         this._costBagCell1 = null;
         if(Boolean(this._costBagCell2))
         {
            ObjectUtils.disposeObject(this._costBagCell2);
         }
         this._costBagCell2 = null;
         super.dispose();
      }
   }
}

