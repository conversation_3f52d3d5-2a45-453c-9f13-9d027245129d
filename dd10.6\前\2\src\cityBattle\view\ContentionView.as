package cityBattle.view
{
   import cityBattle.CityBattleManager;
   import cityBattle.event.CityBattleEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.LanguageMgr;
   import ddt.manager.SocketManager;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class ContentionView extends Sprite implements Disposeable
   {
      
      private var _bg:Bitmap;
      
      private var _infoTxt:FilterFrameText;
      
      private var _currentSocreTxt:FilterFrameText;
      
      private var _currentRankTxt:FilterFrameText;
      
      private var blueTable:ContentionTable;
      
      private var redTable:ContentionTable;
      
      private var _inspireBtn:BaseButton;
      
      private var _blueTotalScore:FilterFrameText;
      
      private var _redTotalScore:FilterFrameText;
      
      public function ContentionView()
      {
         super();
         this.initView();
      }
      
      private function initView() : void
      {
         this._bg = ComponentFactory.Instance.creatBitmap("asset.cityBattle.bg2");
         addChild(this._bg);
         this._infoTxt = ComponentFactory.Instance.creatComponentByStylename("contention.info.txt");
         addChild(this._infoTxt);
         this._infoTxt.text = LanguageMgr.GetTranslation("ddt.cityBattle.inspire.introduce");
         this._currentSocreTxt = ComponentFactory.Instance.creatComponentByStylename("contention.currentSocre.txt");
         addChild(this._currentSocreTxt);
         this._currentRankTxt = ComponentFactory.Instance.creatComponentByStylename("contention.currentRank.txt");
         addChild(this._currentRankTxt);
         this._blueTotalScore = ComponentFactory.Instance.creatComponentByStylename("contention.totalScore.txt");
         addChild(this._blueTotalScore);
         this._redTotalScore = ComponentFactory.Instance.creatComponentByStylename("contention.totalScore.txt");
         addChild(this._redTotalScore);
         PositionUtils.setPos(this._redTotalScore,"contention.redTotalScore.txtPos");
         this.blueTable = new ContentionTable(0);
         addChild(this.blueTable);
         this.redTable = new ContentionTable(1);
         addChild(this.redTable);
         this._inspireBtn = ComponentFactory.Instance.creatComponentByStylename("contention.inspireBtn");
         addChild(this._inspireBtn);
         this._inspireBtn.addEventListener("click",this._inspireBtnHandler);
         if(CityBattleManager.instance.now > 0 && CityBattleManager.instance.now < 8)
         {
            if(CityBattleManager.instance.castellanList[CityBattleManager.instance.now - 1].side == CityBattleManager.instance.winnerExchangeInfo[CityBattleManager.instance.now - 1])
            {
               if(CityBattleManager.instance.castellanList[CityBattleManager.instance.now - 1].side == 0)
               {
                  this._inspireBtn.mouseEnabled = true;
                  this._inspireBtn.filters = null;
               }
               else
               {
                  this._inspireBtn.mouseEnabled = false;
                  this._inspireBtn.filters = ComponentFactory.Instance.creatFilters("grayFilter");
               }
            }
            else
            {
               this._inspireBtn.mouseEnabled = true;
               this._inspireBtn.filters = null;
            }
         }
         else
         {
            this._inspireBtn.mouseEnabled = false;
            this._inspireBtn.filters = ComponentFactory.Instance.creatFilters("grayFilter");
         }
         CityBattleManager.instance.addEventListener("score_rank",this._scoreChange);
         SocketManager.Instance.out.cityBattleScore();
      }
      
      private function _scoreChange(_arg_1:CityBattleEvent) : void
      {
         this._currentSocreTxt.text = String(CityBattleManager.instance.myRankScore);
         this._currentRankTxt.text = Boolean(String(CityBattleManager.instance.myRank == -1)) ? LanguageMgr.GetTranslation("ddt.cityBattle.noHaveRank") : String(CityBattleManager.instance.myRank);
         this._blueTotalScore.text = String(CityBattleManager.instance.blueTotalScore);
         this._redTotalScore.text = String(CityBattleManager.instance.redTotalScore);
      }
      
      private function _inspireBtnHandler(_arg_1:MouseEvent) : void
      {
         var _local_2:ContentionInspireFrame = ComponentFactory.Instance.creatComponentByStylename("contention.inspireFrame");
         LayerManager.Instance.addToLayer(_local_2,3,true,2);
      }
      
      public function dispose() : void
      {
         this._inspireBtn.removeEventListener("click",this._inspireBtnHandler);
         CityBattleManager.instance.removeEventListener("score_rank",this._scoreChange);
         ObjectUtils.disposeObject(this._bg);
         this._bg = null;
         ObjectUtils.disposeObject(this._infoTxt);
         this._infoTxt = null;
         ObjectUtils.disposeObject(this._currentSocreTxt);
         this._currentSocreTxt = null;
         ObjectUtils.disposeObject(this._currentRankTxt);
         this._currentRankTxt = null;
         ObjectUtils.disposeObject(this._inspireBtn);
         this._inspireBtn = null;
         ObjectUtils.disposeObject(this._blueTotalScore);
         this._blueTotalScore = null;
         ObjectUtils.disposeObject(this._redTotalScore);
         this._redTotalScore = null;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

