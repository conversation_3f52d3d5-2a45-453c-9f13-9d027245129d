package com.pickgliss.loader
{
   import com.crypto.NewCrypto;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.events.Event;
   import flash.system.LoaderContext;
   import flash.utils.ByteArray;
   
   public class BitmapLoader extends DisplayLoader
   {
      
      private static const InvalidateBitmapDataID:int = 2015;
      
      private var _sourceBitmap:Bitmap;
      
      public function BitmapLoader(_arg_1:int, _arg_2:String)
      {
         super(_arg_1,_arg_2);
      }
      
      override public function get content() : *
      {
         if(this._sourceBitmap == null)
         {
            return null;
         }
         return this._sourceBitmap;
      }
      
      public function get bitmapData() : BitmapData
      {
         if(<PERSON><PERSON>an(this._sourceBitmap))
         {
            return this._sourceBitmap.bitmapData;
         }
         return null;
      }
      
      override protected function __onContentLoadComplete(_arg_1:Event) : void
      {
         this._sourceBitmap = _displayLoader.content as Bitmap;
         super.__onContentLoadComplete(_arg_1);
      }
      
      override public function loadFromBytes(_arg_1:ByteArray) : void
      {
         var _local_2:* = null;
         if(NewCrypto.isEncryed(_arg_1))
         {
            _displayLoader.contentLoaderInfo.addEventListener("complete",this.__onContentLoadComplete);
            _displayLoader.contentLoaderInfo.addEventListener("ioError",__onDisplayIoError);
            _local_2 = NewCrypto.decry(_arg_1);
            if(domain != null)
            {
               _displayLoader.loadBytes(_local_2,new LoaderContext(false,domain));
            }
            else
            {
               _displayLoader.loadBytes(_local_2,Context);
            }
         }
         else
         {
            super.loadFromBytes(_arg_1);
         }
      }
      
      override public function get type() : int
      {
         return 0;
      }
   }
}

