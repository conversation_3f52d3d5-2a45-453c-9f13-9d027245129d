package cityBattle.analyze
{
   import cityBattle.data.WelfareInfo;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   
   public class CityBattleAnalyze extends DataAnalyzer
   {
      
      public var list:Vector.<WelfareInfo>;
      
      public function CityBattleAnalyze(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         this.list = new Vector.<WelfareInfo>();
         var _local_2:XML = new XML(_arg_1);
         if(_local_2.@value == "true")
         {
            _local_3 = _local_2..Item;
            _local_5 = 0;
            while(_local_5 < _local_3.length())
            {
               _local_4 = new WelfareInfo();
               ObjectUtils.copyPorpertiesByXML(_local_4,_local_3[_local_5]);
               this.list.push(_local_4);
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
         }
      }
   }
}

