package bagAndInfo.sywStrength
{
   import ddt.data.player.SelfInfo;
   import ddt.events.PkgEvent;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import road7th.comm.PackageIn;
   import road7th.data.DictionaryData;
   
   public class SywStrengthManager extends EventDispatcher
   {
      
      private static var _instance:SywStrengthManager;
      
      private var _sywStrengthData:DictionaryData;
      
      private var _self:SelfInfo = new SelfInfo();
      
      public function SywStrengthManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get instance() : SywStrengthManager
      {
         if(_instance == null)
         {
            _instance = new SywStrengthManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(998,1),this.__GetSywInfo);
      }
      
      private function __GetSywInfo(_arg_1:PkgEvent) : void
      {
         var pkg:PackageIn = _arg_1.pkg;
         PlayerManager.Instance.Self.SywAttack = pkg.readInt();
         PlayerManager.Instance.Self.SywDefend = pkg.readInt();
         PlayerManager.Instance.Self.SywAgility = pkg.readInt();
         PlayerManager.Instance.Self.SywLuck = pkg.readInt();
         PlayerManager.Instance.Self.SywMagicAttack = pkg.readInt();
         PlayerManager.Instance.Self.SywMagicDefence = pkg.readInt();
         PlayerManager.Instance.Self.SywSkillID = pkg.readUTF();
         dispatchEvent(new Event("playerSywInfo"));
      }
      
      public function sywStrengthDataSetup(param1:SywStoreInfoAnalyzer) : void
      {
         this._sywStrengthData = param1.SywStoreInfoData;
      }
      
      public function sywStoreInfo(param1:int) : SywStoreInfoVo
      {
         return this._sywStrengthData[param1];
      }
      
      public function get SywStrengthData() : DictionaryData
      {
         return this._sywStrengthData;
      }
      
      public function removeEvent() : void
      {
         SocketManager.Instance.removeEventListener(PkgEvent.format(998,1),this.__GetSywInfo);
      }
   }
}

