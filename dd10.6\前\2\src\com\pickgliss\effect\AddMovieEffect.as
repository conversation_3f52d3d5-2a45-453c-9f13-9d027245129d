package com.pickgliss.effect
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.InteractiveObject;
   import flash.display.MovieClip;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class AddMovieEffect extends BaseEffect implements IEffect
   {
      
      private var _movies:Vector.<DisplayObject>;
      
      private var _rectangles:Vector.<Rectangle>;
      
      private var _data:Array;
      
      public function AddMovieEffect(_arg_1:int)
      {
         super(_arg_1);
      }
      
      override public function initEffect(_arg_1:DisplayObject, _arg_2:Array) : void
      {
         super.initEffect(_arg_1,_arg_2);
         this._data = _arg_2;
         this.creatMovie();
      }
      
      public function get movie() : Vector.<DisplayObject>
      {
         return this._movies;
      }
      
      override public function dispose() : void
      {
         var _local_1:int = 0;
         super.dispose();
         _local_1 = 0;
         while(_local_1 < this._movies.length)
         {
            if(this._movies[_local_1] is MovieClip)
            {
               MovieClip(this._movies[_local_1]).stop();
            }
            if(Boolean(this._movies[_local_1]))
            {
               ObjectUtils.disposeObject(this._movies[_local_1]);
            }
            _local_1++;
         }
         this._movies = null;
      }
      
      override public function play() : void
      {
         var _local_1:int = 0;
         super.play();
         _local_1 = 0;
         while(_local_1 < this._movies.length)
         {
            if(this._movies[_local_1] is MovieClip)
            {
               MovieClip(this._movies[_local_1]).play();
            }
            if(Boolean(_target.parent))
            {
               _target.parent.addChild(this._movies[_local_1]);
            }
            this._movies[_local_1].x = _target.x;
            this._movies[_local_1].y = _target.y;
            if(this._rectangles.length - 1 >= _local_1)
            {
               this._movies[_local_1].x += this._rectangles[_local_1].x;
               this._movies[_local_1].y += this._rectangles[_local_1].y;
            }
            _local_1++;
         }
      }
      
      override public function stop() : void
      {
         var _local_1:int = 0;
         super.stop();
         _local_1 = 0;
         while(_local_1 < this._movies.length)
         {
            if(this._movies[_local_1] is MovieClip)
            {
               MovieClip(this._movies[_local_1]).stop();
            }
            if(Boolean(this._movies[_local_1].parent))
            {
               this._movies[_local_1].parent.removeChild(this._movies[_local_1]);
            }
            _local_1++;
         }
      }
      
      private function creatMovie() : void
      {
         var _local_3:int = 0;
         var _local_1:int = 0;
         var _local_2:int = 0;
         this._movies = new Vector.<DisplayObject>();
         this._rectangles = new Vector.<Rectangle>();
         _local_3 = 0;
         while(_local_3 < this._data.length)
         {
            if(this._data[_local_3] is DisplayObject)
            {
               this._movies.push(this._data[_local_3]);
            }
            else if(this._data[_local_3] is String)
            {
               this._movies.push(ComponentFactory.Instance.creat(this._data[_local_3]));
            }
            _local_3++;
         }
         _local_1 = 0;
         while(_local_1 < this._data.length)
         {
            if(this._data[_local_1] is Point)
            {
               this._rectangles.push(new Rectangle(this._data[_local_1].x,this._data[_local_1].y,0,0));
            }
            else if(this._data[_local_1] is Rectangle)
            {
               this._rectangles.push(this._data[_local_1]);
            }
            _local_1++;
         }
         _local_2 = 0;
         while(_local_2 < this._movies.length)
         {
            if(this._movies[_local_2] is InteractiveObject)
            {
               InteractiveObject(this._movies[_local_2]).mouseEnabled = false;
            }
            if(this._movies[_local_2] is DisplayObjectContainer)
            {
               DisplayObjectContainer(this._movies[_local_2]).mouseChildren = false;
               DisplayObjectContainer(this._movies[_local_2]).mouseEnabled = false;
            }
            _local_2++;
         }
      }
   }
}

