package com.pickgliss.ui.controls
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.geom.InnerRectangle;
   import com.pickgliss.geom.OuterRectPos;
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.core.Component;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.DisplayUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.DisplayObject;
   import flash.display.InteractiveObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   
   [Event(name="response",type="com.pickgliss.events.FrameEvent")]
   public class Frame extends Component
   {
      
      public static const P_backgound:String = "backgound";
      
      public static const P_closeButton:String = "closeButton";
      
      public static const P_closeInnerRect:String = "closeInnerRect";
      
      public static const P_containerX:String = "containerX";
      
      public static const P_containerY:String = "containerY";
      
      public static const P_disposeChildren:String = "disposeChildren";
      
      public static const P_moveEnable:String = "moveEnable";
      
      public static const P_moveInnerRect:String = "moveInnerRect";
      
      public static const P_title:String = "title";
      
      public static const P_titleText:String = "titleText";
      
      public static const P_titleOuterRectPos:String = "titleOuterRectPos";
      
      public static const P_escEnable:String = "escEnable";
      
      public static const P_enterEnable:String = "enterEnable";
      
      protected var _backStyle:String;
      
      protected var _backgound:DisplayObject;
      
      protected var _closeButton:BaseButton;
      
      protected var _closeInnerRect:InnerRectangle;
      
      protected var _closeInnerRectString:String;
      
      protected var _closestyle:String;
      
      protected var _container:Sprite;
      
      protected var _containerPosString:String;
      
      protected var _containerX:Number;
      
      protected var _containerY:Number;
      
      protected var _moveEnable:Boolean;
      
      protected var _moveInnerRect:InnerRectangle;
      
      protected var _moveInnerRectString:String = "";
      
      protected var _moveRect:Sprite;
      
      protected var _title:TextField;
      
      protected var _titleStyle:String;
      
      protected var _titleText:String = "";
      
      protected var _disposeChildren:Boolean = true;
      
      protected var _titleOuterRectPosString:String;
      
      protected var _titleOuterRectPos:OuterRectPos;
      
      protected var _escEnable:Boolean;
      
      protected var _autoExit:Boolean = false;
      
      protected var _enterEnable:Boolean;
      
      public function Frame()
      {
         super();
         addEventListener("addedToStage",this.__onAddToStage);
         addEventListener("mouseDown",this.__onMouseClickSetFocus);
      }
      
      protected function __onMouseClickSetFocus(_arg_1:MouseEvent) : void
      {
         StageReferance.stage.focus = _arg_1.target as InteractiveObject;
      }
      
      public function addToContent(_arg_1:DisplayObject) : void
      {
         this._container.addChild(_arg_1);
      }
      
      public function set backStyle(_arg_1:String) : void
      {
         if(this._backStyle == _arg_1)
         {
            return;
         }
         this._backStyle = _arg_1;
         this.backgound = ComponentFactory.Instance.creat(this._backStyle);
      }
      
      public function set backgound(_arg_1:DisplayObject) : void
      {
         if(this._backgound == _arg_1)
         {
            return;
         }
         ObjectUtils.disposeObject(this._backgound);
         this._backgound = _arg_1;
         if(this._backgound is InteractiveObject)
         {
            InteractiveObject(this._backgound).mouseEnabled = true;
         }
         onPropertiesChanged("backgound");
      }
      
      public function get closeButton() : BaseButton
      {
         return this._closeButton;
      }
      
      public function set closeButton(_arg_1:BaseButton) : void
      {
         if(this._closeButton == _arg_1)
         {
            return;
         }
         if(Boolean(this._closeButton))
         {
            this._closeButton.removeEventListener("click",this.__onCloseClick);
            ObjectUtils.disposeObject(this._closeButton);
         }
         this._closeButton = _arg_1;
         onPropertiesChanged("closeButton");
      }
      
      public function set closeInnerRectString(_arg_1:String) : void
      {
         if(this._closeInnerRectString == _arg_1)
         {
            return;
         }
         this._closeInnerRectString = _arg_1;
         this._closeInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._closeInnerRectString));
         onPropertiesChanged("closeInnerRect");
      }
      
      public function set closestyle(_arg_1:String) : void
      {
         if(this._closestyle == _arg_1)
         {
            return;
         }
         this._closestyle = _arg_1;
         this.closeButton = ComponentFactory.Instance.creat(this._closestyle);
      }
      
      public function set containerX(_arg_1:Number) : void
      {
         if(this._containerX == _arg_1)
         {
            return;
         }
         this._containerX = _arg_1;
         onPropertiesChanged("containerX");
      }
      
      public function set containerY(_arg_1:Number) : void
      {
         if(this._containerY == _arg_1)
         {
            return;
         }
         this._containerY = _arg_1;
         onPropertiesChanged("containerY");
      }
      
      public function set titleOuterRectPosString(_arg_1:String) : void
      {
         if(this._titleOuterRectPosString == _arg_1)
         {
            return;
         }
         this._titleOuterRectPosString = _arg_1;
         this._titleOuterRectPos = ClassUtils.CreatInstance("com.pickgliss.geom.OuterRectPos",ComponentFactory.parasArgs(this._titleOuterRectPosString));
         onPropertiesChanged("titleOuterRectPos");
      }
      
      override public function dispose() : void
      {
         var _local_1:DisplayObject = StageReferance.stage.focus as DisplayObject;
         if(Boolean(_local_1) && contains(_local_1))
         {
            StageReferance.stage.focus = null;
         }
         StageReferance.stage.removeEventListener("mouseUp",this.__onFrameMoveStop);
         StageReferance.stage.removeEventListener("mouseMove",this.__onMoveWindow);
         StageReferance.stage.removeEventListener("keyDown",this.__onKeyDown);
         removeEventListener("mouseDown",this.__onMouseClickSetFocus);
         removeEventListener("addedToStage",this.__onAddToStage);
         if(Boolean(this._backgound))
         {
            ObjectUtils.disposeObject(this._backgound);
         }
         this._backgound = null;
         if(Boolean(this._closeButton))
         {
            this._closeButton.removeEventListener("click",this.__onCloseClick);
            ObjectUtils.disposeObject(this._closeButton);
         }
         this._closeButton = null;
         if(Boolean(this._title))
         {
            ObjectUtils.disposeObject(this._title);
         }
         this._title = null;
         if(this._disposeChildren)
         {
            ObjectUtils.disposeAllChildren(this._container);
         }
         if(Boolean(this._container))
         {
            ObjectUtils.disposeObject(this._container);
         }
         this._container = null;
         if(Boolean(this._moveRect))
         {
            this._moveRect.removeEventListener("mouseDown",this.__onFrameMoveStart);
         }
         ObjectUtils.disposeObject(this._moveRect);
         this._moveRect = null;
         super.dispose();
      }
      
      public function get disposeChildren() : Boolean
      {
         return this._disposeChildren;
      }
      
      public function set disposeChildren(_arg_1:Boolean) : void
      {
         if(this._disposeChildren == _arg_1)
         {
            return;
         }
         this._disposeChildren = _arg_1;
         onPropertiesChanged("disposeChildren");
      }
      
      public function set moveEnable(_arg_1:Boolean) : void
      {
         if(this._moveEnable == _arg_1)
         {
            return;
         }
         this._moveEnable = _arg_1;
         onPropertiesChanged("moveEnable");
      }
      
      public function set moveInnerRectString(_arg_1:String) : void
      {
         if(this._moveInnerRectString == _arg_1)
         {
            return;
         }
         this._moveInnerRectString = _arg_1;
         this._moveInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._moveInnerRectString));
         onPropertiesChanged("moveInnerRect");
      }
      
      public function set title(_arg_1:TextField) : void
      {
         if(this._title == _arg_1)
         {
            return;
         }
         this._title = _arg_1;
         onPropertiesChanged("title");
      }
      
      public function set titleStyle(_arg_1:String) : void
      {
         if(this._titleStyle == _arg_1)
         {
            return;
         }
         this._titleStyle = _arg_1;
         this.title = ComponentFactory.Instance.creat(this._titleStyle);
      }
      
      public function set titleText(_arg_1:String) : void
      {
         if(this._titleText == _arg_1)
         {
            return;
         }
         this._titleText = _arg_1;
         onPropertiesChanged("titleText");
      }
      
      protected function __onAddToStage(_arg_1:Event) : void
      {
         stage.focus = this;
      }
      
      protected function __onCloseClick(_arg_1:MouseEvent) : void
      {
         this.onResponse(0);
      }
      
      protected function __onKeyDown(_arg_1:KeyboardEvent) : void
      {
         var _local_2:DisplayObject = StageReferance.stage.focus as DisplayObject;
         if(DisplayUtils.isTargetOrContain(_local_2,this))
         {
            if(_arg_1.keyCode == 13 && this.enterEnable)
            {
               if(_local_2 is TextField && TextField(_local_2).type == "input")
               {
                  return;
               }
               this.onResponse(2);
               _arg_1.stopImmediatePropagation();
            }
            else if(_arg_1.keyCode == 27 && this.escEnable)
            {
               this.onResponse(1);
               _arg_1.stopImmediatePropagation();
            }
         }
      }
      
      public function set escEnable(_arg_1:Boolean) : void
      {
         if(this._escEnable == _arg_1)
         {
            return;
         }
         this._escEnable = _arg_1;
         onPropertiesChanged("escEnable");
      }
      
      public function get escEnable() : Boolean
      {
         return this._escEnable;
      }
      
      public function set autoExit(_arg_1:Boolean) : void
      {
         if(this._autoExit == _arg_1)
         {
            return;
         }
         this._autoExit = _arg_1;
      }
      
      public function get autoExit() : Boolean
      {
         return this._autoExit;
      }
      
      protected function onFrameClose() : void
      {
      }
      
      public function set enterEnable(_arg_1:Boolean) : void
      {
         if(this._enterEnable == _arg_1)
         {
            return;
         }
         this._enterEnable = _arg_1;
         onPropertiesChanged("enterEnable");
      }
      
      public function get enterEnable() : Boolean
      {
         return this._enterEnable;
      }
      
      protected function onResponse(_arg_1:int) : void
      {
         dispatchEvent(new FrameEvent(_arg_1));
         if(_arg_1 == 0 || _arg_1 == 1)
         {
            this.onFrameClose();
            if(this._autoExit)
            {
               this.dispose();
            }
         }
      }
      
      protected function __onFrameMoveStart(_arg_1:MouseEvent) : void
      {
         StageReferance.stage.addEventListener("mouseMove",this.__onMoveWindow);
         StageReferance.stage.addEventListener("mouseUp",this.__onFrameMoveStop);
         startDrag();
      }
      
      protected function __onFrameMoveStop(_arg_1:MouseEvent) : void
      {
         StageReferance.stage.removeEventListener("mouseUp",this.__onFrameMoveStop);
         StageReferance.stage.removeEventListener("mouseMove",this.__onMoveWindow);
         stopDrag();
      }
      
      override protected function addChildren() : void
      {
         if(Boolean(this._backgound))
         {
            addChild(this._backgound);
         }
         if(Boolean(this._title))
         {
            addChild(this._title);
         }
         addChild(this._moveRect);
         addChild(this._container);
         if(Boolean(this._closeButton))
         {
            addChild(this._closeButton);
         }
      }
      
      override protected function init() : void
      {
         this._container = new Sprite();
         this._moveRect = new Sprite();
         super.init();
      }
      
      override protected function onProppertiesUpdate() : void
      {
         super.onProppertiesUpdate();
         if((Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["width"])) && this._backgound != null)
         {
            this._backgound.width = _width;
            this._backgound.height = _height;
            this.updateClosePos();
         }
         if(Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["width"]) || Boolean(_changedPropeties["moveInnerRect"]))
         {
            this.updateMoveRect();
         }
         if(Boolean(_changedPropeties["closeButton"]))
         {
            this._closeButton.addEventListener("click",this.__onCloseClick);
         }
         if(Boolean(_changedPropeties["closeButton"]) || Boolean(_changedPropeties["closeInnerRect"]))
         {
            this.updateClosePos();
         }
         if(Boolean(_changedPropeties["containerX"]) || Boolean(_changedPropeties["containerY"]))
         {
            this.updateContainerPos();
         }
         if(Boolean(_changedPropeties["titleOuterRectPos"]) || Boolean(_changedPropeties["titleText"]) || Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["width"]))
         {
            if(this._title != null)
            {
               this._title.text = this._titleText;
            }
            this.updateTitlePos();
         }
         if(Boolean(_changedPropeties["moveEnable"]))
         {
            if(this._moveEnable)
            {
               this._moveRect.addEventListener("mouseDown",this.__onFrameMoveStart);
            }
            else
            {
               this._moveRect.removeEventListener("mouseDown",this.__onFrameMoveStart);
            }
         }
         if(this._escEnable || this._enterEnable)
         {
            StageReferance.stage.addEventListener("keyDown",this.__onKeyDown);
         }
         else
         {
            StageReferance.stage.removeEventListener("keyDown",this.__onKeyDown);
         }
      }
      
      protected function updateClosePos() : void
      {
         if(Boolean(this._closeButton) && Boolean(this._closeInnerRect))
         {
            DisplayUtils.layoutDisplayWithInnerRect(this._closeButton,this._closeInnerRect,_width,_height);
         }
      }
      
      protected function updateContainerPos() : void
      {
         this._container.x = this._containerX;
         this._container.y = this._containerY;
      }
      
      protected function updateMoveRect() : void
      {
         if(this._moveInnerRect == null)
         {
            return;
         }
         var _local_1:Rectangle = this._moveInnerRect.getInnerRect(_width,_height);
         this._moveRect.graphics.clear();
         this._moveRect.graphics.beginFill(0,0);
         this._moveRect.graphics.drawRect(_local_1.x,_local_1.y,_local_1.width,_local_1.height);
         this._moveRect.graphics.endFill();
      }
      
      protected function updateTitlePos() : void
      {
         if(this._title == null)
         {
            return;
         }
         if(this._titleOuterRectPos == null)
         {
            return;
         }
         var _local_1:Point = this._titleOuterRectPos.getPos(this._title.width,this._title.height,_width,_height);
         this._title.x = _local_1.x;
         this._title.y = _local_1.y;
      }
      
      protected function __onMoveWindow(_arg_1:MouseEvent) : void
      {
         if(DisplayUtils.isInTheStage(new Point(_arg_1.localX,_arg_1.localY),this))
         {
            _arg_1.updateAfterEvent();
         }
         else
         {
            this.__onFrameMoveStop(null);
         }
      }
   }
}

