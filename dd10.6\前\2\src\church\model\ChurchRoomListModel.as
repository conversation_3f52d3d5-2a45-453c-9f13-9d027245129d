package church.model
{
   import ddt.data.ChurchRoomInfo;
   import road7th.data.DictionaryData;
   
   public class ChurchRoomListModel
   {
      
      private var _roomList:DictionaryData;
      
      public function ChurchRoomListModel()
      {
         super();
         this._roomList = new DictionaryData(true);
      }
      
      public function get roomList() : DictionaryData
      {
         return this._roomList;
      }
      
      public function addRoom(_arg_1:ChurchRoomInfo) : void
      {
         if(<PERSON><PERSON>an(_arg_1))
         {
            this._roomList.add(_arg_1.id,_arg_1);
         }
      }
      
      public function removeRoom(_arg_1:int) : void
      {
         if(<PERSON><PERSON>an(this._roomList[_arg_1]))
         {
            this._roomList.remove(_arg_1);
         }
      }
      
      public function updateRoom(_arg_1:ChurchRoomInfo) : void
      {
         if(Boolean(_arg_1))
         {
            this._roomList.add(_arg_1.id,_arg_1);
         }
      }
      
      public function dispose() : void
      {
         this._roomList = null;
      }
   }
}

