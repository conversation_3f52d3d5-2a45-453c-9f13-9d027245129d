package catalog.data
{
   import flash.utils.Dictionary;
   import road7th.data.DictionaryData;
   
   public class CatalogModel
   {
      
      public static const PropArr:Array = [31,32,33,34,37,36,35,101,102,1,3,7,9,5];
      
      public var cfgChip:DictionaryData = null;
      
      public var cfgCondition:DictionaryData = null;
      
      public var conditionLength:int;
      
      public var playerCatalogArr:Dictionary = null;
      
      public var currentInfo:CatalogConditionData;
      
      public var activationNum:int;
      
      public var conditionProp:Dictionary = new Dictionary();
      
      public function CatalogModel()
      {
         super();
      }
      
      public function isActivation(param1:CatalogConditionData) : Boolean
      {
         var _loc2_:Array = null;
         var _loc3_:int = 0;
         var _loc4_:Array = null;
         var _loc5_:int = 0;
         if(Boolean(param1.NeedPlaces))
         {
            _loc2_ = param1.NeedPlaces.split("|");
            _loc5_ = 0;
            _loc3_ = 0;
            while(_loc3_ < _loc2_.length)
            {
               if(this.getPlaceChip(_loc2_[_loc3_],param1))
               {
                  _loc5_++;
               }
               _loc3_++;
            }
            if(_loc5_ < _loc2_.length)
            {
               return false;
            }
         }
         if(Boolean(param1.NeedAllNumber))
         {
            if(this.playerCatalogArr[param1.FocusId].length < int(param1.NeedAllNumber))
            {
               return false;
            }
         }
         if(Boolean(param1.NeedCharacterNum))
         {
            _loc4_ = param1.NeedCharacterNum.split("|");
            _loc5_ = this.getCharacterNum(_loc4_[0],param1);
            if(_loc5_ < _loc4_[1])
            {
               return false;
            }
         }
         return true;
      }
      
      public function getPlaceChip(param1:int, param2:CatalogConditionData) : Boolean
      {
         var _loc3_:int = 0;
         var _loc4_:CatalogChipTemplateData = null;
         var _loc5_:Array = this.playerCatalogArr[param2.FocusId];
         _loc3_ = 0;
         while(_loc3_ < _loc5_.length)
         {
            _loc4_ = this.cfgChip[_loc5_[_loc3_]];
            if(_loc4_.Place == param1)
            {
               return true;
            }
            _loc3_++;
         }
         return false;
      }
      
      public function getCharacterNum(param1:int, param2:CatalogConditionData) : int
      {
         var _loc3_:int = 0;
         var _loc4_:CatalogChipTemplateData = null;
         var _loc5_:int = 0;
         var _loc6_:Array = this.playerCatalogArr[param2.FocusId];
         _loc3_ = 0;
         while(_loc3_ < _loc6_.length)
         {
            _loc4_ = this.cfgChip[_loc6_[_loc3_]];
            if(_loc4_.Character >= param1)
            {
               _loc5_++;
            }
            _loc3_++;
         }
         return _loc5_;
      }
      
      public function getAllAttribute() : Dictionary
      {
         var _loc12_:* = undefined;
         var _loc13_:* = undefined;
         var _loc14_:* = undefined;
         var _loc15_:* = undefined;
         var _loc1_:Array = null;
         var _loc2_:Array = null;
         var _loc3_:Array = null;
         var _loc4_:CatalogConditionData = null;
         var _loc5_:CatalogChipTemplateData = null;
         var _loc6_:int = 0;
         var _loc7_:Array = null;
         var _loc8_:int = 0;
         var _loc9_:Array = null;
         var _loc10_:Dictionary = new Dictionary();
         var _loc11_:int = 0;
         this.activationNum = 0;
         for(_loc12_ in this.playerCatalogArr)
         {
            _loc1_ = this.playerCatalogArr[_loc12_];
            _loc2_ = new Array(0,0,0,0,0,0,0,0,0,0,0,0,0,0);
            _loc3_ = new Array(0,0,0,0,0,0,0,0,0,0,0,0,0,0);
            for(_loc13_ in this.cfgCondition)
            {
               if(this.cfgCondition[_loc13_].FocusId == _loc12_)
               {
                  _loc4_ = this.cfgCondition[_loc13_];
                  break;
               }
            }
            _loc11_ = 0;
            while(_loc11_ < _loc1_.length)
            {
               _loc5_ = this.cfgChip[_loc1_[_loc11_]];
               _loc6_ = 0;
               while(_loc6_ < 14)
               {
                  _loc14_ = _loc6_;
                  _loc15_ = _loc2_[_loc14_] + _loc5_["Attribute" + (_loc6_ + 1)];
                  _loc2_[_loc14_] = _loc15_;
                  _loc3_[_loc6_] += _loc5_["Attribute" + (_loc6_ + 1)];
                  _loc6_++;
               }
               _loc11_++;
            }
            if(Boolean(_loc4_) && this.isActivation(_loc4_))
            {
               ++this.activationNum;
               _loc7_ = _loc4_.AttributeValue.split(",");
               _loc8_ = 0;
               while(_loc8_ < _loc7_.length)
               {
                  _loc9_ = _loc7_[_loc8_].split("|");
                  _loc3_[PropArr.indexOf(int(_loc9_[0]))] = _loc3_[PropArr.indexOf(int(_loc9_[0]))] + int(_loc9_[1]);
                  _loc8_++;
               }
               _loc11_ = 0;
               while(_loc11_ < 14)
               {
                  _loc15_ = _loc11_;
                  _loc14_ = _loc3_[_loc15_] * (1 + _loc4_.AttributePercent / 1000);
                  _loc3_[_loc15_] = _loc14_;
                  _loc11_++;
               }
            }
            this.conditionProp[_loc12_] = _loc3_;
            _loc10_[_loc12_] = _loc2_;
         }
         return _loc10_;
      }
   }
}

