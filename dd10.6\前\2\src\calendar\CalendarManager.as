package calendar
{
   import activeEvents.data.ActiveEventsInfo;
   import calendar.event.CalendarEvent;
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import ddt.data.analyze.ActiveEventsAnalyzer;
   import ddt.data.analyze.ActiveExchangeAnalyzer;
   import ddt.data.analyze.CalendarSignAnalyze;
   import ddt.data.analyze.DaylyGiveAnalyzer;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PathManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.utils.AssetModuleLoader;
   import ddt.utils.RequestVairableCreater;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.net.URLVariables;
   import flash.utils.Dictionary;
   import flash.utils.getTimer;
   import mainbutton.MainButtnController;
   import road7th.comm.PackageIn;
   import wonderfulActivity.WonderfulActivityManager;
   
   public class CalendarManager extends EventDispatcher
   {
      
      private static var _isOK:Boolean;
      
      private static var _ins:CalendarManager;
      
      private var _localVisible:Boolean = false;
      
      private var _model:CalendarModel;
      
      private var _today:Date;
      
      private var _signCount:int;
      
      private var _restroSignCount:int;
      
      private var _dayLogDic:Dictionary = new Dictionary();
      
      private var _startTime:int;
      
      private var _luckyNum:int = -1;
      
      private var _myLuckyNum:int = -1;
      
      private var _initialized:Boolean = false;
      
      private var _responseLuckyNum:Boolean = true;
      
      private var _currentModel:int;
      
      private var _times:int;
      
      private var _price:int;
      
      private var _isQQopen:Boolean = false;
      
      private var _activeID:int;
      
      private var _showInfo:ActiveEventsInfo;
      
      private var _eventActives:Array;
      
      private var _activeExchange:Array;
      
      private var _dailyInfo:Array;
      
      private var _signAwards:Array;
      
      private var _signAwardCounts:Array;
      
      private var _signPetInfo:Array;
      
      private var _dailyAwardState:Boolean = true;
      
      public function CalendarManager()
      {
         super();
      }
      
      public static function getInstance() : CalendarManager
      {
         if(_ins == null)
         {
            _ins = new CalendarManager();
         }
         return _ins;
      }
      
      public function initialize() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(161),this.__userLuckyNum);
      }
      
      public function requestLuckyNum() : void
      {
         if(this._responseLuckyNum)
         {
            SocketManager.Instance.out.sendUserLuckyNum(-1,false);
            this._responseLuckyNum = false;
         }
      }
      
      private function __userLuckyNum(_arg_1:PkgEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         this._luckyNum = _local_3.readInt();
         var _local_2:String = _local_3.readUTF();
         if(Boolean(this._model))
         {
            this._model.luckyNum = this._luckyNum;
            this._model.myLuckyNum = this._myLuckyNum;
         }
         this._responseLuckyNum = true;
      }
      
      public function open(_arg_1:int, _arg_2:Boolean = false) : void
      {
         this._currentModel = _arg_1;
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatDailyInfoLoader());
         AssetModuleLoader.addRequestLoader(this.request());
         var _local_3:Boolean = this._initialized && (!this._localVisible || _arg_2) && Boolean(this._today);
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatActionExchangeInfoLoader(),_local_3);
         AssetModuleLoader.addModelLoader("ddtcalendar",6);
         AssetModuleLoader.startCodeLoader(this.loadDataComplete);
      }
      
      private function loadDataComplete() : void
      {
         this._localVisible = true;
         SocketManager.Instance.addEventListener(PkgEvent.format(293),this.__onOpenDailyView);
         SocketManager.Instance.out.sendOpenDailyView();
      }
      
      private function __onOpenDailyView(_arg_1:PkgEvent) : void
      {
         SocketManager.Instance.removeEventListener(PkgEvent.format(293),this.__onOpenDailyView);
         this._model = new CalendarModel(this._today,this._signCount,this._dayLogDic,this._signAwards,this._signAwardCounts,this._eventActives,this._activeExchange);
         this._model.luckyNum = this._luckyNum;
         this._model.myLuckyNum = this._myLuckyNum;
         var _local_2:Date = new Date();
         if(_local_2.time - this._today.time > 86400000)
         {
            SocketManager.Instance.out.sendErrorMsg("打开签到的时候，客户端时间与服务器时间间隔超过一天。by" + PlayerManager.Instance.Self.NickName);
         }
         dispatchEvent(new CalendarEvent("calendarOpenView"));
         this.requestLuckyNum();
      }
      
      public function get luckyNum() : int
      {
         return this._luckyNum;
      }
      
      public function qqOpen(_arg_1:int) : void
      {
         this._isQQopen = true;
         this._activeID = _arg_1;
         if(this._initialized && !this._localVisible)
         {
            this.open(2);
         }
         dispatchEvent(new CalendarEvent("calendarqqOpenView"));
      }
      
      public function request() : BaseLoader
      {
         var _local_1:URLVariables = RequestVairableCreater.creatWidthKey(true);
         _local_1["rnd"] = Math.random();
         var _local_2:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("DailyLogList.ashx"),7,_local_1);
         _local_2.analyzer = new CalendarSignAnalyze(this.calendarSignComplete);
         LoadResourceManager.Instance.startLoad(_local_2);
         return _local_2;
      }
      
      public function requestActiveEvent() : BaseLoader
      {
         var _local_1:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("ActiveList.xml"),5);
         _local_1.loadErrorMessage = LanguageMgr.GetTranslation("ddt.loader.loadingActivityInformationFailure");
         _local_1.analyzer = new ActiveEventsAnalyzer(this.setEventActivity);
         return _local_1;
      }
      
      public function requestActionExchange() : BaseLoader
      {
         var _local_1:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("ActiveConvertItemInfo.xml"),2);
         _local_1.loadErrorMessage = LanguageMgr.GetTranslation("ddt.loader.loadingActivityInformationFailure");
         _local_1.analyzer = new ActiveExchangeAnalyzer(this.setActivityExchange);
         return _local_1;
      }
      
      private function calendarSignComplete(_arg_1:CalendarSignAnalyze) : void
      {
         var _local_6:int = 0;
         var _local_4:int = 0;
         var _local_3:Date = new Date();
         this._startTime = getTimer();
         this._today = _arg_1.date;
         this._times = _arg_1.times;
         this._price = _arg_1.price;
         _isOK = _arg_1.isOK == "True" ? true : false;
         this._signCount = 0;
         var _local_2:Array = _arg_1.dayLog.split(",");
         var _local_5:int = CalendarModel.getMonthMaxDay(this._today.month,this._today.fullYear);
         if(_local_2.length <= 0)
         {
            _isOK = false;
         }
         _local_6 = 0;
         while(_local_6 < _local_5)
         {
            if(_local_6 < _local_2.length && _local_2[_local_6] == "True")
            {
               _local_4 = _local_6 + 1;
               ++this._signCount;
               this._dayLogDic[_local_6 + 1] = "True";
               if(_local_4 == _local_3.date)
               {
                  PlayerManager.Instance.Self.Sign = true;
                  MainButtnController.instance.dispatchEvent(new Event(MainButtnController.CLOSESIGN));
               }
            }
            else
            {
               this._dayLogDic[_local_6 + 1] = "False";
            }
            _local_6++;
         }
         if(Boolean(this._model) && this._localVisible)
         {
            this._model.today = this._today;
            this._model.signCount = this._signCount;
            this._model.dayLog = this._dayLogDic;
         }
      }
      
      public function hasTodaySigned() : Boolean
      {
         return Boolean(this._dayLogDic) && Boolean(this._today) && this._dayLogDic[this._today.date.toString()] == "True";
      }
      
      private function addEvent() : void
      {
      }
      
      private function removeEvent() : void
      {
      }
      
      public function get isShow() : Boolean
      {
         return this._localVisible;
      }
      
      public function checkEventInfo() : Boolean
      {
         var _local_1:ActiveEventsInfo = null;
         for each(_local_1 in this._eventActives)
         {
            if(_local_1.IsShow == true && !_local_1.overdue())
            {
               this._showInfo = _local_1;
               return true;
            }
         }
         return false;
      }
      
      public function getShowActiveInfo() : ActiveEventsInfo
      {
         return this._showInfo;
      }
      
      public function setEventActivity(_arg_1:ActiveEventsAnalyzer) : void
      {
         this._eventActives = _arg_1.list;
         WonderfulActivityManager.Instance.setLimitActivities(this._eventActives);
      }
      
      public function get eventActives() : Array
      {
         return this._eventActives;
      }
      
      public function setActivityExchange(_arg_1:ActiveExchangeAnalyzer) : void
      {
         this._activeExchange = _arg_1.list;
      }
      
      public function get activeExchange() : Array
      {
         return this._activeExchange;
      }
      
      public function setDailyInfo(_arg_1:DaylyGiveAnalyzer) : void
      {
         this._dailyInfo = _arg_1.list;
         this._signAwards = _arg_1.signAwardList;
         this._signAwardCounts = _arg_1.signAwardCounts;
         this._signPetInfo = _arg_1.signPetInfo;
         this._initialized = true;
      }
      
      public function set dailyAwardState(_arg_1:Boolean) : void
      {
         this._dailyAwardState = _arg_1;
      }
      
      public function get dailyAwardState() : Boolean
      {
         return this._dailyAwardState;
      }
      
      public function set isQQopen(_arg_1:Boolean) : void
      {
         this._isQQopen = _arg_1;
      }
      
      public function get isQQopen() : Boolean
      {
         return this._isQQopen;
      }
      
      public function get activeID() : int
      {
         return this._activeID;
      }
      
      public function get model() : CalendarModel
      {
         return this._model;
      }
      
      public function set localVisible(_arg_1:Boolean) : void
      {
         this._localVisible = _arg_1;
      }
      
      public function get currentModel() : int
      {
         return this._currentModel;
      }
      
      public function get startTime() : int
      {
         return this._startTime;
      }
      
      public function set model(_arg_1:CalendarModel) : void
      {
         this._model = _arg_1;
      }
      
      public function set signCount(_arg_1:int) : void
      {
         this._signCount = _arg_1;
      }
      
      public function get isOK() : Boolean
      {
         return _isOK;
      }
      
      public function set isOK(_arg_1:Boolean) : void
      {
         _isOK = _arg_1;
      }
      
      public function get price() : int
      {
         return this._price;
      }
      
      public function get signPetInfo() : Array
      {
         return this._signPetInfo;
      }
      
      public function get times() : int
      {
         return this._times;
      }
      
      public function set times(_arg_1:int) : void
      {
         this._times = _arg_1;
      }
      
      public function get localVisible() : Boolean
      {
         return this._localVisible;
      }
   }
}

