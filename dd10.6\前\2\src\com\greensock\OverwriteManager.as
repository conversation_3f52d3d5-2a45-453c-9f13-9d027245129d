package com.greensock
{
   import com.greensock.core.*;
   
   public final class OverwriteManager
   {
      
      public static var mode:int;
      
      public static var enabled:Boolean;
      
      public static const version:Number = 6.1;
      
      public static const NONE:int = 0;
      
      public static const ALL_IMMEDIATE:int = 1;
      
      public static const AUTO:int = 2;
      
      public static const CONCURRENT:int = 3;
      
      public static const ALL_ONSTART:int = 4;
      
      public static const PREEXISTING:int = 5;
      
      public function OverwriteManager()
      {
         super();
      }
      
      public static function init(_arg_1:int = 2) : int
      {
         TweenLite.overwriteManager = OverwriteManager;
         mode = _arg_1;
         enabled = true;
         return mode;
      }
      
      public static function manageOverwrites(_arg_1:TweenLite, _arg_2:Object, _arg_3:Array, _arg_4:int) : Boolean
      {
         var _local_10:Boolean = false;
         var _local_11:int = 0;
         var _local_8:int = 0;
         var _local_14:Number = NaN;
         var _local_6:Number = NaN;
         var _local_18:int = 0;
         var _local_9:int = 0;
         var _local_16:* = undefined;
         var _local_19:* = undefined;
         var _local_15:* = null;
         var _local_7:* = null;
         var _local_5:* = null;
         if(_arg_4 >= 4)
         {
            _local_8 = int(_arg_3.length);
            _local_11 = 0;
            while(_local_11 < _local_8)
            {
               _local_15 = _arg_3[_local_11];
               if(_local_15 != _arg_1)
               {
                  if(Boolean(_local_15.setEnabled(false,false)))
                  {
                     _local_10 = true;
                  }
               }
               else if(_arg_4 == 5)
               {
                  break;
               }
               _local_11++;
            }
            return _local_10;
         }
         var _local_12:Number = _arg_1.cachedStartTime + 1e-10;
         var _local_13:Array = [];
         var _local_17:Array = [];
         _local_11 = int(_arg_3.length);
         while(--_local_11 > -1)
         {
            _local_15 = _arg_3[_local_11];
            if(!(_local_15 == _arg_1 || Boolean(_local_15.gc) || !_local_15.initted && _local_12 - _local_15.cachedStartTime <= 2e-10))
            {
               if(_local_15.timeline != _arg_1.timeline)
               {
                  if(!getGlobalPaused(_local_15))
                  {
                     _local_19 = _local_18++;
                     _local_17[_local_19] = _local_15;
                  }
               }
               else if(_local_15.cachedStartTime <= _local_12 && _local_15.cachedStartTime + _local_15.totalDuration + 1e-10 > _local_12 && !_local_15.cachedPaused && !(_arg_1.cachedDuration == 0 && _local_12 - _local_15.cachedStartTime <= 2e-10))
               {
                  _local_19 = _local_9++;
                  _local_13[_local_19] = _local_15;
               }
            }
         }
         if(_local_18 != 0)
         {
            _local_6 = _arg_1.cachedTimeScale;
            _local_16 = _local_12;
            _local_5 = _arg_1.timeline;
            while(_local_5)
            {
               _local_6 *= _local_5.cachedTimeScale;
               _local_16 += _local_5.cachedStartTime;
               _local_5 = _local_5.timeline;
            }
            _local_12 = _local_6 * _local_16;
            _local_11 = _local_18;
            while(--_local_11 > -1)
            {
               _local_7 = _local_17[_local_11];
               _local_6 = Number(_local_7.cachedTimeScale);
               _local_16 = _local_7.cachedStartTime;
               _local_5 = _local_7.timeline;
               while(_local_5)
               {
                  _local_6 *= _local_5.cachedTimeScale;
                  _local_16 += _local_5.cachedStartTime;
                  _local_5 = _local_5.timeline;
               }
               _local_14 = _local_6 * _local_16;
               if(_local_14 <= _local_12 && (_local_14 + _local_7.totalDuration * _local_6 + 1e-10 > _local_12 || _local_7.cachedDuration == 0))
               {
                  _local_19 = _local_9++;
                  _local_13[_local_19] = _local_7;
               }
            }
         }
         if(_local_9 == 0)
         {
            return _local_10;
         }
         _local_11 = _local_9;
         if(_arg_4 == 2)
         {
            while(--_local_11 > -1)
            {
               _local_15 = _local_13[_local_11];
               if(Boolean(_local_15.killVars(_arg_2)))
               {
                  _local_10 = true;
               }
               if(_local_15.cachedPT1 == null && Boolean(_local_15.initted))
               {
                  _local_15.setEnabled(false,false);
               }
            }
         }
         else
         {
            while(--_local_11 > -1)
            {
               if(TweenLite(_local_13[_local_11]).setEnabled(false,false))
               {
                  _local_10 = true;
               }
            }
         }
         return _local_10;
      }
      
      public static function getGlobalPaused(_arg_1:TweenCore) : Boolean
      {
         var _local_2:Boolean = false;
         while(Boolean(_arg_1))
         {
            if(_arg_1.cachedPaused)
            {
               _local_2 = true;
               break;
            }
            _arg_1 = _arg_1.timeline;
         }
         return _local_2;
      }
   }
}

