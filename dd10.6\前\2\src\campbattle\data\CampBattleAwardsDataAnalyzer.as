package campbattle.data
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   
   public class CampBattleAwardsDataAnalyzer extends DataAnalyzer
   {
      
      public var _dataList:Array;
      
      public function CampBattleAwardsDataAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_6:int = 0;
         var _local_5:* = null;
         var _local_3:* = null;
         var _local_2:* = null;
         this._dataList = [];
         var _local_4:XML = new XML(_arg_1);
         if(_local_4.@value == "true")
         {
            _local_5 = _local_4..Item;
            _local_6 = 0;
            while(_local_6 < _local_5.length())
            {
               _local_3 = new CampBattleAwardsGoodsInfo();
               ObjectUtils.copyPorpertiesByXML(_local_3,_local_5[_local_6]);
               _local_2 = this._dataList[_local_3.ID - 1];
               if(!_local_2)
               {
                  _local_2 = [];
               }
               _local_2.push(_local_3);
               this._dataList[_local_3.ID - 1] = _local_2;
               _local_6++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_4.@message;
            onAnalyzeError();
         }
      }
   }
}

