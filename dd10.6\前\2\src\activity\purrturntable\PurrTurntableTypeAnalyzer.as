package activity.purrturntable
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import road7th.data.DictionaryData;
   
   public class PurrTurntableTypeAnalyzer extends DataAnalyzer
   {
      
      private var _Dic:DictionaryData;
      
      public function PurrTurntableTypeAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_6:int = 0;
         var _local_4:* = null;
         var _local_5:* = null;
         var _local_2:* = null;
         this._Dic = new DictionaryData();
         var _local_3:XML = new XML(_arg_1);
         if(_local_3.@value == "true")
         {
            _local_4 = _local_3..Item;
            _local_6 = 0;
            while(_local_6 < _local_4.length())
            {
               _local_5 = new PurrTurntableTypeTemp();
               ObjectUtils.copyPorpertiesByXML(_local_5,_local_4[_local_6]);
               if(!this._Dic[_local_5.Type])
               {
                  _local_2 = new DictionaryData();
                  this._Dic.add(_local_5.Type,_local_2);
               }
               this._Dic[_local_5.Type].add(_local_5.Level,[_local_5.ProtectAddCost,_local_5.ConvertCount]);
               _local_6++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_3.@message;
            onAnalyzeError();
         }
      }
      
      public function get data() : DictionaryData
      {
         return this._Dic;
      }
   }
}

