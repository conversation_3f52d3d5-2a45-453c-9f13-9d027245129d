package activity.firstkill
{
   import activity.firstkill.analyzer.FirstKillAnalyzer;
   import com.pickgliss.loader.LoadResourceManager;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.ChatManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.SocketManager;
   import wonderfulActivity.WonderfulActivityManager;
   
   public class FirstKillManager
   {
      
      private static var _instance:FirstKillManager;
      
      private static const OPEN:int = 1;
      
      public static const UPDATE_DATA:int = 3;
      
      private var _begin:Number;
      
      private var _end:Number;
      
      private var _list:Array;
      
      public function FirstKillManager()
      {
         super();
      }
      
      public static function get instance() : FirstKillManager
      {
         if(_instance == null)
         {
            _instance = new FirstKillManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(668,1),this.__onOpen);
      }
      
      private function __onOpen(_arg_1:PkgEvent) : void
      {
         this._begin = _arg_1.pkg.readLong();
         this._end = _arg_1.pkg.readLong();
         var _local_2:Boolean = _arg_1.pkg.readBoolean();
         if(_local_2)
         {
            LoadResourceManager.Instance.startLoad(LoaderCreate.Instance.creatDungeonFirstKillTemplate());
            WonderfulActivityManager.Instance.addElement(53);
            ChatManager.Instance.sysChatAmaranth(LanguageMgr.GetTranslation("FirstCopy.activity.isOpen"));
         }
         else
         {
            WonderfulActivityManager.Instance.removeElement(53);
         }
      }
      
      public function analyzer(_arg_1:FirstKillAnalyzer) : void
      {
         this._list = _arg_1.list;
      }
      
      public function get templateList() : Array
      {
         return this._list;
      }
      
      public function get beginTime() : Number
      {
         return this._begin;
      }
      
      public function get endTime() : Number
      {
         return this._end;
      }
   }
}

