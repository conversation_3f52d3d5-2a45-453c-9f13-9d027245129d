package activity.sweep
{
   import activity.ActivityNormalEvent;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ClassUtils;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.SocketManager;
   import ddt.utils.AssetModuleLoader;
   import flash.display.Sprite;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import flash.utils.Dictionary;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   
   public class SweepManager extends EventDispatcher
   {
      
      private static var _ins:SweepManager;
      
      public var todayRechargeMoney:int;
      
      public var missInfoDic:Dictionary;
      
      public var cardType:int;
      
      public var cardEndDate:Date;
      
      public var missonTypeDic:Dictionary;
      
      public var missonIDDic:Dictionary;
      
      public var conditionIDDic:Dictionary;
      
      public function SweepManager(param1:IEventDispatcher = null)
      {
         super(param1);
         this.missInfoDic = new Dictionary();
      }
      
      public static function get ins() : SweepManager
      {
         if(!_ins)
         {
            _ins = new SweepManager();
         }
         return _ins;
      }
      
      public function setupEvent() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(755,1),this.__onSweepInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(755,2),this.__onSweepMissonInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(755,3),this.__onSweepResult);
         SocketManager.Instance.addEventListener(PkgEvent.format(755,5),this.__onSweepOrderInfo);
      }
      
      private function __onSweepOrderInfo(param1:PkgEvent) : void
      {
         var _loc2_:PackageIn = param1.pkg;
         var _loc3_:int = _loc2_.readInt();
         var _loc4_:int = _loc2_.readInt();
         var _loc5_:int = _loc2_.readInt();
         var _loc6_:Boolean = _loc2_.readBoolean();
         dispatchEvent(new ActivityNormalEvent("updateView2",[_loc3_,_loc4_,_loc5_,_loc6_]));
      }
      
      private function __onSweepResult(param1:PkgEvent) : void
      {
         var sweepId:int = 0;
         var num:int = 0;
         var _loc15_:Object = null;
         var _loc10_:int = 0;
         var reward:Object = null;
         var _loc2_:PackageIn = param1.pkg;
         var len:int = _loc2_.readInt();
         var _loc4_:Array = [];
         for(var index:int = 0; index < len; index++)
         {
            sweepId = _loc2_.readInt();
            num = _loc2_.readInt();
            _loc15_ = {
               "id":sweepId,
               "rewards":[]
            };
            for(_loc10_ = 0; _loc10_ < num; _loc10_++)
            {
               reward = {
                  "id":_loc2_.readInt(),
                  "count":_loc2_.readInt(),
                  "starIndex":_loc2_.readInt(),
                  "isbind":_loc2_.readBoolean()
               };
               _loc15_.rewards.push(reward);
            }
            _loc4_.push(_loc15_);
         }
         var resultSprite:Sprite = ClassUtils.CreatInstance("activity.sweep.view.SweepResult",[_loc4_]) as Sprite;
         LayerManager.Instance.addToLayer(resultSprite,3,true,1);
      }
      
      private function __onSweepMissonInfo(param1:PkgEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:Object = null;
         var _loc5_:PackageIn = param1.pkg;
         var _loc6_:int = _loc5_.readInt();
         var _loc7_:int = _loc5_.readInt();
         _loc2_ = 0;
         while(_loc2_ < _loc7_)
         {
            _loc3_ = _loc5_.readInt();
            _loc4_ = this.missInfoDic[_loc3_] || {};
            _loc4_.id = _loc3_;
            _loc4_.cansweep = _loc5_.readBoolean();
            _loc4_.hassweep = _loc5_.readBoolean();
            _loc4_.isOpen = _loc5_.readBoolean();
            _loc4_.isDoble = _loc5_.readBoolean();
            this.missInfoDic[_loc3_] = _loc4_;
            _loc2_++;
         }
         this.initData();
         dispatchEvent(new ActivityNormalEvent("updateView1",_loc6_));
      }
      
      private function __onSweepInfo(param1:PkgEvent) : void
      {
         this.checkOpen();
         var _loc2_:PackageIn = param1.pkg;
         this.cardType = _loc2_.readInt();
         this.cardEndDate = _loc2_.readDate();
         this.todayRechargeMoney = _loc2_.readInt();
         dispatchEvent(new ActivityNormalEvent("updateView3"));
      }
      
      public function loadRes() : void
      {
         AssetModuleLoader.addModelLoader("sweep",5);
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createSweepMissonLoader());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createSweepConditionLoader());
         AssetModuleLoader.startCodeLoader(this.showFrame);
      }
      
      public function loadSweepMissonXmlComplete(param1:SweepMissonAnalyzer) : void
      {
         this.missonIDDic = param1.infosDic;
      }
      
      private function initData() : void
      {
         var _loc3_:* = undefined;
         var _loc1_:SweepMissonTemp = null;
         var _loc2_:Object = null;
         this.missonTypeDic = new Dictionary();
         for(_loc3_ in this.missonIDDic)
         {
            _loc1_ = this.missonIDDic[_loc3_];
            if(!this.missonTypeDic[_loc1_.Type])
            {
               this.missonTypeDic[_loc1_.Type] = [];
            }
            _loc2_ = this.missInfoDic[_loc3_];
            if(Boolean(_loc2_) && Boolean(_loc2_.isOpen))
            {
               this.missonTypeDic[_loc1_.Type].push(_loc1_);
            }
         }
      }
      
      public function checkOpen() : void
      {
         HallIconManager.instance.updateSwitchHandler("SweepIcon",false);
      }
      
      public function loadSweepConditionXmlComplete(param1:SweepConditionAnalyzer) : void
      {
         this.conditionIDDic = param1.infosDic;
      }
      
      private function showFrame(param1:int = 0) : void
      {
         var _loc2_:Sprite = null;
         _loc2_ = ClassUtils.CreatInstance("activity.sweep.view.SweepMain") as Sprite;
         LayerManager.Instance.addToLayer(_loc2_,3,true,1);
      }
   }
}

