package com.pickgliss.ui.controls.container
{
   import flash.display.DisplayObject;
   import flash.events.Event;
   
   public class VBox extends Box<PERSON>ontainer
   {
      
      public function VBox()
      {
         super();
      }
      
      override public function arrange() : void
      {
         var _local_4:int = 0;
         var _local_1:* = undefined;
         _local_1 = null;
         _width = 0;
         _height = 0;
         var _local_3:* = 0;
         var _local_2:* = 0;
         _local_4 = 0;
         while(_local_4 < _childrenList.length)
         {
            _local_1 = _childrenList[_local_4];
            _local_1.y = _local_3;
            _local_3 += this.getItemHeight(_local_1);
            _local_3 += _spacing;
            if(_autoSize == 2 && _local_4 != 0)
            {
               _local_2 = _childrenList[0].x - (_local_1.width - _childrenList[0].width) / 2;
            }
            else if(_autoSize == 1 && _local_4 != 0)
            {
               _local_2 = _childrenList[0].x - (_local_1.width - _childrenList[0].width);
            }
            else
            {
               _local_2 = _local_1.x;
            }
            _local_1.x = _local_2;
            _height += this.getItemHeight(_local_1);
            _width = Math.max(_width,_local_1.width);
            _local_4++;
         }
         _height += _spacing * (numChildren - 1);
         _height = Math.max(0,_height);
         dispatchEvent(new Event("resize"));
      }
      
      private function getItemHeight(_arg_1:DisplayObject) : Number
      {
         if(isStrictSize)
         {
            return _strictSize;
         }
         return _arg_1.height;
      }
   }
}

