package cloudBuyLottery
{
   import cloudBuyLottery.data.CloudBuyAnalyzer;
   import cloudBuyLottery.loader.LoaderUIModule;
   import cloudBuyLottery.model.CloudBuyLotteryModel;
   import cloudBuyLottery.model.CloudBuyLotteryPackageType;
   import cloudBuyLottery.view.CloudBuyLotteryFrame;
   import cloudBuyLottery.view.ExpBar;
   import cloudBuyLottery.view.WinningLogItemInfo;
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.events.CrazyTankSocketEvent;
   import ddt.manager.PathManager;
   import ddt.manager.SocketManager;
   import ddt.manager.TimeManager;
   import flash.display.Sprite;
   import flash.events.Event;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   
   public class CloudBuyLotteryManager extends Sprite
   {
      
      private static var _instance:CloudBuyLotteryManager;
      
      public static const UPDATE_INFO:String = "updateInfo";
      
      public static const INDIVIDUAL:String = "Individual";
      
      public static const FRAMEUPDATE:String = "frmeupdate";
      
      private var _model:CloudBuyLotteryModel;
      
      private var cloudBuyFrame:CloudBuyLotteryFrame;
      
      public var itemInfoList:Array;
      
      public var logArr:Array;
      
      public function CloudBuyLotteryManager(_arg_1:PrivateClass)
      {
         super();
      }
      
      public static function get Instance() : CloudBuyLotteryManager
      {
         if(CloudBuyLotteryManager._instance == null)
         {
            CloudBuyLotteryManager._instance = new CloudBuyLotteryManager(new PrivateClass());
         }
         return CloudBuyLotteryManager._instance;
      }
      
      public function setup() : void
      {
         this._model = new CloudBuyLotteryModel();
         SocketManager.Instance.addEventListener("cloudBuy",this.__cloudBuyLotteryHandle);
      }
      
      private function __cloudBuyLotteryHandle(_arg_1:CrazyTankSocketEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:int = _arg_1._cmd;
         switch(_local_2)
         {
            case CloudBuyLotteryPackageType.OPEN_GAME:
               this.activityOpen(_local_3);
               return;
            case CloudBuyLotteryPackageType.Enter_GAME:
               this.enterGame(_local_3);
               return;
            case CloudBuyLotteryPackageType.BUY_GOODS:
               this.buyGoods(_local_3);
               return;
            case CloudBuyLotteryPackageType.UPDATE_INFO:
               this.updateInfo(_local_3);
               return;
            case CloudBuyLotteryPackageType.GET_REWARD:
               this.getReward(_local_3);
               return;
            default:
               return;
         }
      }
      
      private function activityOpen(_arg_1:PackageIn) : void
      {
         this._model.isOpen = _arg_1.readBoolean();
         this.initIcon(this._model.isOpen);
         if(!this._model.isOpen)
         {
            return;
         }
         this._loadXml("BuyItemRewardLogList.ashx?ran=" + Math.random(),6);
      }
      
      private function enterGame(_arg_1:PackageIn) : void
      {
         var _local_2:int = 0;
         this._model.templateId = _arg_1.readInt();
         this._model.templatedIdCount = _arg_1.readInt();
         this._model.validDate = _arg_1.readInt();
         this._model.property = _arg_1.readUTF().split(",");
         this._model.count = _arg_1.readInt();
         if(this._model.count <= 0)
         {
            return;
         }
         this._model.buyGoodsIDArray = [];
         this._model.buyGoodsCountArray = [];
         _local_2 = 0;
         while(_local_2 < this._model.count)
         {
            this._model.buyGoodsIDArray[_local_2] = _arg_1.readInt();
            this._model.buyGoodsCountArray[_local_2] = _arg_1.readInt();
            _local_2++;
         }
         this._model.buyMoney = _arg_1.readInt();
         this._model.maxNum = _arg_1.readInt();
         this._model.currentNum = _arg_1.readInt();
         this._model.luckTime = _arg_1.readDate();
         this._model.luckCount = _arg_1.readInt();
         this._model.remainTimes = _arg_1.readInt();
         this._model.isGame = _arg_1.readBoolean();
         LoaderUIModule.Instance.loadUIModule(this.initOpenFrame,null,"cloudBuy");
      }
      
      private function buyGoods(_arg_1:PackageIn) : void
      {
      }
      
      public function templateDataSetup(_arg_1:Array) : void
      {
         this.itemInfoList = _arg_1;
      }
      
      private function updateInfo(_arg_1:PackageIn) : void
      {
         var _local_2:int = _arg_1.readInt();
         this._model.templatedIdCount = _arg_1.readInt();
         this._model.validDate = _arg_1.readInt();
         this._model.property = _arg_1.readUTF().split(",");
         this._model.maxNum = _arg_1.readInt();
         this._model.currentNum = _arg_1.readInt();
         this._model.luckTime = _arg_1.readDate();
         this._model.luckCount = _arg_1.readInt();
         this._model.remainTimes = _arg_1.readInt();
         this._model.isGame = _arg_1.readBoolean();
         this._loadXml("BuyItemRewardLogList.ashx?ran=" + Math.random(),6);
         this._model.templateId = _local_2;
         dispatchEvent(new Event("updateInfo"));
      }
      
      private function getReward(_arg_1:PackageIn) : void
      {
         this._model.isGetReward = _arg_1.readBoolean();
         if(this._model.isGetReward)
         {
            this._model.remainTimes = _arg_1.readInt();
            this._model.luckDrawId = _arg_1.readInt();
         }
         dispatchEvent(new Event("Individual"));
         dispatchEvent(new Event("frmeupdate"));
      }
      
      public function loaderCloudBuyFrame() : void
      {
         if(this._model.isOpen)
         {
            SocketManager.Instance.out.sendEnterGame();
         }
      }
      
      private function initOpenFrame() : void
      {
         this.cloudBuyFrame = ComponentFactory.Instance.creatComponentByStylename("cloudBuyFrame");
         LayerManager.Instance.addToLayer(this.cloudBuyFrame,3,true,2);
      }
      
      public function initIcon(_arg_1:Boolean) : void
      {
         HallIconManager.instance.updateSwitchHandler("cloudbuylottery",_arg_1);
         if(this.cloudBuyFrame != null && _arg_1 == false)
         {
            if(Boolean(this.cloudBuyFrame))
            {
               ObjectUtils.disposeObject(this.cloudBuyFrame);
            }
            this.cloudBuyFrame = null;
         }
      }
      
      public function returnTen(_arg_1:String) : int
      {
         if(_arg_1.length > 1)
         {
            return int(_arg_1.charAt(0) + 1);
         }
         return 1;
      }
      
      public function returnABit(_arg_1:String) : int
      {
         if(_arg_1.length <= 1)
         {
            return int(_arg_1.charAt(0) + 1);
         }
         return int(_arg_1.charAt(1) + 1);
      }
      
      public function refreshTimePlayTxt() : String
      {
         var _local_2:int = 0;
         var _local_7:Number = Number(this._model.luckTime.getTime());
         var _local_6:Number = Number(TimeManager.Instance.Now().getTime());
         var _local_1:Number = _local_7 - _local_6;
         _local_1 = _local_1 < 0 ? 0 : _local_1;
         var _local_8:String = "";
         var _local_4:int = int(int(_local_1 / 3600000));
         var _local_5:int = int(int((_local_1 - _local_4 * 1000 * 60 * 60) / 60000));
         var _local_3:int = int(int((_local_1 - _local_4 * 1000 * 60 * 60 - _local_5 * 1000 * 60) / 1000));
         return _local_4 + ":" + _local_5 + ":" + _local_3;
      }
      
      private function _loadXml(_arg_1:String, _arg_2:int, _arg_3:String = "") : void
      {
         var _local_4:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath(_arg_1),_arg_2);
         _local_4.loadErrorMessage = _arg_3;
         _local_4.analyzer = new CloudBuyAnalyzer(this.logComplete);
         LoadResourceManager.Instance.startLoad(_local_4);
      }
      
      private function logComplete(_arg_1:DataAnalyzer) : void
      {
         var _local_4:int = 0;
         var _local_3:* = null;
         if(_arg_1 is CloudBuyAnalyzer)
         {
            this.logArr = CloudBuyAnalyzer(_arg_1).dataArr;
         }
         if(this.logArr == null || this.logArr.length <= 0)
         {
            return;
         }
         var _local_2:Vector.<WinningLogItemInfo> = new Vector.<WinningLogItemInfo>();
         _local_4 = 0;
         while(_local_4 < this.logArr.length)
         {
            _local_3 = new WinningLogItemInfo();
            _local_3.TemplateID = this.logArr[_local_4].templateId;
            _local_3.validate = this.logArr[_local_4].validate;
            _local_3.count = this.logArr[_local_4].count;
            _local_3.property = this.logArr[_local_4].property.split(",");
            _local_3.nickName = this.logArr[_local_4].nickName;
            _local_2.push(_local_3);
            _local_4++;
         }
         this._model.myGiftData = _local_2;
      }
      
      public function get model() : CloudBuyLotteryModel
      {
         return this._model;
      }
      
      public function get expBar() : ExpBar
      {
         return this.cloudBuyFrame.expBar;
      }
   }
}

class PrivateClass
{
   
   public function PrivateClass()
   {
      super();
   }
}
