package com.pickgliss.utils
{
   import com.pickgliss.events.InteractiveEvent;
   import flash.display.InteractiveObject;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   
   public final class DoubleClickManager
   {
      
      private static var _instance:DoubleClickManager;
      
      private const DoubleClickSpeed:uint = 350;
      
      private var _timer:Timer;
      
      private var _currentTarget:InteractiveObject;
      
      private var _ctrlKey:<PERSON>olean;
      
      public function DoubleClickManager()
      {
         super();
         this.init();
      }
      
      public static function get Instance() : DoubleClickManager
      {
         if(!_instance)
         {
            _instance = new DoubleClickManager();
         }
         return _instance;
      }
      
      public function enableDoubleClick(_arg_1:InteractiveObject) : void
      {
         _arg_1.addEventListener("mouseDown",this.__mouseDownHandler);
      }
      
      public function disableDoubleClick(_arg_1:InteractiveObject) : void
      {
         _arg_1.removeEventListener("mouseDown",this.__mouseDownHandler);
      }
      
      private function init() : void
      {
         this._timer = new Timer(350,1);
         this._timer.addEventListener("timerComplete",this.__timerCompleteHandler);
      }
      
      private function getEvent(_arg_1:String) : InteractiveEvent
      {
         var _local_2:InteractiveEvent = new InteractiveEvent(_arg_1);
         _local_2.ctrlKey = this._ctrlKey;
         return _local_2;
      }
      
      private function __timerCompleteHandler(_arg_1:TimerEvent) : void
      {
         this._currentTarget.dispatchEvent(this.getEvent("interactive_click"));
      }
      
      private function __mouseDownHandler(_arg_1:MouseEvent) : void
      {
         this._ctrlKey = _arg_1.ctrlKey;
         if(this._timer.running)
         {
            this._timer.stop();
            if(this._currentTarget != _arg_1.currentTarget)
            {
               return;
            }
            _arg_1.stopImmediatePropagation();
            this._currentTarget.dispatchEvent(this.getEvent("interactive_double_click"));
         }
         else
         {
            this._timer.reset();
            this._timer.start();
            this._currentTarget = _arg_1.currentTarget as InteractiveObject;
         }
      }
      
      public function clearTarget() : void
      {
         if(Boolean(this._timer))
         {
            this._timer.stop();
         }
         this._currentTarget = null;
      }
   }
}

