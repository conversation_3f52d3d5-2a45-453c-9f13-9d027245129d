package cloudBuyLottery.loader
{
   import com.pickgliss.events.UIModuleEvent;
   import com.pickgliss.loader.UIModuleLoader;
   import ddt.view.UIModuleSmallLoading;
   
   public class LoaderUIModule
   {
      
      private static var _instance:LoaderUIModule;
      
      private var _func:Function;
      
      private var _funcParams:Array;
      
      private var _type:String;
      
      public function LoaderUIModule(_arg_1:PrivateClass)
      {
         super();
      }
      
      public static function get Instance() : LoaderUIModule
      {
         if(LoaderUIModule._instance == null)
         {
            LoaderUIModule._instance = new LoaderUIModule(new PrivateClass());
         }
         return LoaderUIModule._instance;
      }
      
      public function loadUIModule(_arg_1:Function = null, _arg_2:Array = null, _arg_3:String = "") : void
      {
         this._func = _arg_1;
         this._funcParams = _arg_2;
         this._type = _arg_3;
         UIModuleSmallLoading.Instance.progress = 0;
         UIModuleSmallLoading.Instance.show();
         UIModuleLoader.Instance.addEventListener("uiModuleComplete",this.loadCompleteHandler);
         UIModuleLoader.Instance.addEventListener("uiMoudleProgress",this.onUimoduleLoadProgress);
         UIModuleLoader.Instance.addUIModuleImp(this._type);
      }
      
      private function loadCompleteHandler(_arg_1:UIModuleEvent) : void
      {
         if(_arg_1.module == this._type)
         {
            UIModuleSmallLoading.Instance.hide();
            UIModuleLoader.Instance.removeEventListener("uiModuleComplete",this.loadCompleteHandler);
            UIModuleLoader.Instance.removeEventListener("uiMoudleProgress",this.onUimoduleLoadProgress);
            if(null != this._func)
            {
               this._func.apply(null,this._funcParams);
            }
            this._func = null;
            this._funcParams = null;
         }
      }
      
      private function onUimoduleLoadProgress(_arg_1:UIModuleEvent) : void
      {
         if(_arg_1.module == this._type)
         {
            UIModuleSmallLoading.Instance.progress = _arg_1.loader.progress * 100;
         }
      }
   }
}

class PrivateClass
{
   
   public function PrivateClass()
   {
      super();
   }
}
