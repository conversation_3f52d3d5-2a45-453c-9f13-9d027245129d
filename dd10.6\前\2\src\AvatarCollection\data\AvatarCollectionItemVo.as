package AvatarCollection.data
{
   import AvatarCollection.AvatarCollectionManager;
   import ddt.data.BagInfo;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.data.goods.ShopItemInfo;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PlayerManager;
   import ddt.manager.ShopManager;
   
   public class AvatarCollectionItemVo
   {
      
      public var id:int;
      
      public var itemId:int;
      
      private var _otherTemplateID:String = "";
      
      public var sex:int;
      
      public var proArea:String;
      
      public var selected:Boolean = false;
      
      public var needGold:int;
      
      public var isActivity:Boolean;
      
      private var __reg:RegExp = /\/g;
      
      public var buyPrice:int = -1;
      
      public var isDiscount:int = -1;
      
      public var goodsId:int = -1;
      
      private var _canBuyStatus:int = -1;
      
      public var Type:int;
      
      public function AvatarCollectionItemVo()
      {
         super();
      }
      
      public function get OtherTemplateID() : String
      {
         return this._otherTemplateID;
      }
      
      public function set OtherTemplateID(_arg_1:String) : void
      {
         this._otherTemplateID = _arg_1 != "" ? _arg_1.replace(this.__reg,"") : _arg_1;
      }
      
      public function get activateItem() : InventoryItemInfo
      {
         var _local_3:int = 0;
         var _local_4:InventoryItemInfo = null;
         var _local_2:Array = this._otherTemplateID == "" ? [] : this._otherTemplateID.split("|");
         var _local_1:BagInfo = PlayerManager.Instance.Self.getBag(0);
         _local_2.unshift(this.itemId);
         _local_3 = 0;
         while(_local_3 < _local_2.length)
         {
            if(_local_2[_local_3] != 0)
            {
               _local_4 = _local_1.getItemByTemplateId(_local_2[_local_3]);
               if(_local_4 != null)
               {
                  break;
               }
            }
            _local_3++;
         }
         return _local_4;
      }
      
      public function get isHas() : Boolean
      {
         return this.activateItem != null;
      }
      
      public function get itemInfo() : ItemTemplateInfo
      {
         return ItemManager.Instance.getTemplateById(this.itemId);
      }
      
      public function get canBuyStatus() : int
      {
         var _local_1:* = null;
         if(this._canBuyStatus == -1)
         {
            _local_1 = AvatarCollectionManager.instance.getShopItemInfoByItemId(this.itemId,this.sex,this.Type);
            if(_local_1)
            {
               this._canBuyStatus = 1;
               this.buyPrice = _local_1.getItemPrice(1).moneyValue;
               this.isDiscount = _local_1.isDiscount;
               this.goodsId = _local_1.GoodsID;
            }
            else
            {
               this._canBuyStatus = 0;
            }
         }
         return this._canBuyStatus;
      }
      
      public function get priceType() : int
      {
         var _local_2:int = 1;
         var _local_1:ShopItemInfo = ShopManager.Instance.getGoodsByTempId(this.itemId);
         if(Boolean(_local_1))
         {
            _local_2 = _local_1.APrice1 == -8 ? 0 : _local_2;
         }
         return _local_2;
      }
      
      public function set canBuyStatus(_arg_1:int) : void
      {
         this._canBuyStatus = _arg_1;
      }
      
      public function get typeToString() : String
      {
         if(this.Type == 1)
         {
            return LanguageMgr.GetTranslation("avatarCollection.select.decorate");
         }
         return LanguageMgr.GetTranslation("avatarCollection.select.weapon");
      }
   }
}

