package bagAndInfo.info
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.image.ScaleLeftRightImage;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.Graphics;
   import vip.view.VipLevelProgress;
   
   public class NecklaceLevelProgress extends VipLevelProgress
   {
      
      private var _backBG:ScaleLeftRightImage;
      
      public function NecklaceLevelProgress()
      {
         super();
      }
      
      override protected function initView() : void
      {
         _thuck = ComponentFactory.Instance.creatComponentByStylename("NecklacePtetrochemicalView.thunck");
         addChildAt(_thuck,0);
         this._backBG = ComponentFactory.Instance.creatComponentByStylename("NecklacePtetrochemicalView.LeveLBG");
         addChildAt(this._backBG,0);
         _graphics_thuck = ComponentFactory.Instance.creatComponentByStylename("NecklacePtetrochemicalView.thuckBitData").getBitmapdata();
         _progressLabel = ComponentFactory.Instance.creatComponentByStylename("NecklaceProgressTxt");
         addChild(_progressLabel);
      }
      
      override protected function drawProgress() : void
      {
         var _local_2:Number = _value / _max > 1 ? 1 : _value / _max;
         var _local_1:Graphics = _thuck.graphics;
         _local_1.clear();
         if(_local_2 >= 0)
         {
            _progressLabel.text = _value.toString() + "/" + _max.toString();
            _local_1.beginBitmapFill(_graphics_thuck);
            _local_1.drawRect(0,0,_width * _local_2,_height - 8);
            _local_1.endFill();
         }
      }
      
      override public function dispose() : void
      {
         ObjectUtils.disposeObject(this._backBG);
         this._backBG = null;
         super.dispose();
      }
   }
}

