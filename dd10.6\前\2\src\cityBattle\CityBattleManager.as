package cityBattle
{
   import cityBattle.analyze.CityBattleAnalyze;
   import cityBattle.data.CastellanInfo;
   import cityBattle.data.ContentionInfo;
   import cityBattle.data.WelfareInfo;
   import cityBattle.event.CityBattleEvent;
   import cityBattle.view.CityBattleMainFrame;
   import cityBattle.view.JoinCityBattleView;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import ddt.events.PkgEvent;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.utils.AssetModuleLoader;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   
   public class CityBattleManager extends EventDispatcher
   {
      
      private static var _instance:CityBattleManager;
      
      public var redTotalScore:int;
      
      public var blueTotalScore:int;
      
      public var winnerExchangeInfo:Array;
      
      public var myExchangeInfo:Vector.<WelfareInfo> = new Vector.<WelfareInfo>();
      
      public var contentionFirstData:Boolean;
      
      public var now:int;
      
      public var myScore:int;
      
      public var myRankScore:int;
      
      public var mySide:int;
      
      public var myRank:int;
      
      public var isOpen:Boolean;
      
      public var blueList:Vector.<ContentionInfo>;
      
      public var redList:Vector.<ContentionInfo>;
      
      public var welfareList:Vector.<WelfareInfo>;
      
      public var castellanList:Vector.<CastellanInfo>;
      
      public var _mainFrame:CityBattleMainFrame;
      
      private var _joinView:JoinCityBattleView;
      
      public function CityBattleManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get instance() : CityBattleManager
      {
         if(!_instance)
         {
            _instance = new CityBattleManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(368,0),this._activityOpenHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(368,1),this._playerEnterHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(368,2),this._joinBattleHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(368,3),this._contentionHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(368,4),this._exchangeHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(368,8),this._scoreAndRankHandler);
      }
      
      public function CityBattleSystemsHandler(_arg_1:CityBattleAnalyze) : void
      {
         this.welfareList = _arg_1.list;
      }
      
      private function _scoreAndRankHandler(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this.blueTotalScore = _local_2.readInt();
         this.redTotalScore = _local_2.readInt();
         this.myRankScore = _local_2.readInt();
         this.myRank = _local_2.readInt();
         dispatchEvent(new CityBattleEvent("score_rank"));
      }
      
      private function _joinBattleHandler(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this.mySide = _local_2.readInt();
         dispatchEvent(new CityBattleEvent("joinBattle"));
      }
      
      private function _playerEnterHandler(_arg_1:PkgEvent) : void
      {
         var _local_10:int = 0;
         var _local_13:int = 0;
         var _local_7:int = 0;
         var _local_11:int = 0;
         var _local_6:int = 0;
         var _local_8:int = 0;
         var _local_3:int = 0;
         var _local_12:* = null;
         var _local_9:* = null;
         var _local_2:* = null;
         this.castellanList = new Vector.<CastellanInfo>();
         var _local_4:PackageIn = _arg_1.pkg;
         this.now = _local_4.readInt();
         var _local_5:int = _local_4.readInt();
         _local_10 = 0;
         while(_local_10 < _local_5)
         {
            _local_12 = new CastellanInfo();
            _local_13 = _local_4.readInt();
            _local_7 = _local_4.readInt();
            _local_9 = _local_4.readUTF();
            _local_11 = _local_4.readInt();
            _local_2 = PlayerManager.Instance.findPlayer(_local_11,_local_7);
            _local_2.zoneName = _local_9;
            _local_2.NickName = _local_4.readUTF();
            _local_2.Sex = _local_4.readBoolean();
            _local_2.Hide = _local_4.readInt();
            _local_2.Style = _local_4.readUTF();
            _local_2.Colors = _local_4.readUTF();
            _local_2.Skin = _local_4.readUTF();
            _local_12.winner = _local_2;
            _local_12.side = _local_13;
            this.castellanList[_local_10] = _local_12;
            _local_10++;
         }
         _local_6 = _local_5;
         while(_local_6 < 7)
         {
            _local_12 = new CastellanInfo();
            _local_12.side = 0;
            this.castellanList[_local_6] = _local_12;
            _local_6++;
         }
         this.winnerExchangeInfo = [];
         _local_8 = 0;
         while(_local_8 < 7)
         {
            _local_3 = _local_4.readInt();
            this.winnerExchangeInfo.push(_local_3);
            _local_8++;
         }
         this.show();
      }
      
      private function _activityOpenHandler(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this.isOpen = _local_2.readBoolean();
         this.mySide = _local_2.readInt();
         this.updateIcon();
      }
      
      public function updateIcon() : void
      {
         HallIconManager.instance.updateSwitchHandler("cityBattle",this.isOpen);
      }
      
      private function _contentionHandler(_arg_1:PkgEvent) : void
      {
         var _local_7:int = 0;
         var _local_6:int = 0;
         var _local_4:* = null;
         var _local_5:PackageIn = _arg_1.pkg;
         this.contentionFirstData = true;
         this.blueList = new Vector.<ContentionInfo>();
         var _local_3:int = _local_5.readInt();
         _local_7 = 0;
         while(_local_7 < _local_3)
         {
            _local_4 = new ContentionInfo();
            _local_4.rank = _local_7 + 1;
            _local_4.name = _local_5.readUTF();
            _local_4.socre = _local_5.readInt();
            _local_4.server = _local_5.readUTF();
            this.blueList.push(_local_4);
            _local_7++;
         }
         this.redList = new Vector.<ContentionInfo>();
         var _local_2:int = _local_5.readInt();
         _local_6 = 0;
         while(_local_6 < _local_2)
         {
            _local_4 = new ContentionInfo();
            _local_4.rank = _local_6 + 1;
            _local_4.name = _local_5.readUTF();
            _local_4.socre = _local_5.readInt();
            _local_4.server = _local_5.readUTF();
            this.redList.push(_local_4);
            _local_6++;
         }
         this._mainFrame.changeView(2);
      }
      
      private function _exchangeHandler(_arg_1:PkgEvent) : void
      {
         var _local_7:int = 0;
         var _local_4:int = 0;
         var _local_6:* = null;
         var _local_5:* = null;
         var _local_2:PackageIn = _arg_1.pkg;
         var _local_3:int = _local_2.readInt();
         for(_local_7 = 0; _local_7 < _local_3; )
         {
            _local_6 = new WelfareInfo();
            _local_6.ID = _local_2.readInt();
            _local_6.myExchangeCount = _local_2.readInt();
            _local_4 = 0;
            while(true)
            {
               if(_local_4 >= this.myExchangeInfo.length)
               {
                  this.myExchangeInfo.push(_local_6);
                  break;
               }
               _local_5 = this.myExchangeInfo[_local_4];
               if(_local_6.ID == _local_5.ID)
               {
                  this.myExchangeInfo[_local_4].myExchangeCount = _local_6.myExchangeCount;
                  break;
               }
               _local_4++;
            }
            _local_7++;
         }
         this.myScore = _local_2.readInt();
         dispatchEvent(new CityBattleEvent("scoreChange"));
         if(this._mainFrame.changeBtn)
         {
            this._mainFrame.changeBtn = false;
            this._mainFrame.changeView(3);
         }
      }
      
      public function show() : void
      {
         AssetModuleLoader.addModelLoader("cityBattle",6);
         AssetModuleLoader.startCodeLoader(this.onLoaded);
      }
      
      private function onLoaded() : void
      {
         if(this.mySide == 0)
         {
            this._joinView = new JoinCityBattleView();
            LayerManager.Instance.addToLayer(this._joinView,3,false,1);
            this._joinView.x = 141;
            this._joinView.y = 30;
         }
         else
         {
            this._mainFrame = ComponentFactory.Instance.creatComponentByStylename("cityBattle.mainFrame");
            LayerManager.Instance.addToLayer(this._mainFrame,3,true,1);
         }
      }
   }
}

