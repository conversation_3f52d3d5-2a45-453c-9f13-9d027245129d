package cityWide
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.manager.CacheSysManager;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.action.FrameShowAction;
   import ddt.data.player.PlayerInfo;
   import ddt.manager.ChatManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import flash.events.Event;
   import flash.utils.setInterval;
   import im.IMEvent;
   
   public class CityWideManager
   {
      
      private static var _instance:CityWideManager;
      
      private const TIMES:int = 300000;
      
      private var _cityWideView:CityWideFrame;
      
      private var _playerInfo:PlayerInfo;
      
      private var _canOpenCityWide:Boolean = true;
      
      public function CityWideManager()
      {
         super();
      }
      
      public static function get Instance() : CityWideManager
      {
         if(_instance == null)
         {
            _instance = new CityWideManager();
         }
         return _instance;
      }
      
      public function init() : void
      {
         PlayerManager.Instance.addEventListener("ons_playerInfo",this._updateCityWide);
      }
      
      private function _updateCityWide(_arg_1:CityWideEvent) : void
      {
         this._canOpenCityWide = true;
         if(this._canOpenCityWide)
         {
            this._playerInfo = _arg_1.playerInfo;
            this.showView(this._playerInfo);
            this._canOpenCityWide = false;
            setInterval(this.changeBoolean,300000);
         }
      }
      
      public function toSendOpenCityWide() : void
      {
         SocketManager.Instance.out.sendOns();
      }
      
      private function changeBoolean() : void
      {
         this._canOpenCityWide = true;
      }
      
      public function showView(_arg_1:PlayerInfo) : void
      {
         if(PlayerManager.Instance.Self.Grade < 11)
         {
            return;
         }
         this._cityWideView = ComponentFactory.Instance.creatComponentByStylename("CityWideFrame");
         this._cityWideView.playerInfo = _arg_1;
         this._cityWideView.addEventListener("submit",this._submitExit);
         if(CacheSysManager.isLock("alertInFight"))
         {
            CacheSysManager.getInstance().cache("alertInFight",new FrameShowAction(this._cityWideView));
         }
         else if(CacheSysManager.isLock("church_guide"))
         {
            CacheSysManager.getInstance().cache("church_guide",new FrameShowAction(this._cityWideView));
         }
         else if(CacheSysManager.isLock("consortia_guide"))
         {
            CacheSysManager.getInstance().cache("consortia_guide",new FrameShowAction(this._cityWideView));
         }
         else if(CacheSysManager.isLock("sales_room_guide"))
         {
            CacheSysManager.getInstance().cache("sales_room_guide",new FrameShowAction(this._cityWideView));
         }
         else if(CacheSysManager.isLock("farm_guide"))
         {
            CacheSysManager.getInstance().cache("farm_guide",new FrameShowAction(this._cityWideView));
         }
         else if(CacheSysManager.isLock("arena_guide"))
         {
            CacheSysManager.getInstance().cache("arena_guide",new FrameShowAction(this._cityWideView));
         }
         else if(CacheSysManager.isLock("secret_area_guide"))
         {
            CacheSysManager.getInstance().cache("secret_area_guide",new FrameShowAction(this._cityWideView));
         }
         else if(CacheSysManager.isLock("crypt_guide"))
         {
            CacheSysManager.getInstance().cache("crypt_guide",new FrameShowAction(this._cityWideView));
         }
         else
         {
            this._cityWideView.show();
         }
      }
      
      public function hideView() : void
      {
         this._cityWideView && ObjectUtils.disposeObject(this._cityWideView);
      }
      
      private function _submitExit(_arg_1:Event) : void
      {
         var _local_3:int = 0;
         var _local_2:* = null;
         this._cityWideView = null;
         if(PlayerManager.Instance.Self.IsVIP)
         {
            _local_3 = PlayerManager.Instance.Self.VIPLevel + 2;
         }
         if(PlayerManager.Instance.friendList.length >= 200 + _local_3 * 50)
         {
            _local_2 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("tank.view.im.IMController.addFriend",200 + _local_3 * 50),"","",false,false,false,2);
            _local_2.addEventListener("response",this._close);
            return;
         }
         SocketManager.Instance.out.sendAddFriend(this._playerInfo.NickName,0,false,true);
         PlayerManager.Instance.addEventListener("addnewfriend",this._addAlert);
      }
      
      private function _close(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         if(Boolean(_local_2))
         {
            _local_2.removeEventListener("response",this._close);
            _local_2.dispose();
            _local_2 = null;
         }
      }
      
      private function _addAlert(_arg_1:IMEvent) : void
      {
         PlayerManager.Instance.removeEventListener("addnewfriend",this._addAlert);
         var _local_3:BaseAlerFrame = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation(""),LanguageMgr.GetTranslation("tank.view.bagII.baglocked.complete"),LanguageMgr.GetTranslation("tank.view.scenechatII.PrivateChatIIView.privatename"),false,false,false,2);
         _local_3.info.enableHtml = true;
         var _local_2:String = LanguageMgr.GetTranslation("cityWideFrame.ONSAlertInfo");
         _local_2 = _local_2.replace(/r/g,this._playerInfo.NickName);
         _local_3.info.data = _local_2;
         _local_3.moveEnable = false;
         _local_3.addEventListener("response",this._responseII);
      }
      
      private function _responseII(_arg_1:FrameEvent) : void
      {
         var _local_2:int = _arg_1.responseCode;
         SoundManager.instance.play("008");
         _arg_1.currentTarget.removeEventListener("response",this._responseII);
         ObjectUtils.disposeObject(_arg_1.currentTarget);
         switch(_local_2)
         {
            case 4:
               ChatManager.Instance.privateChatTo(this._playerInfo.NickName,this._playerInfo.ID);
               ChatManager.Instance.setFocus();
         }
      }
   }
}

