package caveloot.cnf
{
   import caveloot.data.CaveLootMinePoolCnfInfo;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import road7th.data.DictionaryData;
   
   public class CaveLootMinePoolAnalyzer extends DataAnalyzer
   {
      
      private var _poolDic:DictionaryData;
      
      private var _poolItemIdDic:DictionaryData;
      
      private var _floorBonusDic:DictionaryData;
      
      private var _specialItemArr:Array;
      
      public function CaveLootMinePoolAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         this._poolDic = new DictionaryData();
         this._poolItemIdDic = new DictionaryData();
         this._floorBonusDic = new DictionaryData();
         this._specialItemArr = [];
         var _local_2:XML = new XML(_arg_1);
         if(_local_2.@value == "true")
         {
            _local_3 = _local_2..Item;
            _local_5 = 0;
            while(_local_5 < _local_3.length())
            {
               _local_4 = new CaveLootMinePoolCnfInfo();
               ObjectUtils.copyPorpertiesByXML(_local_4,_local_3[_local_5]);
               if(!this._poolDic.hasKey(_local_4.Type))
               {
                  this._poolDic.add(_local_4.Type,new DictionaryData());
               }
               (this._poolDic[_local_4.Type] as DictionaryData).add(_local_4.Id,_local_4);
               if(!this._poolItemIdDic.hasKey(_local_4.Type))
               {
                  this._poolItemIdDic.add(_local_4.Type,new DictionaryData());
               }
               (this._poolItemIdDic[_local_4.Type] as DictionaryData).add(_local_4.ItemId,_local_4);
               if(_local_4.Protect > 0)
               {
                  if(!this._floorBonusDic.hasKey(_local_4.Type))
                  {
                     this._floorBonusDic.add(_local_4.Type,new DictionaryData());
                  }
                  (this._floorBonusDic[_local_4.Type] as DictionaryData).add(_local_4.Id,_local_4);
               }
               if(_local_4.IsTips == 2)
               {
                  this._specialItemArr.push(_local_4.Id);
               }
               _local_5++;
            }
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
         }
         onAnalyzeComplete();
      }
      
      public function get poolDic() : DictionaryData
      {
         return this._poolDic;
      }
      
      public function get poolItemIdDic() : DictionaryData
      {
         return this._poolItemIdDic;
      }
      
      public function get floorBonusDic() : DictionaryData
      {
         return this._floorBonusDic;
      }
      
      public function get tipsItemArr() : Array
      {
         return this._specialItemArr;
      }
   }
}

