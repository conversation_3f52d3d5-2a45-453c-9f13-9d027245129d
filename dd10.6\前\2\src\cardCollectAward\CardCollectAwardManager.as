package cardCollectAward
{
   import cardCollectAward.data.CardCollectAwardInfo;
   import ddt.events.CEvent;
   import ddt.events.PkgEvent;
   import ddt.manager.SocketManager;
   import ddt.utils.HelperUIModuleLoad;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import road7th.comm.PackageIn;
   
   public class CardCollectAwardManager extends EventDispatcher
   {
      
      private static var awardM:CardCollectAwardManager;
      
      public static const OPEN_VIEW:String = "openView";
      
      private var _dataPkg:PackageIn = null;
      
      private var _showAward:Boolean = false;
      
      private var _awardInfo:CardCollectAwardInfo = new CardCollectAwardInfo();
      
      public function CardCollectAwardManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get Instance() : CardCollectAwardManager
      {
         if(awardM == null)
         {
            awardM = new CardCollectAwardManager();
         }
         return awardM;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(329,55),this.openView_Handler);
      }
      
      private function initData() : void
      {
         this._awardInfo.title = "版本更新，认真填写！";
         this._awardInfo.desc = "版本更新，认真填写";
         this._awardInfo.minLv = 12;
         this._awardInfo.quertions = "体验感,开心;一般;痛哭;|最有爱的更新,试练之地;弹弹奇缘功能体验亮点;暂未体验;|更多期待,竞技玩法;休闲玩法如;|最不给力的玩法,试练之地玩法;弹弹奇缘活动;温泉系统;";
         this._awardInfo.qq = "1234567";
         this._awardInfo.phone = "3156416546";
         this.showView();
      }
      
      protected function openView_Handler(_arg_1:PkgEvent) : void
      {
         this._dataPkg = _arg_1.pkg;
         if(this._awardInfo != null)
         {
            this._awardInfo.title = this._dataPkg.readUTF();
            this._awardInfo.desc = this._dataPkg.readUTF();
            this._awardInfo.minLv = this._dataPkg.readInt();
            this._awardInfo.quertions = this._dataPkg.readUTF();
            this._awardInfo.beginTime = this._dataPkg.readDate();
            this._awardInfo.endTime = this._dataPkg.readDate();
            this._awardInfo.qq = this._dataPkg.readUTF();
            this._awardInfo.phone = this._dataPkg.readUTF();
         }
         this.showView();
      }
      
      public function showView() : void
      {
         if(this._showAward && this._dataPkg != null)
         {
            if(this._awardInfo == null)
            {
               return;
            }
            new HelperUIModuleLoad().loadUIModule(["cardCollectAward"],function():void
            {
               _showAward = false;
               dispatchEvent(new CEvent("openView"));
            });
         }
         else
         {
            this._showAward = true;
         }
      }
      
      public function get awardInfo() : CardCollectAwardInfo
      {
         return this._awardInfo;
      }
      
      public function closeAward() : void
      {
         this._dataPkg = null;
         this._awardInfo = null;
         this._showAward = false;
      }
      
      public function dataPkg() : PackageIn
      {
         return this._dataPkg;
      }
   }
}

