package bagAndInfo.bag
{
   import bagAndInfo.cell.BagCell;
   import bagAndInfo.cell.DragEffect;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.TextButton;
   import ddt.data.EquipType;
   import ddt.interfaces.IDragable;
   import ddt.manager.DragManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.ShopManager;
   import ddt.manager.SoundManager;
   import ddt.view.goods.AddPricePanel;
   import flash.events.MouseEvent;
   
   public class ContinueGoodsBtn extends TextButton implements IDragable
   {
      
      public var _isContinueGoods:Boolean;
      
      public function ContinueGoodsBtn()
      {
         super();
         this._isContinueGoods = false;
         this.addEvt();
      }
      
      private function addEvt() : void
      {
         this.addEventListener("click",this.clickthis);
      }
      
      private function removeEvt() : void
      {
         this.removeEventListener("click",this.clickthis);
      }
      
      private function clickthis(_arg_1:MouseEvent) : void
      {
         var _local_2:* = null;
         SoundManager.instance.play("008");
         if(this._isContinueGoods == false)
         {
            this._isContinueGoods = true;
            _local_2 = ComponentFactory.Instance.creatBitmap("bagAndInfo.bag.continueIconAsset");
            DragManager.startDrag(this,this,_local_2,_arg_1.stageX,_arg_1.stageY,"move",false);
         }
         else
         {
            this._isContinueGoods = false;
         }
      }
      
      public function getSource() : IDragable
      {
         return this;
      }
      
      public function dragStop(_arg_1:DragEffect) : void
      {
         var _local_2:* = null;
         if(this._isContinueGoods && _arg_1.target is BagCell)
         {
            _local_2 = _arg_1.target as BagCell;
            _local_2.locked = false;
            this._isContinueGoods = false;
            if(ShopManager.Instance.canAddPrice(_local_2.itemInfo.TemplateID) && _local_2.itemInfo.getRemainDate() != 2147483647 && !EquipType.isProp(_local_2.itemInfo))
            {
               AddPricePanel.Instance.setInfo(_local_2.itemInfo,false);
               AddPricePanel.Instance.show();
               return;
            }
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.cantAddPrice"));
            return;
         }
         this._isContinueGoods = false;
      }
      
      public function get isContinueGoods() : Boolean
      {
         return this._isContinueGoods;
      }
   }
}

