package cloudBuyLottery.view
{
   import bagAndInfo.cell.BagCell;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.container.SimpleTileList;
   import com.pickgliss.ui.image.MutipleImage;
   import com.pickgliss.ui.image.Scale9CornerImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.SoundManager;
   import flash.display.Bitmap;
   import flash.events.MouseEvent;
   
   public class LookGoodsFrame extends Frame
   {
      
      public static const SUM_NUMBER:int = 20;
      
      private var _list:SimpleTileList;
      
      private var _items:Vector.<BagCell>;
      
      private var _prevBtn:BaseButton;
      
      private var _nextBtn:BaseButton;
      
      private var _pageTxt:FilterFrameText;
      
      private var _boxTempIDList:Vector.<InventoryItemInfo>;
      
      private var _page:int = 1;
      
      public function LookGoodsFrame()
      {
         super();
         this.initView();
         this.initEvents();
      }
      
      private function initView() : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:MutipleImage = ComponentFactory.Instance.creatComponentByStylename("IndividualLottery.TrophyBGI");
         var _local_2:Bitmap = ComponentFactory.Instance.creatBitmap("asset.IndividualLottery.lookFont");
         this._list = ComponentFactory.Instance.creatCustomObject("IndividualLottery.TrophyList",[5]);
         var _local_1:Scale9CornerImage = ComponentFactory.Instance.creatComponentByStylename("IndividualLottery.PageCountBg");
         this._pageTxt = ComponentFactory.Instance.creatComponentByStylename("IndividualLottery.pageTxt");
         this._prevBtn = ComponentFactory.Instance.creatComponentByStylename("IndividualLottery.prevBtn");
         this._nextBtn = ComponentFactory.Instance.creatComponentByStylename("IndividualLottery.nextBtn");
         this._items = new Vector.<BagCell>();
         this._list.beginChanges();
         _local_5 = 0;
         while(_local_5 < 20)
         {
            _local_3 = new BagCell(_local_5);
            this._items.push(_local_3);
            this._list.addChild(_local_3);
            _local_5++;
         }
         this._list.commitChanges();
         addToContent(_local_4);
         addToContent(_local_1);
         addToContent(_local_2);
         addToContent(this._list);
         addToContent(this._pageTxt);
         addToContent(this._prevBtn);
         addToContent(this._nextBtn);
         escEnable = true;
         titleText = LanguageMgr.GetTranslation("tank.view.caddy.lookTrophy");
      }
      
      private function initEvents() : void
      {
         addEventListener("response",this._response);
         this._prevBtn.addEventListener("click",this._prevClick);
         this._nextBtn.addEventListener("click",this._nextClick);
      }
      
      private function removeEvents() : void
      {
         removeEventListener("response",this._response);
         if(Boolean(this._prevBtn))
         {
            this._prevBtn.removeEventListener("click",this._prevClick);
         }
         if(Boolean(this._nextBtn))
         {
            this._nextBtn.removeEventListener("click",this._nextClick);
         }
      }
      
      private function _response(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         if(_arg_1.responseCode == 0 || _arg_1.responseCode == 1)
         {
            this.hide();
         }
      }
      
      private function _nextClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         ++this.page;
         if(this.page > this.pageSum())
         {
            this.page = 1;
         }
         this.fillPage();
      }
      
      private function _prevClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         --this.page;
         if(this.page < 1)
         {
            this.page = this.pageSum();
         }
         this.fillPage();
      }
      
      private function fillPage() : void
      {
         var _local_3:int = 0;
         var _local_1:int = 0;
         var _local_4:int = (this.page - 1) * 20;
         var _local_2:int = this.page * 20;
         _local_3 = _local_4;
         while(_local_3 < _local_2)
         {
            if(_local_1 < this._items.length && _local_3 < this._boxTempIDList.length)
            {
               this._items[_local_1].info = this._boxTempIDList[_local_3];
            }
            else
            {
               this._items[_local_1].info = null;
            }
            _local_3++;
            _local_1++;
         }
      }
      
      private function getTemplateInfo(_arg_1:int) : InventoryItemInfo
      {
         var _local_2:InventoryItemInfo = new InventoryItemInfo();
         _local_2.TemplateID = _arg_1;
         ItemManager.fill(_local_2);
         return _local_2;
      }
      
      public function set page(_arg_1:int) : void
      {
         this._page = _arg_1;
         this._pageTxt.text = this._page + "/" + this.pageSum();
      }
      
      public function get page() : int
      {
         return this._page;
      }
      
      public function pageSum() : int
      {
         return Math.ceil(this._boxTempIDList.length / 20);
      }
      
      public function show(_arg_1:Vector.<InventoryItemInfo>) : void
      {
         this._boxTempIDList = _arg_1;
         this.page = 1;
         this.fillPage();
         LayerManager.Instance.addToLayer(this,2,true,2);
         this.y -= 50;
      }
      
      public function hide() : void
      {
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
      
      override public function dispose() : void
      {
         var _local_1:int = 0;
         this.removeEvents();
         if(Boolean(this._list))
         {
            ObjectUtils.disposeObject(this._list);
         }
         this._list = null;
         if(Boolean(this._prevBtn))
         {
            ObjectUtils.disposeObject(this._prevBtn);
         }
         this._prevBtn = null;
         if(Boolean(this._nextBtn))
         {
            ObjectUtils.disposeObject(this._nextBtn);
         }
         this._nextBtn = null;
         if(Boolean(this._pageTxt))
         {
            ObjectUtils.disposeObject(this._pageTxt);
         }
         this._pageTxt = null;
         if(this._items != null)
         {
            _local_1 = 0;
            while(_local_1 < this._items.length)
            {
               ObjectUtils.disposeObject(this._items[_local_1]);
               _local_1++;
            }
            this._items = null;
         }
         this._boxTempIDList = null;
         super.dispose();
         ObjectUtils.disposeAllChildren(this);
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

